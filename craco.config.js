const path = require('path');

module.exports = {
  webpack: {
    alias: {
      '@common': path.resolve(__dirname, 'src/common/'),
      '@shared': path.resolve(__dirname, 'src/shared/'),
      '@core': path.resolve(__dirname, 'src/core/'),
      '@hooks': path.resolve(__dirname, 'src/hooks/'),
      '@eximShared': path.resolve(__dirname, 'src/shared/components/'),
      '@assets': path.resolve(__dirname, 'src/assets/'),
      '@modules': path.resolve(__dirname, 'src/modules/'),
      '@submodules': path.resolve(
        __dirname,
        'src/modules/src/shared-components/'
      ),
      '@store': path.resolve(__dirname, 'src/rootStore/store'),
      '@profile': path.resolve(__dirname, 'src/pages/Profile/'),
      '@subscription': path.resolve(__dirname, 'src/pages/Subscription/'),
      '@pages': path.resolve(__dirname, 'src/pages/'),
      '@utils': path.resolve(__dirname, 'src/utils'),
    }
  },
  jest: {
    configure: {
      moduleNameMapper: {
        '^@common/(.*)$': '<rootDir>/src/common/$1',
        '^@shared/(.*)$': '<rootDir>/src/shared/$1',
        '^@core/(.*)$': '<rootDir>/src/core/$1',
        '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
        '^@eximShared/(.*)$': '<rootDir>/src/shared/components/$1',
        '^@assets/(.*)$': '<rootDir>/src/assets/$1',
        '^@submodules/(.*)$': '<rootDir>/src/modules/src/shared-components/$1',
        '^@store(.*)$': '<rootDir>/src/rootStore/store',
        '^@profile/(.*)$': '<rootDir>/src/pages/Profile/$1',
        '^@subscription/(.*)$': '<rootDir>/src/pages/Subscription/$1',
        '^@pages/(.*)$': '<rootDir>/src/pages/$1',
        '^@utils(.*)$': '<rootDir>/src/utils',
      },
    },
  },
};
