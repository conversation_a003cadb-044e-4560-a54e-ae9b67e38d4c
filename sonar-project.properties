sonar.projectkey= GSTFEQA
sonar.login=****************************************
sonar.encoding=UTF-8
sonar.sources=/home/<USER>/.jen<PERSON>_workspace/workspace/GSTH_FE_QA/src
sonar.host.url=http://*************
sonar.exclusions=coverage/**,.nyc_output/**,node_modules/**, reports/**
sonar.nodejs.executable=/usr/bin/nodejs
# sonar.tests= ./src          
# sonar.test.inclusions=src/**/*.spec.js,src/**/*.spec.jsx,src/**/*.test.js,src/**/*.test.jsx
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.testExecutionReports=output/coverage/jest/cobertura-coverage.xml