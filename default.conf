#resolver @VAR_RESOLVER_IP@  valid=10s;      #**********
server {
    listen       80;
    server_name  localhost;
    server_tokens off;

    #charset koi8-r;
    access_log  /var/log/nginx/host.access.log;
    error_log  /var/log/nginx/error.log;
    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html =404;
    }
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options   "nosniff" always;
    add_header Content-Security-Policy  "default-src 'self' https://*; img-src * data:; frame-src * blob: data:; connect-src *; manifest-src *; style-src * 'unsafe-inline'; script-src-elem * 'unsafe-inline'; font-src *; script-src * 'unsafe-hashes' 'unsafe-inline'" always;
    add_header Strict-Transport-Security    "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options  "SAMEORIGIN" always;

    client_max_body_size 50M;


##
# `gzip` Settings
#
#
gzip on;
gzip_disable "msie6";

gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_buffers 16 8k;
gzip_http_version 1.1;
gzip_min_length 256;
gzip_types
  application/atom+xml
  application/geo+json
  application/javascript
  application/x-javascript
  application/json
  application/ld+json
  application/manifest+json
  application/rdf+xml
  application/rss+xml
  application/xhtml+xml
  application/xml
  font/eot
  font/otf
  font/ttf
  image/svg+xml
  image/gif
  text/css
  text/javascript
  text/plain
  text/xml;
}