{"name": "db_bank_fe", "version": "0.1.0", "private": true, "cracoConfig": "craco.config.js", "module": "true", "dependencies": {"@craco/craco": "^7.0.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/material": "^5.11.6", "@reduxjs/toolkit": "^1.8.5", "@tanstack/react-query": "^4.2.3", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react-datepicker": "^4.8.0", "@types/react-dom": "^18.0.6", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.10", "@types/react-transition-group": "^4.4.5", "@types/redux-persist": "^4.3.1", "axios": "^0.27.2", "axios-mock-adapter": "^1.21.2", "babel-plugin-macros": "^3.1.0", "crypto-js": "^4.1.1", "date-fns": "^2.29.3", "formik": "^2.2.9", "i18next": "^21.8.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.1", "i18next-xhr-backend": "^3.2.2", "lint-staged": "^13.0.3", "moment": "^2.29.4", "react": "^18.2.0", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-i18next": "^11.18.3", "react-icons": "^4.4.0", "react-redux": "^8.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-scroll-to-top": "^3.0.0", "react-slick": "^0.29.0", "react-transition-group": "^4.4.5", "redux-mock-store": "^1.5.4", "redux-persist": "^6.0.0", "rfs": "^9.0.6", "sass": "^1.54.9", "slick-carousel": "^1.8.1", "styled-components": "^5.3.5", "web-vitals": "^2.1.4", "yarn": "^1.22.22", "yup": "^0.32.11"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "lint": "eslint .", "fix": "eslint . --fix", "check-types": "tsc -p tsconfig.json --noEmit", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "format": "prettier --write \"./**/*.{ts,tsx,json}", "prepare": "husky install", "test-coverage": "npm run test -- --coverage --coverageDirectory=output/coverage/jest --watchAll=false"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"moduleNameMapper": {"^@common/(.*)$": "<rootDir>/src/common/$1", "^@shared/(.*)$": "<rootDir>/src/shared/$1", "^@core/(.*)$": "<rootDir>/src/core/$1", "^@eximShared/(.*)$": "<rootDir>/src/shared/components/$1", "^@assets/(.*)$": "<rootDir>/src/assets/$1", "^@modules/(.*)$": "<rootDir>/src/modules/$1", "^@submodules/(.*)$": "<rootDir>/src/modules/src/shared-components/$1", "^@store(.*)$": "<rootDir>/src/rootStore/store", "^@profile/(.*)$": "<rootDir>/src/pages/Profile/$1", "^@subscription/(.*)$": "<rootDir>/src/pages/Subscription/$1", "^@pages/(.*)$": "<rootDir>/src/pages/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.stories.tsx", "!src/assets/i18n/index.ts", "!src/modules/**/*.{js,jsx,ts,tsx}"], "transform": {"^.+\\.(js|jsx)$": "babel-jest"}, "coverageReporters": ["text", "cobertura"]}, "devDependencies": {"@babel/core": "^7.18.10", "@storybook/addon-actions": "^6.5.10", "@storybook/addon-essentials": "^6.5.10", "@storybook/addon-interactions": "^6.5.10", "@storybook/addon-links": "^6.5.10", "@storybook/builder-webpack5": "^6.5.10", "@storybook/manager-webpack5": "^6.5.10", "@storybook/node-logger": "^6.5.10", "@storybook/preset-create-react-app": "^4.1.2", "@storybook/react": "^6.5.10", "@storybook/testing-library": "^0.0.13", "@trivago/prettier-plugin-sort-imports": "^3.3.0", "@types/crypto-js": "^4.1.2", "@types/react": "^18.0.17", "@types/redux-mock-store": "^1.0.3", "@types/styled-components": "^5.1.25", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.5", "css-loader": "^6.7.1", "eslint": "^8.27.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.4", "husky": "^8.0.2", "prettier": "^2.7.1", "react-test-renderer": "^18.2.0", "sass-loader": "^10.2.0", "style-loader": "^3.3.1", "typescript": "^4.7.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint"]}}