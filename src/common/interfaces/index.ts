import {AxiosResponse} from 'axios';

export interface ICustomAxiosResp extends AxiosResponse {
  msg: string;
  message: string;
  statusCode: number;
}

export interface IIConProps {
  fill?: string;
  width?: string | number;
  height?: string | number;
  opacity?: number;
}

export interface ITableCommonHeader {
  title: string;
  width?: string;
  colspan?: number;
  rowSpan?: number;
  order?: 'asc' | 'desc' | undefined;
  sortingKey?: string;
}

export interface IUserData {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  userID: string;
  uuid: string;
  createdAt: string;
  isNewUser?: boolean;
}
export interface IUserProfileData {
  email: string;
  firstName: string;
  lastName: string;
  mobile: string;
  uuid: string;
}
export interface ICompanyProfileData {
  pan: string;
  orgName: string;
  unitName: string;
  stateOfBusiness: string;
  typeOfDealer: string;
  city: string;
  userType: string;
  iecDetails: {
    iecCode: string;
    iceGateUserName: string;
  };
  gstinNumber: string;
}

export interface IBusinessProfileDetails {
  businessName: string;
  unitName: string;
  statusOfBusiness: string;
  city: string;
  typeOfDealer: string;
  userType: string;
}

export interface IUserDetails {
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  designation: string;
  type: string;
  status?: string;
  uuid?: string;
}

export interface IProductDetails {
  availableProductRoles?: string[];
  productName: string;
  roleName: string;
}
export interface IBusinessDetails {
  iecCode: string;
  pan: string;
  organizationName: string;
  productDetails: IProductDetails[];
}
export interface IAllUsersDetails {
  userDetails: IUserDetails;
  businessDetailsBeans: IBusinessDetails[];
}

export interface IAddSignatoryDetails {
  name: string;
  designation: string;
  report_type: string;
}

export interface ISignatoryDetails extends IAddSignatoryDetails {
  ref_id: number;
}

export interface ISubscribedProducts {
  [key: string]: {
    [key: string]: boolean;
  };
}

export interface IGstinDetails {
  gstinNumber: string;
  panNumber: string;
  iecNumber: string;
}

export interface IPartnerDetailsResp {
  companyName: string;
  partnerCode: string;
}

export interface IPlan {
  planCode: string;
  planName: string;
  featureName: string;
  featureDescription: string;
  value: string;
}

export interface ISubscriptionAddon {
  name: string;
  addonCode: string;
  description: string;
  price: number | null;
  type: string | null;
  uiSequence: number;
  planCode: string | null;
  productName: string | null;
  purchased: boolean;
}

export interface IPlanData {
  planCode: string;
  planName: string;
}

export interface IRegularDropdownData {
  value: string;
  label: string;
  id: string | number;
}

export interface IActionableDropdownData {
  value: string;
  label: string;
  id: string | number;
  disabled?: boolean;
  hasSubDropdown?: boolean;
  subDropdownList?: {
    value: string;
    label: string;
    id: string | number;
    disabled?: boolean;
  }[];
}

export type DropdownOptionType = {
  id: number;
  title: string;
  uniqueId: string;
  isChecked?: boolean;
};

export type SetFieldValue = (field: string, value: string | boolean) => void;
export type SetFieldTouched = (field: string, touched: boolean) => void;
export type SetFieldError = (field: string, error: string) => void;

export interface IUploadLogs {
  'file-type': string;
  'file-name': string;
  'processing-status': string;
  'last-updated-date': string;
  'txn-id': string;
  'txn-type': string;
  'processing-details': {
    [key: string]: number;
  };
  'total-file-count': number;
  'last-updated-by': string;
  'error-message'?: string;
}

export interface IUploadFileDetails {
  'file-id': string;
  'file-name': string;
}

export interface IUploadDetails {
  'last-updated-by': string;
  'last-updated-date': string;
  'total-file-count': number;
  'file-details': IUploadFileDetails;
  'processing-details': {
    [key: string]: number;
  };
}

export interface IProcessedFile {
  'file-id': string;
  'file-name': string;
  'sb-no'?: string;
  'be-type'?: string;
  'boe-no'?: string;
  'sb-date'?: string;
  'boe-date'?: string;
  'last-updated-date': string;
  'upload-date': string;
  'last-updated-by': string;
  isSelected: boolean;
}
export interface IFileUploadHistoryData {
  'txn-id': string;
  'file-name': string;
  'last-updated-date': string;
  'last-updated-by': string;
  'processing-status': string;
  parsedDate: Date;
}

export interface IExportHistoryData {
  'file-type': string;
  'export-period': string;
  'file-id': string;
  'txn-id': string;
  'export-id': string;
  'exported-by': string;
  'file-count': number;
  'export-time': string;
  status: string;
  remark: string;
}

export interface IInputFilesProcessDetails extends IFileUploadHistoryData {
  'file-type': string;
  'txn-type': string;
  'error-message': string;
}

export interface ISbListTableDetails {
  selected?: boolean;
  'sb-inv-no'?: string;
  'sb-inv-date'?: string;
  'leo-date'?: string;
  'total-prods'?: number;
  'total-fob-value'?: number;
  'sb-ref-id': string;
  'applicant-type'?: string;
  'is-selected'?: boolean;
  'sb-prod-code'?: string;
}

export interface IBoeListTableDetails {
  selected?: boolean;
  'boe-inv-no'?: string;
  'boe-inv-date'?: string;
  'leo-date'?: string;
  'total-prods'?: number;
  'total-fob-value'?: number;
  'boe-ref-id': string;
  'applicant-type'?: string;
  'is-selected'?: boolean;
}

export interface ICheckedList {
  pageNumber: number;
  perPage: string;
}

export interface IInvoiceValues {
  value: string | number;
  isValid: boolean;
  errorMessage: string | null;
}

export interface IImportInvoices {
  selected: boolean;
  'boe-ref-id-list': string[];
  'boe-no': IInvoiceValues;
  'no-of-items': string;
  'total-duty': string;
  'boe-date': IInvoiceValues;
  'custom-house-name': IInvoiceValues;
  hsn: IInvoiceValues;
  'imported-qty': IInvoiceValues;
  'total-accessible-val': IInvoiceValues;
  uqc: IInvoiceValues;
  'bcd-rate': IInvoiceValues;
  'custom-cess-rate': IInvoiceValues;
  'total-item-qty-available': IInvoiceValues;
}

export interface IImportViewInvoices {
  selected: boolean;
  'item-code': IInvoiceValues;
  'item-desc': IInvoiceValues;
  'total-purchased-qty': IInvoiceValues;
  'total-value': IInvoiceValues;
  'total-available-qty': IInvoiceValues;
  'imported-qty': IInvoiceValues;
  hsn: IInvoiceValues;
  'total-accessible-val': IInvoiceValues;
  uqc: IInvoiceValues;
  'bcd-rate': IInvoiceValues;
  'custom-cess-rate': IInvoiceValues;
  'is-selected': boolean;
}

export interface IImportViewAllDetails {
  'boe-item-list'?: IImportViewInvoices[];
  'supplier-name'?: IInvoiceValues;
  'boe-no': IInvoiceValues;
  'boe-ref-id-list'?: string[];
  'boe-date': IInvoiceValues;
  'custom-house-name'?: IInvoiceValues;
  hsn?: IInvoiceValues;
  'imported-qty'?: IInvoiceValues;
  'total-accessible-val'?: IInvoiceValues;
  uqc?: IInvoiceValues;
  'bcd-rate'?: IInvoiceValues;
  'custom-cess-rate'?: IInvoiceValues;
  'is-final-assessment'?: IInvoiceValues;
  'imported-country'?: IInvoiceValues;
  'foreign-materials-sup-name'?: IInvoiceValues;
  'purchase-inv-date': IInvoiceValues;
  'purchase-inv-no': IInvoiceValues;
  'total-available-qty': IInvoiceValues;
  'total-utilized-qty': number;
}

export interface IExportInvoices {
  selected: boolean;
  'sb-inv-no': IInvoiceValues;
  'sb-inv-date': IInvoiceValues;
  'inv-val': IInvoiceValues;
  'shipping-bill-No': IInvoiceValues;
  'shipping-bill-date': IInvoiceValues;
  'total-fob-value': IInvoiceValues;
  'port-of-export': IInvoiceValues;
  'leo-date': IInvoiceValues;
  'applicant-type'?: IInvoiceValues;
  'iec-code'?: IInvoiceValues;
  'sb-ref-id': string;
}
export interface IExportViewInvoices {
  'fob-val': IInvoiceValues;
  'is-selected': boolean;
  'qty-considered': IInvoiceValues;
  'sb-prod-code': IInvoiceValues;
  'sb-prod-desc': IInvoiceValues;
  'sb-uqc': IInvoiceValues;
  selected: boolean;
  'total-available-qty': IInvoiceValues;
  'total-qty-exported': IInvoiceValues;
}

export interface IExportViewAllDetails extends IExportInvoices {
  'sb-prod-list': IExportViewInvoices[];
}

export interface IBOMListTable {
  'bom-ref-id': string;
  'prod-code': IInvoiceValues;
  'prod-desc': IInvoiceValues;
  'total-items'?: number;
  'bom-version': string;
}
export interface IDbkRateList {
  'product-code': IInvoiceValues;
  'product-description': IInvoiceValues;
  'dbk-rate': IInvoiceValues;
}
export interface IBomReviews {
  'ref-id': string;
  'prod-code': string;
  'prod-desc': string;
  'bom-version': string;
  'bom-version-list': [];
  'total-items': number;
  'item-list': [];
}

export interface IListWithoutBom {
  'prod-code': string;
  'prod-desc': string;
}

interface IBoeDetailsCommon {
  selected: boolean;
  'boe-no': string;
  'boe-date': string;
  'purchase-inv-no': string;
  'is-selected': boolean;
}

export interface IDbkBoeDetails extends IBoeDetailsCommon {
  'boe-ref-id-list': string[];
  'item-count': number;
  'total-item-qty-available': number;
  'total-accessable-val': number;
  'is-selected': boolean;
}

export interface IBoeItemSelection extends IBoeDetailsCommon {
  'boe-no': string;
  'item-code': string;
  'item-desc': string;
  'total-purchased-qty': number;
  'total-value': number;
  'total-available-qty': number;
  'qty-considered': number;
  'boe-ref-id': string;
}

export interface IBomDetails {
  'item-code': IInvoiceValues;
  'item-desc': IInvoiceValues;
  'procurement-type': IInvoiceValues;
  'item-qty': IInvoiceValues;
  uqc: IInvoiceValues;
}

export interface ISaveSbList {
  'claim-txn-id': string;
  'sb-product-select-txn-id': string;
  'start-period': string;
  'end-period': string;
  'total-sb-available': number;
  'total-sb-considered': number;
  'sb-list': ISbListTableDetails[];
  'total-prod-available': number;
  'total-prod-considered': number;
  'total-prod-qty-available': number;
  'total-prod-qty-considered': number;
  'total-boe-available': number;
  'total-boe-considered': number;
  'total-boe-items-available': number;
  'total-boe-items-considered': number;
  'total-boe-items-qty-available': number;
  'total-boe-items-qty-considered': number;
  'total-duty-amt': number;
  'total-records': number;
}

export interface IDbkClaimProductList {
  selected: boolean;
  'sb-prod-code': string;
  'sb-inv-no': string;
  'sb-prod-desc': string;
  'fob-val': number;
  'total-qty-exported': number;
  'total-available-qty': number;
  'qty-considered': number;
  'sb-ref-id': string;
  'is-selected': boolean;
}
export interface IDbkClaimItemList {
  selected: boolean;
  'boe-no': string;
  'boe-date': string;
  'purchase-inv-no': string;
  'item-code': string;
  'item-desc': string;
  'total-purchased-qty': number;
  'total-value': number;
  'total-available-qty': number;
  'qty-considered': number;
  'boe-ref-id': string;
  'is-selected': boolean;
}

export interface ISaveSbProductList {
  'claim-txn-id': string;
  'sb-product-select-txn-id': string;
  'total-sb-available': number;
  'total-sb-considered': number;
  'total-prod-available': number;
  'total-prod-considered': number;
  'total-prod-qty-available': number;
  'total-prod-qty-considered': number;
  'sb-prod-list': IDbkClaimProductList[];
  'total-boe-available': number;
  'total-boe-considered': number;
  'total-boe-items-available': number;
  'total-boe-items-considered': number;
  'total-boe-items-qty-available': number;
  'total-boe-items-qty-considered': number;
  'total-duty-amt': number;
  'total-records': number;
}

export interface IDbkClaimOverallSummary {
  particulars: string;
  shippingBills?: string;
  product?: string;
  billOfEntries?: string;
  item?: string;
}
export interface IBoeAllDataList {
  'claim-txn-id': string;
  'boe-item-select-txn-id': string;
  'total-sb-available': number;
  'total-sb-considered': number;
  'total-prod-available': number;
  'total-prod-considered': number;
  'total-prod-qty-available': number;
  'total-prod-qty-considered': number;
  'total-boe-available': number;
  'total-boe-considered': number;
  'total-boe-items-available': number;
  'total-boe-items-considered': number;
  'total-boe-items-qty-available': number;
  'total-boe-items-qty-considered': number;
  'total-duty-amt': number;
  'boe-list': IDbkBoeDetails[];
  'boe-item-list': IBoeItemSelection[];
}

export interface IClaimHistoryData {
  'claim-txn-id': string;
  status: string;
  'claim-name': string;
  'claim-status': string;
  'claim-date': string;
  'start-period': string;
  'end-period': string;
  'total-sb-available': number;
  'total-sb-considered': number;
  'total-prod-available': number;
  'total-prod-considered': number;
  'total-prod-qty-available': number;
  'total-prod-qty-considered': number;
  'total-boe-available': number;
  'total-boe-considered': number;
  'total-boe-items-available': number;
  'total-boe-items-considered': number;
  'total-boe-items-qty-available': number;
  'total-boe-items-qty-considered': number;
  'total-duty-amt': number;
}

export interface ICommonKeyValue {
  key: string;
  value: string;
}

export interface ISaveBoeItemList {
  'claim-txn-id': string;
  'boe-item-list': IDbkClaimItemList[];
}

export interface ISchemeStatus {
  'sub-txn-type': string;
  'sub-txn-name': string | null;
  'txn-status': string;
  'last-updated-date': string;
  'last-updated-by': string;
  'sub-txn-id': string | null;
  'claim-txn-id': string;
}

export interface ICalculationSummary {
  title: string;
  sb: number | null;
  prod: number | null;
  prodQty: number | null;
  boe: number | null;
  boeItems: number | null;
  itemsQty: number | null;
}

export interface IDbkCalculationData {
  'total-sb-available': number;
  'total-sb-value-available': number;
  'total-sb-considered': number;
  'total-sb-value-considered': number;
  'total-prod-available': number;
  'total-prod-considered': number;
  'total-prod-value-considered': number;
  'total-prod-qty-available': number;
  'total-prod-qty-considered': number;
  'total-prod-value-available': number;
  'total-boe-available': number;
  'total-boe-value-available': number;
  'total-boe-considered': number;
  'total-boe-value-considered': number;
  'total-boe-items-available': number;
  'total-boe-items-value-available': number;
  'total-boe-items-considered': number;
  'total-boe-items-qty-available': number;
  'total-boe-items-qty-considered': number;
  'total-boe-items-value-considered': number;
}

export interface ISupportedDoc {
  claimTxnId: string;
  fileUploadTxnId: string;
  fileName: string;
  category: string;
  lastUploadedDate: string;
  lastUploadedBy: string;
  status: string;
}

export interface IFinalSummaryClaimDetails {
  govDbkAmt: number;
  govDbkRate: number;
  sbDate: string;
  sbNo: string;
  prodCode: string;
  prodDesc: string;
  qualifyingStatus: string;
  systemDbkAmt: number;
  systemDbkRate: number;
  totalFobVal: number;
  totalItemQtyShortfall: number;
  totalQtyConsidered: number;
  disqualificationReason: string;
}

// subscription
export interface IPlanDetails {
  code: string;
  name: string;
  description?: string;
  basePrice?: number;
}
export interface IFormInitial {
  iecCode: string;
  iceGateUserName: string;
  iceGatePassword: string;
}

export interface IPartnerDetailsProps {
  partnerCode: string;
  partnerDetailsData: IPartnerDetailsResp;
}

export interface GSTINDetails {
  gstin: string;
  legalName: string;
  tradeName: string;
}

export interface IECDetails {
  iceGatePassword: string;
  iceGateUserName: string;
  iecCode: string;
}

// Partner Details
export interface IPartnerDetails {
  companyName: string;
  partnerCode: string;
}

// Product Details
export interface AddonDetails {
  addonCode: string;
  purchaseStatus: string;
}

export interface PlanDetails {
  basePrice?: number;
  code: string;
  description?: string;
  name?: string;
  productName?: string;
}

export interface ProductDetails {
  addonDetails?: AddonDetails[];
  planDetails: PlanDetails;
  productName: string;
}

export interface OrganizationDetails {
  gstinDetails: GSTINDetails;
  iecDetails: IECDetails;
  orgName: string;
  pan: string;
}
export interface IGstDetails {
  businessName: string;
  taxPayerType: string;
  tradeName: string;
  status: string;
  gstin: string;
  city: string;
  stj: string;
  constitutionOfBusiness: string;
  registrationDate: string;
  panNumber: string;
}

export interface IProductPlanDetails {
  productName: string;
  planDetails: IPlanDetails;
}

export interface ICreateSubscription {
  organizationDetails: OrganizationDetails;
  partnerDetails: IPartnerDetails;
  productDetails: ProductDetails;
}
export interface ICreateSaveAndSubscribeLater {
  organizationDetails: OrganizationDetails;
  productDetails: {
    productName: string;
  };
}

export interface ISubscriptionData {
  pan: string;
  iecCode: string;
  unit?: number;
  paymentStatus: string;
  paymentStatusToolTip: string;
  businessName: string;
  unitName: string | null;
  planName: string;
  subscriptionId: string | null;
  endDate: string;
  organizationVOList: string | null;
  owner: boolean;
  iceGateVerified: boolean;
}

export interface ITransactionList {
  txn_id: string;
  txn_name: string;
  txn_type: string;
  start_prd: string;
  end_prd: string;
  status: string;
}

export interface ISubTransaction {
  sub_txn_type: string;
  sub_txn_name: string;
  status: string;
  last_updated_date: string;
  last_updated_by: string;
  sub_txn_id: string;
  txn_id: string;
}

export interface IMoowrInputFilesUploadStatus {
  file_type: string;
  file_name: string;
  processing_status: string;
  last_updated_date: string;
  last_updated_by: string;
  txn_id: string;
  txn_type: string;
  error_message: string | null;
}

export interface IMoowrProductItem {
  selected: boolean;
  ref_id: string;
  invoice_no: string;
  invoice_date: string;
  prod_code: string;
  prod_desc: string;
  hsn: string;
  uqc: string;
  qty_sold: number;
}

export interface IMoowrVersionList {
  bom_version: string;
  selected: boolean;
  ref_id: string;
}

export interface IMoowrBomDetails {
  item_code: IInvoiceValues;
  item_desc: IInvoiceValues;
  per_unit_qty: IInvoiceValues;
  uqc: IInvoiceValues;
  procurement_type: IInvoiceValues;
  sale_val_per_unit: IInvoiceValues;
}

export interface IMoowrBomReviews {
  prod_code: string;
  prod_desc: string;
  bom_version_dtls_list: IMoowrVersionList[];
  total_item_count: number;
}

export interface IMoowrMissingBom {
  prod_code: string;
  prod_desc: string;
}

export interface IMoowrInwardBoeList {
  purchase_inv_no: string;
  purchase_inv_date: string;
  total_unique_item_code: number;
  total_balance_qty: number;
  total_assessable_val: number;
}

export interface IMoowrInwardItemList {
  item_code: string;
  item_desc: string;
  total_val: number;
  total_imported_qty: number;
  purchase_inv_no: string;
  purchase_inv_date: string;
  total_unique_item_code: number;
  total_balance_qty: number;
  total_assessable_val: number;
}

export interface IMoowrCalcSummary {
  title: string;
  invoices: number | null;
  uniqueProd: number | null;
  prodQty: number | null;
  boe: number | null;
  uniqueItems: number | null;
}

export interface IMoowrTransFinalSummary {
  txn_id: string;
  pan: string;
  invoice_no: string;
  invoice_date: string;
  sb_no: string;
  sb_date: string;
  sale_type: string;
  total_inv_val: number;
  prod_code: string;
  prod_desc: string;
  uqc: string;
  total_sale_qty: number;
  total_item_qty_consumed: number;
  total_item_qty_balance: number;
  total_inv_val_per_prod: number;
  total_assessable_val: number;
  total_igst: number;
  total_bcd_amt: number;
  total_sgst: number;
  total_cgst: number;
  total_cess: number;
  total_duty: number;
}

export interface IMoowrViewPartialOutward {
  ref_id: string;
  selected: boolean;
  sale_type: IInvoiceValues;
  invoice_no: IInvoiceValues;
  invoice_date: IInvoiceValues;
  prod_code: IInvoiceValues;
  prod_desc: IInvoiceValues;
  hsn: IInvoiceValues;
  uqc: IInvoiceValues;
  qty_sold: IInvoiceValues;
}

export interface IMoowrViewInwardRegister {
  selected: boolean;
  purchase_type: string;
  boe_no: string;
  boe_date: string;
  boe_inv_no: string;
  boe_inv_date: string;
  sup_gstin: string;
  total_assessable_val: string;
  assessable_val: string;
  bcd_amt: number;
  sws_amt: string;
  oth_duties: number;
  total_duties: string;
  igst_amt: number;
  cgst_amt: number;
  sgst_amt: number;
  cess_amt: number;
  total_taxes: string;
  total_unique_item_code: string;
  total_balance_qty: string;
  boe_ref_id_list: string[];
}

export interface IMoowrViewBomList {
  selected: boolean;
  bom_ref_id: string;
  bom_version: string;
  total_items: number;
  item_list: string;
  prod_code: IInvoiceValues;
  prod_desc: IInvoiceValues;
}

export interface IMoowrViewOutwardRegister {
  selected: boolean;
  ref_id: string;
  sale_type: string;
  sb_no: string;
  sb_date: string;
  invoice_no: string;
  invoice_date: string;
  total_sale_qty: number;
  total_fob_or_taxable_value: number;
  total_taxes: number;
}

export interface IMoowrOutwardRegItemDtls {
  prod_code: IInvoiceValues;
  prod_desc: IInvoiceValues;
  hsn: IInvoiceValues;
  uqc: IInvoiceValues;
  qty_sold: IInvoiceValues;
  fob_or_taxable_value: IInvoiceValues;
  tax_rt: IInvoiceValues;
  igst_amt: IInvoiceValues;
  cgst_amt: IInvoiceValues;
  sgst_amt: IInvoiceValues;
  cess_amt: IInvoiceValues;
}

export interface IMoowrOutwardRegViewDtls {
  sale_type: IInvoiceValues;
  sb_no: IInvoiceValues;
  sb_date: IInvoiceValues;
  leo_date: IInvoiceValues;
  port_of_export: IInvoiceValues;
  sup_gstin: IInvoiceValues;
  invoice_no: IInvoiceValues;
  invoice_date: IInvoiceValues;
  ctin: IInvoiceValues;
  pos: IInvoiceValues;
  total_inv_val: number;
  products: IMoowrOutwardRegItemDtls[];
}

export interface IMoowrJobWork {
  ref_id: string;
  selected: boolean;
  type_of_Processing: string;
  date: string;
  time: string;
  total_item_count: number;
  total_val: number;
  delivery_chalan: string;
}

export interface IMoowrJobWorkItemDtls {
  // item_code: {values: string[]};
  item_code: IInvoiceValues;
  item_desc: IInvoiceValues;
  item_qty: IInvoiceValues;
  uqc: IInvoiceValues;
  value: IInvoiceValues;
}

export interface IMoowrJobWorkViewDtls {
  type_of_Processing: IInvoiceValues;
  date: IInvoiceValues;
  time: IInvoiceValues;
  ewb: IInvoiceValues;
  delivery_chalan: IInvoiceValues;
  job_worker_name_address: IInvoiceValues;
  job_worker_gstin: IInvoiceValues;
  total_item_count: number;
  jw_item_list: IMoowrJobWorkItemDtls[];
}

export interface IFiledIdentificationStrategy {
  active: boolean;
  'is-active': boolean;
  'file-type': string;
  'target-field': string;
  'source-field': string;
  strategy: string;
  termination: string;
  'prefix-suffix': string;
  length: number;
  'special-char': string;
}

export interface IMoorFileUploadHistory {
  file_name: string;
  processing_status: string;
  last_updated_date: string;
  last_updated_by: string;
  txn_id: string;
  txn_type: string;
}

export interface IMoowrReconciliationInv {
  id: string;
  selected: boolean;
  consumptionName: string;
  consReportInvNo: string;
  consReportInvDate: string;
  consReportProdCode: string;
  consReportQtySold: string;
  sbNo: string;
  gstInvNo: string;
  gstInvDate: string;
  prodCode: string;
  qtySold: string;
}

export interface IMoowrRectifySummary {
  invCount: number;
  prodCount: number;
  prodQty: number;
}

export interface IInputFilesUploadStatus {
  file_type: string;
  file_name: string;
  processing_status: string;
  last_updated_date: string;
  last_updated_by: string;
  txn_id: string;
}

export interface IIrmDetails {
  irmIssueDate: string;
  irmNumber: string;
  irmStatus: string;
  remittanceDate: string;
  remittanceFCC: string;
  remittanceFCCAmount: number;
  ormAmountFCC: number | null;
  remitterName: string;
  purposeOfRemittance: string;
  irmAvailableAmt: string;
}

export interface IEbrcSbList {
  shippingBillNo: string;
  shippingBillDate: string;
  portCode: string;
  invoiceKey: string;
  billNo: string;
  sbFcVal: number;
  sbFcCd: string;
  sacCode: number;
}

export interface IOpenIrmDetails {
  selected: boolean;
  id: string;
  irmNumber: string;
  irmIssueDate: string;
  remitterName: string;
  remittanceFCC: string;
  remittanceFCCAmount: number;
}

export interface IOpenSbDetails {
  selected: boolean;
  id: string;
  shippingBillNo: string;
  shippingBillDate: string;
  consigneeName: string;
  sbFcVal: number;
  sbFcCd: string;
}

export interface ILinkIrmAndSb {
  iecCode: string;
  txnId: string;
  startPeriod: string;
  endPeriod: string;
  irmIdList: string[];
  sbIdList: string[];
}

export interface IPendingEbrc extends IEbrcSbList {
  selected: boolean;
  id: string;
  txnId: string;
  irmNumber: string;
  irmIssueDate: string;
  remitterName: string;
  remittanceFCC: string;
  remittanceFCCAmount: number;
  purposeOfRemittance: string;
}

export interface IGeneratedEbrc {
  selected: boolean;
  id: string;
  txnId: string;
  requestId: string;
  requestProcessingStatus: string;
  iec: string;
  billNo: string;
  irmNo: string;
  irmDate: string;
  sbCumInvoiceNumber: string;
  sbCumInvoiceDate: string;
  realisedValueFCC: number;
  currencyCode: string;
  realizationDt: string;
  brcUtilStatus: string;
  eBRCNumber: string;
  eBRCDate: string;
  eBRCStatus: string;
  requestProcessingErrorDetails: string;
}

export interface IBillingOrganizationDetails {
  gstinDetails: GSTINDetails;
  orgName: string;
  pan: string;
  orgRole?: string;
}

export interface ICreateBillingSubscription {
  organizationDetails: IBillingOrganizationDetails;
  partnerDetails: IPartnerDetails;
  productDetails?: IProductPlanDetails[];
}

export interface IAccountSetupAddress {
  address1: string;
  address2: string;
  pincode: string;
  city: string;
  state: string;
  country: string;
  billingAddress1: string;
  billingAddress2: string;
  billingPincode: string;
  billingCity: string;
  billingState: string;
  billingCountry: string;
  sameAsBilling: boolean;
}

export interface IEbrcSummaryData {
  totalIrmValue: number;
  totalEbrcGenerated: number;
  totalNotConsiderForEbrc: number;
  totalShippingBillValue: number;
  totalShippingBillGenerated: number;
  totalNotConsiderForShippingBill: number;
}

export interface ICommonScrollableData {
  title: string;
  value: string;
  isPrimary?: boolean;
  productName?: string;
  status?: string;
  subscriptionId?: string;
  subscriptionEndDate?: string;
  businessUnitSubscribed?: number;
  availableSubscriptionCount?: number;
  disabled?: boolean;
  subscriptionStatus?: string;
  tinId?: number;
  planCode?: string;
}

export interface IProductData {
  productName: string;
  status: string;
  businessUnitSubscribed: number;
  availableSubscriptionCount: number;
  subscriptionId?: string;
  subscriptionStatus?: string;
  subscriptionEndDate?: string;
}

export interface IBusinessData {
  primary: boolean;
  isPrimary: boolean;
  pan: string;
  gstin: string;
  orgId: number;
  organizationName: string;
}

export interface IDashboardBusinessDetails {
  'total-records': number;
  records: IBusinessData[];
}

export interface ISubscriptionDetails {
  availableSubscriptionCount: number;
  businessUnitSubscribed: number;
}
