import store from '@store';
import {fireEvent, render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';

import Navbar from '.';

describe('Navbar component', () => {
  it('render the Navbar default props', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <Navbar />
        </Provider>
      </BrowserRouter>
    );
  });

  expect(screen.getAllByTestId('Navbar-wrapper'));
  const menuOptions = screen.getAllByTestId('menu-btn');
  expect(menuOptions).toHaveLength(9);

  const userMenuBtn = menuOptions[7];
  userEvent.click(userMenuBtn);

  const openDrop = screen.getAllByTestId('Navbar-dropdown');
  expect(openDrop[7]).toHaveClass('Navbar-dropdown-open');

  const outsideDiv = screen.getByTestId('outside-div');
  fireEvent.mouseDown(outsideDiv);

  expect(openDrop[7]).not.toHaveClass('Navbar-dropdown-open');
});
