@import '@utils/main.scss';

.header-wrapper {
  background-color: $tertiary;
  position: sticky;
  top: 0;
  z-index: 9;

  nav {
    .header-container {
      @include flex-item(_, space-between, _);
      color: $white;
      height: 50px;
      @include padding(0 5px 0 20px);
      z-index: 100;
      position: sticky;
      top: 0;
      opacity: 1;

      a {
        color: inherit;
      }
      & > li {
        @include flex-item(_, center, center);
      }
      .middle-part {
        @include margin-left(50px);
        @include flex-item(_, flex-start, _, _, 0px);
        flex: 1;
        @include font-size($font-size-sm);
        .menu-wrapper {
          .menu-list {
            button {
              @include padding(15px 15px);
            }
          }
        }
      }

      .right-part {
        .authenticated {
          @include flex-item(_, flex-start, center, _, 0px);
          .menu-wrapper {
            position: relative;
            .menu-list {
              button {
                @include padding(15px 18px);
                &:hover {
                  background-color: $primary;
                  color: $white;
                }
              }
            }
          }
        }
        .unauthenticated {
          @include flex-item(_, flex-start, _, _, 0px);
          .menu-wrapper {
            position: relative;
            .menu-list {
              button {
                @include margin(0px 15px 0px 0px);
                @include padding-top(14px);
              }
              .btn-active {
                background-color: inherit;
                color: inherit;
              }
            }
          }
        }
      }
    }
  }
}

.menu-wrapper {
  .menu-list {
    height: 3.21875rem;
    .menu-btn {
      @include flex-item(_, _, center, _, 10px);
      outline: none;
      border: none;
      background: inherit;
      height: 50px;
      color: $white;
      cursor: pointer;
      @include font-size($font-size-sm);
      transition: all 0.5s;
    }
    .menu-tags {
      &:hover {
        background-color: $primary;
        color: $white;
      }
    }
    .btn-active {
      background-color: $primary;
      color: $white;
    }
    .user-initials {
      height: 28px;
      width: 28px;
      border-radius: 50%;
      align-self: center;
      font-weight: $font-weight-semi-bold;
      letter-spacing: 0.5px;
      @include padding(5px 2px 0px 2px);
      background-color: $primary-border-light;
      color: $header-user-initials-color;
    }
    .rotate-180-deg {
      transform: rotate(180deg);
    }
  }

  .header-dropdown-wrapper {
    min-width: 200px;
    max-height: 0;
    width: max-content;
    position: absolute;
    overflow-y: hidden;
    display: none;
    top: 50px;
    @include flex-item(column, space-around, flex-start);
    background-color: $white;
    color: $text-color;
    box-shadow: 0px 3px 6px $btn-box-shadow-hover;
    @include rfs(0px 0px 5px 5px, 'border-radius');
    font-weight: $font-weight-regular;
    flex-wrap: nowrap;
    transition: all 0.25s;

    .header-dropdown-list {
      width: 100%;
      transition: all 0.5s;
      &:hover {
        cursor: pointer;
        background-color: $primary-border-light;
        @include rfs(0px 0px 5px 5px, 'border-radius');
      }
      .text-primary {
        color: $primary;
        font-weight: $font-weight-bold;
      }
      .header-dropdown-item {
        @include padding(10px 15px);
        width: 100%;
        z-index: 100;
      }
    }
  }

  .header-dropdown-open {
    max-height: fit-content;
    transition: all 0.4s;
  }

  .user-header-dropdown {
    position: absolute;
    height: auto;
    top: 50px;
    right: 0;
    text-align: center;
    width: 250px;
    @include font-size($font-size-sm);

    li:first-child {
      @include margin-bottom(5px);
      &:hover {
        background: none;
        cursor: default;
      }
    }

    li:last-child {
      width: 100%;
      border-top: 1px solid $primary-border-light;
      @include flex-item(row, center, center, _, _);
      color: $primary;
      a {
        .header-dropdown-item {
          width: 250px;
        }
      }
      .sing-out {
        color: inherit;
        @include font-size($font-size-md);
        font-weight: $font-weight-heavy;
        @include padding(5px);
      }
      &:hover {
        color: $text-color;
      }
    }
  }

  .settings-header {
    @include flex-item(_, _, baseline, _, _);
    min-width: 492px;
    position: absolute;
    top: 50px;
    z-index: 10;
    color: $black;
    right: 0;
    width: max-content;
    box-shadow: 0px 3px 6px $btn-box-shadow-hover;
    background-color: $white;
    max-height: 0;
    overflow-y: hidden;
    transition: all 0.35s;
    .settings-menu-container {
      width: 50%;
      min-width: 246px;
      @include flex-item(column, center, flex-start);
      @include padding(20px 0);

      .settings-menu-title {
        @include padding(10px 36px);
        @include font-size($font-size-md);
        font-weight: $font-weight-bold;
        background-color: $white;
        color: $table-head-1;
        text-align: left !important;
      }
      .setting-li-ul {
        width: 100%;
        @include padding-right(24px);
      }
      .settings-header-dropdown {
        height: fit-content;
        font-size: $font-size-sm;
        @include padding-left(24px);
        li {
          justify-content: flex-start;
          &:hover {
            cursor: pointer;
            background: $primary-border-light;
          }
        }

        .header-dropdown-list {
          width: 100%;
          @include padding(12px 10px 8px 10px);
          color: $text-color;
          font-weight: $font-weight-light;
          &:hover {
            border-radius: 0;
          }
          .settings-link-container {
            @include flex-item(_, _, _, _, 8px);
          }
        }
      }
    }

    .border-left {
      border-left: 1px solid $btn-box-shadow-hover;
    }
  }

  .settings-header-open {
    z-index: 10;
    max-height: 300px;
    transition: all 0.35s;
  }

  .animated-link {
    & a {
      background-size: 200% 100%;
      background-position: -100%;
      display: inline-block;
      @include padding(5px 0);
      position: relative;
      transition: all 0.3s ease-in-out;
    }
    & a:before {
      content: '';
      background: $white;
      display: block;
      position: absolute;
      bottom: 4px;
      left: 3px;
      width: 0;
      height: 2px;
      transition: all 0.3s ease-in-out;
    }

    & a:hover {
      background-position: 0;
    }

    & a:hover::before {
      width: 100%;
    }

    .icon-text-center {
      height: 20px;
      @include flex-item(_, _, _, _, 5px);
      @include margin-top(-2px);
      .support-contact-icon {
        width: 20px;
        @include flex-item(_, flex-end, baseline);
        @include margin-right(2px);
      }

      .support-contact {
        width: auto;
        height: 18px;
        @include margin-top(-2px);
      }
      .support-contact-pipe {
        @include margin(-2px 0 0 12px);
      }
    }
  }
}

// Style for user headers profile
.user-profile-header-dropdown {
  .header-dropdown-wrapper {
    min-width: 331px;
    height: auto;
  }
  .user-info-container {
    min-width: 100%;
    .user-info {
      @include padding(0 20px 10px);
      .profile-container {
        @include flex-item(_, flex-start, _, _, 28px);
        @include padding(32px);
        .user-profile {
          position: relative;
          span {
            position: absolute;
            bottom: 4px;
            right: -5px;
            cursor: pointer;
          }
        }
        .user-details {
          @include flex-item(column, _, flex-start, _, 4px);
          .user-name {
            text-transform: capitalize;
            @include font-styles(
              $font-size-md,
              $font-weight-semi-bold,
              $heading-color
            );
          }
          .user-created {
            font-size: $font-size-xsm;
            color: $label-color;
            text-align: left;
          }
          .user-actions {
            @include flex-item(_, center, center, _, 10px);
            font-weight: $font-weight-semi-bold;
            font-size: $font-size-xsm;
            letter-spacing: 0.5px;
            .my-profile {
              color: $information;
              cursor: pointer;
            }
            .my-profile + span {
              color: $label-color;
            }
            .logout {
              color: $error;
              cursor: pointer;
            }
          }
        }
      }
      .subscribe-details {
        @include flex-item(_, space-between);
        @include padding-left(32px);
        span {
          @include font-styles(
            $font-size-sm,
            $font-weight-semi-bold,
            $heading-color
          );
          letter-spacing: 0.5px;
          align-self: stretch;
          cursor: pointer;
        }
      }
    }

    .subscribe-products-container {
      height: max-content;

      .subscribe-products {
        @include flex-item(_, _, center, _, 20px);
        border-top: 1px solid $primary-border;
        @include padding(18px 52px);

        .product-icon {
          width: 58px;
          height: 44px;
          img {
            width: 100%;
          }
        }

        .product-details {
          @include flex-item(column, _, flex-start, _, 4px);
          .product-name {
            font-size: $font-size-sm;
            color: $heading-color;
          }
          .gstin-count,
          .expiry-date {
            font-size: $font-size-xsm;
            color: $label-color;
          }
        }
      }
    }
  }
}
