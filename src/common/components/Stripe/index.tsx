import EximButton from '@shared/components/EximButton';
import {ReactNode} from 'react';

import './index.scss';

interface IStripeProps {
  variant?: 'tertiary' | 'success' | 'info' | 'primary';
  children?: ReactNode;
  content: ReactNode;
  isBtn?: boolean;
  btnText?: string;
  onBtnClick?: () => void;
}

function Stripe({
  variant,
  content,
  isBtn,
  btnText,
  onBtnClick,
  children,
}: IStripeProps) {
  return (
    <div className={`stripe-container ${variant}`}>
      <p className='text'>{content}</p>
      {isBtn ? (
        <EximButton size='small' color={variant} onClick={onBtnClick}>
          {btnText}
        </EximButton>
      ) : null}
      {children ? <div className='children-container'>{children}</div> : null}
    </div>
  );
}

Stripe.defaultProps = {
  variant: 'tertiary',
  children: null,
  isBtn: false,
  btnText: 'Download',
  onBtnClick: () => {
    /* */
  },
};

export default Stripe;
