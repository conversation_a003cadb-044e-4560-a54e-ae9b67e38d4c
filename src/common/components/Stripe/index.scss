@import '@utils/main.scss';

.stripe-container {
  width: 100%;
  height: 48px;
  @include flex-item(_, space-between, center);
  @include padding(8px 16px);
  font-size: $font-size-sm;
  @include margin(18px 0);
  box-shadow: $alert-box-shadow;
  border-radius: 5px;

  &.success {
    background: $success-background;
    border: 1px solid $success-border;
    .text {
      color: $success-color;
    }
  }
  &.tertiary {
    background: $note-background;
    border: 1px solid $note-border;
    .text {
      color: $tertiary;
    }
  }
  &.info {
    background: $default-background;
    border: 1px solid $info-stripe-border;
    .text {
      color: $tertiary;
    }
  }
  &.primary {
    background: $error-background-color;
    border: 1px solid $error-border-color;
    .text {
      color: $error-color;
    }
  }

  .button-wrapper {
    .base-btn {
      font-size: $font-size-sm;
      height: 32px;
      min-width: 100px;
    }
  }
}
