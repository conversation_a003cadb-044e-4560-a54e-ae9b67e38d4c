import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {ReactNode} from 'react';

import './index.scss';

interface IFilingHead {
  filingHead: string;
  isBetaFeature?: boolean;
  isDownloadTemplate?: boolean;
  hasGuide?: boolean;
  onGuideClick?: () => void;
  onDownloadTemplatesClick?: () => void;
  templateDataTestId?: string;
  guideDataTestId?: string;
  children?: ReactNode;
}

export default function FilingHead({
  filingHead,
  isBetaFeature,
  isDownloadTemplate,
  hasGuide,
  onGuideClick,
  onDownloadTemplatesClick,
  templateDataTestId,
  guideDataTestId,
  children,
}: IFilingHead) {
  return (
    <div className='filing-head'>
      <div>
        <EximTypography variant='h4' fontWeight='bold'>
          {filingHead}
        </EximTypography>
        {hasGuide ? (
          <div className='guideBtn'>
            <EximButton
              // TODO: add button click actions for step by step guide
              onClick={onGuideClick}
              size='small'
              variant='outlined'
              color='tour-guide'
              dataTestId={guideDataTestId}>
              Guide
            </EximButton>
          </div>
        ) : null}

        {/* Beta feature note if available */}
        {isBetaFeature && (
          <>
            <EximTypography variant='body2'>Beta Version</EximTypography>
            <EximTypography>
              Final release version is available for certain type of
              subscriptions only
            </EximTypography>
          </>
        )}
      </div>
      {/* TODO: add templates downloading feature */}
      {isDownloadTemplate && (
        <EximButton
          dataTestId={templateDataTestId}
          onClick={onDownloadTemplatesClick}>
          Download Excel Templates
        </EximButton>
      )}
      <span className='children-item'>{children}</span>
    </div>
  );
}

FilingHead.defaultProps = {
  isBetaFeature: false,
  isDownloadTemplate: false,
  hasGuide: false,
  onGuideClick: () => {
    /** empty function */
  },
  onDownloadTemplatesClick: () => {
    /** empty function */
  },
  templateDataTestId: 'templateDataTestId',
  guideDataTestId: 'guideDataTestId',
  children: null,
};
