@import '@utils/main.scss';

.filing-head {
  height: 80px;
  border-bottom: 1px solid $primary-border;
  @include flex-item(_, space-between, center, _, _);
  @include padding(0 16px);

  & > div {
    @include flex-item(_, _, center, _, 12px);
    .guideBtn {
      .button-wrapper {
        min-width: 52px;
        width: 52px;
        .base-btn {
          @include margin(0);
          height: 21px;
          border: 2px solid;
          @include rfs(18px, border-radius);
          .btn-children {
            @include font-size($font-size-xsm);
          }

          &:hover {
            border: none;
          }
        }
      }
    }

    .typography-variant-h4 {
      width: max-content;
    }

    .typography-variant-h4,
    .typography-variant-body1 {
      color: $text-color;
      letter-spacing: 0.2px;
    }

    .typography-variant-body2 {
      color: $white;
      font-weight: $font-weight-semi-bold;
      @include font-size($font-size-sm);
      background-color: $warning;
      @include padding(0 5px);
      @include rfs(5px, border-radius);
    }
  }
  .button-wrapper {
    .base-btn {
      @include margin(0);
      @include padding(7px 10px);
      .btn-children {
        @include font-size($font-size-sm);
      }
    }
  }
}
