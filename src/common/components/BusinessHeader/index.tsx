import {EximProducts, Path} from '@common/constants';
import EximAvatar from '@shared/components/EximAvatar';
import EximLink from '@shared/components/EximLink';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {ReactNode, memo} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router';

import './index.scss';

interface IBusinessHeader {
  children?: ReactNode;
}

function BusinessHeader({children}: IBusinessHeader) {
  const {pathname} = useLocation();

  const {
    dashboardActiveProduct,
    selectedBusinessName,
    selectedPanNumber,
    selectedIECNumber,
  } = useSelector((state: RootState) => state.dashboard);

  return (
    <div className='business-details-card'>
      <EximPaper>
        <div className='business-container'>
          <div className='business-details'>
            <EximAvatar
              firstName={
                selectedBusinessName?.split(' ')[0] || selectedBusinessName[0]
              }
              lastName={
                selectedBusinessName?.split(' ')[1] || selectedBusinessName[0]
              }
              rounded
              alt='business name'
              size='small'
            />
            <div className='business-gstin-status'>
              <span>{selectedBusinessName}</span>
              <span>|</span>
              {dashboardActiveProduct === EximProducts.DATA_EXTRACTOR &&
              pathname?.includes(Path.DATA_EXTRACTOR) ? (
                <span>PAN No. {selectedPanNumber}</span>
              ) : (
                <span>IEC {selectedIECNumber}</span>
              )}
              <span>|</span>
              <span className='view-profile-link'>
                <EximLink
                  href={`${Path.PROFILE}${Path.COMPANY_PROFILE}`}
                  variantColor='primary'>
                  View Profile
                </EximLink>
              </span>
            </div>
          </div>
          <div className='business-header-actions'>{children}</div>
        </div>
      </EximPaper>
    </div>
  );
}

export default memo(BusinessHeader);

BusinessHeader.defaultProps = {
  children: null,
};
