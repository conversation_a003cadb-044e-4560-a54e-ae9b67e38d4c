@import '@utils/main.scss';

.business-details-card {
  .paper-wrapper-rounded {
    @include padding(0 16px);
    @include margin(0px auto 24px);
  }

  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
  }

  .business-container {
    @include padding(10px 0);
    @include flex-item(_, space-between, center);

    .avatar-wrapper {
      .avatar-container {
        .small {
          width: 30px;
          height: 30px;
        }

        .avatar-text {
          background-color: $default-background;
          border: none;
          color: $secondary-text;
          @include font-size($font-size-sm);
        }
      }
    }

    .business-details,
    .business-tax-period {
      color: $text-color;
      @include flex-item(_, _, center, _, 10px);

      .business-gstin-status {
        @include flex-item(_, _, center, _, 10px);
        font-size: $font-size-sm;
        line-height: 20px;

        .view-profile-link {
          font-size: $font-size-xsm;
        }
      }
    }

    .business-tax-period {
      .base-date-picker {
        width: 154px;
        height: 32px;
      }
    }
  }
}
