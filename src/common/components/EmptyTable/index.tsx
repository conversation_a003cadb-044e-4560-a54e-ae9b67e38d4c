import {ReactComponent as DashboardTableEmptyImage} from '@assets/EximImages/DashboardTableEmptyImage.svg';
import {Path} from '@common/constants';
import EximButton from '@shared/components/EximButton';
import {Plus} from '@shared/icons';
import OopsIcon from '@shared/icons/OopsIcon';
import {Link} from 'react-router-dom';

import './index.scss';

interface IEmptyTable {
  colSpan: number;
  isNoSubscription?: boolean;
}

export default function EmptyTable(props: IEmptyTable) {
  const {colSpan, isNoSubscription} = props;
  return (
    <tbody>
      <tr>
        <td colSpan={colSpan}>
          <div className='empty-table-cell'>
            {isNoSubscription ? (
              <div className='dashboard-no-subscription'>
                <DashboardTableEmptyImage />
                <div className='business-add-button'>
                  <Link to={`${Path.SUBSCRIPTION}`}>
                    <EximButton color='tertiary'>
                      <Plus fill='#fff' height={12} width={12} />
                      Add New Business
                    </EximButton>
                  </Link>
                </div>
              </div>
            ) : (
              <>
                <OopsIcon />
                <h1>OOOPS!</h1>
                <p>No matching records found</p>
              </>
            )}
          </div>
        </td>
      </tr>
    </tbody>
  );
}

EmptyTable.defaultProps = {
  isNoSubscription: false,
};
