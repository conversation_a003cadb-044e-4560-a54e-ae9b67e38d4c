@import '@utils/main.scss';

.empty-table-cell {
  @include flex-item(column, center, center, nowrap, 7px);
  min-height: 250px;
  border-bottom: 1px solid $input-border;
  border-left: 1px solid $input-border;
  border-right: 1px solid $input-border;
  color: $text-color;
  p {
    font-size: $font-size-md;
  }

  .dashboard-no-subscription {
    @include flex-item(column, center, center, nowrap);
    @include margin(41px 0 51px 0);

    .business-add-button {
      @include margin-top(17px);
      color: $white;
      min-width: 100px;

      .button-wrapper {
        width: 156px;
        .btn-children {
          font-size: $font-size-sm;
        }
        .base-btn {
          @include padding(10px 2px);
        }
      }

      .base-btn {
        @include padding(8px 10px);
        @include margin(0);
      }
    }
  }
}
