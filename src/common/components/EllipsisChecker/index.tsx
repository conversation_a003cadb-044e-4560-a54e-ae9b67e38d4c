import {useEffect, useRef, useState} from 'react';

import './index.scss';

interface IEllipsisCheckerProps {
  text: string;
  handleViewMore: () => void;
}

export default function EllipsisChecker({
  text,
  handleViewMore,
}: IEllipsisCheckerProps) {
  const textRef = useRef<HTMLDivElement>(null);
  const [hasEllipsis, setHasEllipsis] = useState<boolean>(false);

  useEffect(() => {
    if (textRef.current) {
      setHasEllipsis(
        textRef.current.scrollWidth > textRef.current.clientWidth + 5
      );
    }
  }, [text]);

  return (
    <div className='ellipsis-container'>
      <div className='ellipsis-text' ref={textRef}>
        {text}
      </div>
      {hasEllipsis && (
        <button
          className='view-more-btn'
          type='button'
          onClick={handleViewMore}>
          View More
        </button>
      )}
    </div>
  );
}
