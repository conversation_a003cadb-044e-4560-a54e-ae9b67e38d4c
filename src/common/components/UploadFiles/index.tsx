import {UploadIcon} from '@shared/icons';
import {ChangeEvent, ReactNode} from 'react';

import './index.scss';

interface IUploadFilesProps {
  multiple?: boolean;
  disabled?: boolean;
  title?: string;
  icon?: ReactNode;
  accept: string;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
}

function UploadFiles({
  title,
  icon,
  accept,
  multiple,
  disabled,
  onChange,
}: IUploadFilesProps) {
  return (
    <div className={`file-input-container ${disabled && 'disabled'}`}>
      <input
        type='file'
        id='file-input'
        className='hidden-input'
        onChange={onChange}
        multiple={multiple}
        disabled={disabled}
        accept={accept}
      />
      <label htmlFor='file-input' className='file-button'>
        {icon ? <span className='upload-icon'>{icon}</span> : null}
        <span className='upload-title'>{title}</span>
      </label>
    </div>
  );
}

export default UploadFiles;

UploadFiles.defaultProps = {
  title: 'Upload & Process Files',
  icon: <UploadIcon />,
  multiple: false,
  disabled: false,
};
