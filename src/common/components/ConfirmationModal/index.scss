@import '@utils/main.scss';

.confirmation-modal {
  letter-spacing: 0.2px;
  .modal-title h2 {
    color: $secondary-text;
    font-size: $font-size-xl;
  }
  .modal-body {
    width: 525px;
    .modal-content {
      width: 100%;
      padding-top: 0;
      min-height: 80px;
      .confirm-modal-container {
        width: 100%;
        @include flex-item(column, flex-start, flex-start, _, 24px);
        .btn-container {
          width: 100%;
          @include flex-item(_, flex-end, center, _, 16px);
          .button-wrapper {
            min-width: 100px;
            .base-btn {
              height: 32px;
              font-size: $font-size-sm;
              @include padding(7px 16px);
            }
          }
        }
      }
    }
  }
}
