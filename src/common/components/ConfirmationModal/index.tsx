import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';

import './index.scss';

interface IDeleteModalProps {
  isOpen: boolean;
  title?: string;
  content?: string;
  alignContent?: 'left' | 'center' | 'right';
  onClose: () => void;
  handleConfirm: () => void;
  primaryBtnText?: string;
  secondaryBtnText?: string;
}

export default function ConfirmationModal({
  isOpen,
  title,
  content,
  alignContent,
  onClose,
  handleConfirm,
  primaryBtnText,
  secondaryBtnText,
}: IDeleteModalProps) {
  return (
    <div className='confirmation-modal'>
      <EximModal
        isOpen={isOpen}
        onClose={onClose}
        onOutSideClickClose={onClose}
        content={
          <div className='confirm-modal-container'>
            <EximTypography
              classNames='modal-title'
              variant='h4'
              align={alignContent}>
              {content}
            </EximTypography>
            <div className='btn-container'>
              <EximButton size='small' color='secondary' onClick={onClose}>
                {secondaryBtnText}
              </EximButton>
              <EximButton size='small' onClick={handleConfirm}>
                {primaryBtnText}
              </EximButton>
            </div>
          </div>
        }
        header={
          <EximTypography variant='h2' fontWeight='bold'>
            {title}
          </EximTypography>
        }
        closeIcon={<CloseIcon width={15} height={15} />}
        footer={false}
      />
    </div>
  );
}

ConfirmationModal.defaultProps = {
  title: '',
  alignContent: 'center',
  content: 'Are you sure you want to delete the document?',
  primaryBtnText: 'Confirm',
  secondaryBtnText: 'Cancel',
};
