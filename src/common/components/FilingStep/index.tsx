import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {ReactNode} from 'react';

import './index.scss';

interface IFilingStep {
  stepIcon: ReactNode;
  stepEndIcon?: ReactNode;
  statusIcon?: ReactNode;
  filingName: string;
  status?: ReactNode;
  recentUpdate?: string;
  updatedBy?: string;
  btnName: string;
  btnColor?: 'tertiary' | 'secondary';
  btnVariant?: 'contained' | 'text' | 'outlined';
  btnDisable?: boolean;
  onBtnClick: () => void;
  btnDataTestId?: string;
  secondBtnName?: string;
  secondBtnColor?: 'tertiary' | 'secondary';
  secondBtnVariant?: 'contained' | 'text' | 'outlined';
  secondBtnDisable?: boolean;
  onSecondBtnClick?: () => void;
  secondBtnDataTestId?: string;
}

export default function FilingStep({
  stepIcon,
  stepEndIcon,
  statusIcon,
  filingName,
  status,
  recentUpdate,
  updatedBy,
  btnName,
  btnColor,
  btnVariant,
  btnDisable,
  onBtnClick,
  btnDataTestId,
  secondBtnName,
  secondBtnColor,
  secondBtnVariant,
  secondBtnDisable,
  secondBtnDataTestId,
  onSecondBtnClick,
}: IFilingStep) {
  return (
    <div className='filing-step'>
      <div className='details-container'>
        <div className='filing-type'>
          {stepIcon}
          <EximTypography fontWeight='normal'>{filingName}</EximTypography>
          {stepEndIcon}
        </div>
        <div className='filing-status'>
          {status}
          {statusIcon ? (
            <span className='status-icon'>{statusIcon}</span>
          ) : null}
        </div>
        <div className='filing-last-update'>
          <EximTypography fontWeight='normal'>{recentUpdate}</EximTypography>
        </div>
        <div className='filing-user'>
          <EximTypography fontWeight='normal'>{updatedBy}</EximTypography>
        </div>
      </div>
      <div className='button-container'>
        <EximButton
          onClick={onBtnClick}
          disabled={btnDisable}
          variant={btnVariant}
          color={btnColor}
          dataTestId={btnDataTestId}>
          {btnName}
        </EximButton>
        {secondBtnName && (
          <EximButton
            onClick={onSecondBtnClick}
            disabled={secondBtnDisable}
            variant={secondBtnVariant}
            color={secondBtnColor}
            dataTestId={secondBtnDataTestId}>
            {secondBtnName}
          </EximButton>
        )}
      </div>
    </div>
  );
}

FilingStep.defaultProps = {
  status: '',
  recentUpdate: '',
  updatedBy: '',
  stepEndIcon: null,
  statusIcon: null,
  btnColor: 'secondary',
  btnVariant: 'contained',
  btnDisable: true,
  btnDataTestId: 'btnDataTestId',
  secondBtnName: '',
  secondBtnColor: 'secondary',
  secondBtnVariant: 'contained',
  secondBtnDisable: true,
  onSecondBtnClick: () => {
    /** empty function */
  },
  secondBtnDataTestId: 'secondBtnDataTestId',
};
