@import '@utils/main.scss';
.filing-step {
  border-bottom: 1px solid $primary-border;
  @include flex-item(_, space-between, center, _, 12px);
  @include padding(22px 0);

  .details-container {
    flex: 0.8;
    @include flex-item(_, _, center, _, 20px);
    width: 100%;

    .filing-type {
      flex: 0.4;
      @include flex-item(_, _, center, _, 20px);
      min-width: 130px;
      .typography-container .typography-variant-body1.regular {
        font-weight: $font-weight-semi-bold;
      }

      .info-icons {
        @include margin-left(-10px);
        @include margin-top(2.7px);
        :hover {
          cursor: pointer;
        }
      }
    }

    .typography-container {
      font-weight: $font-weight-semi-bold;
      color: $text-color;
    }

    .filing-status {
      @include flex-item(_, _, center, _, 18px);
      flex: 0.3;
      min-width: 60px;
      .typography-container .typography-variant-body1.regular {
        font-weight: $font-weight-semi-bold;
      }
      .status-icon {
        height: 22px;
        &:hover {
          cursor: pointer;
        }
      }
    }

    .in-progress {
      color: $warning;
    }
    .failed {
      color: $error;
    }

    .discarded {
      color: $exim-success-background;
      opacity: 0.5;
    }

    .completed,
    .reconciled,
    .draft {
      color: $exim-success-background;
    }

    .filing-last-update {
      flex: 0.3;
      color: $heading-color;
      min-width: 90px;
      .typography-container .typography-variant-body1.regular {
        font-weight: $font-weight-semi-bold;
      }
    }

    .filing-user {
      flex: 0.3;
      color: $heading-color;
      text-align: center;
      min-width: 90px;
      text-transform: capitalize;
      .typography-container .typography-variant-body1.regular {
        font-weight: $font-weight-semi-bold;
      }
    }
  }

  .button-container {
    flex: 0.2;
    @include flex-item(_, flex-end, _, _, 28px);
    .button-wrapper {
      width: max-content;
      .base-btn {
        min-width: 114px;
        @include margin(0);
        @include padding(7px 10px);
        .btn-children {
          @include font-size($font-size-sm);
        }
      }
    }
  }
}
