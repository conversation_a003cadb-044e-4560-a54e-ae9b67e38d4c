import './index.scss';

interface IFileSelectionProps {
  selectedFileCount: number;
  totalRecords: number;
  handleToggleSelectAll: (type: 'CLEAR_ALL' | 'SELECT_ALL') => void;
}

export default function AllFileSelectionStripe({
  handleToggleSelectAll,
  selectedFileCount,
  totalRecords,
}: IFileSelectionProps) {
  return (
    <div className='file-selection-container'>
      <span className='selected-text'>
        {selectedFileCount === totalRecords
          ? `All ${selectedFileCount} `
          : `${selectedFileCount} `}
        {selectedFileCount > 1 ? 'Documents' : 'Document'} Selected on this
        page.
      </span>
      {selectedFileCount === totalRecords ? (
        <button
          type='button'
          className='select-btn'
          onClick={() => handleToggleSelectAll('CLEAR_ALL')}>
          Clear Selection
        </button>
      ) : (
        <button
          type='button'
          className='select-btn'
          onClick={() => handleToggleSelectAll('SELECT_ALL')}>
          Select All {totalRecords} Docs
        </button>
      )}
    </div>
  );
}
