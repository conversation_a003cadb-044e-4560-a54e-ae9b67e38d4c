@import '@utils/main.scss';

.file-selection-container {
  @include flex-item(_, flex-start, center, _, 16px);
  @include margin(0px 0 20px);
  @include padding(8px 0 8px 32px);
  background-color: $total-record-stripe;
  font-size: $font-size-sm;
  .selected-text {
    font-weight: $font-weight-semi-bold;
  }
  .select-btn {
    border: 1px solid $selection-all-border;
    background-color: $label-background;
    @include padding(3px 16px);
    @include rfs(3px, border-radius);
    cursor: pointer;
  }
}
