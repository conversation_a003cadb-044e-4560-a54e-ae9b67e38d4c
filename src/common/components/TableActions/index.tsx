import EximTooltip from '@shared/components/EximTooltip';
import {DeleteSolid, DownloadIcon, EditIcon, ViewIcon} from '@shared/icons';
import {ReactNode, memo} from 'react';

import './index.scss';

interface ITableActionsProps {
  children?: ReactNode;
  customIcon?: ReactNode;
  isViewIcon?: boolean;
  isEditIcon?: boolean;
  isDeleteIcon?: boolean;
  isDownloadIcon?: boolean;
  isViewIconDisabled?: boolean;
  isEditIconDisabled?: boolean;
  isDeleteIconDisabled?: boolean;
  isDownloadIconDisabled?: boolean;
  isCustomIconDisabled?: boolean;
  handleView?: () => void;
  handleEdit?: () => void;
  handleDelete?: () => void;
  handleDownload?: () => void;
  handleCustom?: () => void;
  viewToolTipText?: string;
  editToolTipText?: string;
  downloadToolTipText?: string;
  deleteToolTipText?: string;
  customToolTipText?: string;
}

function TableActions({
  children,
  customIcon,
  isViewIcon,
  isEditIcon,
  isDeleteIcon,
  isDownloadIcon,
  isViewIconDisabled,
  isEditIconDisabled,
  isDeleteIconDisabled,
  isDownloadIconDisabled,
  isCustomIconDisabled,
  viewToolTipText,
  editToolTipText,
  downloadToolTipText,
  deleteToolTipText,
  customToolTipText,
  handleView,
  handleEdit,
  handleDelete,
  handleDownload,
  handleCustom,
}: ITableActionsProps) {
  return (
    <div className='action-container'>
      {isViewIcon ? (
        <EximTooltip
          content={viewToolTipText}
          direction='top'
          className='icon-tooltip'>
          <span
            className={`action-icon ${
              isViewIconDisabled ? 'disabled-icon' : ''
            }`}
            role='presentation'
            onClick={isViewIconDisabled ? undefined : handleView}>
            <ViewIcon />
          </span>
        </EximTooltip>
      ) : null}
      {isDownloadIcon ? (
        <EximTooltip
          content={downloadToolTipText}
          direction='top'
          className='icon-tooltip'>
          <span
            className={`action-icon ${
              isDownloadIconDisabled ? 'disabled-icon' : ''
            }`}
            role='presentation'
            onClick={isDownloadIconDisabled ? undefined : handleDownload}>
            <DownloadIcon />
          </span>
        </EximTooltip>
      ) : null}
      {isEditIcon ? (
        <EximTooltip
          content={editToolTipText}
          direction='top'
          className='icon-tooltip'>
          <span
            className={`action-icon ${
              isEditIconDisabled ? 'disabled-icon' : ''
            }`}
            role='presentation'
            onClick={isEditIconDisabled ? undefined : handleEdit}>
            <EditIcon />
          </span>
        </EximTooltip>
      ) : null}
      {customIcon ? (
        <EximTooltip
          content={customToolTipText}
          direction='top'
          className='icon-tooltip'>
          <span
            className={`action-icon ${
              isCustomIconDisabled ? 'disabled-icon' : ''
            }`}
            role='presentation'
            onClick={isCustomIconDisabled ? undefined : handleCustom}>
            {customIcon}
          </span>
        </EximTooltip>
      ) : null}
      {isDeleteIcon ? (
        <EximTooltip
          content={deleteToolTipText}
          direction='top'
          className='icon-tooltip'>
          <span
            className={`action-icon ${
              isDeleteIconDisabled ? 'disabled-icon' : ''
            }`}
            role='presentation'
            onClick={isDeleteIconDisabled ? undefined : handleDelete}>
            <DeleteSolid />
          </span>
        </EximTooltip>
      ) : null}
      <span className='action-icon'>{children}</span>
    </div>
  );
}

export default memo(TableActions);

TableActions.defaultProps = {
  children: null,
  customIcon: null,
  isViewIcon: false,
  isEditIcon: false,
  isDeleteIcon: false,
  isDownloadIcon: false,
  isViewIconDisabled: false,
  isEditIconDisabled: false,
  isDeleteIconDisabled: false,
  isDownloadIconDisabled: false,
  isCustomIconDisabled: false,
  viewToolTipText: null,
  editToolTipText: null,
  downloadToolTipText: null,
  deleteToolTipText: null,
  customToolTipText: null,
  handleView: () => {
    /* */
  },
  handleEdit: () => {
    /* */
  },
  handleDelete: () => {
    /* */
  },
  handleDownload: () => {
    /* */
  },
  handleCustom: () => {
    /* */
  },
};
