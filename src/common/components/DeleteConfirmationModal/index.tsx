import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximTypography from '@shared/components/EximTypography';

import './index.scss';

interface IDeleteModalProps {
  isOpen: boolean;
  content?: string;
  onClose: () => void;
  handleConfirm: () => void;
}

export default function DeleteConfirmationModal({
  isOpen,
  content,
  onClose,
  handleConfirm,
}: IDeleteModalProps) {
  return (
    <div className='delete-confirmation-modal'>
      <EximModal
        isOpen={isOpen}
        onClose={onClose}
        onOutSideClickClose={onClose}
        content={
          <div className='delete-modal-container'>
            <EximTypography
              classNames='modal-title'
              variant='h4'
              align='center'>
              {content}
            </EximTypography>
            <div className='btn-container'>
              <EximButton size='small' color='secondary' onClick={onClose}>
                Cancel
              </EximButton>
              <EximButton size='small' onClick={handleConfirm}>
                Confirm
              </EximButton>
            </div>
          </div>
        }
        footer={false}
        header={null}
        closeIcon={null}
      />
    </div>
  );
}

DeleteConfirmationModal.defaultProps = {
  content: 'Are you sure you want to delete the document?',
};
