@import '@utils/main.scss';

.thead-container {
  .table-head-tr {
    .MuiTableCell-head.checkbox-th {
      .checkbox-wrapper {
        .checkbox-container {
          margin-bottom: 0;
          .checkmarks {
            top: -13px;
            left: 10px;
          }
        }
      }
    }
    .MuiTableCell-head {
      font-family: 'Source Sans Pro', sans-serif;
      position: relative;
      text-align: left;
      background-color: $table-head-primary;
      border-right: 1px solid $input-border;
      color: $white;
      font-size: $font-size-13;
      @include padding(13px 10px 13px 10px);
      border-top-width: 0px;
      border-bottom-width: 0px;
      font-weight: bold;
      line-height: 20px;
      letter-spacing: 0.2px;
      vertical-align: baseline;
      &:last-child {
        border-right: none;
      }
    }
    .sorting-arrow {
      cursor: pointer;
      position: absolute;
      right: 10px;
      top: 15px;
      height: 24px;
      svg {
        color: $white;
      }
    }
  }
  .sub-header {
    .border-top {
      background-color: $table-head-primary;
      border-top-width: 1px;
    }
  }
}

// Global style for tbody cells
.thead-container + .MuiTableBody-root {
  tr td {
    font-family: 'Source Sans Pro', sans-serif;
    border-left: 1px solid $input-border;
    color: $text-color;
    @include padding(9px 10px);
    height: 40px;
    &:last-child {
      border-right: 1px solid $input-border;
    }
  }
  .MuiTableCell-root.checkbox-td {
    .checkbox-wrapper {
      .checkbox-container {
        margin-bottom: 0;
        .checkmarks {
          top: -9.5px;
          left: 10px;
        }
      }
    }
  }
}
