import store from '@store';
import {render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';

import TableHeader from '.';

const TABLE_HEADER = [
  {title: 'Thead-1', width: 'auto'},
  {title: 'Thead-2', width: 'auto'},
];

describe('render the Table Head', () => {
  test('render the thead', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <TableHeader mainHeader={TABLE_HEADER} />
        </Provider>
      </BrowserRouter>
    );

    expect(screen.getByTestId('thead-container')).toBeInTheDocument();
    expect(screen.getByTestId('thead-container').children.length).toBe(1);
  });
});
