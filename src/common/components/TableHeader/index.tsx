import {ITableCommonHeader} from '@common/interfaces';
import {TableCell, TableHead, TableRow} from '@mui/material';
import EximCheckbox from '@shared/components/EximCheckbox';
import {AscendingSorting} from '@shared/icons/AscendingSorting';
import {DescendingSorting} from '@shared/icons/DescendingSorting';
import {SortingArrow} from '@shared/icons/SortingArrow';
import {memo, useEffect, useState} from 'react';

import './index.scss';

interface ITableHeadProps {
  mainHeader: ITableCommonHeader[];
  subHeader?: ITableCommonHeader[];
  checked?: boolean;
  disabledCheckbox?: boolean;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSortBy?: (sortBy: string, order: 'asc' | 'desc') => void;
}

function TableHeader({
  mainHeader,
  subHeader,
  onChange,
  checked,
  disabledCheckbox,
  handleSortBy,
}: ITableHeadProps) {
  const [sortingHeader, setSortingHeader] = useState<ITableCommonHeader[]>([]);

  const handleRequestSort = (sortBy: string, order: 'asc' | 'desc') => {
    handleSortBy?.(sortBy, (order = order === 'asc' ? 'desc' : 'asc'));
    setSortingHeader((prevItems) =>
      prevItems.map((item) =>
        item.sortingKey === sortBy
          ? {...item, order: item.order === 'asc' ? 'desc' : 'asc'}
          : item
      )
    );
  };

  useEffect(() => {
    setSortingHeader(mainHeader);
  }, [mainHeader]);

  return (
    <TableHead data-testid='thead-container' className='thead-container'>
      <TableRow className='table-head-tr'>
        {sortingHeader?.map((column, index) => {
          const {title, width, colspan, rowSpan, sortingKey, order} = column;
          return title === 'checkbox' ? (
            <TableCell
              className='checkbox-th'
              key={`mainHeader${index + 1}`}
              colSpan={colspan as number}
              rowSpan={rowSpan as number}
              align='center'
              width={width}>
              {onChange && (
                <EximCheckbox
                  id='allData'
                  name='allData'
                  dataTestId='allData'
                  color='#2CB544'
                  size='medium'
                  disabled={disabledCheckbox}
                  checked={checked}
                  onChange={onChange}
                />
              )}
            </TableCell>
          ) : (
            <TableCell
              key={`mainHeader${index + 1}`}
              colSpan={colspan as number}
              align='left'
              width={width}>
              {title}
              {sortingKey ? (
                <span
                  role='presentation'
                  className='sorting-arrow'
                  onClick={() =>
                    handleRequestSort(sortingKey || '', order ?? 'desc')
                  }>
                  {!order ? <SortingArrow /> : null}
                  {order === 'desc' ? <DescendingSorting /> : null}
                  {order === 'asc' ? <AscendingSorting /> : null}
                </span>
              ) : null}
            </TableCell>
          );
        })}
      </TableRow>
      <TableRow className='table-head-tr sub-header'>
        {subHeader?.map(({title, width, sortingKey, order}, index) => (
          <TableCell
            key={`subHeader${index + 1}`}
            align='left'
            width={width}
            className={title === '' ? '' : 'border-top'}>
            {title}
            {sortingKey ? (
              <span
                role='presentation'
                className='sorting-arrow'
                onClick={() =>
                  handleRequestSort(sortingKey || '', order ?? 'desc')
                }>
                {!order ? <SortingArrow /> : null}
                {order === 'desc' ? <DescendingSorting /> : null}
                {order === 'asc' ? <AscendingSorting /> : null}
              </span>
            ) : null}
          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}

export default memo(TableHeader);

TableHeader.defaultProps = {
  subHeader: [],
  checked: true,
  disabledCheckbox: false,
  onChange: () => {
    /* */
  },
  handleSortBy: () => {
    /* */
  },
};
