@import '@utils/main.scss';

.records-card {
  height: 104px;
  position: relative;
  @include flex-item(_, space-between, center, _, 18px);
  box-shadow: 0px 3px 6px $box-shadow-color;
  @include padding(10px 20px 10px 10px);
  @include rfs(5px, border-radius);

  .records-card-left {
    @include flex-item(_, _, center, _, 20px);
    height: 100%;
    width: max-content;
    .icon-container {
      @include flex-item(_, center, center);
      @include rfs(5px, border-radius);
      height: 100%;
      width: 85px;
    }
  }

  .records-card-right {
    .button-wrapper {
      .base-btn {
        @include padding(7px 16px);
        @include margin(0);
        font-size: $font-size-13;
        font-weight: $font-weight-semi-bold;
        .btn-children {
          & > svg {
            @include margin(1px 0 0 4px);
          }
        }
      }
      .rotate-180-deg {
        .btn-children {
          & > svg {
            transform: rotate(180deg);
          }
        }
      }
      .success-view-details-btn {
        border: 1px solid $invoice-record-success;
        color: $invoice-record-success;
        &:hover {
          background: inherit;
          color: $invoice-record-success;
        }
      }
      .error-view-details-btn {
        border: 1px solid $invoice-record-error;
        color: $invoice-record-error;
        &:hover {
          background: inherit;
          color: $invoice-record-error;
        }
      }
    }
  }
  .triangle {
    position: absolute;
    right: 40px;
    top: 100%;
    border-left: 19px solid transparent;
    border-right: 19px solid transparent;
  }

  .count-container {
    @include flex-item(column, _, baseline);
    .records-details {
      @include flex-item(_, center, flex-end, _, 12px);
    }
    .success-count {
      color: $invoice-record-success;
      font-size: $font-size-xxxl;
    }
    .warning-count {
      color: $invoice-record-warning;
      font-size: $font-size-xxxl;
    }
    .error-count {
      color: $invoice-record-error;
      font-size: $font-size-xxxl;
    }
  }
}

// Success record card style
.success-records-card.active-card {
  outline: 2px solid $invoice-record-success;
}
.success-records-card {
  .records-card-left {
    .success-icon {
      background: $invoice-record-success;
    }
  }
  .success-triangle {
    border-top: 24px solid $invoice-record-success;
  }
}

// Error record card style
.error-records-card.active-card {
  outline: 2px solid $invoice-record-error;
}
.error-records-card {
  .records-card-left {
    .error-icon {
      background: $invoice-record-error;
    }
  }
  .error-triangle {
    border-top: 24px solid $invoice-record-error;
  }
}
