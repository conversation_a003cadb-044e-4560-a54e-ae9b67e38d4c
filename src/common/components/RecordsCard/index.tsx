import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {SolidDownAngle} from '@shared/icons';
import {ReactNode, memo} from 'react';

import './index.scss';

interface IInvoiceRecordCardProps {
  title: string;
  icon: ReactNode;
  isActive: boolean;
  children?: ReactNode;
  recordCount: string | number;
  recordType: 'success' | 'error';
  handleViewRecord: (type: 'success' | 'error') => void;
}

function RecordCard({
  icon,
  title,
  isActive,
  children,
  recordType,
  recordCount,
  handleViewRecord,
}: IInvoiceRecordCardProps) {
  const activeCard = isActive ? 'active-card' : '';

  return (
    <div className={`records-card ${recordType}-records-card ${activeCard}`}>
      <div className='records-card-left'>
        <div className={`${recordType}-icon icon-container`}>{icon}</div>
        <div className='count-container'>
          <div className='records-details'>
            <EximTypography fontWeight='bold'>{title}</EximTypography>
          </div>
          <EximTypography fontWeight='bold' classNames={`${recordType}-count`}>
            {recordCount}
          </EximTypography>
        </div>
      </div>
      <div className='records-card-middle'>{children}</div>
      <div className='records-card-right'>
        <EximButton
          onClick={() => handleViewRecord(recordType)}
          color=''
          variant='outlined'
          size='small'
          className={`${recordType}-view-details-btn ${
            isActive ? 'rotate-180-deg' : ''
          }`}>
          View Details
          <SolidDownAngle
            height={14}
            width={14}
            fill={`${recordType === 'success' ? '#4CA75B' : '#E0342F'}`}
          />
        </EximButton>
      </div>
      {isActive ? <div className={`triangle ${recordType}-triangle`} /> : null}
    </div>
  );
}

export default memo(RecordCard);

RecordCard.defaultProps = {
  children: null,
};
