import {render, screen} from '@testing-library/react';

import Footer from './index';

describe('Footer Common Component', () => {
  test('Should render footer', () => {
    render(<Footer showGstLogo />);
    const footer = screen.getByTestId('footer');
    const footerContent = screen.getByTestId('footer-content');
    expect(footer).toBeInTheDocument();
    expect(footerContent).toHaveTextContent(
      `Copyright © ${new Date().getFullYear()} Perennial Systems. All rights reserved. Privacy | Feedback`
    );
  });
});
