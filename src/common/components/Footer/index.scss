@import '@utils/main.scss';

.main-footer {
  width: 100%;
  background: $secondary-background;

  .footer-container {
    @include padding(18px);
    position: relative;
    letter-spacing: 0.5px;

    .footer-content {
      @include flex-item(_, center, center, _, 5px);
      color: $text-color;
      font-size: $font-size-15;

      .animation-underline a {
        font-size: $font-size-sm;
        @include padding(3.2px 0);
        font-weight: bold;
        color: $text-color;

        &::before {
          height: 2px;
          bottom: 0;
        }
      }

      .privacy-feedback {
        min-width: 121px;
        font-size: $font-size-sm;
        opacity: 0.6;
        letter-spacing: 0.1px;
        word-spacing: 5px;
        @include margin-left(25px);
      }
    }

    .footer-logo {
      position: absolute;
      right: 36px;
      top: 18px;
    }
  }
}