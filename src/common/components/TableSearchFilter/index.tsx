import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import {ChangeEvent, ReactNode} from 'react';

import './index.scss';

interface ITableSearchFilterProps {
  children?: ReactNode;
  options?: string[];
  isHideInput?: boolean;
  isInputDisabled?: boolean;
  isHideShowEntries?: boolean;
  btnProps?: {
    disabled?: boolean;
    onClick?: () => void;
    color?: string;
    text: string;
  };
  handleShowEntries: (event: ChangeEvent<HTMLSelectElement>) => void;
  handleSearchQuery: (event: ChangeEvent<HTMLInputElement>) => void;
}

function TableSearchFilter({
  children,
  options,
  btnProps,
  isHideInput,
  isInputDisabled,
  isHideShowEntries,
  handleShowEntries,
  handleSearchQuery,
}: ITableSearchFilterProps) {
  return (
    <div className='table-search-container'>
      <div className='search-div'>
        {!isHideShowEntries && (
          <div className='show-entries-container'>
            <span>Showing</span>
            <select
              className='select-entries'
              data-testid='show-entries'
              onChange={handleShowEntries}>
              {options?.map((val) => (
                <option key={val} value={val}>
                  {val}
                </option>
              ))}
            </select>
            <span>entries</span>
          </div>
        )}
        {children && <div className='middle-content-div'>{children}</div>}
        {!isHideInput ? (
          <div className='table-input'>
            <p>Search</p>
            <div>
              <EximInput
                type='text'
                dataTestid='search-input'
                onChange={handleSearchQuery}
                disabled={isInputDisabled}
              />
            </div>
          </div>
        ) : null}
        {btnProps?.text ? (
          <EximButton
            size='small'
            onClick={btnProps.onClick}
            color={btnProps.color}>
            {btnProps.text}
          </EximButton>
        ) : null}
      </div>
    </div>
  );
}

export default TableSearchFilter;

TableSearchFilter.defaultProps = {
  options: ['5', '10', '25', '50', '100'],
  children: null,
  isHideInput: false,
  isInputDisabled: false,
  isHideShowEntries: false,
  btnProps: {
    disabled: false,
    onClick: () => {
      // Default function
    },
    color: 'primary',
    text: '',
  },
};
