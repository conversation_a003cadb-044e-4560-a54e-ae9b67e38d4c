@import '@utils/main.scss';

.table-search-container {
  .search-div {
    @include flex-item(_, space-between, _, _, 16px);
    font-size: $font-size-sm;

    .show-entries-container {
      @include flex-item(_, _, center);
      flex: 1;
    }

    .select-entries {
      margin: 0 5px;
      width: 50px;
      outline: none;
      border-radius: 4px;
      padding: 2px 0 2px 5px;
      border: 1px solid $secondary;
      background: transparent;
      text-transform: capitalize;
    }

    .table-input {
      display: flex;
      gap: 10px;
      align-items: center;

      .input-wrapper .form-input {
        input {
          width: 177px;
        }
      }
    }
    .middle-content-div {
      // Searching DropDown Style
      .select-dropdown {
        .custom-dropdown {
          border-radius: none;
          width: 232px;
          .custom-dropdown-selection > span {
            max-width: calc(100% - 16px);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .error-message {
          display: none;
        }
      }
    }

    .button-wrapper {
      min-width: 100px;
      .base-btn {
        height: 32px;
        font-size: $font-size-sm;
        @include padding(7px 16px);
      }
    }
  }
}
