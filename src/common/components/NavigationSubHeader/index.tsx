import EximBreadcrumbs from '@shared/components/EximBreadcrumbs';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {ArrowLeftIcon} from '@shared/icons';
import {BreadcrumbType} from '@submodules/Breadcrumbs/Breadcrumbs';
import {ReactNode} from 'react';
import {Link, useNavigate} from 'react-router-dom';

import './index.scss';

interface ISubscriptionProps {
  hasLeftArrow?: boolean;
  hasTitle?: boolean;
  hasGuide?: boolean;
  handleGuideClick?: () => void;
  breadCrumbSeparator?: '>' | '-' | '/';
  breadCrumbData?: BreadcrumbType[];
  leftArrowText?: string;
  leftArrowRoute: string;
  isNavigate?: boolean;
  children?: ReactNode;
}

export default function NavigationSubHeader({
  hasLeftArrow,
  hasTitle,
  hasGuide,
  handleGuideClick,
  breadCrumbSeparator,
  breadCrumbData,
  leftArrowText,
  leftArrowRoute,
  isNavigate,
  children,
}: ISubscriptionProps) {
  const navigate = useNavigate();

  const handleBack = () => {
    if (leftArrowRoute && leftArrowRoute !== '#') {
      return navigate(leftArrowRoute);
    }
    return isNavigate ? navigate(-1) : null;
  };

  return (
    <div className='subscription-header'>
      <div className='subscription-header-left'>
        {hasLeftArrow ? (
          <Link to={leftArrowRoute} onClick={handleBack}>
            <ArrowLeftIcon width={20} height={20} />
          </Link>
        ) : null}
        {hasTitle ? (
          <EximTypography variant='h1' dataTestId='product-header'>
            {leftArrowText}
          </EximTypography>
        ) : null}
        {hasGuide ? (
          <div className='guideBtn'>
            <EximButton
              onClick={handleGuideClick}
              size='small'
              variant='outlined'
              color='tour-guide'
              dataTestId='navigate-guide-btn'>
              Guide
            </EximButton>
          </div>
        ) : null}
      </div>
      {children ? <div className='navigate-children'>{children}</div> : null}
      {/* {breadCrumbData && (
        <EximBreadcrumbs
          breadcrumbs={breadCrumbData}
          separator={breadCrumbSeparator}
        />
      )} */}
    </div>
  );
}

NavigationSubHeader.defaultProps = {
  hasLeftArrow: false,
  hasTitle: false,
  hasGuide: false,
  handleGuideClick: () => {
    /* */
  },
  breadCrumbSeparator: '>',
  breadCrumbData: [],
  leftArrowText: 'EximHero Products',
  isNavigate: false,
  children: null,
};
