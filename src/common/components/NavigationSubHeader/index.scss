@import '@utils/main.scss';
.subscription-header {
  @include flex-item(_, space-between, center, _, _);
  @include padding(20px 0);
  @include margin(0px 20px);
  height: 70px;
  .subscription-header-left {
    display: flex;
    .tooltip-wrapper {
      @include padding(0 4px);
    }
    @include flex-item(_, center, center, _, 12px);
    a {
      @include flex-item(_, center, center, _, _);
    }

    .typography-variant-h1 {
      font-size: $font-size-xxxl;
      height: 26px;
      @include flex-item(_, center, center, _, _);
    }

    .guideBtn {
      .button-wrapper {
        min-width: 52px;
        @include margin-left(6px);

        .base-btn {
          height: 20px;
          border: 2px solid;
          @include margin(0);
          @include rfs(18px, border-radius);
          @include rfs(6px, padding-bottom);
          @include font-size(12px);
          &:hover {
            border: none;
          }
          .btn-children {
            @include margin-top(2.5px);
          }
        }
      }
    }
  }

  .breadcrumb-container {
    width: unset;
    .breadcrumb-separator {
      color: $text-color;
    }
    .breadcrumb-text-wrapper {
      span {
        color: $bread-crumb-title;
      }
    }
    .breadcrumb-inactive:hover {
      text-decoration: none;
    }
  }
}
