import store from '@store';
import {render} from '@testing-library/react';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';

import NavigationSubHeader from '.';
import {breadcrumbsWithRoutes, separator} from './config';

describe('NavigationSubHeader component', () => {
  it('render the NavigationSubHeader default props', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <NavigationSubHeader
            hasTitle
            hasLeftArrow
            breadCrumbData={breadcrumbsWithRoutes}
            breadCrumbSeparator={separator}
            leftArrowRoute='/dashboard'
          />
        </Provider>
      </BrowserRouter>
    );
  });
  it('render the NavigationSubHeader when no props', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <NavigationSubHeader leftArrowRoute='/dashboard' />
        </Provider>
      </BrowserRouter>
    );
  });
});
