import {DashboardIcon} from '@shared/icons';
import {BreadcrumbType} from '@submodules/Breadcrumbs/Breadcrumbs';

export const separator = '>';

const dashboard: BreadcrumbType = {
  id: 1,
  text: 'Dashboard',
  icon: <DashboardIcon fill='#444444' width='18' height='10' />,
  route: '/dashboard',
};

export const breadcrumbsWithRoutes: BreadcrumbType[] = [
  dashboard,
  {
    id: 2,
    text: 'Exim Products Page',
    route: '/new',
  },
];

export const dbkClaimBreadcrumbs: BreadcrumbType[] = [
  {
    id: 1,
    text: 'Dashboard',
    icon: <DashboardIcon fill='#444444' width='18' height='10' />,
    route: '/duty-drawback',
  },
  {
    id: 2,
    text: 'Duty Drawback',
    route: '/duty-drawback',
  },
];

export const moowrTransactionBreadcrumbs: BreadcrumbType[] = [
  {
    id: 1,
    text: 'Dashboard',
    icon: <DashboardIcon fill='#444444' width='18' height='10' />,
    route: '/moowr',
  },
  {
    id: 2,
    text: 'MOOWR',
    route: '/moowr',
  },
];

export const historyBreadcrumbs: BreadcrumbType[] = [
  dashboard,
  {
    id: 2,
    text: 'History',
    route: '/duty-drawback/claim-history',
  },
];
