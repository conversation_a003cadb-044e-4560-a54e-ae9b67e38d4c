import EximPagination from '@shared/components/EximPagination';
import {memo} from 'react';

import {
  filterEntriesCount,
  initialEntriesCount,
  targetEntriesCount,
  totalShowingEntriesCount,
} from './helper';
import './index.scss';

interface ITableFooter {
  page: number;
  searchQuery: string;
  hasBackendPagination?: boolean;
  showEntries: string | number;
  totalRecords: number;
  searchData: [];
  renderData: [];
  handlePageChange: (pageNumber: number | string) => void;
}

function TableFooter(props: ITableFooter) {
  const {
    page,
    searchQuery,
    showEntries,
    totalRecords,
    searchData,
    renderData,
    hasBackendPagination,
    handlePageChange,
  } = props;

  return (
    <div className='table-footer'>
      <div className='show-entries-div'>
        {`Showing ${initialEntriesCount(
          page,
          searchQuery,
          showEntries,
          totalRecords,
          searchData,
          renderData
        )} ${targetEntriesCount(
          hasBackendPagination || false,
          page,
          searchQuery,
          showEntries,
          totalRecords,
          searchData
        )} of ${totalShowingEntriesCount(
          hasBackendPagination || false,
          searchQuery,
          totalRecords,
          searchData
        )} entries ${filterEntriesCount(
          hasBackendPagination || false,
          searchQuery,
          totalRecords,
          searchData
        )}`}
      </div>
      <div data-testid='pagination'>
        <EximPagination
          totalItems={
            searchQuery.length > 0 && !hasBackendPagination
              ? searchData?.length
              : totalRecords
          }
          itemsPerPage={(() => {
            if (showEntries === 'all' && searchQuery.length === 0) {
              return totalRecords;
            }
            if (showEntries === 'all' && searchQuery.length > 0) {
              return searchData?.length;
            }
            return Number(showEntries);
          })()}
          isInput
          currentPage={page}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
}

export default memo(TableFooter);

TableFooter.defaultProps = {
  hasBackendPagination: false,
};
