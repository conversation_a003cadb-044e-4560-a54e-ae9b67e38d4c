// Below function to check the show entries length with all data length, it will return true or false
export const checkEntriesLength = (
  totalRecords: number,
  showEntries: number | string,
  type: 'lessThan' | 'greaterThan'
) => {
  if (type === 'lessThan') {
    return Number(showEntries) <= totalRecords && showEntries !== 'all';
  }
  return Number(showEntries) > totalRecords && showEntries !== 'all';
};

// Below function to return the count of show entries based on the current page
export const getEntriesCount = (
  totalRecords: number,
  showEntries: number | string,
  page: number
) => {
  return Number(showEntries) * page <= totalRecords
    ? Number(showEntries) * page
    : totalRecords;
};

// Below function to show the target entries on table footer
export const targetEntriesCount = (
  hasBackendPagination: boolean,
  page: number,
  searchQuery: string,
  showEntries: number | string,
  totalRecords: number,
  searchData: []
) => {
  // INFO: if the pagination implemented from the backend then no need to check further statement
  if (
    checkEntriesLength(totalRecords, showEntries, 'lessThan') &&
    searchQuery?.length === 0
  ) {
    return getEntriesCount(totalRecords, showEntries, page);
  }
  if (
    checkEntriesLength(totalRecords, showEntries, 'greaterThan') &&
    searchQuery?.length === 0
  ) {
    return totalRecords;
  }
  if (
    checkEntriesLength(
      hasBackendPagination ? totalRecords : searchData?.length,
      showEntries,
      'lessThan'
    ) &&
    searchQuery?.length > 0
  ) {
    return getEntriesCount(
      hasBackendPagination ? totalRecords : searchData?.length,
      showEntries,
      page
    );
  }
  if (
    checkEntriesLength(
      hasBackendPagination ? totalRecords : searchData?.length,
      showEntries,
      'greaterThan'
    ) &&
    searchQuery?.length > 0
  ) {
    return hasBackendPagination ? totalRecords : searchData?.length;
  }
  return '';
};

export const initialEntriesCount = (
  page: number,
  searchQuery: string,
  showEntries: number | string,
  totalRecords: number,
  searchData: [],
  renderData: []
) => {
  if (showEntries === 'all') {
    return searchQuery?.length === 0
      ? `1 to ${totalRecords}`
      : `1 to ${searchData?.length}`;
  }
  if (searchQuery.length > 0) {
    return searchData?.length > 0
      ? `${(page - 1) * Number(showEntries) + 1} to `
      : `0 to `;
  }
  return renderData?.length > 0
    ? `${(page - 1) * Number(showEntries) + 1} to `
    : `0 to `;
};

export const totalShowingEntriesCount = (
  hasBackendPagination: boolean,
  searchQuery: string,
  totalRecords: number,
  searchData: []
) => {
  return searchQuery?.length === 0 || hasBackendPagination
    ? totalRecords
    : searchData?.length;
};

export const filterEntriesCount = (
  hasBackendPagination: boolean,
  searchQuery: string,
  totalRecords: number,
  searchData: []
) => {
  return searchQuery?.length > 0 &&
    searchData?.length < totalRecords &&
    !hasBackendPagination
    ? ` (filtered from ${totalRecords} total entries)`
    : '';
};
