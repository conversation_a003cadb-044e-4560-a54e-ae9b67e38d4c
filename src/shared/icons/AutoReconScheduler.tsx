import {IIConProps} from '../utils/interface';

export function AutoReconScheduler({fill, width, height}: IIConProps) {
  return (
    <svg
      id='business_black_24dp'
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 21 21'>
      <path id='Path_910' data-name='Path 910' d='M0,0H21V21H0Z' fill='none' />
      <g id='business_black_24dp-2' data-name='business_black_24dp'>
        <path
          id='Path_910-2'
          data-name='Path 910'
          d='M0,0H21V21H0Z'
          fill='none'
        />
        <path
          id='Subtraction_3'
          data-name='Subtraction 3'
          d='M6.184,13.309h0L5.573,13v.045L.9,13.05a.9.9,0,0,1-.9-.9V.9A.9.9,0,0,1,.9,0H5.573L9.295,1.863l4.688,0a.9.9,0,0,1,.9.9V6.777a6.011,6.011,0,0,0-5.587-.033V4.1l0,1.465h3.166A.56.56,0,0,0,13.029,5V4.321a.568.568,0,0,0-.566-.568H9.3l0-.072L5.573,1.817v.069H2.452a.628.628,0,0,0-.626.629v.612A.576.576,0,0,0,2.4,3.7H5.561v-.02l.013.006L9.286,5.545v1.2a6.02,6.02,0,0,0-.563.334L5.573,5.5v.112H2.456a.629.629,0,0,0-.63.627v.612a.577.577,0,0,0,.576.577H5.573l1.792.9a6,6,0,0,0-.835,1.4l-.957-.48v.094H2.452a.627.627,0,0,0-.626.627v.612a.577.577,0,0,0,.576.577H5.573v.027l.515.258a6.067,6.067,0,0,0,.095,1.862Z'
          transform='translate(2.109 2.12)'
          fill={fill}
          stroke='rgba(0,0,0,0)'
          strokeWidth='1'
        />
        <path
          id='noun-timer-2093326'
          d='M110.725,24.855a4.669,4.669,0,1,0,3.3,1.365,4.658,4.658,0,0,0-3.3-1.365Zm0,.928a3.742,3.742,0,1,1-2.643,1.1,3.728,3.728,0,0,1,2.643-1.1Zm0,.472v3.269h-3.268a3.268,3.268,0,1,0,3.268-3.269Z'
          transform='translate(-96.503 -15.314)'
          fill={fill}
        />
      </g>
    </svg>
  );
}

AutoReconScheduler.defaultProps = {
  fill: '#5478b5',
  width: '21',
  height: '21',
};
