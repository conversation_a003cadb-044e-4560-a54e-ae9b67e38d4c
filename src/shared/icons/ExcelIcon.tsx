import {IIConProps} from '@common/interfaces';

export function ExcelIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 55.641 77.684'>
      <g
        id='Group_13391'
        data-name='Group 13391'
        transform='translate(18700 19020.684)'>
        <g id='XMLID_16_' transform='translate(-18700 -19026.533)'>
          <path
            id='XMLID_18_'
            d='M32.859,5.85v5.055c6.327,0,12.654.016,18.982-.016.54.016,1.78.016,2.321.079,1.065.127,1.4.573,1.463,1.653.032.556-.016,1.526.016,2.067-.032,10.969-.016,21.937-.016,32.907-.032,1.844.016,5.516.016,5.516a1.185,1.185,0,0,1-.509,1.144,7.462,7.462,0,0,1-2.607.271c-6.55.016-13.1-.016-19.665,0v5.691L0,55.163V10.922Z'
            transform='translate(0 0)'
            fill={fill}
          />
          <path
            id='XMLID_22_'
            d='M302.37,69.92h20.857v39.822H302.37v-3.8h5.055v-4.42H302.37V99h5.055v-4.42H302.37V92.049h5.055v-4.42H302.37V85.1h5.055v-4.42H302.37V78.154h5.055v-4.42H302.37Z'
            transform='translate(-269.511 -57.107)'
            fill='#fff'
          />
          <path
            id='XMLID_23_'
            d='M372.15,104.74H381v4.42H372.15Z'
            transform='translate(-331.708 -88.144)'
            fill={fill}
          />
          <path
            id='XMLID_24_'
            d='M86.4,157.224c1.431-.1,2.861-.191,4.292-.254-1.686,3.449-3.386,6.915-5.1,10.365,1.733,3.545,3.513,7.058,5.262,10.588-1.526-.1-3.036-.191-4.562-.286a72.058,72.058,0,0,1-3.147-7.933c-.858,2.559-2.082,4.991-3.068,7.5-1.383-.016-2.766-.079-4.149-.127,1.621-3.18,3.195-6.391,4.864-9.538-1.415-3.259-2.988-6.454-4.451-9.681l4.165-.238c.938,2.464,1.971,4.912,2.75,7.44A83.731,83.731,0,0,1,86.4,157.224Z'
            transform='translate(-67.679 -134.698)'
            fill='#fff'
          />
          <path
            id='XMLID_25_'
            d='M372.15,168.81H381v4.42H372.15Z'
            transform='translate(-331.708 -145.251)'
            fill={fill}
          />
          <path
            id='XMLID_26_'
            d='M372.15,232.74H381v4.42H372.15Z'
            transform='translate(-331.708 -202.234)'
            fill={fill}
          />
          <path
            id='XMLID_27_'
            d='M372.15,296.67H381v4.42H372.15Z'
            transform='translate(-331.708 -259.216)'
            fill={fill}
          />
          <path
            id='XMLID_28_'
            d='M372.15,360.74H381v4.42H372.15Z'
            transform='translate(-331.708 -316.324)'
            fill={fill}
          />
        </g>
        <text
          id='CUSTOM'
          transform='translate(-18700 -18948)'
          fill='#262626'
          fontSize='12'
          fontFamily='SourceSansPro-Regular, Source Sans Pro'
          letterSpacing='0.04em'>
          <tspan x='0' y='0'>
            CUSTOM
          </tspan>
        </text>
      </g>
    </svg>
  );
}

ExcelIcon.defaultProps = {
  fill: '#207245',
  width: '55',
  height: '77',
};
