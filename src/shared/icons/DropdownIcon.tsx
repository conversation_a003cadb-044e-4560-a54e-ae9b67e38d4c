import {IIConProps} from '@common/interfaces';

export function DropdownIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      id='Drop_Down'
      data-name='Drop Down'
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 20 16'>
      <rect
        id='Rectangle_15204'
        data-name='Rectangle 15204'
        width='20'
        height='20'
        fill='none'
      />
      <path
        id='Path_5858'
        data-name='Path 5858'
        d='M5,7.5l5,5,5-5'
        fill='none'
        stroke={fill}
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1'
      />
    </svg>
  );
}

DropdownIcon.defaultProps = {
  fill: '#fff',
  width: '20',
  height: '20',
};
