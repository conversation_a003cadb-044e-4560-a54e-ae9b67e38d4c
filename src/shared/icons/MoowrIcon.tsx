import {IIConProps} from '@common/interfaces';

export function MoowrIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 57.513 48.001'>
      <g
        id='Group_15038'
        data-name='Group 15038'
        transform='translate(20985.455 21813)'>
        <path
          id='Path_6014'
          data-name='Path 6014'
          d='M52,408c6.605,3.072,17.026,5.053,28.756,5.053s22.151-1.981,28.756-5.053Z'
          transform='translate(-21037.455 -22178.053)'
          fill='#b3b8d1'
        />
        <path
          id='Path_6015'
          data-name='Path 6015'
          d='M271.739,161.23H262.4l1.936-24.03H269.8Z'
          transform='translate(-21217.904 -21945.73)'
          fill='#556492'
        />
        <path
          id='Path_6016'
          data-name='Path 6016'
          d='M246.752,257.41v16.8H216.8V252l9.965,5.41V252l10.022,5.41V252Z'
          transform='translate(-21178.795 -22044.26)'
          fill='#ef5928'
        />
        <rect
          id='Rectangle_16655'
          data-name='Rectangle 16655'
          width='19.361'
          height='38.478'
          transform='translate(-20981.355 -21808.529)'
          fill='#1e2e5f'
        />
        <g
          id='Group_15036'
          data-name='Group 15036'
          transform='translate(-20978.965 -21805.482)'>
          <rect
            id='Rectangle_16656'
            data-name='Rectangle 16656'
            width='3.53'
            height='3.53'
            transform='translate(0 0)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16657'
            data-name='Rectangle 16657'
            width='3.53'
            height='3.53'
            transform='translate(5.523 0)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16658'
            data-name='Rectangle 16658'
            width='3.53'
            height='3.53'
            transform='translate(11.047 0)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16659'
            data-name='Rectangle 16659'
            width='3.53'
            height='3.53'
            transform='translate(0 6.15)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16660'
            data-name='Rectangle 16660'
            width='3.53'
            height='3.53'
            transform='translate(5.523 6.15)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16661'
            data-name='Rectangle 16661'
            width='3.53'
            height='3.53'
            transform='translate(11.047 6.15)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16662'
            data-name='Rectangle 16662'
            width='3.53'
            height='3.53'
            transform='translate(0 12.243)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16663'
            data-name='Rectangle 16663'
            width='3.53'
            height='3.53'
            transform='translate(5.523 12.243)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16664'
            data-name='Rectangle 16664'
            width='3.53'
            height='3.53'
            transform='translate(11.047 12.243)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16665'
            data-name='Rectangle 16665'
            width='3.53'
            height='3.53'
            transform='translate(0 18.393)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16666'
            data-name='Rectangle 16666'
            width='3.53'
            height='3.53'
            transform='translate(5.523 18.393)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16667'
            data-name='Rectangle 16667'
            width='3.53'
            height='3.53'
            transform='translate(11.047 18.393)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16668'
            data-name='Rectangle 16668'
            width='3.53'
            height='3.53'
            transform='translate(0 24.486)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16669'
            data-name='Rectangle 16669'
            width='3.53'
            height='3.53'
            transform='translate(5.523 24.486)'
            fill='#d1d4e5'
          />
          <rect
            id='Rectangle_16670'
            data-name='Rectangle 16670'
            width='3.53'
            height='3.53'
            transform='translate(11.047 24.486)'
            fill='#d1d4e5'
          />
        </g>
        <g
          id='Group_15037'
          data-name='Group 15037'
          transform='translate(-20959.773 -21785.113)'>
          <rect
            id='Rectangle_16671'
            data-name='Rectangle 16671'
            width='1.651'
            height='3.075'
            transform='translate(0 0)'
            fill='#fff'
          />
          <rect
            id='Rectangle_16672'
            data-name='Rectangle 16672'
            width='1.651'
            height='3.075'
            transform='translate(2.107 0)'
            fill='#fff'
          />
          <rect
            id='Rectangle_16673'
            data-name='Rectangle 16673'
            width='1.651'
            height='3.075'
            transform='translate(10.591 0)'
            fill='#fff'
          />
          <rect
            id='Rectangle_16674'
            data-name='Rectangle 16674'
            width='1.651'
            height='3.075'
            transform='translate(12.755 0)'
            fill='#fff'
          />
          <rect
            id='Rectangle_16675'
            data-name='Rectangle 16675'
            width='1.651'
            height='3.075'
            transform='translate(21.24 0)'
            fill='#fff'
          />
          <rect
            id='Rectangle_16676'
            data-name='Rectangle 16676'
            width='1.651'
            height='3.075'
            transform='translate(23.404 0)'
            fill='#fff'
          />
        </g>
        <path
          id='Path_6017'
          data-name='Path 6017'
          d='M293.218,102.843a.176.176,0,0,0-.057-.114,2.239,2.239,0,0,0-.114-.342,1.9,1.9,0,0,1,0-.569,2.711,2.711,0,0,1,.285-.8,1.61,1.61,0,0,1,.854-.626,3.5,3.5,0,0,1,1.082-.114,6.274,6.274,0,0,1,1.025.114l1.025.171c.342.057.626.057.911.114h.456c.171,0,.285-.057.4-.057a4.679,4.679,0,0,0,1.424-.626,3.952,3.952,0,0,0,.854-.8c.171-.228.285-.4.285-.4v.114a.846.846,0,0,1-.057.342,2.683,2.683,0,0,1-.626,1.2,3.4,3.4,0,0,1-1.594,1.139c-.171.057-.4.114-.569.171a1.937,1.937,0,0,1-.569.057,7.632,7.632,0,0,1-1.139-.057,6.622,6.622,0,0,1-1.025-.228,6.288,6.288,0,0,0-.911-.171,4.33,4.33,0,0,0-.8-.057,1.012,1.012,0,0,0-.569.228,2.166,2.166,0,0,0-.569.968Z'
          transform='translate(-21244.166 -21911.801)'
          fill='#b3b8d1'
        />
      </g>
    </svg>
  );
}

MoowrIcon.defaultProps = {
  fill: '#f15929',
  width: '24',
  height: '24',
};
