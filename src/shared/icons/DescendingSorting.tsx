import {IIConProps} from '@common/interfaces';

export function DescendingSorting({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 15 13'>
      <g
        id='Group_9338'
        data-name='Group 9338'
        transform='translate(-1174 -1509)'>
        <g
          id='Group_9336'
          data-name='Group 9336'
          transform='translate(21491 23723)'>
          <path
            id='Polygon_5'
            data-name='Polygon 5'
            d='M3.5,0,7,4H0Z'
            transform='translate(-20310 -22201) rotate(180)'
            fill={fill}
          />
          <rect
            id='Rectangle_8863'
            data-name='Rectangle 8863'
            width='3'
            height='11'
            transform='translate(-20315 -22214)'
            fill={fill}
          />
        </g>
        <rect
          id='Rectangle_8870'
          data-name='Rectangle 8870'
          width='4'
          height='2'
          transform='translate(1182 1518)'
          fill={fill}
        />
        <rect
          id='Rectangle_8871'
          data-name='Rectangle 8871'
          width='5'
          height='2'
          transform='translate(1182 1515)'
          fill={fill}
        />
        <rect
          id='Rectangle_8872'
          data-name='Rectangle 8872'
          width='6'
          height='2'
          transform='translate(1182 1512)'
          fill={fill}
        />
        <rect
          id='Rectangle_8873'
          data-name='Rectangle 8873'
          width='7'
          height='2'
          transform='translate(1182 1509)'
          fill={fill}
        />
      </g>
    </svg>
  );
}
DescendingSorting.defaultProps = {
  fill: '#fff',
  width: '20',
  height: '24',
};
