import {IIConProps} from '@common/interfaces';

export function HistoryIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 28 28'>
      <g
        id='Group_10321'
        data-name='Group 10321'
        transform='translate(-34 -1373)'>
        <circle
          id='Ellipse_281'
          data-name='Ellipse 281'
          cx='14'
          cy='14'
          r='14'
          transform='translate(34 1373)'
          fill={fill}
        />
        <path
          id='noun-history-3846716'
          d='M157.254,59.355h-5.975a2.4,2.4,0,0,0-2.4,2.4v5.9a2.713,2.713,0,0,0,1.083,5.2,2.655,2.655,0,0,0,2.124-1.042h5.166a2.4,2.4,0,0,0,2.4-2.4V61.753a2.4,2.4,0,0,0-2.4-2.4Zm-7.291,12.814a2.025,2.025,0,0,1-.624-3.954,1.861,1.861,0,0,1,.624-.1,2.032,2.032,0,0,1,2.028,2.028,2.056,2.056,0,0,1-.35,1.137,2.033,2.033,0,0,1-1.679.891Zm6.749-3.645h-3.262a.343.343,0,0,1,0-.685h3.262a.343.343,0,0,1,.343.343A.348.348,0,0,1,156.713,68.523Zm0-2.6H151.82a.343.343,0,0,1,0-.685h4.893a.348.348,0,0,1,.343.343A.343.343,0,0,1,156.713,65.926Zm0-2.59H151.82a.343.343,0,0,1,0-.685h4.893a.343.343,0,0,1,.343.343.348.348,0,0,1-.343.343Zm-6.407,5.8v1.008a.343.343,0,0,1-.343.343h-.741a.343.343,0,0,1,0-.685h.4v-.665a.343.343,0,1,1,.685,0Z'
          transform='translate(-105.906 1321.895)'
          fill='#fff'
        />
      </g>
    </svg>
  );
}

HistoryIcon.defaultProps = {
  fill: '#002662',
  width: '28',
  height: '28',
};
