import {IIConProps} from '@common/interfaces';

export function LinkIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 21.318 21.481'>
      <g id='link-svgrepo-com' transform='translate(-2.7 -2.425)'>
        <g
          id='Group_15445'
          data-name='Group 15445'
          transform='translate(3.207 8.513)'>
          <path
            id='Path_6292'
            data-name='Path 6292'
            d='M7.886,35.854a4.5,4.5,0,0,1-2.573-.806l-.165-.115A4.526,4.526,0,0,1,4.013,28.64l4.313-5.754A4.52,4.52,0,0,1,14.6,21.77l.165.115a4.537,4.537,0,0,1,1.757,2.43.619.619,0,1,1-1.188.351A3.246,3.246,0,0,0,14.064,22.9l-.165-.115a3.282,3.282,0,0,0-4.569.824L5.018,29.365a3.283,3.283,0,0,0,.837,4.551l.165.115a3.287,3.287,0,0,0,4.569-.823l1.318-1.9a.619.619,0,0,1,1.017.707l-1.318,1.9A4.519,4.519,0,0,1,7.886,35.854Z'
            transform='translate(-3.207 -20.961)'
            fill={fill}
            stroke={fill}
            strokeWidth='1'
          />
        </g>
        <g
          id='Group_15446'
          data-name='Group 15446'
          transform='translate(10.166 2.932)'>
          <path
            id='Path_6293'
            data-name='Path 6293'
            d='M30.208,17.826a4.506,4.506,0,0,1-2.577-.807l-.167-.116a4.533,4.533,0,0,1-1.755-2.429.619.619,0,1,1,1.188-.351,3.246,3.246,0,0,0,1.275,1.763l.167.116a3.287,3.287,0,0,0,4.568-.825l4.312-5.755a3.282,3.282,0,0,0-.837-4.551l-.164-.113a3.282,3.282,0,0,0-4.57.823l-1.317,1.9a.619.619,0,1,1-1.017-.706l1.317-1.9a4.52,4.52,0,0,1,6.292-1.134l.164.113a4.527,4.527,0,0,1,1.136,6.294L33.911,15.9A4.5,4.5,0,0,1,30.208,17.826Z'
            transform='translate(-25.684 -2.932)'
            fill={fill}
            stroke={fill}
            strokeWidth='1'
          />
        </g>
      </g>
    </svg>
  );
}

LinkIcon.defaultProps = {
  fill: '#5478b5',
  width: '16',
  height: '16',
};
