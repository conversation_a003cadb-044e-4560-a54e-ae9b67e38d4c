import {IIConProps} from '@common/interfaces';

export function SubscriptionUser({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 54 54'>
      <g
        id='Group_9903'
        data-name='Group 9903'
        transform='translate(-1168 -791)'>
        <path
          id='Path_1375'
          data-name='Path 1375'
          d='M126.955,73.427a.96.96,0,0,0,.957-.957c0-3.062,1.77-3.875,1.77-3.924.048,0,6.172-2.392,8.182-3.062a3.115,3.115,0,0,0,2.44-3.206V59.456l2.01,1.675a4.235,4.235,0,0,0,2.727,1.053,3.842,3.842,0,0,0,2.536-.909l2.3-1.77v3.253c-.1.813.287,2.345,2.44,3.062,2.009.67,8.134,3.062,8.134,***********,1.818.909,1.818,3.924a.957.957,0,1,0,1.914,0c0-4.354-2.871-5.646-3.014-5.694-.239-.1-6.22-2.44-8.277-3.11-1.2-.383-1.148-1-1.148-1V58.02a3.875,3.875,0,0,0,1.1-2.727v-.048a1.433,1.433,0,0,0,.861-.287,1.76,1.76,0,0,0,.67-1.053l.526-3.062a1.65,1.65,0,0,0-.766-1.675c1.053-2.392,2.536-7.081-.383-9.713a12.636,12.636,0,0,0-9.856-3.11c-2.44.191-4.545.957-5.359,2.01a4.989,4.989,0,0,0-1.053,2.105,3.171,3.171,0,0,0-1.77,1.148,3.98,3.98,0,0,0-.622,3.349,32.823,32.823,0,0,0,1.627,4.115,1.7,1.7,0,0,0-1,1.77l.526,3.062a1.418,1.418,0,0,0,.67,1.053,2.113,2.113,0,0,0,.766.287v.1a4.15,4.15,0,0,0,.813,2.44.608.608,0,0,0-.048.287v4.449c0,.144,0,.813-1.148,1.2-2.057.67-8.038,3.014-8.325,3.11-.1.048-2.966,1.292-2.966,5.646a.96.96,0,0,0,.957.957ZM151.069,55.1v.144a2.141,2.141,0,0,1-.766,1.675l-3.828,2.919a2.177,2.177,0,0,1-2.823-.1l-3.3-2.775a2.142,2.142,0,0,1-.718-1.627v-.1L139.3,50.6c.383-.479-.263-6.528,5.742-6.124,5.252-.026,6.459,2.057,6.794,5.742Zm-13.205-7.026a18.534,18.534,0,0,1-.957-3.6,1.95,1.95,0,0,1,.287-1.675,1.641,1.641,0,0,1,1.387-.574.981.981,0,0,0,.909-.622,1.028,1.028,0,0,0-.1-.909,2.59,2.59,0,0,1,.67-1.2c.335-.431,1.818-1.1,4.019-1.292a11.487,11.487,0,0,1,8.517,2.632c1.531,1.387,1.053,4.824.287,6.977-.993-3.4-3.874-4.891-7.846-5.015-6.162-.2-6.938,4.888-7.177,5.271Z'
          transform='translate(1049.977 763.705)'
          fill={fill}
        />
        <g
          id='Ellipse_284'
          data-name='Ellipse 284'
          transform='translate(1168 791)'
          fill='none'
          stroke='#002662'
          strokeWidth='2'>
          <circle cx='27' cy='27' r='27' stroke='none' />
          <circle cx='27' cy='27' r='26' fill='none' />
        </g>
      </g>
    </svg>
  );
}
SubscriptionUser.defaultProps = {
  fill: '#f15929',
  width: '54',
  height: '54',
};
