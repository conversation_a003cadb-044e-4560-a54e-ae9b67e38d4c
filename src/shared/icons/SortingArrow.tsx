import {IIConProps} from '@common/interfaces';

export function SortingArrow({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 15.46 15.876'>
      <g
        id='Group_7237'
        data-name='Group 7237'
        transform='translate(-1113.736 -1509.091)'>
        <path
          id='Path_1223'
          data-name='Path 1223'
          d='M3.865,25.875l3.865-5.384H5.136V10H2.6V20.491H0Z'
          transform='translate(1113.736 1499.092)'
          fill='#6f89a5'
          fillRule='evenodd'
        />
        <path
          id='Path_1224'
          data-name='Path 1224'
          d='M3.865,15.875l3.865-5.384H5.136V0H2.6V10.491H0Z'
          transform='translate(1129.196 1524.967) rotate(180)'
          fill={fill}
          fillRule='evenodd'
        />
      </g>
    </svg>
  );
}
SortingArrow.defaultProps = {
  fill: '#6f89a5',
  width: '15.4',
  height: '15.4',
};
