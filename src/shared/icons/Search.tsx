import {IIConProps} from '@common/interfaces';

export function SearchIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 16 16'>
      <path
        id='solid_search'
        data-name='solid search'
        d='M14.983,13.134l-2.958-2.958a.711.711,0,0,0-.5-.208h-.483A6.172,6.172,0,1,0,9.97,11.037v.483a.711.711,0,0,0,.208.5l2.958,2.958a.709.709,0,0,0,1.006,0l.839-.839a.715.715,0,0,0,0-1.008ZM6.172,9.97a3.8,3.8,0,1,1,3.8-3.8A3.8,3.8,0,0,1,6.172,9.97Z'
        transform='translate(-0.001 0)'
        fill={fill}
      />
    </svg>
  );
}

SearchIcon.defaultProps = {
  fill: '#012D66',
  width: '14',
  height: '14',
};
