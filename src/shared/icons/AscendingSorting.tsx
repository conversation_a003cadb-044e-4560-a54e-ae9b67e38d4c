import {IIConProps} from '@common/interfaces';

export function AscendingSorting({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 14 13'>
      <g
        id='Group_9337'
        data-name='Group 9337'
        transform='translate(-1148 -1509)'>
        <g
          id='Group_9335'
          data-name='Group 9335'
          transform='translate(21465 23723)'>
          <path
            id='Polygon_5'
            data-name='Polygon 5'
            d='M3.5,0,7,4H0Z'
            transform='translate(-20310 -22201) rotate(180)'
            fill={fill}
          />
          <rect
            id='Rectangle_8863'
            data-name='Rectangle 8863'
            width='3'
            height='11'
            transform='translate(-20315 -22214)'
            fill={fill}
          />
        </g>
        <rect
          id='Rectangle_8864'
          data-name='Rectangle 8864'
          width='3'
          height='2'
          transform='translate(1156 1509)'
          fill={fill}
        />
        <rect
          id='Rectangle_8865'
          data-name='Rectangle 8865'
          width='4'
          height='2'
          transform='translate(1156 1512)'
          fill={fill}
        />
        <rect
          id='Rectangle_8866'
          data-name='Rectangle 8866'
          width='5'
          height='2'
          transform='translate(1156 1515)'
          fill={fill}
        />
        <rect
          id='Rectangle_8867'
          data-name='Rectangle 8867'
          width='6'
          height='2'
          transform='translate(1156 1518)'
          fill={fill}
        />
      </g>
    </svg>
  );
}
AscendingSorting.defaultProps = {
  fill: '#fff',
  width: '20',
  height: '24',
};
