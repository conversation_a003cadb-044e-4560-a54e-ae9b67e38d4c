import {IIConProps} from '@common/interfaces';

export function RightCheck(props: IIConProps) {
  const {fill, width, height} = props;
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 120 82.028'>
      <path
        id='solid_check'
        data-name='solid check'
        d='M40.758,145.515l-39-35.75a5.182,5.182,0,0,1,0-7.778l8.485-7.778a6.392,6.392,0,0,1,8.485,0L45,118.292l56.272-51.583a6.392,6.392,0,0,1,8.485,0l8.485,7.778a5.182,5.182,0,0,1,0,7.778l-69,63.25a6.392,6.392,0,0,1-8.485,0Z'
        transform='translate(0 -65.098)'
        fill={fill}
      />
    </svg>
  );
}

RightCheck.defaultProps = {
  fill: '#fff',
  width: '16',
  height: '16',
};
