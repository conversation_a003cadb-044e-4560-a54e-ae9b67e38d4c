import {IIConProps} from '@common/interfaces';

export function SubscriptionGSTIN({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 46.984 47.982'>
      <g
        id='Group_9904'
        data-name='Group 9904'
        transform='translate(-1102.984 -1325.352)'>
        <path
          id='Union_7'
          data-name='Union 7'
          d='M18196.1,20918H18179a2,2,0,0,1-2-2v-40a2,2,0,0,1,2-2h40a2,2,0,0,1,2,2v17.807a1.027,1.027,0,0,1,.016.174v1.045a.977.977,0,0,1-1.953,0v-.635l-.016-.006V20878a2.006,2.006,0,0,0-2-2h-36.092a2,2,0,0,0-2,2v36a2,2,0,0,0,2,2h16.086a1,1,0,0,1,0,2Z'
          transform='translate(-17074.016 -19548.648)'
          fill='#002662'
        />
        <text
          id='GSTIN'
          transform='translate(1108.484 1343.35)'
          fill='#f15929'
          stroke='#f15929'
          strokeWidth='0.1'
          fontSize='10.5'
          fontFamily='SourceSansPro-Bold, Source Sans Pro'
          fontWeight='700'>
          <tspan x='0' y='0'>
            GSTIN
          </tspan>
        </text>
        <g
          id='Ellipse_282'
          data-name='Ellipse 282'
          transform='translate(1124.984 1348.35)'
          fill='none'
          stroke='#002662'
          strokeWidth='2'>
          <circle cx='12.492' cy='12.492' r='12.492' stroke='none' />
          <circle cx='12.492' cy='12.492' r='11.492' fill='none' />
        </g>
        <path
          id='solid_check'
          data-name='solid check'
          d='M3.608,72.1.156,68.934a.459.459,0,0,1,0-.689l.751-.689a.566.566,0,0,1,.751,0l2.326,2.132L10.6,63.316a.926.926,0,0,1,1.178-.217l.447.5a.874.874,0,0,1-.118,1.032L4.359,72.1a.566.566,0,0,1-.751,0Z'
          transform='translate(1131.797 1293.229)'
          fill='#f15929'
          stroke='#f15929'
          strokeWidth='0.3'
        />
        <rect
          id='Rectangle_11421'
          data-name='Rectangle 11421'
          width='17'
          height='2'
          rx='1'
          transform='translate(1108 1348.35)'
          fill={fill}
        />
        <rect
          id='Rectangle_11422'
          data-name='Rectangle 11422'
          width='13'
          height='2'
          rx='1'
          transform='translate(1108 1355.35)'
          fill={fill}
        />
      </g>
    </svg>
  );
}
SubscriptionGSTIN.defaultProps = {
  fill: '#002662',
  width: '46.984',
  height: '47.982',
};
