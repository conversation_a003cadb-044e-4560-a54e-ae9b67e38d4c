interface INumberProps {
  fill?: string;
  textFill?: string;
  width?: string | number;
  height?: string | number;
  label?: string;
}

export function Number(props: INumberProps) {
  const {fill, textFill, width, height, label} = props;
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 24 24'>
      <g id='Group_700' data-name='Group 700' transform='translate(-900 -1700)'>
        <circle
          id='Ellipse_34'
          data-name='Ellipse 34'
          cx='12'
          cy='12'
          r='12'
          transform='translate(900 1700)'
          fill={fill}
        />
        <text
          id='_1'
          data-name='1'
          transform='translate(909 1717)'
          fill={textFill}
          fontSize='14'
          fontFamily='SourceSansPro-Regular, Source Sans Pro'>
          <tspan x='0' y='0'>
            {label}
          </tspan>
        </text>
      </g>
    </svg>
  );
}

Number.defaultProps = {
  fill: '#c2e2e3',
  textFill: '#012d66',
  width: '16',
  height: '16',
  label: '1',
};
