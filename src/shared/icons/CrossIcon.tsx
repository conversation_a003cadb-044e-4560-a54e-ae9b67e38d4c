import {IIConProps} from '@common/interfaces';

export function CrossIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 10.5 10.5'>
      <path
        id='solid_times'
        data-name='solid times'
        d='M7.24,85.25l2.985-2.985a.938.938,0,0,0,0-1.327l-.663-.663a.938.938,0,0,0-1.327,0L5.25,83.26,2.265,80.275a.938.938,0,0,0-1.327,0l-.663.663a.938.938,0,0,0,0,1.327L3.26,85.25.275,88.235a.938.938,0,0,0,0,1.327l.663.663a.938.938,0,0,0,1.327,0L5.25,87.24l2.985,2.985a.938.938,0,0,0,1.327,0l.663-.663a.938.938,0,0,0,0-1.327Z'
        transform='translate(0 -80)'
        fill={fill}
      />
    </svg>
  );
}

CrossIcon.defaultProps = {
  fill: '#fe4242',
  width: '10.5',
  height: '10.5',
};
