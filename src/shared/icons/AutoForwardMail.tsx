import {IIConProps} from '../utils/interface';

export function AutoForwardMail({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 21 21'>
      <g
        id='Group_12843'
        data-name='Group 12843'
        transform='translate(-1036 -341)'>
        <g
          id='Group_7572'
          data-name='Group 7572'
          transform='translate(-2.485 -1.749)'>
          <path
            id='Subtraction_6'
            data-name='Subtraction 6'
            d='M7.138,10.436H1.947A1.934,1.934,0,0,1,.685,9.968L4.472,5.881l.616.675a3.264,3.264,0,0,0,2.129.976,3.343,3.343,0,0,0,2.146-.975l.651-.676.861.936a5.134,5.134,0,0,0-3.737,3.618ZM.221,9.215h0c-.029-.068-.058-.122-.084-.17l-.005-.01A.924.924,0,0,1,0,8.482V1.953a.877.877,0,0,1,.221-.738L3.893,5.25Zm12.327-2.5H12l-.033-.037L10.723,5.363l.437-.445,3.191-3.613a2.224,2.224,0,0,1,.1.565v.005a.184.184,0,0,0,.006.06V5.746l-.261-.316a1.185,1.185,0,0,0-.776-.3.811.811,0,0,0-.869.839v.743ZM7.3,6.615H7.227a2.128,2.128,0,0,1-1.571-.694L.685.467A1.936,1.936,0,0,1,1.946,0H12.578a1.933,1.933,0,0,1,1.261.467L8.867,5.921A2.125,2.125,0,0,1,7.3,6.615Z'
            transform='translate(1040.485 346.749)'
            fill={fill}
          />
          <path
            id='Path_1282'
            data-name='Path 1282'
            d='M295.719,297.479l-2.614-2.66c-.319-.319-.822-.216-.822.241V296.1h-.639c-2.145,0-4.412,1.427-4.412,3.573a4,4,0,0,0,0,1.278c.137.365.376.27.649-.05a4.652,4.652,0,0,1,3.763-1.539h.593v1.224a.474.474,0,0,0,.822.3l2.614-2.722A.46.46,0,0,0,295.719,297.479Z'
            transform='translate(761.352 58.068)'
            fill={fill}
          />
        </g>
        <rect
          id='Rectangle_7928'
          data-name='Rectangle 7928'
          width='21'
          height='21'
          transform='translate(1036 341)'
          fill='none'
        />
      </g>
    </svg>
  );
}

AutoForwardMail.defaultProps = {
  fill: '#5478b5',
  width: '21',
  height: '21',
};
