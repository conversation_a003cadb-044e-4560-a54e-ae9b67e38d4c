import {IIConProps} from '@common/interfaces';

export function ExclamationMark(props: IIConProps) {
  const {fill, width, height} = props;
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 24 24'>
      <g id='Group_699' data-name='Group 699' transform='translate(-35 -312)'>
        <circle
          id='Ellipse_35'
          data-name='Ellipse 35'
          cx='12'
          cy='12'
          r='12'
          transform='translate(35 312)'
          fill={fill}
        />
        <path
          id='solid_exclamation'
          data-name='solid exclamation'
          d='M20,10.8a2,2,0,1,1-2-2A2,2,0,0,1,20,10.8ZM16.231.63l.34,6.8a.6.6,0,0,0,.6.57h1.658a.6.6,0,0,0,.6-.57l.34-6.8a.6.6,0,0,0-.6-.63H16.83a.6.6,0,0,0-.6.63Z'
          transform='translate(29 317)'
          fill='#fff'
        />
      </g>
    </svg>
  );
}

ExclamationMark.defaultProps = {
  width: '24',
  height: '24',
  fill: '#f8b226',
};
