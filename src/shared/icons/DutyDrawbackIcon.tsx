import {IIConProps} from '@common/interfaces';

export function DutyDrawbackIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 61.003 48.613'>
      <g id='Layer_1-2' transform='translate(0 0.007)'>
        <path
          id='Path_5709'
          data-name='Path 5709'
          d='M56.246,136.4H50.3l-.585-.66h6.536Z'
          transform='translate(-41.423 -113.118)'
          fill='#1e2e5f'
          opacity='0.4'
        />
        <rect
          id='Rectangle_14254'
          data-name='Rectangle 14254'
          width='6.55'
          height='3.084'
          transform='translate(9.805 18.768)'
          fill='#fff'
        />
        <path
          id='Path_5710'
          data-name='Path 5710'
          d='M62.587,108h-7.4a.366.366,0,0,0-.365.362v3.9a.365.365,0,0,0,.365.365h7.4a.373.373,0,0,0,.26-.107.368.368,0,0,0,.108-.258v-3.9a.368.368,0,0,0-.367-.362h0Zm-6.606,3.532v-2.439a.237.237,0,0,1,.46,0v2.439a.238.238,0,0,1-.46,0Zm1.07-.1v-2.249a.2.2,0,0,1,.043-.187.243.243,0,0,1,.187-.083.248.248,0,0,1,.187.083.207.207,0,0,1,.043.187v2.249a.2.2,0,0,1-.043.187.242.242,0,0,1-.187.083.248.248,0,0,1-.187-.083A.207.207,0,0,1,57.05,111.434Zm1.07.105V109.2a.218.218,0,0,1,.043-.2.241.241,0,0,1,.187-.088.236.236,0,0,1,.187.088.223.223,0,0,1,.043.2v2.342a.24.24,0,0,1-.46,0Zm1.072-.007v-2.439a.237.237,0,0,1,.46,0v2.439a.238.238,0,0,1-.46,0Zm1.07.007V109.2a.218.218,0,0,1,.043-.2.244.244,0,0,1,.373,0,.223.223,0,0,1,.043.2v2.337a.24.24,0,0,1-.46,0Zm1.532-.007h0a.238.238,0,0,1-.46,0v-2.439a.237.237,0,0,1,.46,0Z'
          transform='translate(-45.681 -90.002)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5711'
          data-name='Path 5711'
          d='M106.577,60.42H105.32l1.257,1.529Z'
          transform='translate(-87.763 -50.354)'
          fill='#fff'
        />
        <path
          id='Path_5712'
          data-name='Path 5712'
          d='M106.577,91.78V90.25H105.32Z'
          transform='translate(-87.763 -75.211)'
          fill='#8088af'
        />
        <path
          id='Path_5713'
          data-name='Path 5713'
          d='M103.64,64.654H104.9L103.64,63.12Z'
          transform='translate(-86.363 -52.604)'
          fill='#fff'
        />
        <path
          id='Path_5714'
          data-name='Path 5714'
          d='M103.64,92.97V94.5H104.9Z'
          transform='translate(-86.363 -77.478)'
          fill='#8088af'
        />
        <path
          id='Path_5715'
          data-name='Path 5715'
          d='M106.577,75.33H105.32l1.257,1.53Z'
          transform='translate(-87.763 -62.778)'
          fill='#fff'
        />
        <path
          id='Path_5716'
          data-name='Path 5716'
          d='M103.64,78.05v1.529H104.9Z'
          transform='translate(-86.363 -65.045)'
          fill='#fff'
        />
        <path
          id='Path_5717'
          data-name='Path 5717'
          d='M105.32,105.17l1.257,1.517V105.17Z'
          transform='translate(-87.763 -87.644)'
          fill='#8088af'
        />
        <path
          id='Path_5718'
          data-name='Path 5718'
          d='M103.64,107.87V109.4H104.9Z'
          transform='translate(-86.363 -89.894)'
          fill='#8088af'
        />
        <path
          id='Path_5719'
          data-name='Path 5719'
          d='M106.577,121.619V120.09H105.32Z'
          transform='translate(-87.763 -100.077)'
          fill='#8088af'
        />
        <path
          id='Path_5720'
          data-name='Path 5720'
          d='M72.06,86.24H66.285a.286.286,0,0,0-.285.282v3.039a.285.285,0,0,0,.285.285H72.06a.285.285,0,0,0,.285-.285V86.522a.286.286,0,0,0-.287-.282h0ZM66.905,89v-1.9a.184.184,0,0,1,.357,0V89a.184.184,0,0,1-.357,0Zm.835-.077V87.165a.167.167,0,0,1,.033-.147.188.188,0,0,1,.145-.065.194.194,0,0,1,.145.065.162.162,0,0,1,.033.147v1.754a.167.167,0,0,1-.033.147.188.188,0,0,1-.145.065.194.194,0,0,1-.145-.065A.162.162,0,0,1,67.74,88.919Zm.835.082V87.174a.176.176,0,0,1,.033-.152.188.188,0,0,1,.29,0,.173.173,0,0,1,.033.152V89a.185.185,0,0,1-.178.135A.183.183,0,0,1,68.576,89ZM69.412,89v-1.9a.184.184,0,0,1,.357,0V89a.184.184,0,0,1-.357,0Zm.835.005V87.177a.177.177,0,0,1,.033-.152.188.188,0,0,1,.29,0,.172.172,0,0,1,.033.152V89a.186.186,0,0,1-.357,0Zm1.2-.005h0a.184.184,0,0,1-.357,0v-1.9a.184.184,0,0,1,.357,0Z'
          transform='translate(-54.998 -71.87)'
          fill='#1e2e5f'
          opacity='0.4'
        />
        <path
          id='Path_5721'
          data-name='Path 5721'
          d='M103.64,124.312H104.9l-1.257-1.532Z'
          transform='translate(-86.363 -102.318)'
          fill='#8088af'
        />
        <path
          id='Path_5722'
          data-name='Path 5722'
          d='M112.86,120.08v0h0Z'
          transform='translate(-94.046 -100.068)'
          fill='#8088af'
        />
        <path
          id='Path_5723'
          data-name='Path 5723'
          d='M48.2,58.8h0l-.352-1.229a.261.261,0,0,0-.242-.185H46.451V42.73a.255.255,0,0,0-.077-.183.264.264,0,0,0-.183-.077h-2.1a.237.237,0,0,0-.073.038.271.271,0,0,0-.068.093.177.177,0,0,0-.015.042V44H32.82c-.138,0-.26.08-.26.18v.683c0,.1.115.182.26.182H43.9V57.388H42.735a.259.259,0,0,0-.242.185L42.14,58.8h0a.243.243,0,0,0,.038.218.262.262,0,0,0,.2.1h5.586a.257.257,0,0,0,.2-.1A.242.242,0,0,0,48.2,58.8ZM45.946,42.975v1.53l-1.257-1.53Zm0,4.973v1.53l-1.257-1.53Zm-1.537-.5V45.914l1.257,1.532Zm.282-1.982h1.257v1.527Zm-.282-2.035,1.257,1.53H44.409Zm0,4.973,1.257,1.53H44.409Zm1.537,2.035v1.53l-1.257-1.53Zm-1.537.453,1.257,1.53H44.409Zm0,2.486,1.257,1.532H44.409Zm0,4.018V55.861l1.257,1.532Zm1.539-.453-1.257-1.527h1.257v0h0Zm-1.257-4.014h1.257v1.517Z'
          transform='translate(-27.132 -35.396)'
          fill='#1e2e5f'
        />
        <rect
          id='Rectangle_14255'
          data-name='Rectangle 14255'
          width='6.55'
          height='3.084'
          transform='translate(6.261 13.6)'
          fill='#fff'
        />
        <rect
          id='Rectangle_14256'
          data-name='Rectangle 14256'
          width='6.55'
          height='3.084'
          transform='translate(6.138 13.6)'
          fill='#fff'
        />
        <path
          id='Path_5724'
          data-name='Path 5724'
          d='M36.107,57.882v.582a.6.6,0,0,0,.1.335.585.585,0,0,0,.272.22.381.381,0,0,1,.237.353.362.362,0,0,1-.075.22l-.2-.1a.242.242,0,0,0-.2,0l-.208.1a.371.371,0,0,1-.073-.22.237.237,0,0,0-.46,0,.822.822,0,0,0,.118.422l-2.632,1.272h-.348a.365.365,0,0,0-.365.365v3.892a.365.365,0,0,0,.365.365h7.4a.367.367,0,0,0,.368-.365V61.431a.361.361,0,0,0-.108-.258.367.367,0,0,0-.26-.107h-.348l-2.634-1.272a.788.788,0,0,0,.12-.418.839.839,0,0,0-.522-.78.136.136,0,0,1-.085-.13V57.88M33.89,64.6a.238.238,0,0,1-.46,0V62.159a.238.238,0,0,1,.46,0Zm1.027.09a.242.242,0,0,1-.187.083.248.248,0,0,1-.187-.083A.207.207,0,0,1,34.5,64.5V62.253a.2.2,0,0,1,.043-.187.243.243,0,0,1,.187-.083.248.248,0,0,1,.187.083.207.207,0,0,1,.043.187V64.5A.2.2,0,0,1,34.917,64.688Zm1.114-.09a.238.238,0,0,1-.46,0V62.159a.238.238,0,0,1,.46,0Zm1.072,0a.238.238,0,0,1-.46,0V62.159a.238.238,0,0,1,.46,0Zm1.07,0a.238.238,0,0,1-.46,0V62.159a.238.238,0,0,1,.46,0Zm.613-2.439a.238.238,0,0,1,.46,0v2.434h0a.237.237,0,0,1-.46,0Zm-.153-1.1H34.039l1.94-.945h0a.854.854,0,0,0,.357.083.789.789,0,0,0,.353-.085Z'
          transform='translate(-26.891 -48.237)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5725'
          data-name='Path 5725'
          d='M292.317,189.356a.278.278,0,0,0,.278-.268v-2.979a.279.279,0,0,0-.278-.278h-2.979a.279.279,0,0,0-.278.273v2.979a.279.279,0,0,0,.278.273Z'
          transform='translate(-240.873 -154.858)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5726'
          data-name='Path 5726'
          d='M264.3,185.83a.279.279,0,0,0-.278.278v2.979a.279.279,0,0,0,.278.273h2.979a.279.279,0,0,0,.278-.273v-2.979a.279.279,0,0,0-.278-.278Z'
          transform='translate(-220.007 -154.858)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5727'
          data-name='Path 5727'
          d='M280.272,165.531a.285.285,0,0,0,.207-.07.271.271,0,0,0,.092-.2v-2.979a.279.279,0,0,0-.273-.278h-2.979a.285.285,0,0,0-.207.07.271.271,0,0,0-.092.2v2.979a.279.279,0,0,0,.273.278Z'
          transform='translate(-230.84 -135.006)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5728'
          data-name='Path 5728'
          d='M255.362,165.476a.276.276,0,0,0,.2-.082.27.27,0,0,0,.078-.2v-2.979a.28.28,0,0,0-.078-.2.27.27,0,0,0-.2-.082h-2.979a.279.279,0,0,0-.273.278V165.2a.279.279,0,0,0,.273.278Zm-.954-2.831H253.3v-.253h1.112Z'
          transform='translate(-210.083 -134.95)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5729'
          data-name='Path 5729'
          d='M243.685,186.108a.279.279,0,0,0-.273-.278h-2.979a.277.277,0,0,0-.2.082.27.27,0,0,0-.078.2v2.979a.274.274,0,0,0,.273.273h2.979a.274.274,0,0,0,.273-.273v-2.979Zm-1.222.417h-1.1v-.253h1.112Z'
          transform='translate(-200.125 -154.858)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5730'
          data-name='Path 5730'
          d='M233.13,209.27v3.416a1.959,1.959,0,0,1,1.062.5.493.493,0,0,0,.33.125.5.5,0,0,0,.33-.125h0l.105-.1a1.987,1.987,0,0,1,2.681,0l.1.09a.5.5,0,0,0,.333.128.494.494,0,0,0,.333-.128h0l.065-.06a1.987,1.987,0,0,1,2.681,0,.5.5,0,0,0,.333.128.494.494,0,0,0,.333-.128,1.987,1.987,0,0,1,1.345-.5,1.951,1.951,0,0,1,1.335.5l.105.1a.5.5,0,0,0,.333.128.494.494,0,0,0,.333-.128l.133-.123a1.888,1.888,0,0,1,.6-.377l1.724-3.441H233.133Z'
          transform='translate(-194.267 -174.39)'
          fill='#1e2e5f'
        />
        <g
          id='Group_13200'
          data-name='Group 13200'
          transform='translate(31.972 38.676)'
          opacity='0.4'>
          <path
            id='Path_5731'
            data-name='Path 5731'
            d='M240.488,232.452h0l-.133.123a.994.994,0,0,1-1.337,0h0l-.105-.1a1.49,1.49,0,0,0-2.01,0,.994.994,0,0,1-1.337,0,1.49,1.49,0,0,0-2.01,0l-.085.04a.994.994,0,0,1-1.337,0l-.105-.1a1.489,1.489,0,0,0-1.987,0l-.105.1h0a.994.994,0,0,1-1.272.023c-.86-.849-1.347-.362-1.637-.368l-.2.637c.143.043.717-.707,1.5.083a1.489,1.489,0,0,0,1.987,0l.105-.1a.994.994,0,0,1,1.337,0l.1.1a1.49,1.49,0,0,0,2.01,0h0l.065-.06a.994.994,0,0,1,1.337,0,1.493,1.493,0,0,0,1.987,0,.993.993,0,0,1,1.34,0l.105.1a1.489,1.489,0,0,0,1.987,0h0l.138-.123a.992.992,0,0,1,.829-.243v-.5a1.493,1.493,0,0,0-1.167.382Z'
            transform='translate(-220.989 -232.042)'
            fill='#1e2e5f'
          />
          <path
            id='Path_5732'
            data-name='Path 5732'
            d='M211.269,239.5l-.133.1a1.217,1.217,0,0,1-1.337,0l-.105-.073a1.825,1.825,0,0,0-2.01,0,1.217,1.217,0,0,1-1.337,0,1.825,1.825,0,0,0-2.01,0l-.065.047a1.217,1.217,0,0,1-1.337,0h0l-.105-.073a1.829,1.829,0,0,0-1.987,0h0l-.105.073h0a1.165,1.165,0,0,1-.64.193,1.188,1.188,0,0,1-.65-.175,1.574,1.574,0,0,0-.6-.258c-.04-.01-.082-.018-.123-.025h-.007a1.973,1.973,0,0,0-.315-.025,1.757,1.757,0,0,0-1,.307l-.063.047a1.169,1.169,0,0,1-.667.2,1.183,1.183,0,0,1-.67-.2h0l-.105-.073a1.826,1.826,0,0,0-1.985.01h0l-.1.073h0a1.225,1.225,0,0,1-1.292.025,1.6,1.6,0,0,0-.727-.28l0,.383a1.08,1.08,0,0,1,.388.163,1.826,1.826,0,0,0,1.985-.01h0l.1-.073a1.168,1.168,0,0,1,.667-.2,1.183,1.183,0,0,1,.67.2l.1.068a1.828,1.828,0,0,0,2.012-.01l.063-.047a1.165,1.165,0,0,1,.667-.2,1.248,1.248,0,0,1,.507.105l.035.017c.03.015.058.032.085.048l.032.02a1.765,1.765,0,0,0,.994.293h.04a.715.715,0,0,0,.1-.005c.027,0,.053-.005.08-.008l.052-.005c.043-.007.085-.013.128-.023h0a1.686,1.686,0,0,0,.6-.255,1.214,1.214,0,0,1,1.34-.007h0l.105.072a1.836,1.836,0,0,0,1.84.078,1.5,1.5,0,0,0,.253-.143l.065-.047a1.165,1.165,0,0,1,.648-.2,1.016,1.016,0,0,1,.143.01v0a1.129,1.129,0,0,1,.542.19,1.829,1.829,0,0,0,1.987,0,1.214,1.214,0,0,1,1.34,0h0l.105.073a1.829,1.829,0,0,0,1.987,0l.138-.1a1.149,1.149,0,0,1,.829-.188V239.2a1.792,1.792,0,0,0-1.167.292Z'
            transform='translate(-191.79 -238.005)'
            fill='#1e2e5f'
          />
        </g>
        <circle
          id='Ellipse_283'
          data-name='Ellipse 283'
          cx='17.069'
          cy='17.069'
          r='17.069'
          transform='translate(13.458 7.231)'
          fill='#fff'
        />
        <circle
          id='Ellipse_284'
          data-name='Ellipse 284'
          cx='16.398'
          cy='16.398'
          r='16.398'
          transform='translate(14.128 7.901)'
          fill='#8088af'
        />
        <circle
          id='Ellipse_285'
          data-name='Ellipse 285'
          cx='13.876'
          cy='13.876'
          r='13.876'
          transform='translate(10.903 24.299) rotate(-45)'
          fill='#1e2e5f'
        />
        <path
          id='Path_5733'
          data-name='Path 5733'
          d='M155.12,108.627h5.311v1.517H155.12v1.937h4.969a2.987,2.987,0,0,1-2.641,1.6h-2.327v1.484l5.553,6.31,1.454-1.28-4.061-4.614a4.923,4.923,0,0,0,4.106-3.5h2.23v-1.937H162.37v-1.517H164.4V106.69h-9.282v1.937Z'
          transform='translate(-129.261 -88.911)'
          fill='#fff'
        />
        <path
          id='Path_5734'
          data-name='Path 5734'
          d='M216.128,10.508h0L208.041.662a1.77,1.77,0,0,0-2.02-.552,1.98,1.98,0,0,0-1.2,1.85V4.915h0V7.234h-18.1a17.064,17.064,0,0,1,14.5,9.144h3.606V18.7h0v2.954a1.98,1.98,0,0,0,1.2,1.85,1.773,1.773,0,0,0,2.02-.55h0l8.087-9.845a2.039,2.039,0,0,0,0-2.6Z'
          transform='translate(-155.593)'
          fill='#f05a29'
        />
        <path
          id='Path_5735'
          data-name='Path 5735'
          d='M15.358,157.114H11.776V154.8h0v-2.954a1.98,1.98,0,0,0-1.2-1.85,1.77,1.77,0,0,0-2.02.552l-8.09,9.844h0a2.039,2.039,0,0,0,0,2.6l8.087,9.845h0a1.773,1.773,0,0,0,2.02.55,1.98,1.98,0,0,0,1.2-1.85v-2.954h0v-2.32H29.978a17.065,17.065,0,0,1-14.621-9.144Z'
          transform='translate(0 -124.895)'
          fill='#f05a29'
        />
      </g>
    </svg>
  );
}

DutyDrawbackIcon.defaultProps = {
  fill: '#fff',
  width: '20',
  height: '20',
};
