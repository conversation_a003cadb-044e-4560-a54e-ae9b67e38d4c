interface IRoundCheckProps {
  fill?: string;
  width?: number;
  height?: number;
}

export function SolidCheck({fill, width, height}: IRoundCheckProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 14.233 9.729'>
      <path
        id='solid_check'
        data-name='solid check'
        d='M4.835,74.637.209,70.4a.615.615,0,0,1,0-.923l1.006-.923a.758.758,0,0,1,1.006,0l3.116,2.856,6.675-6.118a.758.758,0,0,1,1.006,0l1.006.923a.615.615,0,0,1,0,.923l-8.184,7.5a.758.758,0,0,1-1.006,0Z'
        transform='translate(0 -65.098)'
        fill={fill}
      />
    </svg>
  );
}

SolidCheck.defaultProps = {
  fill: '#2cb544',
  width: '14',
  height: '9',
};
