import {IIConProps} from '@common/interfaces';

export function GstLogo({fill, width, height}: IIConProps) {
  return (
    <svg
      id='Logo'
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 160.262 78.029'>
      <defs>
        <linearGradient
          id='linear-gradient'
          x1='0.019'
          y1='0.662'
          x2='0.77'
          y2='0.167'
          gradientUnits='objectBoundingBox'>
          <stop offset='0' stopColor='#ec411b' />
          <stop offset='1' stopColor='#ae0721' />
        </linearGradient>
        <linearGradient
          id='linear-gradient-2'
          x1='-0.032'
          y1='0.5'
          x2='1'
          y2='0.5'
          gradientUnits='objectBoundingBox'>
          <stop offset='0' stopColor='#ae0721' />
          <stop offset='0.089' stopColor='#bd151f' />
          <stop offset='0.265' stopColor='#d72d1d' />
          <stop offset='0.424' stopColor='#e63b1b' />
          <stop offset='0.552' stopColor='#ec411b' />
          <stop offset='0.707' stopColor='#e93e1b' />
          <stop offset='0.805' stopColor='#e0361c' />
          <stop offset='0.886' stopColor='#d2281d' />
          <stop offset='0.958' stopColor='#bd151f' />
          <stop offset='1' stopColor='#ae0721' />
        </linearGradient>
      </defs>
      <path
        id='Path_1'
        data-name='Path 1'
        d='M73.488,203.4a2.277,2.277,0,0,0-.327-1.317c-.035-.063-.072-.124-.11-.184-.253-.394-.936-1-1.228-1.363l-10.57-10.523-.285-.271-12.6,12.542a2.181,2.181,0,0,0-.556,1.34v24.719c0,.433.25.536.556.232l24.966-24.858A.559.559,0,0,0,73.488,203.4Z'
        transform='translate(-47.815 -150.727)'
        fill='url(#linear-gradient)'
      />
      <path
        id='Path_2'
        data-name='Path 2'
        d='M74.1,99.4v-.235c.005-.184,0-.405,0-.709V74.4a2.172,2.172,0,0,0-.557-1.338L48.371,47.99c-.307-.305-.556-.2-.556.231V72.94a2.174,2.174,0,0,0,.556,1.34L71.823,97.63c.292.359.974.969,1.228,1.363.038.059.075.12.11.184a2.277,2.277,0,0,1,.327,1.317.567.567,0,0,1-.151.32A2.219,2.219,0,0,0,74,99.838a1.92,1.92,0,0,0,.073-.3A.34.34,0,0,0,74.1,99.4Z'
        transform='translate(-47.815 -47.815)'
        fill='url(#linear-gradient-2)'
      />
      <g id='Group_1' data-name='Group 1' transform='translate(34.171 29.699)'>
        <path
          id='Path_3'
          data-name='Path 3'
          d='M546.365,165.347c0-5.331,3.6-9.335,8.963-9.332,5.33,0,8.954,4.009,8.95,9.34s-3.629,9.334-8.962,9.333C549.96,174.686,546.362,170.677,546.365,165.347Zm15.588.006c0-4.221-2.6-7.335-6.625-7.337-4.06,0-6.636,3.111-6.636,7.333,0,4.193,2.569,7.334,6.631,7.335C559.353,172.687,561.95,169.549,561.953,165.353Z'
          transform='translate(-444.199 -155.967)'
          fill={fill}
        />
        <path
          id='Path_4'
          data-name='Path 4'
          d='M174.753,165.172c.005-5.711,4.335-9.335,9.61-9.333.343,0,.7.047,1.038.079s.664.075.992.135a7.5,7.5,0,0,1,1.753.539,8.269,8.269,0,0,1,2.623,1.909c.2.22.387.452.567.69.088.116.174.234.259.352.063.089.155.2.131.319-.028.139-.174.189-.281.244-.158.081-.321.153-.476.237l-1.564.848c-.135.073-.267.15-.4.224a.685.685,0,0,1-.323.124c-.117,0-.164-.086-.224-.171-.079-.111-.156-.224-.245-.329a5.55,5.55,0,0,0-.521-.531q-.152-.135-.314-.257a4.741,4.741,0,0,0-1.819-.854,5.759,5.759,0,0,0-1.193-.15,5.932,5.932,0,0,0-.005,11.851,6.1,6.1,0,0,0,3.843-1.377v-2.166l-4.76,0v-3.02c0-.063,0-.126,0-.189,0-.045,0-.1.049-.123a.307.307,0,0,1,.144-.022h7.449a1.417,1.417,0,0,1,.754.1c.244.147.219.388.213.63-.005.226,0,.452,0,.679v1.668q0,.873,0,1.747,0,.716,0,1.429c0,.22,0,.309,0,.529,0,0,0,.141,0,.142a10.006,10.006,0,0,1-7.686,3.405C179.08,174.536,174.75,170.853,174.753,165.172Z'
          transform='translate(-174.753 -155.839)'
          fill={fill}
        />
        <path
          id='Path_5'
          data-name='Path 5'
          d='M244.6,171.725s0-.011,0-.016c-.008-.092.056-.15.1-.218l.435-.609.548-.768.518-.728c.079-.11.158-.223.231-.338.127-.2.207-.2.329-.087.071.064.137.133.209.2.119.1.238.209.358.313.033.029.065.057.1.085a7.877,7.877,0,0,0,.968.658,7.988,7.988,0,0,0,1.123.539,6.809,6.809,0,0,0,1.262.348,12.428,12.428,0,0,0,1.389.145c.057,0,.115.01.172.01,2.113,0,3.141-.972,3.141-2,0-3.194-10.387-1-10.385-7.8,0-3,2.6-5.49,6.845-5.49a14.538,14.538,0,0,1,1.612.105,10.374,10.374,0,0,1,1.513.281,9.753,9.753,0,0,1,1.387.481,8.279,8.279,0,0,1,1.253.68,12.358,12.358,0,0,1,1.027.774.285.285,0,0,1,.061.441c-.15.2-.3.393-.448.589q-.282.373-.565.745l-.534.7c-.077.1-.016.023-.089.125a.815.815,0,0,1-.246.244c-.209.076-.378-.094-.51-.2-.156-.121-.315-.239-.478-.351a6.525,6.525,0,0,0-.987-.555,7.93,7.93,0,0,0-2.136-.619c-.359-.05-.72-.071-1.082-.087-.043,0-.086,0-.13,0-1.651,0-2.572.729-2.572,1.812,0,2.866,10.365.95,10.363,7.691,0,3.3-2.356,5.789-7.228,5.786a15.3,15.3,0,0,1-1.956-.134,10.281,10.281,0,0,1-1.718-.375,10.844,10.844,0,0,1-1.51-.6,8.575,8.575,0,0,1-1.323-.788,7.1,7.1,0,0,1-.564-.464c-.123-.11-.254-.216-.369-.335A.385.385,0,0,1,244.6,171.725Z'
          transform='translate(-225.398 -155.932)'
          fill={fill}
        />
        <path
          id='Path_6'
          data-name='Path 6'
          d='M434.165,157.028l-.007,18.05,11.825.008c0-.056,0-.112,0-.167,0-.312,0-.622,0-.933,0-.273.052-.464-.178-.7s-.482-.207-.78-.207h-8.618v-6.224l8.554,0v-2l-8.554-.008,0-5.815h8.425c.213,0,.107,0,.4,0a.946.946,0,0,0,.475-.077.6.6,0,0,0,.28-.459v-1.474Z'
          transform='translate(-362.841 -156.701)'
          fill={fill}
        />
        <path
          id='Path_7'
          data-name='Path 7'
          d='M382.076,168.211l0-11.209h-1.728c-.087,0-.087,0-.261,0a.215.215,0,0,0-.21.133.563.563,0,0,0-.051.305c0,.241,0,.463,0,.694v6.63l-10.558,0v-.013q0-.373,0-.746,0-.8,0-1.6,0-.978,0-1.956,0-.932,0-1.865v-.8a1.029,1.029,0,0,0-.107-.633c-.12-.175-.246-.156-.422-.156l-1.72,0-.006,12.393h0l0,5.675h1.687c.176,0,.334.015.454-.159a1.029,1.029,0,0,0,.107-.634v-.8q0-.933,0-1.866,0-.979,0-1.956c0-.48,0-2.405,0-2.884l10.557,0,0,2.615h0v4.531c0,.23-.006.463,0,.7a.561.561,0,0,0,.051.305.218.218,0,0,0,.21.134l.261,0h1.728l0-6.85Zm-12.8-1.443h0Z'
          transform='translate(-314.167 -156.676)'
          fill={fill}
        />
        <path
          id='Path_8'
          data-name='Path 8'
          d='M613.4,160.7h-.36v-2.779h-.982V157.6h2.324v.321H613.4Z'
          transform='translate(-491.834 -157.112)'
          fill={fill}
        />
        <path
          id='Path_9'
          data-name='Path 9'
          d='M623.584,160.7l-1.051-2.748h-.017q.031.326.03.776V160.7h-.333v-3.1h.543l.981,2.557h.017l.991-2.557h.538v3.1h-.361v-2q0-.344.03-.747h-.017l-1.06,2.744Z'
          transform='translate(-499.194 -157.112)'
          fill={fill}
        />
        <path
          id='Path_10'
          data-name='Path 10'
          d='M497.4,167.748a4.986,4.986,0,0,0,4.683-5.247c0-3.328-2.351-5.44-5.654-5.44l-7.249-.006-.008,18.05h1.59a.57.57,0,0,0,.563-.2.844.844,0,0,0,.118-.554c0-.355-.027-.686-.027-1.041q0-1.739,0-3.477,0-.947,0-1.895h3.6l.01.015.139.217q.154.238.306.478l.435.679.519.81.584.911c.2.308.4.616.593.924s.387.605.58.907q.267.415.532.831.208.322.415.646l.225.353a1.008,1.008,0,0,0,.277.318.508.508,0,0,0,.284.081h2.349Zm-1.245-1.786h-4.735l0-6.9,4.736,0a3.452,3.452,0,1,1,0,6.9Z'
          transform='translate(-402.73 -156.72)'
          fill={fill}
        />
        <path
          id='Path_11'
          data-name='Path 11'
          d='M316.918,159.656v-.833q0-.439,0-.876c0-.231,0-.461,0-.692v-.281l-14.4-.005h0v2.7c.017.451.113.677.558.677h.622l4.1,0-.01,14.67h3.873l.005-14.667H315.8c.156,0,.156,0,.343-.005.12,0,.12,0,.457-.006a.318.318,0,0,0,.321-.363C316.923,159.873,316.918,159.766,316.918,159.656Z'
          transform='translate(-267.394 -156.658)'
          fill={fill}
        />
      </g>
    </svg>
  );
}

GstLogo.defaultProps = {
  fill: '082556',
  width: '160',
  height: '78',
};
