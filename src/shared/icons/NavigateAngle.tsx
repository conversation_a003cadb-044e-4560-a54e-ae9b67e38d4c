import {IIConProps} from '@common/interfaces';

export function NavigateAngle({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 11.207 21.414'>
      <path
        id='Path_5909'
        data-name='Path 5909'
        d='M5,7.5l10,10,10-10'
        transform='translate(18 -4.293) rotate(90)'
        fill='none'
        stroke='#163463'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1'
      />
    </svg>
  );
}

NavigateAngle.defaultProps = {
  fill: 'none',
  width: 10,
  height: 22,
};
