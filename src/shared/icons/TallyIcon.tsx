import {IIConProps} from '@common/interfaces';

export function TallyIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 105 77'>
      <g
        id='Group_13390'
        data-name='Group 13390'
        transform='translate(20674 19535)'>
        <g id='Tally-Logo-Vector-eps' transform='translate(-20773.439 -19535)'>
          <path
            id='Path_3514'
            data-name='Path 3514'
            d='M136.079,29.761a30.418,30.418,0,0,0,4.321-4.645c.2-.347.769-.943.1-1.514-.67-.6-1.167-.248-1.888.1a57.691,57.691,0,0,0-5.189,3.75c-1.167.943-4.172,3.576-4.172,3.576s-1.637,1.439-1.092,2.161c.619.694,1.143.446,2.062.2a47.444,47.444,0,0,0,5.859-3.627h0Zm67.167,17.084c.446.2,1.565.5,1.463,1.565a10.531,10.531,0,0,1-1.364,2.259c-.422.2-1.116.2-2.756-.446-1.589-.623-8.864-2.484-10.851-2.882-1.936-.4-6.431-1.116-6.431-1.116s-.548-.347-1.467,1.068c-.919,1.467-3.825,5.638-3.825,5.638a.851.851,0,0,1-.721.572,11.751,11.751,0,0,1-3.773-.619c-.919-.5-1.116-.446-.6-1.392.548-.919,2.583-4.941,3.45-5.761.82-.8-.548-.6-1.017-.7-.4-.1-9.214-.544-11.821-.5-2.559.051-11.718.051-15,.248-3.253.2-15.172,1.341-18.326,1.814-3.178.548-15.917,2.358-17.656,2.705-1.715.347-5.138.97-5.138.97a5.968,5.968,0,0,1-3.4-.347c-1.688-.769-4.4-1.861-2.11-2.583,2.358-.745,13.607-2.981,17.132-3.477,3.525-.548,14.478-2.11,19.119-2.358,4.617-.248,17.01-.919,21.7-.919,4.645.051,11.123.347,13.855.572,2.756.2,4.593.347,5.114.4.548.1.6-.3,1.116-.919.548-.646,2.283-2.906,2.283-2.906s.572-.868-.548-.122a3.363,3.363,0,0,1-3.773-.051c-.868-.895-1.29-1.64-1.688-1.341a23.38,23.38,0,0,1-2.957,1.341c-.919.323-4.791,1.218-7.077-.347-2.358-1.613-2.409-2.212-2.705-3-.323-.721-.473-.67-1.218-.174-.769.473-3.375,1.763-4.345,2.184a5.62,5.62,0,0,1-3.205.646c-1.49-.1-3.154-.548-3.872-1.763-.67-1.218-.769-2.11-1.589-1.565a37.049,37.049,0,0,1-7.275,3.675c-2.31.67-2.855.966-4.148.721a4.9,4.9,0,0,1-3.2-1.514c-.5-.769-.548-.97-1.167-.521-.572.473-2.184,1.715-3.426,1.613a8.232,8.232,0,0,1-6.108-4.121c-1.739-3.229-.5-4.546,1.068-6.159,1.538-1.589,4.3-3.923,4.3-3.923a44.6,44.6,0,0,1,4.57-3.079c1.191-.548,3-1.613,4.2-1.712a6.322,6.322,0,0,1,4.57,1.364,10.071,10.071,0,0,1,2.855,3.277c.2.548.919,2.484-.248,3.825-1.218,1.317-4.645,4.495-4.645,4.495s-1.317.8-.721,1.711c.619.994,2.855-.6,3.624-1.068.721-.548,4.645-3.872,5.859-4.992,1.167-1.191,8.816-8.615,9.534-9.313.67-.694,6.975-7,7.9-7.6a3.039,3.039,0,0,1,2.953-.67,10.186,10.186,0,0,1,2.756.919s1.786.694.347,1.814c-1.415,1.092-6.21,5.09-7.377,6.38-1.218,1.218-4.692,4.692-5.812,5.958s-4.223,5.039-4.743,5.784c-.521.721-.769,1.514,0,2.062.769.473,2.086-.051,3.525-1.242,1.415-1.116,5.536-4.743,7.026-6.159,1.467-1.467,9.388-9.013,10.082-9.66.721-.572,4.4-3.525,4.4-3.525a1.865,1.865,0,0,1,1.514-.619,8.619,8.619,0,0,1,2.807.446,18.613,18.613,0,0,1,1.837.97s.769.347-.5,1.266a124.28,124.28,0,0,0-9.585,8.044c-1.786,1.912-5.362,5.812-6.679,7.622-1.341,1.814-2.236,2.606-1.218,3.825a2.581,2.581,0,0,0,2.855.745,19.243,19.243,0,0,0,4.121-3.2c1.467-1.514,6.108-6.455,6.581-7.054.5-.572,2.184-2.283,2.184-2.283a1.448,1.448,0,0,1,1.317-.7,11.4,11.4,0,0,1,2.559.347c.966.2,3.056.646,1.888,1.814-1.242,1.218-4.073,4.172-4.692,4.893-.619.745-3.056,3.426-3.525,4.223-.446.82-1.068,1.715-.446,2.086.67.4,2.184-.126,3.773-1.017a31.288,31.288,0,0,0,5.812-4.121c1.987-1.763,5.189-4.692,5.189-4.692s.572-1.167,2.855-.67a14.79,14.79,0,0,1,2.855.82s1.79.5.619,1.514c-1.191,1.092-5.165,4.645-6.679,6.084-1.538,1.467-6.183,6.206-7,7.125-.793.943-3,3.056-2.086,3.154s7.8,1.415,9.435,1.861c1.64.422,7.026,2.236,7.5,2.484h0ZM125.055,9.25a17.6,17.6,0,0,0-4.121-.051c-2.184.248-8.864.943-10.2,1.467-1.266.544-2.184-.67-2.681-1.218-.572-.5-1.837-1.814-1.837-1.814s-.67-.793.5-1.068c1.242-.248,8.02-1.439,10.157-1.613,2.137-.2,12.491-.793,15.223-.793,2.705.051,12.29.548,14.8,1.218,0,0,.5-.15,1.017,1.092a5.817,5.817,0,0,1,.4,2.385s.521.895-1.266.793c-1.739-.15-4.645-.3-6.628-.4-1.936-.15-6.679-.5-7.4-.446-.646.051-.844,0-1.266.548-.4.6-2.283,2.981-2.756,3.627-.446.6-2.433,3.127-4.121,5.437-1.688,2.235-3.825,5.09-5.71,8.071-1.837,2.981-3.576,5.737-4.27,7a12.616,12.616,0,0,0-1.119,2.31,1.246,1.246,0,0,1-1.143.82c-.844.051-4.842-.1-4.842-.1s-1.664.15-.646-1.715c1.017-1.912,3.3-5.686,4.27-7.152s5.91-8.268,6.88-9.534c.919-1.218,6.778-8.864,6.778-8.864h0Z'
            transform='translate(-1.049 -2.745)'
            fill='#ed1c24'
            fillRule='evenodd'
          />
          <path
            id='Path_3515'
            data-name='Path 3515'
            d='M134.486,25.55a32.83,32.83,0,0,0,4.321-4.645c.2-.3.769-.895.1-1.514-.646-.6-1.167-.248-1.888.075-.694.347-3.974,2.855-5.189,3.773-1.167.943-4.172,3.576-4.172,3.576s-1.64,1.415-1.068,2.161c.548.745,1.119.446,1.987.2.919-.248,4.519-2.708,5.91-3.627ZM130.512,0h-.844c-3.6.051-12.44.548-14.4.721-2.137.2-8.915,1.392-10.157,1.664-1.218.3-.5,1.045-.5,1.045s1.266,1.317,1.837,1.861c.5.473,1.415,1.763,2.705,1.218,1.317-.6,8-1.218,10.181-1.514a16.893,16.893,0,0,1,4.073.051s-5.812,7.622-6.73,8.915c-.966,1.218-5.91,8.071-6.877,9.486-.966,1.467-3.253,5.288-4.321,7.152-1.017,1.861.694,1.763.694,1.763s4,.1,4.842.1a1.133,1.133,0,0,0,1.092-.895,24.581,24.581,0,0,1,1.167-2.283c.721-1.242,2.385-3.974,4.27-6.951s4.022-5.883,5.71-8.119c1.688-2.259,3.675-4.866,4.121-5.437.473-.6,2.358-3.056,2.756-3.576.422-.6.572-.548,1.266-.6.721-.051,5.465.3,7.4.446,1.987.15,4.842.248,6.628.4s1.266-.8,1.266-.8a5.675,5.675,0,0,0-.4-2.358c-.572-1.266-1.017-1.068-1.017-1.068C142.717.5,133.183.007,130.5.007h0ZM201.65,42.634c.4.248,1.565.544,1.415,1.565a10.029,10.029,0,0,1-1.364,2.31c-.422.15-1.116.15-2.705-.5a101.466,101.466,0,0,0-10.851-2.831c-1.936-.4-6.431-1.191-6.431-1.191s-.548-.323-1.463,1.143c-.97,1.415-3.824,5.563-3.824,5.563s-.2.6-.721.6a11.229,11.229,0,0,1-3.773-.646c-.919-.422-1.116-.374-.6-1.368.548-.919,2.583-4.941,3.4-5.761.868-.8-.5-.6-.97-.694-.446-.051-9.211-.548-11.817-.5s-11.718.051-14.975.248c-3.277.248-15.247,1.317-18.4,1.861-3.178.5-15.866,2.358-17.6,2.708-1.712.323-5.141.919-5.141.919a5.534,5.534,0,0,1-3.4-.347c-1.688-.769-4.4-1.861-2.11-2.532,2.358-.745,13.607-3,17.132-3.525,3.525-.5,14.475-2.11,19.119-2.358,4.593-.248,17.01-.919,21.7-.868,4.645.051,11.123.323,13.855.521,2.756.2,4.594.347,5.114.446.544.1.6-.3,1.116-.919.544-.646,2.283-2.953,2.283-2.953s.572-.82-.548-.1a3.359,3.359,0,0,1-3.773-.1c-.919-.868-1.29-1.565-1.688-1.317a18.56,18.56,0,0,1-3,1.364c-.868.3-4.743,1.191-7.026-.371-2.358-1.565-2.409-2.208-2.705-2.953-.3-.769-.473-.721-1.218-.248a43.909,43.909,0,0,1-4.345,2.208,5.6,5.6,0,0,1-3.2.694c-1.49-.15-3.154-.6-3.872-1.814s-.769-2.11-1.589-1.514a41.712,41.712,0,0,1-7.275,3.675c-2.31.619-2.855.919-4.148.721-1.317-.248-2.732-.769-3.2-1.565-.5-.769-.6-.97-1.167-.5-.572.446-2.184,1.715-3.426,1.565a7.96,7.96,0,0,1-6.159-4.1c-1.739-3.178-.446-4.57,1.068-6.135,1.589-1.613,4.345-3.947,4.345-3.947s3.4-2.508,4.57-3.1c1.191-.521,2.953-1.613,4.2-1.715a6.657,6.657,0,0,1,4.57,1.392A10.223,10.223,0,0,1,143.894,19c.225.473.919,2.433-.3,3.773C142.431,24.094,139,27.272,139,27.272s-1.364.82-.694,1.763c.6.919,2.855-.6,3.552-1.116.769-.5,4.692-3.876,5.91-5.015,1.191-1.167,8.816-8.615,9.486-9.238.67-.7,6.979-7.054,7.945-7.649a3.041,3.041,0,0,1,2.953-.67,10.266,10.266,0,0,1,2.708.919s1.786.694.371,1.814c-1.392,1.116-6.182,5.141-7.35,6.356-1.215,1.242-4.692,4.716-5.812,5.985s-4.27,5.039-4.743,5.784c-.5.721-.769,1.565,0,2.038.769.544,2.086-.051,3.525-1.167,1.415-1.167,5.536-4.743,7.026-6.206,1.415-1.463,9.388-9.013,10.082-9.609.721-.619,4.4-3.576,4.4-3.576a1.865,1.865,0,0,1,1.514-.619,7.933,7.933,0,0,1,2.8.473,13.579,13.579,0,0,1,1.837.994s.769.3-.5,1.218c-1.293.943-7.846,6.135-9.633,8.02-1.739,1.912-5.315,5.886-6.679,7.649-1.317,1.814-2.184,2.63-1.167,3.825a2.448,2.448,0,0,0,2.855.721,17.36,17.36,0,0,0,4.121-3.178c1.466-1.467,6.108-6.407,6.581-7.05.5-.572,2.184-2.283,2.184-2.283a1.449,1.449,0,0,1,1.317-.694,11.4,11.4,0,0,1,2.559.347c.966.248,3.056.619,1.888,1.861-1.218,1.167-4.073,4.1-4.692,4.842s-3.056,3.426-3.525,4.246c-.446.793-1.068,1.715-.446,2.062.619.4,2.184-.15,3.726-1.017a32.873,32.873,0,0,0,5.859-4.073c1.987-1.814,5.189-4.692,5.189-4.692s.521-1.218,2.855-.694a14.459,14.459,0,0,1,2.855.793s1.786.5.619,1.565c-1.191,1.068-5.165,4.593-6.679,6.06-1.538,1.439-6.182,6.182-6.979,7.1-.82.994-3.028,3.056-2.11,3.154.919.075,7.8,1.467,9.435,1.888,1.64.4,7.026,2.259,7.5,2.46h0Z'
            transform='translate(0)'
            fill={fill}
            fillRule='evenodd'
          />
        </g>
        <text
          id='Power_of_Simplicity'
          data-name='Power of Simplicity'
          transform='translate(-20674 -19463)'
          fill='#262626'
          fontSize='13'
          fontFamily='SourceSansPro-Regular, Source Sans Pro'>
          <tspan x='0' y='0'>
            Power of Simplicity
          </tspan>
        </text>
      </g>
    </svg>
  );
}

TallyIcon.defaultProps = {
  fill: '#231f20',
  width: '105',
  height: '77',
};
