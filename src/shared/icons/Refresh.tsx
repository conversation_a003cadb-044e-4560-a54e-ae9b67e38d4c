import {IIConProps} from '@common/interfaces';

export function RefreshIcon({fill, width, height}: IIConProps) {
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 16 16'>
      <path
        id='solid_sync-alt'
        data-name='solid sync-alt'
        d='M19.7,12.041a5.421,5.421,0,0,0-8.957,2.631.388.388,0,0,1-.376.3H8.519a.387.387,0,0,1-.381-.457,8,8,0,0,1,13.388-4.295l1.152-1.152A.774.774,0,0,1,24,9.611v4.324a.774.774,0,0,1-.774.774H18.9a.774.774,0,0,1-.547-1.322ZM8.774,17.29H13.1a.774.774,0,0,1,.547,1.322L12.3,19.959a5.421,5.421,0,0,0,8.957-2.631.388.388,0,0,1,.376-.3h1.849a.387.387,0,0,1,.381.457,8,8,0,0,1-13.388,4.295L9.322,22.936A.774.774,0,0,1,8,22.389V18.065A.774.774,0,0,1,8.774,17.29Z'
        transform='translate(-8 -8)'
        fill={fill}
      />
    </svg>
  );
}

RefreshIcon.defaultProps = {
  fill: '#002662',
  width: '16',
  height: '16',
};
