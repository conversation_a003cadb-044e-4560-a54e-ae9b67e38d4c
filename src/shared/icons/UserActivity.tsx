import {IIConProps} from '@common/interfaces';

export function UserActivity(props: IIConProps) {
  const {fill, width, height} = props;
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      viewBox='0 0 16.353 16.377'>
      <g id='noun-activity-4147465' transform='translate(-153.821 -83.441)'>
        <path
          id='Path_3377'
          data-name='Path 3377'
          d='M168.7,129.57v-6.585a2.66,2.66,0,0,1-.607.047,3.329,3.329,0,0,1-3.316-3.316,4.914,4.914,0,0,1,.047-.607h-6.585A4.405,4.405,0,0,0,155,120.276a4.5,4.5,0,0,0-1.168,3.246v6.048a4.048,4.048,0,0,0,4.413,4.413h6.048a4.405,4.405,0,0,0,3.246-1.168,4.6,4.6,0,0,0,1.168-3.246Zm-3.082-4.6-2.615,3.176a.719.719,0,0,1-.957.117l-2.125-1.588-2.078,2.5a.735.735,0,0,1-.537.257.616.616,0,0,1-.444-.164.7.7,0,0,1-.093-.981l2.522-3.012a.719.719,0,0,1,.957-.117l2.125,1.588,2.2-2.639a.682.682,0,0,1,.981-.093A.667.667,0,0,1,165.621,124.97Z'
          transform='translate(0 -34.172)'
          fill={fill}
        />
        <path
          id='Path_3378'
          data-name='Path 3378'
          d='M454.77,85.427a1.985,1.985,0,1,1-1.985-1.985,1.985,1.985,0,0,1,1.985,1.985'
          transform='translate(-284.595)'
          fill={fill}
        />
      </g>
    </svg>
  );
}

UserActivity.defaultProps = {
  color: '#b5bad3',
  width: '16',
  height: '16',
};
