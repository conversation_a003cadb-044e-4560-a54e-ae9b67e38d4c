import {fireEvent, render, screen} from '@testing-library/react';

import EximDropdown from '.';

test('Should render EximDropdown', () => {
  const optionsList = [
    {
      id: '1',
      value: 'green',
      label: 'Green',
    },
  ];
  const onChange = jest.fn;
  render(
    <EximDropdown
      onChange={onChange}
      optionsList={optionsList}
      isInvalid
      isRequired
      label='label'
    />
  );
  const {getByRole} = screen;
  const dropdown = getByRole('combobox');
  fireEvent.click(dropdown);
  fireEvent.change(dropdown, {target: {value: 'green'}});
  expect(screen.getByText('Green')).toBeInTheDocument();
});
