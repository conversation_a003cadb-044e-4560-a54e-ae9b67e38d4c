@import '@utils/main.scss';
.select-dropdown .dropdown {
  color: $dark-grey;
  &:hover {
    border-color: $blue-hover;
  }
}

.select-dropdown .placeholder {
  color: $text-color;
  background-color: $white;
}

.isInvalid {
  .select {
    .dropdown {
      border-color: $error;
    }
  }
}

.error-message {
  color: $error;
}

.dropdown-label {
  @include margin-bottom(3px);
}

.star::after {
  content: '*';
  color: $error;
  font-size: 20px;
  position: relative;
  top: 4px;
  left: 3px;
}
