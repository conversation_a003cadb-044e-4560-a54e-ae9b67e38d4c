import Dropdown, {
  Props as DropdownInterface,
} from '@submodules/Dropdown/Dropdown';

import './index.scss';

interface Prop extends DropdownInterface {
  isInvalid?: boolean;
  errorMessage?: string | boolean;
  label?: string;
  isRequired?: boolean;
}

export default function EximDropdown({
  optionsList,
  value,
  placeholder,
  onChange,
  isInvalid,
  disabled,
  errorMessage,
  label,
  isRequired,
  dataTestId,
  name,
}: Prop) {
  return (
    <div className={`select-dropdown ${isInvalid && 'isInvalid'} `}>
      {label && (
        <div className={`dropdown-label ${isRequired && 'star'}`}>{label}</div>
      )}
      <Dropdown
        optionsList={optionsList}
        value={value}
        name={name}
        placeholder={placeholder}
        onChange={onChange}
        disabled={disabled}
        dataTestId={dataTestId}
      />
      <div className='error-message'>{errorMessage}</div>
    </div>
  );
}

EximDropdown.defaultProps = {
  isInvalid: false,
  errorMessage: '',
  label: '',
  isRequired: false,
};
