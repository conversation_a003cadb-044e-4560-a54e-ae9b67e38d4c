import ProgressBar, {
  ProgressBarProps,
} from '@submodules/ProgressBar/ProgressBar';

import './index.scss';

interface EximProgressBarProps extends ProgressBarProps {
  progressLabel?: string;
  uploadSize?: number;
  unit?: 'B' | 'KB' | 'MB';
  bgColor: 'success';
}
function EximProgressBar(props: EximProgressBarProps) {
  const {progressLabel, unit, uploadSize, progress, bgColor} = props;
  return (
    <div className='progress-wrapper'>
      <div className='progress-bar-label'>{progressLabel}</div>
      <ProgressBar bgColor={bgColor} progress={progress} />
      <div className='progress-info'>
        <span className='upload-size'>{`${uploadSize} ${unit}`}</span>
        <span className='upload-progress'>Uploading.. {progress}%</span>
      </div>
    </div>
  );
}

EximProgressBar.defaultProps = {
  progressLabel: '',
  uploadSize: null,
  unit: null,
};

export default EximProgressBar;
