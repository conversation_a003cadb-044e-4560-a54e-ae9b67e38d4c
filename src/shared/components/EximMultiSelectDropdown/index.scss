@import '@utils/main.scss';

.multi-select-container {
  width: 220px;
  max-height: 33px;
  .select-field {
    width: 100%;
    height: 100%;
    position: relative;
    text-transform: capitalize;
    &-container {
      max-width: 220px;
      height: 33px;
      @include padding(4.5px 8px);
      border: 1px solid $primary-border;
      @include font-size($font-size-sm);
      border-radius: 5px;
      @include flex-item(_, space-between, center, nowrap);
      text-align: left;
      &.open {
        border-radius: 5px 5px 0 0;
      }
      .down-icon {
        cursor: pointer;
      }
      .up-icon {
        transform: rotate(180deg);
      }
      &-btn {
        width: 90%;
        max-height: 100%;
        @include flex-item(_, flex-start, center, nowrap, 5px);
        .option-text-container {
          width: 100%;
          @include flex-item(_, flex-start, center, _, 5px);
          .option-inp-text {
            min-width: 55%;
            max-width: 55%;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
        &-input {
          @include font-size($font-size-sm);
          outline: none;
          border: none;
          color: $black;
        }
      }
    }

    &-content {
      width: 100%;
      max-height: 250px;
      overflow-y: scroll;
      @include hide-scrollbar();
      position: absolute;
      z-index: 1;
      text-align: left;
      left: 0;
      background: $white;
      border: 1px solid $primary-border;
      border-top: none;
      border-radius: 0 0 5px 5px;
      @include flex-item(column, flex-start, center, nowrap);
      color: $title-color;
      &-item {
        width: 100%;
        @include flex-item(_, flex-start, center, _, 8px);
        @include padding(6.5px 8px);
        transition: all 0.2s;
        text-transform: capitalize;
        border-top: 1px solid $primary-border;
        cursor: pointer;
        .checkbox {
          width: 18px;
          height: 18px;
          .checkmarks {
            background-color: none;
          }
        }
        span {
          flex: 1;
          max-width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          @include font-size($font-size-sm);
        }
        &:hover {
          background: $secondary;
        }
      }
      .options-btn-header {
        @include flex-item(_, space-between, center);
        background-color: $primary-bg;
        border-bottom: 1px solid $primary-border;
        width: 100%;
        position: sticky;
        top: 0;
        z-index: 2;
        button {
          color: $primary;
          @include padding(6px 8px);
          @include font-size($font-size-xxsm);
          font-weight: $font-weight-semi-bold;
          cursor: pointer;
        }
        button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
      .options-footer {
        @include flex-item(_, space-between, center);
        background-color: $primary-bg;
        border-top: 1px solid $primary-border;
        @include padding(6px 8px);
        width: 100%;
        position: sticky;
        bottom: 0;
        z-index: 2;
        span {
          @include font-size($font-size-xxsm);
          font-weight: $font-weight-semi-bold;
          color: $label-color;
        }
        .footer-btn {
          @include flex-item(_, center, center, _, 8px);
          .cancel-btn {
            color: $primary;
            @include font-size($font-size-xxsm);
            font-weight: $font-weight-semi-bold;
            cursor: pointer;
          }
          .apply-btn {
            color: $white;
            background-color: $tertiary-light;
            @include font-size($font-size-sm);
            border-radius: 5px;
            @include padding(4.25px 21px 5.75px 21px);
            cursor: pointer;
            &:disabled {
              opacity: 0.6;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}
.multi-select-container.bottom-top {
  .select-field-container.open {
    border-radius: 0 0 5px 5px;
  }
  .select-field-content.bottom-top {
    top: auto;
    width: 99.8%;
    bottom: 100%;
    @include margin-top(0px);
    border-radius: 5px 5px 0 0;
  }
  .options-btn-header {
    border-top: 1px solid $primary-border;
    border-bottom: none;
  }
}

.multi-select-container.disabled {
  opacity: 0.7;
  pointer-events: none;
}
