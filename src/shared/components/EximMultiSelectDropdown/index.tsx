import {DropdownOptionType} from '@common/interfaces';
import useClickOutSide from '@hooks/useOutSideClick';
import {SolidDownAngle} from '@shared/icons';
import React, {
  ChangeEvent,
  ReactNode,
  memo,
  useCallback,
  useEffect,
  useState,
} from 'react';

import EximCheckbox from '../EximCheckbox';
import './index.scss';

export interface ISelectFieldProps {
  /** checkBoxes are required or not */
  isCheckBoxes?: boolean;
  /** Search is required or not */
  isSearch?: boolean;
  /** disabled dropdown */
  disabled?: boolean;
  /** options */
  options: DropdownOptionType[];
  /** Multiple select or not */
  isMulti?: boolean;
  /** placeholder for the input */
  placeholder?: string;
  /** placeholder for the input when dropdown open */
  searchPlaceholder?: string;
  /** Onselect handle tracking the selected options */
  onSelect: (data: DropdownOptionType[]) => void;
  /** dropdown height */
  maxMenuHeight?: number;
  /** dropdown option height */
  eachOptionHeight?: number;
  /** checkbox sizes */
  checkBoxSize?: 'small' | 'medium' | 'large';
  /** customize icon */
  downIcon?: ReactNode;
}

function EximMultiSelectDropdown(props: ISelectFieldProps) {
  const {
    options,
    isSearch,
    disabled,
    isMulti,
    placeholder,
    searchPlaceholder,
    onSelect,
    maxMenuHeight,
    eachOptionHeight,
    isCheckBoxes,
    checkBoxSize,
    downIcon,
  } = props;

  const [selectedOptions, setSelectedOptions] = useState<DropdownOptionType[]>(
    []
  );
  const [selectedText, setSelectedText] = useState<string>('');
  const [allOptions, setAllOptions] = useState<DropdownOptionType[]>(options);
  const [renderOptions, setRenderOptions] =
    useState<DropdownOptionType[]>(options);
  const [isActive, setIsActive] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [isDropUp, setIsDropUp] = useState<boolean>(false);

  // outside click do dropdown are closed
  const domNode = useClickOutSide(() => setIsActive(false));

  const handleActive = useCallback(() => {
    setIsActive((prev) => !prev);
    if (!isActive) {
      setSelectedText('');
    }
  }, [isActive]);

  const handleDropDown = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation();
      setIsActive((prev) => !prev);
      if (!isActive) {
        setSelectedText('');
      }
    },
    [isActive]
  );

  // Clearing all selected options
  const handleClearAll = useCallback(() => {
    setSelectedOptions([]);
    setAllOptions(options);
    setRenderOptions(options);
    setSelectedText('');
  }, [options]);

  // Selecting all options
  const handleSelectAll = useCallback(() => {
    const newData = renderOptions.map((el) => {
      return {...el, isChecked: true};
    });
    setRenderOptions(newData);
    setAllOptions(newData);
    setSelectedOptions(newData);
  }, [renderOptions]);

  // filter option after click on Apply button
  const handleApply = () => {
    const filterCheckbox = selectedOptions.map((opt) => {
      const obj = {...opt};
      delete obj.isChecked;
      return obj;
    });
    onSelect(filterCheckbox);
    setIsActive(false); // Closing dropdown
    const textString = selectedOptions.map((item) => item.title).join(' ');
    setSelectedText(textString);
  };

  // select options and move to Data field  based on isMulti or not
  const handleSelect = useCallback(
    (option: DropdownOptionType) => {
      if (isMulti) {
        const newList = allOptions.map((opt) =>
          opt.id === option.id ? {...opt, isChecked: !option.isChecked} : opt
        );
        setRenderOptions(newList);
        setAllOptions(newList);
        const selected = newList.filter((el) => el.isChecked);
        setSelectedOptions(selected);
      } else {
        const newList = allOptions.map((opt) =>
          opt.id === option.id
            ? {...opt, isChecked: true}
            : {...opt, isChecked: false}
        );
        setRenderOptions(newList);
        setAllOptions(newList);
        const newObj = {...option};
        delete newObj.isChecked;
        onSelect([newObj]);
        setSelectedText(newObj.title);
        setIsActive(false); // Closing the dropdown
      }
      setSearchValue('');
    },

    [isMulti, allOptions, onSelect]
  );

  // useEffect handle the filter options part based on Search box;
  useEffect(() => {
    if (isSearch) {
      const searchData = allOptions.filter((option) =>
        option.title.toLowerCase().includes(searchValue.toLowerCase().trim())
      );
      if (searchValue === '') {
        setRenderOptions(allOptions);
      } else {
        setRenderOptions(searchData);
      }
    }
  }, [allOptions, isSearch, searchValue]);

  // Set the initial options
  useEffect(() => {
    setAllOptions(options);
    setRenderOptions(options);
  }, [options]);

  // useEffect handle dropUp behavior of select title
  useEffect(() => {
    const determineDropUp = () => {
      const options1 = options;
      const node = domNode.current;
      const windowHeight = window.innerHeight;
      const menuHeight = Math.min(
        maxMenuHeight as number,
        options1.length * (eachOptionHeight as number)
      );
      const instOffsetWithMenu =
        node && node.getBoundingClientRect().bottom + menuHeight;
      setIsDropUp(instOffsetWithMenu >= windowHeight);
    };
    window.addEventListener('resize', determineDropUp);
    window.addEventListener('scroll', determineDropUp);
    return () => {
      window.removeEventListener('resize', determineDropUp);
      window.removeEventListener('scroll', determineDropUp);
    };
  }, [domNode, eachOptionHeight, maxMenuHeight, options]);

  return (
    <div
      className={`multi-select-container ${isDropUp ? 'bottom-top' : ''} ${
        disabled ? 'disabled' : ''
      }`}>
      <div className='select-field' ref={domNode}>
        <div
          className={`select-field-container ${isActive && 'open'}`}
          data-testid='select-container'
          role='button'
          tabIndex={0}
          onClick={handleActive}
          onKeyDown={handleActive}>
          <div className='select-field-container-btn'>
            {selectedText ? (
              <div className='option-text-container'>
                <p className='option-inp-text'>{selectedText}</p>
                {isMulti && selectedOptions.length - 1 !== 0 ? (
                  <span>{` + ${selectedOptions.length - 1} selected`}</span>
                ) : null}
              </div>
            ) : null}
            {isSearch && !selectedText && (
              <input
                className='select-field-container-btn-input'
                data-testid='select-searchbox'
                type='text'
                value={searchValue}
                placeholder={
                  isActive && searchPlaceholder
                    ? searchPlaceholder
                    : placeholder
                }
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setSearchValue(e.target.value)
                }
              />
            )}
          </div>
          <span
            role='presentation'
            onClick={handleDropDown}
            className={`down-icon ${isActive && 'up-icon'}`}>
            {downIcon}
          </span>
        </div>
        {isActive && renderOptions.length > 0 && (
          <div
            className={`select-field-content ${isDropUp && 'bottom-top'}`}
            data-testid='options'>
            {isMulti ? (
              <div className='options-btn-header'>
                <button
                  type='button'
                  onClick={handleSelectAll}
                  disabled={renderOptions?.length === selectedOptions?.length}>
                  SELECT ALL
                </button>
                <button
                  type='button'
                  data-testid='clear'
                  onClick={handleClearAll}
                  disabled={selectedOptions?.length === 0}>
                  CLEAR ALL
                </button>
              </div>
            ) : null}
            {renderOptions.map((option) => (
              <div key={option.id} className='select-field-content-item'>
                {isCheckBoxes && (
                  <div className='checkbox' data-testid='item-checkbox'>
                    <EximCheckbox
                      id={option.id.toString()}
                      name='checkbox'
                      onChange={() => handleSelect(option)}
                      checked={option.isChecked}
                      color='#2CB544'
                      size={checkBoxSize}
                    />
                  </div>
                )}
                <span
                  role='presentation'
                  data-testid='field'
                  onClick={() => handleSelect(option)}>
                  {option.title}
                </span>
              </div>
            ))}
            {isMulti ? (
              <div className='options-footer'>
                <span>{`${
                  allOptions?.filter((item) => item.isChecked).length
                } of ${allOptions.length}`}</span>
                <div className='footer-btn'>
                  <button
                    type='button'
                    className='cancel-btn'
                    onClick={() => {
                      setIsActive(false);
                      handleClearAll(); // Reset the selection
                    }}>
                    CANCEL
                  </button>
                  <button
                    type='button'
                    className='apply-btn'
                    disabled={selectedOptions.length === 0}
                    onClick={handleApply}>
                    Apply
                  </button>
                </div>
              </div>
            ) : null}
          </div>
        )}
      </div>
    </div>
  );
}

EximMultiSelectDropdown.defaultProps = {
  isSearch: true,
  isMulti: true,
  disabled: false,
  placeholder: 'Search',
  searchPlaceholder: null,
  maxMenuHeight: 250,
  eachOptionHeight: 32,
  isCheckBoxes: true,
  checkBoxSize: 'medium',
  downIcon: <SolidDownAngle width={10} height={6.5} />,
};

export default memo(EximMultiSelectDropdown);
