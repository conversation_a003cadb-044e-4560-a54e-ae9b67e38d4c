@import '@utils/main.scss';

.base-date-picker {
  width: max-content;
  @include flex-item(_, _, center, _, _);
  position: relative;
  border-radius: 5px;
  .left {
    width: 100%;
  }

  .calendar-btn {
    background: $tertiary;
    border-radius: 5px 0px 0px 5px;
    @include padding(8px 12px 6px);
    border: none;
    cursor: pointer;
  }

  .date-input {
    width: 100%;
    @include padding(7px 0 7px 16px);
    border-radius: 0px 5px 5px 0px;
    outline: none;
    border: 1px solid $primary-border;
    font-size: 14px;
    background-color: $primary-border-light;
  }

  .datepicker-custom-header {
    @include flex-item(_, space-between, center, _, 5px);
    @include padding(4px 11px);
    .left-arrow {
      transform: rotate(180deg);
    }
    .left-arrow,
    .right-arrow {
      cursor: pointer;
      @include flex-item(_, center, center);
      &:disabled {
        opacity: 0.5;
        cursor: default;
      }
    }

    & > :nth-child(2) {
      width: 70px !important;
    }
    & > :nth-child(3) {
      width: 105px !important;
    }

    .select-dropdown {
      .custom-dropdown {
        height: 24px;
        .item-holder {
          max-height: 224px;
          overflow-y: scroll;
          @include hide-scrollbar();

          .dropdown-item {
            height: 24px;
          }
        }
      }
    }
    select {
      @include padding(0 4px);
      outline: none;
      border-radius: 4px;
      background-color: none;
    }
  }

  // Below classes coming from the react-datepicker
  .react-datepicker__header {
    background-color: $white;
  }
  .react-datepicker {
    box-shadow: 0 5px 10px $modal-box-shadow;
    border: none;

    .react-datepicker__navigation-icon::before {
      border-color: $tertiary;
    }
    button:hover {
      .react-datepicker__navigation-icon::before {
        border-color: $white;
      }
    }
  }

  .react-datepicker__month- {
    &wrapper div {
      padding: 8px 0;
      font-size: 14px;

      &:hover {
        background-color: $tertiary;
        color: $white;
      }
    }

    &text--keyboard-selected {
      background: $tertiary;
      color: $white;
    }
  }
  .react-datepicker__day--disabled:hover {
    background-color: inherit;
  }

  .react-datepicker__ {
    &week > div:not(.react-datepicker__day--disabled) {
      @include padding(0 6px);

      &:hover {
        background: $tertiary;
        color: $white;
      }
    }

    &day--keyboard-selected,
    &day--selected {
      background: $tertiary;
      color: $white;
    }
  }

  .react-datepicker__triangle {
    transform: translate(0px, 0px) !important;
    &::before {
      bottom: 0;
      border-bottom-color: $input-border !important;
    }
    &::after {
      border-bottom-color: $white !important;
    }
  }

  .left {
    .react-datepicker__triangle {
      left: 20px !important;
    }
  }

  .center {
    .react-datepicker__triangle {
      left: 122px !important;
    }
  }

  .right {
    .react-datepicker__triangle {
      left: 220px !important;
    }
  }

  .react-datepicker__navigation {
    &:hover {
      background: $tertiary;
      color: $white;
      border-radius: 4px;
    }
  }

  .react-datepicker-popper {
    transform: translate(0px, 34px) !important;
  }

  .react-datepicker__month-container {
    min-width: 241px;
    font-size: 14px;
  }

  .react-datepicker__month-select,
  .react-datepicker__year-select {
    outline: none;
    padding: 1px 2px;
    border-radius: 4px;
    background: none;
    margin-top: 5px;
  }
  .date-error-message {
    position: absolute;
    top: 35px;
    font-size: $font-size-sm;
    color: $error;
  }
}
.base-date-picker.disabled-date-picker {
  pointer-events: none;
  opacity: 0.6;
}
.base-date-picker.invalid-date {
  .calendar-btn {
    background: $error;
  }
  .react-datepicker-wrapper {
    input {
      border: 1px solid $error;
      border-left: none;
    }
  }
}
