import {Calendar, SolidRightTriangle} from '@shared/icons';
import {format} from 'date-fns';
import {
  MouseEvent,
  ReactNode,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import EximCustomDropdown from '../EximCustomDropdown';
import './index.scss';

export interface DatePickerProps {
  /* To define id of the date picker */
  id?: string;
  /* It defines the disable of the date picker */
  disabled?: boolean;
  /* It defines the validation of the date */
  isInvalid?: boolean;
  /* It defines the calender should close or not after date select */
  shouldCloseOnSelect?: boolean;
  /* It defines the readOnly of the date picker */
  readOnly?: boolean;
  /* It defines the default value of the date picker */
  defaultValue?: string;
  /* It defines the default value of the date picker */
  calendarIcon?: ReactNode;
  /* It defines the start date of the date picker */
  minDate: string;
  /* It defines the end date of the date picker */
  maxDate?: string;
  /* To define type of the date picker */
  calendarType?: 'monthCalendar' | 'dateCalendar';
  /* It defines to set selected date of the date picker */
  onChange: (value: string) => void;
  /* It defines to the triangle icon position of the date picker */
  trianglePosition?: 'left' | 'center' | 'right';
  /* It defines error message of the date picker */
  errorMessage?: string;
}

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
const monthOptions = months.map((month, index) => ({
  id: index,
  label: month,
  value: month.toLowerCase(),
}));

const invertDayAndMonth = (date: string) => {
  const [day, month, year] = date.split('/');
  return `${month}/${day}/${year}`;
};

function EximDatePicker(props: DatePickerProps) {
  const {
    id,
    minDate,
    maxDate,
    disabled,
    isInvalid,
    readOnly,
    onChange,
    defaultValue,
    calendarType,
    calendarIcon,
    shouldCloseOnSelect,
    trianglePosition,
    errorMessage,
  } = props;

  const currentDate = new Date().getDate();
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();

  const [, defaultMonth, defaultYear] = defaultValue
    ?.split('/')
    .map(Number) || [currentDate, currentMonth, currentYear];

  const [currDate, setCurrDate] = useState<Date>();
  const [selectDate, setSelectDate] = useState<string>('');
  const [fromDay, fromMonth, fromYear] = minDate.split('/').map(Number); // splitting min date
  const [toDay, toMonth, toYear] = maxDate?.split('/').map(Number) || [
    currentDate,
    currentMonth,
    currentYear,
  ]; // splitting max date
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const datePickerRef = useRef<HTMLDivElement | null>(null);

  // INFO: Creating an array in range of start and end year to show in the dropdown
  const years = Array.from({length: toYear - fromYear + 1}, (_, index) =>
    (fromYear + index).toString()
  );
  const yearOptions = years.map((year, index) => ({
    id: index + 1,
    label: year,
    value: year,
  }));

  const handleChange = (day: Date) => {
    if (disabled) return;
    setCurrDate(day);
    const formattedDate = format(day, 'dd/MM/yyyy');
    if (calendarType === 'dateCalendar') {
      setSelectDate(formattedDate);
    } else {
      const year = new Intl.DateTimeFormat('en', {year: 'numeric'}).format(day);
      const month = new Intl.DateTimeFormat('en', {month: 'short'}).format(day);
      setSelectDate(`${month}-${year}`);
    }
    onChange(formattedDate);
  };

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        !datePickerRef?.current?.contains(event.target as Node)
      ) {
        setIsCalendarOpen(false);
      }
    },
    [datePickerRef]
  );

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    document.addEventListener('mousedown', handleClickOutside as any);
    return () => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      document.removeEventListener('mousedown', handleClickOutside as any);
    };
  }, [handleClickOutside]);

  const handleOpenCalendar = () => {
    if (disabled) return;
    setIsCalendarOpen((prev) => !prev);
  };

  useEffect(() => {
    if (defaultValue && defaultValue?.length > 4) {
      setCurrDate(new Date(invertDayAndMonth(defaultValue)));
    } else if (defaultValue && defaultValue?.length < 4) {
      setCurrDate(undefined);
    }
    if (
      defaultValue &&
      defaultValue?.length > 4 &&
      calendarType === 'monthCalendar'
    ) {
      const [day, month, year] = defaultValue.split('/').map(Number);
      setCurrDate(new Date(year, month - 1, day));
      const monthName = months[month - 1];
      setSelectDate(`${monthName.slice(0, 3)}-${year}`);
    }
  }, [defaultValue, calendarType]);

  return (
    <div
      id={id}
      role='presentation'
      onClick={(e) => handleClickOutside(e)}
      ref={datePickerRef}
      className={`base-date-picker ${disabled ? 'disabled-date-picker' : ''} ${
        isInvalid ? 'invalid-date' : ''
      }`}
      data-testid='date-picker'>
      <button
        type='button'
        className='calendar-btn'
        data-testid='calendar-icon-btn'
        onClick={handleOpenCalendar}>
        {calendarIcon}
      </button>
      {calendarType === 'dateCalendar' ? (
        <div className={`${trianglePosition}`} data-testid='date-calendar'>
          <DatePicker
            renderCustomHeader={({
              date,
              changeYear,
              changeMonth,
              decreaseMonth,
              increaseMonth,
              prevMonthButtonDisabled,
              nextMonthButtonDisabled,
            }) => {
              const selectedMonth = date.getMonth() + 1;
              const selectedYear = date.getFullYear();

              return (
                <div className='datepicker-custom-header'>
                  <button
                    type='button'
                    className='left-arrow'
                    onClick={decreaseMonth}
                    disabled={prevMonthButtonDisabled}>
                    <SolidRightTriangle height={20} width={18} />
                  </button>
                  <EximCustomDropdown
                    optionsList={yearOptions}
                    onSelect={(item) => changeYear(Number(item.value))}
                    defaultOption={
                      years.indexOf(selectedYear.toString()) + 1 ||
                      years.indexOf(defaultYear.toString()) + 1
                    }
                    isInvalid={defaultYear > currentYear}
                  />
                  <EximCustomDropdown
                    optionsList={monthOptions}
                    onSelect={(item) => changeMonth(Number(item.id))}
                    defaultOption={selectedMonth || defaultMonth}
                  />
                  <button
                    type='button'
                    className='right-arrow'
                    onClick={increaseMonth}
                    disabled={nextMonthButtonDisabled}>
                    <SolidRightTriangle height={20} width={18} />
                  </button>
                </div>
              );
            }}
            open={isCalendarOpen}
            onInputClick={handleOpenCalendar}
            onSelect={() => setIsCalendarOpen(false)}
            selected={currDate}
            onChange={handleChange}
            placeholderText='DD/MM/YYYY'
            value={selectDate || defaultValue || '-'}
            dateFormat='dd/MM/yyy'
            className='date-input'
            readOnly={readOnly}
            scrollableYearDropdown
            dropdownMode='select'
            shouldCloseOnSelect={shouldCloseOnSelect}
            minDate={new Date(fromYear, fromMonth - 1, fromDay)}
            maxDate={new Date(toYear, toMonth - 1, toDay)}
            onKeyDown={(event) => event.preventDefault()}
            fixedHeight
            closeOnScroll
          />
        </div>
      ) : (
        <div className={`${trianglePosition}`} data-testid='month-calendar'>
          <DatePicker
            open={isCalendarOpen}
            onInputClick={handleOpenCalendar}
            onSelect={() => setIsCalendarOpen(false)}
            selected={currDate}
            onChange={handleChange}
            placeholderText='MMM-YYYY'
            value={selectDate || defaultValue}
            dateFormat='yyyy'
            showMonthYearPicker
            className='date-input'
            readOnly={readOnly}
            shouldCloseOnSelect={shouldCloseOnSelect}
            minDate={new Date(fromYear, fromMonth - 1)}
            maxDate={new Date(toYear, toMonth - 1)}
            onKeyDown={(event) => event.preventDefault()}
            fixedHeight
            closeOnScroll
          />
        </div>
      )}

      {isInvalid && errorMessage ? (
        <p className='date-error-message'>{errorMessage}</p>
      ) : null}
    </div>
  );
}

EximDatePicker.defaultProps = {
  id: '1',
  disabled: false,
  readOnly: false,
  maxDate: null,
  isInvalid: false,
  defaultValue: '',
  shouldCloseOnSelect: true,
  calendarType: 'dateCalendar',
  calendarIcon: <Calendar />,
  trianglePosition: 'left',
  errorMessage: '',
};

export default EximDatePicker;
