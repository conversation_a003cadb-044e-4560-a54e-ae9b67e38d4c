import {fireEvent, render, screen} from '@testing-library/react';

import EximDatePicker from './index';

describe('EximDatePicker Component', () => {
  const onChange = jest.fn();
  it('Should render default EximDatePicker', () => {
    render(
      <EximDatePicker
        id='1'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        onChange={onChange}
      />
    );
    const datePicker = screen.getByTestId('date-picker');
    expect(datePicker).toBeInTheDocument();
  });

  it('Should render month EximDatePicker', () => {
    render(
      <EximDatePicker
        id='2'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        onChange={onChange}
        calendarType='monthCalendar'
      />
    );
    const monthCalendar = screen.getByTestId('month-calendar');
    expect(monthCalendar).toBeInTheDocument();
  });

  it('Should render EximDatePicker and function called with value in dateCalendar', () => {
    render(
      <EximDatePicker
        id='1'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        onChange={onChange}
      />
    );
    const dateInput = screen.getByPlaceholderText('DD/MM/YYYY');
    fireEvent.click(dateInput);
    const date = document.getElementsByClassName('react-EximDatePicker__week');
    fireEvent.click(date[0].children[5]);
    expect(dateInput).toHaveValue('11/4/2022');
  });

  it('Should render EximDatePicker and function called with value in monthCalendar', () => {
    render(
      <EximDatePicker
        id='1'
        minDate='12/2/2022'
        maxDate='12/11/2022'
        onChange={onChange}
        calendarType='monthCalendar'
      />
    );
    const dateInput = screen.getByPlaceholderText('MMM-YYYY');
    fireEvent.click(dateInput);
    const date = screen.getAllByRole('option');
    fireEvent.click(date[2]);
    expect(dateInput).toHaveValue('Mar-2022');
  });

  it('Should render default EximDatePicker and keydown function called', () => {
    render(
      <EximDatePicker
        id='1'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        onChange={onChange}
      />
    );
    const dateInput = screen.getByPlaceholderText('DD/MM/YYYY');
    fireEvent.keyDown(dateInput);
    expect(dateInput).toHaveValue('');
  });

  it('Should render monthCalendar EximDatePicker and keydown function called', () => {
    render(
      <EximDatePicker
        id='1'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        calendarType='monthCalendar'
        onChange={onChange}
      />
    );
    const dateInput = screen.getByPlaceholderText('MMM-YYYY');
    fireEvent.keyDown(dateInput);
    expect(dateInput).toHaveValue('');
  });
  it('Should render monthCalendar EximDatePicker with onClick of the calendar icon', () => {
    const myFunctionMock = jest.fn();
    render(
      <EximDatePicker
        id='1'
        minDate='12/08/2022'
        maxDate='12/11/2022'
        calendarType='monthCalendar'
        onChange={onChange}
      />
    );
    const calenderIcon = screen.getByTestId('calendar-icon-btn');
    fireEvent.click(calenderIcon);
    myFunctionMock();
    fireEvent.click(calenderIcon);
    myFunctionMock();
    expect(myFunctionMock).toHaveBeenCalledTimes(2);
  });
});
