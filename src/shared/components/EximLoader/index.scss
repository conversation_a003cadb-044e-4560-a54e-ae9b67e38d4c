@import '@utils/main.scss';
.loader-circle {
  border: 5px solid $white;
  border-radius: 50%;
  border-top: 5px solid $loader-border;
  width: 108px;
  height: 108px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
  box-shadow: 0px 3px 6px $btn-box-shadow-hover;
}

.loader-exim-img {
  position: absolute;
  top: 4px;
  left: 5px;
}

.wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loader-wrapper {
  position: absolute;
  img {
    position: relative;
    z-index: 1001;
    border-radius: 100px;
  }
  .main-loader-wrapper {
    .gif {
      width: 120px;
      height: 120px;
    }
    .loader-content {
      position: absolute;
      top: 108%;
    }
  }
}
