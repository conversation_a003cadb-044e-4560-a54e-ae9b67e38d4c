import OopsIcon from '@shared/icons/OopsIcon';
import Empty from '@submodules/Empty/Empty';
import React, {ReactNode} from 'react';

import './index.scss';

interface EximEmptyTableProps {
  content?: ReactNode;
  emptyIcon?: ReactNode;
}

function EximEmptyTable(props: EximEmptyTableProps) {
  const {content, emptyIcon} = props;
  return <Empty content={content} emptyIcon={emptyIcon} />;
}

EximEmptyTable.defaultProps = {
  content: 'No data available in table',
  emptyIcon: <OopsIcon />,
};

export default React.memo(EximEmptyTable);
