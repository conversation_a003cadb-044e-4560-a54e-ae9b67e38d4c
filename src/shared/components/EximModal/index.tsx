import Modal from '@submodules/Modal/Modal';
import {ReactNode} from 'react';

import {CloseIcon} from '../../icons';
import './index.scss';

interface IEximModalProps {
  isOpen: boolean;
  content?: ReactNode;
  onClose: () => void;
  isCloseIconVisible?: boolean;
  onOutSideClickClose: () => void;
  header?: ReactNode;
  footer?: ReactNode;
  closeIcon?: ReactNode;
}
function EximModal(props: IEximModalProps) {
  const {
    content,
    header,
    footer,
    isCloseIconVisible,
    onOutSideClickClose,
    onClose,
    isOpen,
    closeIcon,
  } = props;

  return (
    <div className='modalWrapper'>
      <Modal
        content={content}
        header={header}
        footer={footer}
        isCloseIconVisible={isCloseIconVisible}
        onOutSideClickClose={onOutSideClickClose}
        onClose={onClose}
        isOpen={isOpen}
        closeIcon={closeIcon}
      />
    </div>
  );
}

export default EximModal;

EximModal.defaultProps = {
  content: 'Content',
  isCloseIconVisible: true,
  header: 'Title',
  footer: 'footer section',
  closeIcon: <CloseIcon />,
};
