import '@testing-library/jest-dom';
import {render} from '@testing-library/react';

import EximModal from '.';

describe('Test EximModal', () => {
  it('Renders without error', () => {
    render(
      <EximModal
        isOpen
        isCloseIconVisible
        content='content'
        header='header'
        footer='footer'
        closeIcon='X'
        onClose={jest.fn()}
        onOutSideClickClose={jest.fn()}
      />
    );
    expect(document.querySelector('.modalWrapper')).toBeInTheDocument();
  });
});
