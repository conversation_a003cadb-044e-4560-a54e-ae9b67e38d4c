import Radiobutton, {Props} from '@submodules/Radiobutton/Radiobutton';

export default function EximRadioButton({
  label,
  name,
  onChange,
  value,
  isSelected,
  id,
  color,
  style,
  disabled,
  size,
  dataTestId,
}: Props) {
  return (
    <div className='radio-wrapper'>
      <Radiobutton
        label={label}
        id={id}
        onChange={onChange}
        value={value}
        isSelected={isSelected}
        color={color}
        name={name}
        dataTestId={dataTestId}
        disabled={disabled}
        size={size}
        style={style}
      />
    </div>
  );
}
