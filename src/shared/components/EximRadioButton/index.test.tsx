import {fireEvent, render, screen} from '@testing-library/react';

import EximRadioButton from './index';

test('Should render radio Button', () => {
  const isSelected = true;
  const onChange = jest.fn();
  render(
    <EximRadioButton id='1' isSelected={isSelected} onChange={onChange} />
  );
  const {getByRole} = screen;
  const radio = getByRole('radio');
  fireEvent.click(radio);
  expect(screen.getByTestId('radioinput')).toBeInTheDocument();
});
