@import '@utils/main.scss';

/* checkbox overwritten scss */
.checkbox-container {
  .checkmarks {
    @include rfs('5px', border-radius);
    border: 1px solid $primary-border;
    background: $white;

    &::after {
      left: 6px;
      top: 2px;
      width: 2.5px;
      height: 6.5px;
      border-width: 0px 2px 2px 0;
    }
  }
}

/* select-field overwritten css */
.select-field {
  .select-field-container {
    border: 1px solid $primary-border;
    background: $white;

    &:hover {
      border: 1px solid $information;
    }

    &-btn {
      .label {
        font-weight: 600;
      }

      .single-filter {
        @include margin-left(5px);

        .filtered-select {
          font-weight: 600;
        }
      }
    }
  }

  .focus-input {
    border-radius: 0;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;

    &:hover {
      border: 1px solid $primary-border;
    }
  }
}

.select-field-content {
  max-height: 250px;
  overflow-y: hidden;
  border: 1px solid $primary-border;
  background: $white;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  gap: 0px;

  .item-bg {
    background: $white;
  }

  .select-field-content-item {
    background: $white;
    padding: 0;
    @include padding-left(11px);

    button {
      @include padding(10px);
      font-weight: 600;
    }

    &:hover {
      background: $primary-border-light;
    }
  }
}

.select-field-wrapper {
  width: 280px;
  .select-field {
    .select-field-container {
      font-size: $font-size-sm;
      box-shadow: none;
      min-height: 32px;
      .end-clear-box {
        button {
          svg {
            width: 10px;
            height: 6px;
          }
        }
      }
      .filtered-select {
        font-weight: $font-weight-regular;
      }
      .label {
        font-weight: $font-weight-regular;
      }
    }

    .select-field-content {
      .item-bg {
        background-color: $primary-border;

        &:hover {
          background: $primary-border;
        }
      }
      .select-field-content-item {
        padding: 0;
        button {
          padding: none;
          height: 32px;
          color: #262626;
          font-weight: $font-weight-regular;
          font-size: $font-size-sm;
        }
      }
    }
  }
}
