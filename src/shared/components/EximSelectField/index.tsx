import {SolidDownAngle} from '@shared/icons';
import SelectField, {
  ISelectFieldProps,
} from '@submodules/SelectField/SelectField';
import {memo} from 'react';

import './index.scss';

function EximSelectField(props: ISelectFieldProps) {
  const {
    options,
    onSelect,
    clearAllBtn,
    eachOptionHeight,
    maxMenuHeight,
    isCheckBoxes,
    isMulti,
    isSearch,
    placeholder,
    checkBoxSize,
  } = props;
  return (
    <div className='select-field-wrapper'>
      <SelectField
        options={options}
        onSelect={onSelect}
        clearAllBtn={clearAllBtn}
        eachOptionHeight={eachOptionHeight}
        maxMenuHeight={maxMenuHeight}
        isSearch={isSearch}
        isMulti={isMulti}
        isCheckBoxes={isCheckBoxes}
        label='Select'
        placeholder={placeholder}
        checkBoxCheckedColor='#2cb544'
        checkBoxSize={checkBoxSize}
        downIcon={<SolidDownAngle />}
      />
    </div>
  );
}

export default memo(EximSelectField);
