import {OptionType} from '@submodules/SelectField/SelectField';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import EximSelectField from '.';

const optionsArr: OptionType[] = [
  {id: 1, tag: 'Today'},
  {id: 2, tag: 'Weekly'},
  {id: 3, tag: 'Monthly'},
  {id: 4, tag: 'Quarterly'},
  {id: 5, tag: 'Custom'},
];

const onSelect = (data: OptionType[]) => {
  return data;
};

describe('Rendering part for SelectField component testing', () => {
  it('Should render the SelectField with default props', async () => {
    render(
      <EximSelectField
        options={optionsArr}
        onSelect={onSelect}
        label='Choose single'
        placeholder='Search'
      />
    );
    const container = screen.getByTestId('select-container');
    const label = screen.queryByText('Choose single');
    expect(container).toBeInTheDocument();
    userEvent.click(container);
    const upIcon = document.querySelectorAll('.up-icon');
    expect(upIcon).toHaveLength(1);
    userEvent.click(upIcon[0]);
    expect(label).not.toBeInTheDocument();
  });
});
