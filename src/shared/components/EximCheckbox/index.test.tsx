import {fireEvent, render, screen} from '@testing-library/react';

import EximCheckbox from '.';

test('Should render checkboxWrapper', () => {
  const onChange = jest.fn();
  render(
    <EximCheckbox
      id='1'
      onChange={onChange}
      style={{marginRight: '0'}}
      isInvalid
    />
  );
  const {getByRole} = screen;
  const checkbox = getByRole('checkbox');
  fireEvent.click(checkbox);
  expect(checkbox).toBeChecked();
  expect(onChange).toBeCalled();
});
