import Checkbox, {
  Props as checkboxInterface,
} from '@submodules/Checkbox/Checkbox';

import './index.scss';

interface Prop extends checkboxInterface {
  isInvalid?: boolean;
  disabled?: boolean;
}

export default function EximCheckbox({
  id,
  onChange,
  label,
  name,
  isInvalid,
  size,
  color,
  checked,
  disabled,
  dataTestId,
}: Prop) {
  return (
    <div className={`checkbox-wrapper ${isInvalid && 'Invalid'}`}>
      <Checkbox
        id={id}
        onChange={onChange}
        label={label}
        name={name}
        color={color}
        size={size}
        checked={checked}
        disabled={disabled}
        dataTestId={dataTestId}
      />
    </div>
  );
}

EximCheckbox.defaultProps = {
  isInvalid: false,
  disabled: false,
};
