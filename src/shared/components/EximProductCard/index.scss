@import '@utils/main.scss';

.product-card-wrapper {
  min-width: 243px;
  background-color: $white;
  box-shadow: $product-card-box-shadow;
  @include padding(15px);
  @include flex-item(row, flex-start, center, _, 20px);
  @include rfs(5px 5px 0 0, border-radius);

  .product-card-image {
    @include flex-item(row, center, center);
    img {
      width: 80px;
      height: 60px;
    }
    .empty-box {
      height: 55px;
      width: 55px;
      background-color: $tertiary;
    }
  }

  .product-card-content {
    .product-exim-title {
      @include margin-bottom(14px);
      font-weight: $font-weight-bold;
      font-size: $font-size-md;
      color: $text-color;
      text-align: left;
    }

    .product-exim-items {
      @include padding(1px);

      .product-exim-item-total {
        display: flex;
        font-size: $font-size-sm;
        p {
          font-size: $font-weight-regular;
          @include margin(0 5px 0 0);
          color: $label-color;
        }
        span {
          font-size: $font-size-sm;
          color: $label-color;
        }
      }

      .product-exim-item {
        display: flex;
        font-size: $font-size-sm;

        .product-exim-item-active {
          display: flex;

          p {
            @include margin(0 5px 0 0);
            color: $label-color;
          }

          span {
            font-size: $font-size-sm;
            color: $label-color;
          }
        }

        .divide-bar {
          color: $label-color;
          font-weight: 600;
          @include margin(0 5px);
        }

        .product-exim-item-inactive {
          display: flex;

          p {
            @include margin(0 5px 0 0);
            color: $label-color;
          }

          span {
            font-size: $font-size-sm;
            color: $label-color;
          }
        }
      }
    }
  }
}
