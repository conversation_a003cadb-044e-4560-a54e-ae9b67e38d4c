import {EximProducts} from '@common/constants';
import {memo} from 'react';

import './index.scss';

interface EximProductCardProps {
  icon: string;
  title: string;
  totalIEC: number;
  activeCount: number;
  inactiveCount: number;
}

function EximProductCard({
  icon,
  title,
  totalIEC,
  activeCount,
  inactiveCount,
}: EximProductCardProps) {
  return (
    <div className='product-card-wrapper'>
      <div className='product-card-image'>
        {icon ? <img src={icon} alt='logo' /> : <div className='empty-box' />}
      </div>
      <div className='product-card-content'>
        <div className='product-exim-title'>{title}</div>
        <div className='product-exim-items'>
          <div className='product-exim-item-total'>
            {title === EximProducts.DATA_EXTRACTOR ? (
              <p>Total PAN:</p>
            ) : (
              <p>Total IECs:</p>
            )}
            <span>{totalIEC}</span>
          </div>
          <div className='product-exim-item'>
            <div className='product-exim-item-active'>
              <p>Active:</p>
              <span>{activeCount}</span>
            </div>
            <div className='divide-bar'>|</div>
            <div className='product-exim-item-inactive'>
              <p>Inactive:</p>
              <span>{inactiveCount}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(EximProductCard);
