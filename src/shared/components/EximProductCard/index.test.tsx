import {render} from '@testing-library/react';

import EximProductCard from '.';

describe('EximProductCard component', () => {
  test('EximProductCard component should render', () => {
    render(
      <EximProductCard
        icon='https://qa.gsthero.com/GspModel/resources/support/images/gstr.svg'
        title='Duty Drawback'
        totalIEC={0}
        activeCount={0}
        inactiveCount={0}
      />
    );
    expect(document.querySelector('.product-card-wrapper')).toBeInTheDocument();
  });
});
