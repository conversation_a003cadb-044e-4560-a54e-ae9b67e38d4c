@import '@utils/main.scss';

// TODO: commented styles not is use, keeping it for future reference

.actionable-dropdown-container {
  width: max-content;
  z-index: 1;

  .dropdown-container {
    width: 100%;
    position: relative;

    .button-wrapper {
      min-width: 150px;
      max-width: 200px;
      height: 32px;
      // @include rfs(16px, border-radius);
      .base-btn {
        width: 100%;
        // @include rfs(16px, border-radius);
        color: $white;
        // background-color: $secondary-1;
        font-size: $font-size-sm;
        &:hover {
          box-shadow: none;
        }
        .btn-children {
          width: 100%;
          justify-content: space-between;
          svg {
            stroke: none;
          }
        }
      }
    }

    .gray-bg {
      @include rfs(16px, border-radius);
      .base-btn {
        @include rfs(16px, border-radius);
        // color: $secondary-1;
        // background-color: $body-bg-color;
        // &:hover {
        //   background-color: $body-bg-color;
        // }
      }
    }

    .open-dropdown {
      // @include rfs(16px 16px 0 0, border-radius);
      .base-btn {
        // @include rfs(16px 16px 0 0, border-radius);
        color: $white;
        background-color: $information-light;
        &:hover {
          background-color: $information-light;
        }
      }
    }

    .rotate-180 {
      transform: rotate(180deg);
    }
    .select-btn-container {
      width: 100%;
      height: max-content;
      border-top: none;
      border: 1px solid $accordion-border;
      position: absolute;
      top: 32px;
      z-index: 5;
      background: $white;
      // @include rfs(0 0 16px 16px, border-radius);
      @include flex-item(column);
      div.list-item {
        width: 100%;
        @include padding(7px 10px 7px 9px);
        font-size: $font-size-sm;
        // color: $secondary-1;
        cursor: pointer;
        &:hover {
          // background: $body-bg-color;
          font-weight: $font-weight-semi-bold;
          // &:last-child {
          //   @include rfs(0 0 16px 16px, border-radius);
          // }
        }
      }
      div.list-item.disabled {
        pointer-events: none;
        opacity: 0.6;
      }
      div.active-opt {
        // background: $body-bg-color;
        font-weight: $font-weight-semi-bold;
        // &:last-child {
        //   @include rfs(0 0 16px 16px, border-radius);
        // }
        .sub-list-item {
          background: $white;
          font-weight: normal;
          &:first-child,
          &:last-child {
            border-radius: inherit;
          }
        }
        .sub-list-item.disabled {
          pointer-events: none;
          opacity: 0.6;
        }
      }

      // INFO: Sub dropdown style default style for left
      .sub-dropdown {
        position: relative;
        .sub-dropdown-container {
          // @include rfs(16px 0 16px 16px, border-radius);
          @include flex-item(column, flex-start, flex-start);
          box-shadow: 0px 3px 6px $btn-box-shadow-hover;
          border: 1px solid $accordion-border;
          background: $white;
          z-index: 5;
          min-width: 150px;
          max-width: 200px;
          height: max-content;
          position: absolute;
          right: 100%;
          top: 0;
          div.sub-list-item {
            width: 100%;
            @include padding(7px 0px 7px 9px);
            font-size: $font-size-sm;
            // color: $secondary-1;
            cursor: pointer;
            &:hover {
              // background: $body-bg-color;
              font-weight: $font-weight-semi-bold;
              // &:first-child {
              //   @include rfs(16px 0 0 0, border-radius);
              // }
              // &:last-child {
              //   @include rfs(0 0 16px 16px, border-radius);
              // }
            }
          }
        }
        .sub-dropdown-container.right {
          left: 100%;
          // @include rfs(0 16px 16px 16px, border-radius);
          // div.sub-list-item {
          //   &:hover {
          //     @include rfs(0 0 0 0, border-radius);
          //     &:first-child {
          //       @include rfs(0 16px 0 0, border-radius);
          //     }
          //     &:last-child {
          //       @include rfs(0 0 16px 16px, border-radius);
          //     }
          //   }
          // }
        }
      }
    }
  }
}

.actionable-dropdown-container.bottom-top {
  // .open-dropdown {
  //   .base-btn {
  //     @include rfs(0 0 16px 16px, border-radius);
  //   }
  // }
  .select-btn-container {
    // @include rfs(16px 16px 0 0, border-radius);
    top: auto;
    bottom: 100%;
    div.list-item {
      &:hover {
        // background: $body-bg-color;
        font-weight: $font-weight-semi-bold;
        // &:first-child {
        //   @include rfs(16px 16px 0 0, border-radius);
        // }
        // &:last-child {
        //   @include rfs(0, border-radius);
        // }
      }
    }
    div.list-item.disabled {
      pointer-events: none;
      opacity: 0.6;
    }
  }
}
