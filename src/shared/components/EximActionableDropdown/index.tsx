import {IActionableDropdownData} from '@common/interfaces';
import useClickOutSide from '@hooks/useOutSideClick';
import EximButton from '@shared/components/EximButton';
import {DropdownIcon} from '@shared/icons';
import {useEffect, useState} from 'react';

import './index.scss';

interface IActionableDropdown {
  id: string;
  isDisabled?: boolean;
  isGrayBg?: boolean;
  maxMenuHeight?: number;
  eachOptionHeight?: number;
  position?: 'left' | 'right';
  placeholder: string;
  optionList: IActionableDropdownData[];
  onClick: (value: string) => void;
}

function EximActionableDropdown({
  id,
  placeholder,
  isDisabled,
  maxMenuHeight,
  eachOptionHeight,
  position,
  isGrayBg,
  optionList,
  onClick,
}: IActionableDropdown) {
  const [isOpen, setIsOpen] = useState(false);
  const [subDropdownId, setSubDropdownId] = useState('');
  const [isDropUp, setIsDropUp] = useState<boolean>(false);

  // outside click do dropdown are closed
  const dropdownRef = useClickOutSide(() => {
    setIsOpen(false);
    setSubDropdownId('');
  });

  // useEffect handle dropUp behavior of select title
  useEffect(() => {
    const determineDropUp = () => {
      const options1 = optionList;
      const node = dropdownRef.current;
      const windowHeight = window.innerHeight;
      const menuHeight = Math.min(
        maxMenuHeight as number,
        options1.length * (eachOptionHeight as number)
      );
      const instOffsetWithMenu =
        node && node.getBoundingClientRect().bottom + menuHeight;
      setIsDropUp(instOffsetWithMenu >= windowHeight);
    };
    window.addEventListener('resize', determineDropUp);
    window.addEventListener('scroll', determineDropUp);
    return () => {
      window.removeEventListener('resize', determineDropUp);
      window.removeEventListener('scroll', determineDropUp);
    };
  }, [dropdownRef, eachOptionHeight, maxMenuHeight, optionList]);

  return (
    <div
      id={id}
      className={`actionable-dropdown-container ${
        isDropUp ? 'bottom-top' : ''
      }`}>
      <div className='dropdown-container' ref={dropdownRef}>
        <EximButton
          size='small'
          color='tertiary'
          className={`${isOpen ? 'open-dropdown' : ''} ${
            isGrayBg ? 'gray-bg' : ''
          }`}
          disabled={isDisabled}
          onClick={() => setIsOpen((prev) => !prev)}>
          {placeholder}
          <span className={`${isOpen ? 'rotate-180' : ''} `}>
            <DropdownIcon fill={isGrayBg && !isOpen ? '#002662' : '#FFFFFF'} />
          </span>
        </EximButton>
        {isOpen ? (
          <div className='select-btn-container'>
            {optionList.map((item) => (
              <div
                className={`list-item ${
                  item.hasSubDropdown ? 'sub-dropdown' : ''
                } ${item.id === subDropdownId ? 'active-opt' : ''} ${
                  item.disabled ? 'disabled' : ''
                }`}
                key={item.value}
                role='presentation'
                onClick={() => {
                  if (item.hasSubDropdown) {
                    setSubDropdownId(item.id.toString());
                  } else {
                    onClick(item.value);
                    setSubDropdownId('');
                    setIsOpen(false);
                  }
                }}>
                {item.label}
                {item.hasSubDropdown && subDropdownId === item.id ? (
                  <div className={`sub-dropdown-container ${position}`}>
                    {item.subDropdownList?.map((subOpt) => (
                      <div
                        className={`sub-list-item ${
                          subOpt.disabled ? 'disabled' : ''
                        }`}
                        key={subOpt.value}
                        role='presentation'
                        onClick={() => {
                          onClick(subOpt.value);
                          setSubDropdownId('');
                        }}>
                        {subOpt.label}
                      </div>
                    ))}
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        ) : null}
      </div>
    </div>
  );
}

EximActionableDropdown.defaultProps = {
  isDisabled: false,
  isGrayBg: false,
  maxMenuHeight: 250,
  eachOptionHeight: 32,
  position: 'left',
};

export default EximActionableDropdown;
