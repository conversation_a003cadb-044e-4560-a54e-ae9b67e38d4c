import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';

import EximPagination from '.';

describe('Should render the pagination wrapper', () => {
  it('Renders without error', () => {
    const currentPage = {
      value: 1,
    };
    const setCurrentPage = (page: number) => {
      currentPage.value = page;
    };
    render(
      <EximPagination
        className=''
        currentPage={currentPage.value}
        totalItems={100}
        itemsPerPage={5}
        onPageChange={(page) => setCurrentPage(Number(page))}
        siblingCount={2}
      />
    );
    const paginationInput = screen.getByTestId('paginationInput');
    fireEvent.change(paginationInput, {target: {value: '3'}});
    expect(currentPage.value).toBe(3);
    expect(document.querySelector('.pagination-wrapper')).toBeInTheDocument();
  });
});
