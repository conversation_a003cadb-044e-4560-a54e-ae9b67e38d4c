import Pagination, {IPaginationProps} from '@submodules/Pagination/Pagination';
import {memo} from 'react';

import '../EximButton/index.scss';
import './index.scss';

function EximPagination(props: IPaginationProps) {
  const {
    totalItems,
    itemsPerPage,
    siblingCount,
    currentPage,
    className,
    circularBtn,
    btnVariant,
    btnColor,
    prevIcon,
    nextIcon,
    isInput,
    onPageChange,
  } = props;

  return (
    <div className='button-wrapper pagination-wrapper'>
      <Pagination
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        siblingCount={siblingCount}
        currentPage={currentPage}
        className={className}
        circularBtn={circularBtn}
        btnVariant={btnVariant}
        btnColor={btnColor}
        nextIcon={nextIcon}
        prevIcon={prevIcon}
        isInput={isInput}
        onPageChange={onPageChange}
      />
    </div>
  );
}

export default memo(EximPagination);

EximPagination.defaultProps = {
  totalItems: 100,
  itemsPerPage: 10,
  siblingCount: 1,
  currentPage: 1,
  className: 'pagination-bar',
  btnVariant: 'contained',
  btnColor: 'tertiary',
  isInput: true,
  nextIcon: 'Next',
  prevIcon: 'Previous',
};
