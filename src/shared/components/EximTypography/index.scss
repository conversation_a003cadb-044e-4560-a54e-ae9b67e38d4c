@import '@utils/main.scss';

.typography-wrapper {
  color: $text-color;
  width: 100%;
}

@mixin typography-font-weights($element, $element-font-size) {
  .typography-variant-#{$element} {
    @include font-size($element-font-size);
  }
  .typography-variant-#{$element}.regular {
    font-weight: 400;
  }
  .typography-variant-#{$element}.normal {
    font-weight: 500;
  }
  .typography-variant-#{$element}.semi-bold {
    font-weight: 600;
  }
  .typography-variant-#{$element}.bold {
    font-weight: 700;
  }
  .typography-variant-#{$element}.bolder {
    font-weight: 800;
  }
  .typography-variant-#{$element}.extra-bold {
    font-weight: 900;
  }
}

.typography-container {
  @include typography-font-weights(h1, $font-size-xxxl);
  @include typography-font-weights(h2, $font-size-xxl);
  @include typography-font-weights(h3, $font-size-lg);
  @include typography-font-weights(h4, $font-size-md);
  @include typography-font-weights(h5, $font-size-sm);
  @include typography-font-weights(h6, $font-size-xsm);
  @include typography-font-weights(body1, $font-size-sm);
  @include typography-font-weights(body2, $font-size-xxsm);

  @include typography-font-weights(caption, $font-size-xsm);
  @include typography-font-weights(button, $font-size-sm);
}
