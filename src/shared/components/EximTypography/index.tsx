import Typography, {TypographyProps} from '@submodules/Typography/Typography';
import {memo} from 'react';

import './index.scss';

interface EximTypographyProps extends TypographyProps {
  fontWeight?:
    | 'regular'
    | 'normal'
    | 'semi-bold'
    | 'bold'
    | 'extra-bold'
    | 'bolder';
}

function EximTypography(props: EximTypographyProps) {
  const {
    noWrap,
    gutterBottom,
    align,
    variant,
    children,
    classNames,
    fontWeight,
    dataTestId,
  } = props;

  const applyClasses = `${fontWeight} ${classNames}`;
  return (
    <div className={`typography-container ${classNames || ''}`}>
      <Typography
        noWrap={noWrap}
        gutterBottom={gutterBottom}
        align={align}
        variant={variant}
        dataTestId={dataTestId}
        classNames={applyClasses}>
        {children}
      </Typography>
    </div>
  );
}

EximTypography.defaultProps = {
  fontWeight: 'regular',
};

export default memo(EximTypography);
