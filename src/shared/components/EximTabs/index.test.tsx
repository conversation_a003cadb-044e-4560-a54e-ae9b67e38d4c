import {fireEvent, render, screen} from '@testing-library/react';
import {ReactNode} from 'react';

import EximTabs from '.';

const data = [
  {
    id: 1,
    tabName: 'Tab - 1',
    tabContent: <p> Hi i am Tab - 1</p>,
  },
  {
    id: 2,
    tabName: 'Tab - 2',
    tabContent: <p> Hi i am Tab - 2</p>,
  },
  {
    id: 3,
    tabName: 'Tab - 3',
    tabContent: <p> Hi i am Tab - 3</p>,
  },
];

const getActiveTabContent = (content: ReactNode) => {
  return content;
};

describe('Rendering part for Tabs component testing', () => {
  it('should render the Tabs with default props', () => {
    render(<EximTabs values={data} onChange={getActiveTabContent} />);

    const tabsContainer = screen.getByTestId('tabs-container');
    const tabsButtons = document.querySelectorAll('.tab-button');

    expect(tabsContainer).toBeInTheDocument();
    expect(tabsButtons).toHaveLength(3);

    fireEvent.click(tabsButtons[0]); // tab1 click then
    expect(screen.getByTestId(1)).toBeInTheDocument(); // tab-1 content shown
    expect(screen.queryByTestId(2)).not.toBeInTheDocument(); // not tab-2 content
  });
});
