import Tabs, {ITabsProps} from '@submodules/Tabs/Tabs';
import {memo} from 'react';

import './index.scss';

function EximTabs(props: ITabsProps) {
  const {
    values,
    onChange,
    activeTab,
    activeTabColor,
    border,
    centered,
    iconPosition,
    inactiveTab,
    vertical,
  } = props;
  return (
    <div className='tabs-wrapper'>
      <Tabs
        values={values}
        onChange={onChange}
        activeTab={activeTab}
        border={border}
        activeTabColor={activeTabColor}
        centered={centered}
        iconPosition={iconPosition}
        inactiveTab={inactiveTab}
        vertical={vertical}
      />
    </div>
  );
}

export default memo(EximTabs);
