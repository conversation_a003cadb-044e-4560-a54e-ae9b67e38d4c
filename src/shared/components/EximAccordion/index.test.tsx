import {render, screen} from '@testing-library/react';

import EximAccordion, {AccordionTitle} from '.';

describe('EximAccordion wrapper Component', () => {
  test('should render EximAccordion', async () => {
    render(
      <EximAccordion
        title={
          <AccordionTitle
            eximTitle='Easily download Summary Reports on EximHero'
            eximType='GSTR3B'
            eximDate='25th August 2021'
          />
        }
        content='Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letterset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.'
      />
    );

    const accordionTitle = screen.getByTestId('title-exim');

    expect(accordionTitle).toBeInTheDocument();
  });
});
