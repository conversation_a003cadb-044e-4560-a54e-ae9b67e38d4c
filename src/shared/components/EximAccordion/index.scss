@import 'src/utils/main.scss';

.accordion-wrapper {
  width: 100%;
  margin: auto;

  .accordion-item {
    background-color: $white;
    .accordion-title {
      @include rfs(5px, border-radius);
      @include padding(15px);
      height: 78px;
      border: 1px solid $accordion-border;
      font-size: $font-size-md;
      color: $accordion-title;
      font-weight: $font-weight-semi-bold;
      .accordion-wrapper-title {
        @include flex-item(_, _, center, _, 5px);
        font-weight: $font-weight-semi-bold;
        font-size: $font-size-12p5;
        .accordion-wrapper-content {
          @include flex-item(_, _, center, _, 3px);
          color: $text-light;
          span:nth-child(2) {
            @include margin(2px);
          }
        }
      }
      &:hover {
        background-color: unset;
      }
      .accordion-down-icon {
        position: absolute;
        font-weight: $font-weight-regular;
        right: 12px;
        top: 30px;
        transition: unset;
      }
      .accordion-up-icon {
        top: 30px;
      }
    }
  }
  .accordion-content {
    @include padding(16px);
    border-radius: 0 0 5px 5px;
    border: 1px solid $accordion-border;
    border-top: none;
  }
}
