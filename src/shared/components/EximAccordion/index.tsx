import {AccordionCalendar, AccordionFile} from '@shared/icons';
import Accordion, {AccordionProps} from '@submodules/Accordion/Accordion';
import {memo} from 'react';

import './index.scss';

interface AccordionTitleProps {
  eximTitle: string;
  eximType: string;
  eximDate: string;
}

export function AccordionTitle({
  eximTitle,
  eximType,
  eximDate,
}: AccordionTitleProps) {
  return (
    <div>
      <p data-testid='title-exim'>{eximTitle}</p>
      <div className='accordion-wrapper-title'>
        <p className='accordion-wrapper-content'>
          <span>
            <AccordionFile />
          </span>
          <span>{eximType}</span>
        </p>
        <div>|</div>
        <p className='accordion-wrapper-content'>
          <span>
            <AccordionCalendar />
          </span>
          <span>{eximDate}</span>
        </p>
      </div>
    </div>
  );
}
function EximAccordion(props: AccordionProps) {
  const {title, content, hasBorder, isDisabled, accordionIcon} = props;

  return (
    <div className='accordion-wrapper'>
      <Accordion
        title={title}
        content={content}
        hasBorder={hasBorder}
        isDisabled={isDisabled}
        accordionIcon={accordionIcon}
      />
    </div>
  );
}

EximAccordion.defaultProps = {
  hasBorder: true,
  isDisabled: false,
};

export default memo(EximAccordion);
