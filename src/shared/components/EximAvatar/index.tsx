import Avatar, {AvatarProps as IAvatarProps} from '@submodules/Avatar/Avatar';
import {memo} from 'react';

import './index.scss';

function EximAvatar({
  firstName,
  lastName,
  imageUrl,
  alt,
  rounded,
  size,
}: IAvatarProps) {
  return (
    <div className='avatar-wrapper' data-testid='avatar-wrapper'>
      <Avatar
        firstName={firstName}
        lastName={lastName}
        imageUrl={imageUrl}
        alt={alt}
        rounded={rounded}
        size={size}
      />
    </div>
  );
}

export default memo(EximAvatar);
