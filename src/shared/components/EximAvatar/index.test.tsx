import {render, screen} from '@testing-library/react';

import EximAvatar from '.';

describe('EximAvatar component ', () => {
  it('render the avatar', () => {
    render(
      <EximAvatar
        firstName='John'
        lastName='David'
        size='small'
        rounded
        imageUrl='https://res.cloudinary.com/de9kghuz8/image/upload/v1658372152/thumbnail_Newuser_58f9959886.png'
        alt='avatar'
      />
    );
    expect(screen.getByTestId('avatar-wrapper')).toBeInTheDocument();
  });
  it('imageUrl should render ', () => {
    render(
      <EximAvatar
        imageUrl='https://res.cloudinary.com/de9kghuz8/image/upload/v1658372152/thumbnail_Newuser_58f9959886.png'
        alt='avatar'
        size='small'
        rounded
      />
    );
    expect(screen.getByTestId('avatar-wrapper')).toBeInTheDocument();
  });
  it('firstName and lastName render the avatar', () => {
    render(
      <EximAvatar firstName='John' lastName='David' size='small' rounded />
    );
    expect(screen.getByTestId('avatar-wrapper')).toBeInTheDocument();
  });
});
