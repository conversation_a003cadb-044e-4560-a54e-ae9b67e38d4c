@import '@utils/main.scss';
.animation-underline {
  & a {
    background-size: 200% 100%;
    background-position: -100%;
    display: inline-block;
    @include padding(5px 0);
    position: relative;
    transition: all 0.3s ease-in-out;
  }
  & a:before {
    content: '';
    background: $information;
    display: block;
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 0;
    height: 3px;
    transition: all 0.3s ease-in-out;
  }

  & a:hover {
    background-position: 0;
  }

  & a:hover::before {
    width: 100%;
  }
}

.link-wrapper a:active {
  color: $link-default;
}

$variantColor: (
  information: $information,
  tertiary: $tertiary-link,
  shadow: $btn-box-shadow-hover,
  text: $text-color,
  white: $white,
  primary: $primary,
);

@each $key, $val in $variantColor {
  .#{$key} a {
    color: $val;
  }
  .#{$key} a:before {
    background: $val;
  }
}
