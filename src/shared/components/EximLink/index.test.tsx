import {render, screen} from '@testing-library/react';
import {BrowserRouter} from 'react-router-dom';

import EximLink from './index';

test('Should render Link Component', () => {
  render(
    <BrowserRouter>
      <EximLink
        href='/'
        underline='always'
        animationUnderline
        variantColor='primary'>
        Home
      </EximLink>
    </BrowserRouter>
  );
  expect(screen.getByText(/Home/i)).toBeInTheDocument();
});
