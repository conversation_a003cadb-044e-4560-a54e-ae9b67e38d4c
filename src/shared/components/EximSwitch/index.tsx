import Switch, {SwitchProps} from '@submodules/Switch/Switch';

import './index.scss';

interface EximSwitchProps extends SwitchProps {
  id: string;
  name: string;
  isActive?: boolean;
  onToggle?: () => void;
  disabled?: boolean;
}

function EximSwitchWrapper(props: EximSwitchProps) {
  const {id, name, isActive, onToggle, disabled} = props;
  return (
    <span className={disabled ? 'switch-disabled' : ''}>
      <Switch
        id={id}
        name={name}
        color='success'
        size='medium'
        isActive={isActive}
        onToggle={onToggle}
        disabled={disabled}
      />
    </span>
  );
}
EximSwitchWrapper.defaultProps = {
  isActive: false,
  onToggle: () => {
    /*
    Empty function
    */
  },
  disabled: false,
};
export default EximSwitchWrapper;
