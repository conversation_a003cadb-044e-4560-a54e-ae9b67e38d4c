import {render} from '@testing-library/react';

import EximSwitchWrapper from '.';

describe('Switch component', () => {
  it('Should render default Switch ', () => {
    const {getByTestId} = render(
      <EximSwitchWrapper id='my-toggle' name='my-toggle' />
    );
    const switchComp = getByTestId('switch');
    expect(switchComp).toBeInTheDocument();
  });

  it('Should render default Switch ', () => {
    const {getByTestId} = render(
      <EximSwitchWrapper id='my-toggle' name='my-toggle' disabled />
    );
    const switchComp = getByTestId('switch');
    expect(switchComp).toBeInTheDocument();
  });
  it('Should toggle the switch', () => {
    render(<EximSwitchWrapper id='my-toggle' name='my-toggle' />);
    expect(EximSwitchWrapper.defaultProps.onToggle()).toBe(undefined);
  });
});
