@import '@utils/eximColors';
@import '@utils/main.scss';

$sizes: (
  small: 'small',
  medium: 'medium',
  large: 'large',
);

.toggle-wrapper {
  .toggle-input:checked ~ .switch-success {
    background-color: $success;
  }
  .toggle {
    .toggle-fill-medium {
      border: 1px solid $primary-border;
      width: 80px;
      height: 32px;
    }
    .toggle-input:checked ~ .toggle-fill-medium::after {
      transform: translateX(47px);
    }
    .toggle-fill-medium::after {
      border: 1px solid $primary-border;
      opacity: 1;
      transition: 0.3s;
      width: 32px;
      height: 32px;
      box-shadow: none;
      @include rfs(50%, border-radius);
      z-index: 1;
      top: -2px;
      left: -2px;
    }
  }
}

label {
  position: relative;
  &::after {
    content: 'OFF';
    position: absolute;
    right: 16px;
    top: 8px;
    @include font-size(12px);
    color: $label-color;
    font-weight: bold;
  }
  &::before {
    content: 'ON';
    position: absolute;
    left: 13px;
    top: 8px;
    @include font-size(12px);
    color: $white;
    z-index: 1;
    font-weight: bold;
  }
}
.switch-disabled {
  label {
    &::after {
      display: none;
    }
  }
}
