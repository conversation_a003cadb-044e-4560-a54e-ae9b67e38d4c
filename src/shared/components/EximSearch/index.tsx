import SearchBar from '@submodules/SearchBar/SearchBar';
import {ChangeEvent, KeyboardEvent, memo} from 'react';

import {SearchIcon} from '../../icons';
import './index.scss';

interface ISearchProps {
  placeholder?: string;
  searchText: string;
  autoComplete?: 'on' | 'off';
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onClick: () => void;
  onKeyDown: (e: KeyboardEvent<HTMLInputElement>) => void;
  isHeader?: boolean;
}

function EximSearch(props: ISearchProps) {
  const {
    isHeader,
    placeholder,
    autoComplete,
    searchText,
    onChange,
    onKeyDown,
    onClick,
  } = props;
  const icon = <SearchIcon fill={`${isHeader ? '#ffffff' : null}`} />;
  return (
    <div
      className={`search-${
        isHeader ? 'header' : 'filter'
      }-wrapper search-wrapper`}>
      <SearchBar
        iconPosition='right'
        placeholder={placeholder}
        searchText={searchText}
        autoComplete={autoComplete}
        onChange={onChange}
        onClick={onClick}
        onKeyDown={onKeyDown}
        searchIcon={icon}
      />
    </div>
  );
}

export default memo(EximSearch);

EximSearch.defaultProps = {
  isHeader: false,
  placeholder: 'Search',
  autoComplete: 'off',
};
