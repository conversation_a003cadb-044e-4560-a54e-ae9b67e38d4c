import '@testing-library/jest-dom';
import {render} from '@testing-library/react';

import EximSearch from '.';

describe('Should render the SearchBar wrapper', () => {
  const setValue = jest.fn();
  const getValue = jest.fn();
  const getValueByEnter = jest.fn();

  it('Should renders search filter without error', () => {
    render(
      <EximSearch
        placeholder='search'
        searchText='Search me'
        autoComplete='off'
        onChange={setValue}
        onClick={getValue}
        onKeyDown={getValueByEnter}
      />
    );
    expect(
      document.querySelector('.search-filter-wrapper')
    ).toBeInTheDocument();
  });

  it('Should renders search header without error', () => {
    render(
      <EximSearch
        isHeader
        placeholder='search'
        searchText='Search me'
        autoComplete='off'
        onChange={setValue}
        onClick={getValue}
        onKeyDown={getValueByEnter}
      />
    );
    expect(
      document.querySelector('.search-header-wrapper')
    ).toBeInTheDocument();
  });
});
