@import '@utils/main.scss';

.search-wrapper {
  .form-input {
    padding: 0 !important;
    input {
      padding: 0px 10px;
      background-color: transparent;
    }
  }

  .searchbar-container {
    margin: 0;
  }

  .searchbar-container .clear-n-search-icon div {
    padding: 4px;
  }

  .clear-n-search-icon {
    background: transparent;
    .clear-btn {
      display: none;
    }
  }
}

.search-header-wrapper {
  width: 400px;
  height: 50px;
  background-color: $tertiary-light;

  .form-input {
    height: 50px;

    input {
      border-radius: 0;
      font-size: 14px;
      color: $white;

      &::placeholder {
        color: $white;
      }
    }
  }

  .searchbar-container {
    border: none;
    &:hover {
      border: none;
      .clear-n-search-icon .search-icon-right {
        border-left: none;
      }
    }
  }

  .clear-n-search-icon {
    .search-icon-right {
      border: 0;
      margin-right: 10px;
    }
  }
}

.search-filter-wrapper {
  width: 260px;

  .form-input {
    min-height: 32px !important;
    input {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      height: 100%;
    }
  }

  .searchbar-container {
    &:hover {
      border: 1px solid $information;
      .clear-n-search-icon .search-icon-right {
        border-left: 1px solid $information;
      }
    }
  }

  .clear-n-search-icon {
    background: transparent;
    .search-icon-right {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      background-color: $secondary;
      display: flex;
      justify-content: center;
      width: 32px;
    }
  }
}
