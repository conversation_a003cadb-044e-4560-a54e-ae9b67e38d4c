import Button, {ButtonProps} from '@submodules/Button/Button';
import '@utils/main.scss';

import './index.scss';

function EximButton(props: ButtonProps) {
  const {
    variant,
    color,
    size,
    disabled,
    onClick,
    id,
    children,
    isLoading,
    type,
    rounded,
    iconBtn,
    className,
    ref,
    dataTestId,
  } = props;
  return (
    <div className={`button-wrapper ${className || ''}`}>
      <Button
        ref={ref}
        variant={variant}
        color={color}
        size={size}
        disabled={disabled}
        onClick={onClick}
        id={id}
        isLoading={isLoading}
        type={type}
        rounded={rounded}
        iconBtn={iconBtn}
        className={className}
        dataTestId={dataTestId}>
        {children}
      </Button>
    </div>
  );
}

export default EximButton;

EximButton.defaultProps = {
  variant: 'contained',
  color: 'primary',
  size: 'medium',
  disabled: false,
  onClick: () => {
    /** empty function */
  },
  id: '',
  children: 'Click me',
  ref: null,
  rounded: false,
  iconBtn: false,
  className: '',
  type: 'button',
};
