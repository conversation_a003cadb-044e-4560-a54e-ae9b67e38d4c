@import '@utils/main.scss';

.button-wrapper {
  .base-btn {
    width: 100% !important;
    color: $white;
    &:not(:disabled):hover {
      opacity: 1;
      box-shadow: 0 3px 5px $btn-box-shadow-hover;
    }
    &:not(:disabled):focus {
      opacity: 1;
    }
    @include margin(0);
    letter-spacing: 0.25px;
  }

  .premium {
    background-image: linear-gradient(
      btn-colors('premium-gradient-one'),
      btn-colors('premium-gradient-two')
    );
  }

  @each $key, $val in $btn-colors {
    .contained {
      background-color: btn-colors('primary-normal');
      color: $white;

      &[class^='#{$key}'] {
        background-color: colors(#{$key});
        background-color: btn-colors('#{$key}');
        &:not(:disabled):hover {
          background-color: btn-colors('#{$key}-hover');
        }
      }

      @if #{$key} == 'secondary' {
        &[class^='#{$key}'] {
          color: btn-colors('secondary-font');
        }
      }

      @if #{$key} == 'primary' {
        &[class^='#{$key}'] {
          &:not(:disabled):active {
            background-color: btn-colors('primary-clicked');
          }
        }
      }
    }

    .outlined {
      background-color: transparent;
      color: btn-colors('primary-normal');
      border: 1px solid btn-colors('primary-normal');

      &:not(:disabled):hover {
        background-color: btn-colors('primary-normal');
        color: $white;
      }

      &[class^='#{$key}'] {
        color: $val;
        border: 1px solid $val;

        &:not(:disabled):hover {
          background-color: $val;
          color: $white;
        }
      }

      @if #{$key} == 'secondary' {
        &[class^='#{$key}'] {
          color: btn-colors('secondary-font');
          &:not(:disabled):hover {
            color: btn-colors('secondary-font');
          }
        }
      }
    }

    .text {
      &[class^='#{$key}'] {
        color: $val;
        background-color: transparent;
      }
      @if #{$key} == 'secondary' {
        &[class^='#{$key}'] {
          color: btn-colors('secondary-font');
        }
      }
    }
  }
}
