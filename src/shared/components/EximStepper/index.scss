@import '@utils/main.scss';

.stepper {
  .completed.active {
    color: $success;
  }
  .connector-line.active {
    border-top: 1px solid $success !important ;
  }
  .active {
    color: $secondary;
  }
  .step-txt {
    fill: $text-color;
    @include font-size(14px);
    font-weight: bold;
  }
  .disable {
    color: $secondary-background;
  }

  .stepper-horizontal {
    .step-item {
      .stepper-connector {
        left: calc(-50% + 12px);
        right: calc(50% + 12px);
      }
      .step-item-inside {
        .step-label {
          color: $text-color;
          @include margin-top(8px);
        }
      }
    }
  }
}
