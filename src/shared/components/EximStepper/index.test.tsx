import {render} from '@testing-library/react';

import Stepper from './index';

describe('Stepper component', () => {
  const steps = [
    {label: 'step1', isActive: true, isCompleted: true},
    {label: 'step2', isActive: true, isCompleted: false},
    {label: 'step3', isActive: false, isCompleted: false},
  ];
  it('Should render default Stepper component', () => {
    const {getByTestId} = render(<Stepper steps={steps} />);
    const stepper = getByTestId('stepper');
    expect(stepper).toBeInTheDocument();
  });
});
