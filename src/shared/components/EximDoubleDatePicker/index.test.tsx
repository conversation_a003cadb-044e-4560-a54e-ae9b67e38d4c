import {fireEvent, render, screen} from '@testing-library/react';

import EximDoubleDatePicker from './index';

describe('EximDoubleDatePicker Component', () => {
  const onChange = jest.fn();
  it('Should render default EximDoubleDatePicker', () => {
    render(
      <EximDoubleDatePicker id='1' minDate='08/2022' onChange={onChange} />
    );
    const datePicker = screen.getByTestId('date-picker');
    expect(datePicker).toBeInTheDocument();
  });

  it('Should render EximDoubleDatePicker with default date', () => {
    render(
      <EximDoubleDatePicker
        id='2'
        minDate='08/2022'
        maxDate='11/2022'
        onChange={onChange}
        defaultStartDate='04/2023'
        defaultEndDate='05/2023'
      />
    );
    const monthCalendar = screen.getByTestId('month-calendar');
    expect(monthCalendar).toBeInTheDocument();
  });

  it('Should render EximDoubleDatePicker and select the date', () => {
    render(
      <EximDoubleDatePicker
        id='1'
        minDate='02/2022'
        maxDate='11/2022'
        onChange={onChange}
      />
    );
    const dateInput = screen.getByPlaceholderText('MMM-YYYY MMM-YYYY');
    fireEvent.click(dateInput);
    const date = screen.getAllByRole('option');
    fireEvent.click(date[2]);
    fireEvent.click(date[3]);
    expect(dateInput).toHaveValue('Mar 2022 - Apr 2022');
  });

  it('Should render default EximDoubleDatePicker and disabled', () => {
    render(
      <EximDoubleDatePicker
        id='1'
        minDate='08/2022'
        maxDate='11/2022'
        onChange={onChange}
        disabled
      />
    );
    const dateInput = screen.getByPlaceholderText('MMM-YYYY MMM-YYYY');
    fireEvent.click(dateInput);
    expect(dateInput).toBeDisabled();
  });
});
