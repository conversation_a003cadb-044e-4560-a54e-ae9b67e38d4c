@import '@utils/main.scss';

.base-date-picker {
  width: 224px;
  @include flex-item(_, _, center);
  position: relative;

  .calendar-btn {
    background: $tertiary;
    @include rfs(5px 0px 0px 5px, border-radius);
    @include padding(8px 12px 6px);
    border: none;
    cursor: pointer;
    position: relative;
    .triangle {
      position: absolute;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-bottom: 13px solid #f0f0f0;
      bottom: -13px;
      left: 10px;
    }
  }

  .date-input {
    width: 150px;
    @include padding(7px 4px 7px 12px);
    @include rfs(0px 5px 5px 0px, border-radius);
    outline: none;
    border: 1px solid $primary-border;
    font-size: $font-size-sm;
    background-color: $primary-border-light;
  }

  // Below classes coming from the react-datepicker
  .react-datepicker {
    box-shadow: 0 5px 10px $modal-box-shadow;

    .react-datepicker__navigation-icon::before {
      border-color: $tertiary;
    }
    button:hover {
      .react-datepicker__navigation-icon::before {
        border-color: $white;
      }
    }
  }

  .react-datepicker__month- {
    &wrapper div {
      @include padding(8px 0);
      font-size: $font-size-sm;

      &:hover:not(.react-datepicker__month--range-start, .react-datepicker__month--range-end) {
        background: $secondary;
        color: $tertiary;
      }
    }
    &wrapper > .react-datepicker__month--in-range {
      background: $secondary;
      color: $tertiary;
    }
    &wrapper > .react-datepicker__month--range-end,
    &wrapper > .react-datepicker__month--range-start {
      background: $tertiary;
      color: $white;
    }
    &wrapper > .react-datepicker__month-text--today {
      font-weight: normal;
    }

    &text--keyboard-selected {
      background: $tertiary;
      color: $white;
      &:hover {
        background: $tertiary;
        color: $white;
      }
    }
  }
  .react-datepicker__month-wrapper
    div:hover:is(.react-datepicker__month-text--keyboard-selected) {
    background: $tertiary;
    color: $white;
  }

  .react-datepicker__ {
    &week > div {
      @include padding(2px 5px);

      &:hover {
        background: $tertiary;
        color: $white;
      }
    }

    &day--keyboard-selected,
    &day--selected {
      background: $tertiary;
    }
  }

  .react-datepicker__triangle {
    transform: translate(0px, 0px) !important;
  }

  .react-datepicker__navigation {
    &:hover {
      @include rfs(4px, border-radius);
      background: $tertiary;
      color: $white;
    }
  }

  .react-datepicker-popper {
    transform: translate(0px, 34px) !important;
  }

  .react-datepicker__month-container {
    min-width: 241px;
    font-size: $font-size-sm;
  }

  .react-datepicker__month-select,
  .react-datepicker__year-select {
    @include padding(1px 2px);
    @include margin-top(5px);
    outline: none;
    border-radius: 4px;
    background: none;
  }
}
.disabled-date-picker {
  pointer-events: none;
  .calendar-btn {
    background: $label-color;
  }
  .date-input {
    background: $disable-color;
    border: 1px solid $disable-color;
  }
}

.date-picker-container {
  position: relative;
  .react-datepicker__portal {
    position: absolute;
    width: fit-content;
    height: fit-content;
    background: none;
    top: 45px;
    left: -38px;
    .react-datepicker {
      border: none;
    }
  }
}
// Highlight the placeholder text
.date-picker-container.placeholder-text {
  .date-input::placeholder {
    color: $text-color;
  }
}
