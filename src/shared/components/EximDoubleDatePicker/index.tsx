import {Calendar} from '@shared/icons';
import {format, parse} from 'date-fns';
import {ReactNode, useState} from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import './index.scss';

export interface DatePickerProps {
  /* To define id of the date picker */
  id: string;
  /* It defines the disable of the date picker */
  disabled?: boolean;
  /* It defines the readOnly of the date picker */
  readOnly?: boolean;
  /* It defines the default value of the date picker */
  calendarIcon?: ReactNode;
  /* It defines the start date of the date picker */
  minDate: string; // format: 01/2023 ==> month and year
  /* It defines the end date of the date picker */
  maxDate?: string; // format: 01/2023 ==> month and year
  /* It defines to set selected date of the date picker */
  onChange: (start: string, end: string) => void;
  /* It defines default start date of the date picker */
  defaultStartDate?: string; // format: 01/2023 ==> month and year
  /* It defines default end date of the date picker */
  defaultEndDate?: string; // format: 01/2023 ==> month and year
}

function EximDoubleDatePicker(props: DatePickerProps) {
  const {
    id,
    minDate,
    maxDate,
    disabled,
    readOnly,
    onChange,
    calendarIcon,
    defaultStartDate,
    defaultEndDate,
  } = props;

  const [fromMonth, fromYear] = minDate.split('/').map(Number); // splitting min date
  const [toMonth, toYear] = maxDate?.split('/')?.map(Number) || [null, null]; // splitting max date

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [startDate, endDate] = dateRange;

  const handleChange = (rangeOfDate: [Date, Date]) => {
    if (disabled) return;
    const start = format(new Date(rangeOfDate[0]), 'MM-yyyy');
    const end = format(new Date(rangeOfDate[1]), 'MM-yyyy');

    if (end !== '01-1970') {
      onChange(start, end);
    }
    setDateRange(rangeOfDate);
  };

  const parseDate = (date: string) => {
    const parsedDate = parse(date, 'MM/yyyy', new Date());
    return format(parsedDate, 'MMM yyyy');
  };

  // Below function to click on the date input element by clicking on calendar icon
  const handleCalendarOpen = () => {
    const divElement: HTMLElement | null = document.getElementById(id);
    if (divElement) {
      const inputElement: HTMLInputElement | null =
        divElement.querySelector('.date-input');
      if (inputElement) inputElement.click();
    }
  };

  return (
    <div
      id={id}
      className={`base-date-picker ${disabled ? 'disabled-date-picker' : ''}`}
      data-testid='date-picker'>
      <button
        onClick={handleCalendarOpen}
        type='button'
        className='calendar-btn'
        data-testid='calendar-icon-btn'>
        {calendarIcon}
        {isDatePickerOpen ? <span className='triangle' /> : null}
      </button>

      <div
        className={`date-picker-container ${
          defaultStartDate ? 'placeholder-text' : ''
        }`}
        data-testid='month-calendar'>
        <DatePicker
          open
          selectsRange
          startDate={startDate}
          endDate={endDate}
          onChange={handleChange}
          withPortal
          placeholderText={`${
            defaultStartDate ? parseDate(defaultStartDate) : 'MM-YYYY'
          } - ${defaultEndDate ? parseDate(defaultEndDate) : 'MM-YYYY'}`}
          dateFormat='MMM yyyy'
          className='date-input'
          readOnly={readOnly}
          disabled={disabled}
          showMonthYearPicker
          minDate={new Date(fromYear, fromMonth - 1)}
          maxDate={
            toYear && toMonth ? new Date(toYear, toMonth - 1) : new Date()
          }
          onCalendarOpen={() => setIsDatePickerOpen(true)}
          onCalendarClose={() => setIsDatePickerOpen(false)}
        />
      </div>
    </div>
  );
}

EximDoubleDatePicker.defaultProps = {
  disabled: false,
  readOnly: false,
  maxDate: '',
  defaultStartDate: '',
  defaultEndDate: '',
  calendarIcon: <Calendar />,
};

export default EximDoubleDatePicker;
