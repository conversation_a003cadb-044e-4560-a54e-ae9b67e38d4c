import HomeIcon from '@submodules/icons/HomeIcon/HomeIcon';
import {render, screen} from '@testing-library/react';

import EximBreadcrumbs from '.';

const breadcrumbsData = [
  {
    id: 1,
    text: 'M<PERSON>',
  },
  {
    id: 2,
    text: 'Core',
  },
  {
    id: 3,
    text: 'Breadcrumbs',
  },
];

const breadcrumbsWithROutes = [
  {
    id: 1,
    text: 'M<PERSON>',
    icon: <HomeIcon width={12} height={12} />,
    route: 'https://perennialsys.com/',
  },
  {
    id: 2,
    text: 'Core',
    icon: <HomeIcon width={12} height={12} />,
    route: 'https://storybook.js.org/',
  },
  {
    id: 3,
    text: 'Breadcrumbs',
    icon: <HomeIcon width={12} height={12} />,
    route: 'https://reactjs.org/',
  },
];

describe('Rendering part for Breadcrumbs component testing', () => {
  it('should render Breadcrumbs with default props', () => {
    render(<EximBreadcrumbs breadcrumbs={breadcrumbsData} />);

    const breadcrumbsWrapper = screen.getByTestId('breadcrumb-wrapper');

    expect(breadcrumbsWrapper).toBeInTheDocument();
  });
  it('should render Breadcrumbs with icons and routes props', () => {
    render(<EximBreadcrumbs breadcrumbs={breadcrumbsWithROutes} />);

    expect(screen.getAllByTestId('breadcrumb-icon')).toHaveLength(3);
  });
});
