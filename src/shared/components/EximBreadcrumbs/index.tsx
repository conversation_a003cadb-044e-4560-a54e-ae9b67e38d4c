import Breadcrumbs, {
  IBreadcrumbsProps,
} from '@submodules/Breadcrumbs/Breadcrumbs';
import {memo} from 'react';

import './index.scss';

function EximBreadcrumbs(props: IBreadcrumbsProps) {
  const {
    breadcrumbs,
    activeBreadcrumbColor,
    inactiveBreadcrumbColor,
    separator,
  } = props;
  return (
    <div className='breadcrumb-wrapper'>
      <Breadcrumbs
        breadcrumbs={breadcrumbs}
        activeBreadcrumbColor={activeBreadcrumbColor}
        inactiveBreadcrumbColor={inactiveBreadcrumbColor}
        separator={separator}
      />
    </div>
  );
}

export default memo(EximBreadcrumbs);
