import Badge, {BadgeProps} from '@submodules/Badge/Badge';

import './index.scss';

interface IBadge extends BadgeProps {
  position?: 'center';
}

export default function EximBadge({
  id,
  vertical,
  horizontal,
  content,
  maxValue,
  children,
  color,
  position,
}: IBadge) {
  return (
    <div
      className={`badge-wrapper ${position && position}`}
      data-testid='badgeWrapper'>
      <Badge
        id={id}
        vertical={vertical}
        horizontal={horizontal}
        content={content}
        maxValue={maxValue}
        color={color}>
        {children}
      </Badge>
    </div>
  );
}

EximBadge.defaultProps = {
  position: 'center',
};
