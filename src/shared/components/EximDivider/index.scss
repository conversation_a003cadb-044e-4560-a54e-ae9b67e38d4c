@import '@utils/main.scss';

.divider .divider-center {
  border: 1px solid $secondary;
  opacity: 0.5;
}

.divider hr {
  border-color: $secondary;
}

.business-details-card + .summary-header .divider .divider-text:after {
  color: $text-mute;
  font-size: $font-size-xsm + 1;
}

.divider {
  .divider-text:after {
    background-color: $primary-bg;
    @include padding(0 10px 0 0);
    color: $label-color;
    @include font-size(14px);
    font-weight: $font-weight-semi-bold;
    letter-spacing: 0.5px;
  }
  hr {
    border-top: 0px solid $primary-border;
    @include margin(20px 0);
    &:after {
      top: -12px;
      left: -1px;
    }
  }
}
