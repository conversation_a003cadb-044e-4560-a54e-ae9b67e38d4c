@import '@utils/main.scss';

.input-container {
  @include padding(0);
  height: inherit;
  .disabled:hover {
    border: inherit;
  }
}
.input-wrapper {
  width: 100%;
  height: 32px; //we can adjust input height
  margin: auto;

  .input-group label {
    left: 0px;
    top: -27.5px;
    color: $label-color;
    font-size: $font-size-sm;
    font-weight: $font-weight-regular;
    span {
      @include padding(0);
    }

    .label-star::after {
      content: '*';
      color: $error;
      font-size: 21px;
      position: relative;
      top: 3px;
      left: 1px;
    }
  }
  .form-input {
    border: none;
    min-height: unset;
    height: inherit;
    @include padding(0);
    input {
      background-color: unset;
      border: 1px solid $primary-border;
      @include padding(2px 8px 2px 10px);
      @include rfs(3px, border-radius);
      &:focus {
        border: 1px solid $information;
      }
      &:hover {
        border: 1px solid $information;
      }
      &:not(:disabled)::placeholder {
        border-color: $primary-border;
      }
      &:disabled {
        background-color: unset;
        border-color: $primary-border;
        &::placeholder {
          color: $label-color;
          opacity: 0.8;
        }
      }
      &::placeholder {
        color: $text-color;
        opacity: 0.5;
      }
    }

    input[name='password'] {
      @include padding-right(35px);
    }
  }
  &.isInvalid {
    .invalid {
      input {
        border: 1px solid $error;
        &:focus {
          border-color: $error;
        }
        &:hover {
          border-color: $error;
        }
      }
    }
  }
  .input-normal-wrapper {
    height: 100%;
  }
  .input-mobile-wrapper {
    .input-container {
      .disabled {
        background-color: unset;
      }
    }

    .input-group label {
      left: 0px;
      top: -24px;
      color: $label-color;
      font-size: $font-size-sm;
      font-weight: $font-weight-regular;
      background-color: unset;
    }
    position: relative;
    height: 100%;
    .form-input {
      min-height: unset;
      input {
        text-indent: 35px;
      }
    }

    .mobile-label {
      height: 94.7%;
      position: absolute;
      @include rfs(2px 0px 0 2px, border-radius);
      border: none;
      margin: 1px;
      border-right: 1px solid $primary-border;
      font-size: $font-size-sm;
      @include padding(0px 5px);
      @include flex-item(row, center, center, 0);
      background-color: $base-background;
      color: $label-color;
      z-index: 1;
    }
  }
  &.disabled {
    border: none;
  }
  .error-message {
    color: $error;
    @include padding(2px 0 0 0);
    @include font-size($font-size-sm);
  }
}
