import {render} from '@testing-library/react';

import EximInput from '.';

describe('EximInput wrapper component', () => {
  test('should render correctly', () => {
    render(<EximInput type='number' value='mobile' />);
  });
  test('should show email field', () => {
    const {container} = render(
      <EximInput type='email' value='email' isInvalid />
    );
    expect(container).toBeInTheDocument();
  });
  test('should input field disabled', () => {
    const {container} = render(
      <EximInput type='email' value='email' isInvalid disabled />
    );
    expect(container).toBeInTheDocument();
  });
  test('should mobile field enable', () => {
    const {container} = render(
      <EximInput
        type='number'
        value='mobile'
        fieldType='mobileNumber'
        isInvalid
        disabled
      />
    );
    expect(container).toBeInTheDocument();
  });
});
