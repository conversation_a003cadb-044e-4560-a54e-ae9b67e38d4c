import InputField, {InputProps} from '@submodules/InputField/InputField';
import {memo} from 'react';

import './index.scss';

interface EximInputProps extends InputProps {
  fieldType?: InputProps['type'] | 'mobileNumber';
}

function EximInput(props: EximInputProps) {
  const {
    id,
    type,
    placeholder,
    isInvalid,
    errorMessage,
    label,
    isRequired,
    disabled,
    onChange,
    onBlur,
    value,
    name,
    readOnly,
    minLength,
    maxLength,
    fieldType,
    autoComplete,
    dataTestid,
    passwordEyeOpenIcon,
    passwordEyeCloseIcon,
    isFocused,
    isPreventPaste,
    className,
  } = props;
  return (
    <div
      className={`input-wrapper ${className} ${isInvalid && 'isInvalid'} ${
        disabled && 'disabled'
      }`}>
      <div
        className={`${
          fieldType === 'mobileNumber'
            ? 'input-mobile-wrapper'
            : 'input-normal-wrapper'
        }`}>
        {fieldType === 'mobileNumber' && (
          <span className='mobile-label'>+91</span>
        )}
        <InputField
          id={id}
          isInvalid={isInvalid}
          errorMessage={errorMessage}
          type={type}
          placeholder={placeholder}
          label={label}
          isRequired={isRequired}
          disabled={disabled}
          onChange={onChange}
          onBlur={onBlur}
          name={name}
          value={value}
          readOnly={readOnly}
          minLength={minLength}
          maxLength={maxLength}
          autoComplete={autoComplete}
          dataTestid={dataTestid}
          passwordEyeOpenIcon={passwordEyeOpenIcon}
          passwordEyeCloseIcon={passwordEyeCloseIcon}
          isFocused={isFocused}
          isPreventPaste={isPreventPaste}
        />
      </div>
    </div>
  );
}

EximInput.defaultProps = {
  fieldType: 'text',
};

export default memo(EximInput);
