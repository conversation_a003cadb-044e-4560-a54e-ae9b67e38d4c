import {CloseIcon} from '@shared/icons';
import {ReactNode} from 'react';

import EximModal from '../EximModal';
import EximOTP from './components/EximOTP';
import './index.scss';

interface IEximOTPProps {
  isOTPModalVisible: boolean;
  OTPHeader?: ReactNode;
  description: ReactNode;
  resendBtnLabel?: string;
  verifyBtnLabel?: string;
  isVerifyBtnDisabled?: boolean;
  handleResendOTP: () => void;
  handleVerifyOTP: () => void;
  onClose: () => void;
  onOutSideClickClose: () => void;
  onChange: (e: string) => void;
}

function EximOTPModal(props: IEximOTPProps) {
  const {
    isOTPModalVisible,
    description,
    resendBtnLabel,
    verifyBtnLabel,
    isVerifyBtnDisabled,
    handleResendOTP,
    handleVerifyOTP,
    onClose,
    onOutSideClickClose,
    onChange,
    OTPHeader,
  } = props;

  return (
    <EximModal
      isOpen={isOTPModalVisible}
      content={
        <EximOTP
          handleResendOTP={handleResendOTP}
          handleVerifyOTP={handleVerifyOTP}
          onChange={onChange}
          description={description}
          isVerifyBtnDisabled={isVerifyBtnDisabled as boolean}
          resendBtnLabel={resendBtnLabel as string}
          verifyBtnLabel={verifyBtnLabel as string}
        />
      }
      closeIcon={<CloseIcon />}
      header={OTPHeader}
      footer=''
      isCloseIconVisible
      onClose={onClose}
      onOutSideClickClose={onOutSideClickClose}
    />
  );
}

export default EximOTPModal;

EximOTPModal.defaultProps = {
  OTPHeader: 'Enter OTP',
  resendBtnLabel: 'Resend OTP',
  verifyBtnLabel: 'Verify',
  isVerifyBtnDisabled: false,
};
