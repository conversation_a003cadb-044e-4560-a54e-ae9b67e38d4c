import '@testing-library/jest-dom';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';

import EximOTPModal from '.';

describe('Test EximOTPModal', () => {
  it('Renders OTP modal without error', async () => {
    render(
      <EximOTPModal
        isOTPModalVisible
        OTPHeader='Enter OTP'
        description={<p>this is description</p>}
        resendBtnLabel='Resend OTP'
        verifyBtnLabel='Verify'
        handleResendOTP={jest.fn()}
        handleVerifyOTP={jest.fn()}
        onClose={jest.fn()}
        onOutSideClickClose={jest.fn()}
        onChange={jest.fn()}
      />
    );
    const container = screen.queryByText('Enter OTP');
    expect(container).toBeInTheDocument();

    const inputOne = await waitFor(() => screen.getByTestId('pin-input-0'));
    const inputTwo = await waitFor(() => screen.getByTestId('pin-input-1'));
    fireEvent.change(inputOne, {target: {value: '9'}});
    fireEvent.change(inputTwo, {target: {value: '9'}});
    fireEvent.focus(inputTwo);
    fireEvent.select(inputTwo);
    expect((inputOne as HTMLInputElement).value).toBe('9');
  });

  it('Should able to paste OTP in OTP modal without error', async () => {
    render(
      <EximOTPModal
        isOTPModalVisible
        OTPHeader='Enter OTP'
        description={<p>this is description</p>}
        resendBtnLabel='Resend OTP'
        verifyBtnLabel='Verify'
        handleResendOTP={jest.fn()}
        handleVerifyOTP={jest.fn()}
        onClose={jest.fn()}
        onOutSideClickClose={jest.fn()}
        onChange={jest.fn()}
      />
    );
    const container = screen.queryByText('Enter OTP');
    expect(container).toBeInTheDocument();

    const inputTwo = await waitFor(() => screen.getByTestId('otp-pin'));
    const inputOne = await waitFor(() => screen.getByTestId('pin-input-0'));
    fireEvent.click(inputTwo);
    fireEvent.paste(inputTwo, {
      clipboardData: {
        getData: () => '123456',
      },
    });
    expect((inputOne as HTMLInputElement).value).toBe('1');
  });

  it('Should able to remove text and go back to previous input on Backspace', async () => {
    render(
      <EximOTPModal
        isOTPModalVisible
        OTPHeader='Enter OTP'
        description={<p>this is description</p>}
        resendBtnLabel='Resend OTP'
        verifyBtnLabel='Verify'
        handleResendOTP={jest.fn()}
        handleVerifyOTP={jest.fn()}
        onClose={jest.fn()}
        onOutSideClickClose={jest.fn()}
        onChange={jest.fn()}
      />
    );
    const container = screen.queryByText('Enter OTP');
    expect(container).toBeInTheDocument();

    const inputOne = await waitFor(() => screen.getByTestId('pin-input-0'));
    const inputTwo = await waitFor(() => screen.getByTestId('pin-input-1'));
    fireEvent.change(inputTwo, {target: {value: '9'}});
    fireEvent.keyUp(inputTwo, {code: 'Backspace', target: {value: ''}});
    fireEvent.focus(inputOne);
    fireEvent.select(inputOne);
    expect((inputOne as HTMLInputElement).value).toBe('');
  });
});
