@import '@utils/main.scss';

.business-details-card {
  .modal-body {
    width: 374px;
    position: relative;
    .modal-header {
      @include padding(28px 44px 16px);

      .modal-title {
        @include font-size(18px);
        color: $title-color;
        line-height: 25px;
      }

      .close-button {
        top: 32px;
        right: 28px;
      }
    }

    .modal-content {
      @include padding(0 40px 20px 44px);
    }
  }
}

.exim-otp {
  // each input pin style
  .otp-pin {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    @include margin(16px 0);
  }
  .pin-input {
    height: 32px;
    width: 40px;
    @include font-size(16px);
    border: 1px solid $input-border;
    border-radius: 5px;
    padding-left: 15px;
    outline: none;

    &:focus {
      border-color: $information;
      transform: scale(1.01);
    }
  }
  // Resend button style
  .resend-btn {
    .button-wrapper {
      width: fit-content;
      .base-btn {
        height: 18px;
        @include margin(0 0 4px);
        padding: 0;

        &:hover {
          box-shadow: none;
        }
        .btn-children {
          @include font-size(12px);
          line-height: 17px;
        }
      }
    }
  }
  // Verify button style
  .verify-btn {
    display: flex;
    justify-content: flex-end;
    .button-wrapper {
      width: 100px;
      .base-btn {
        margin: 0;
        height: 32px;
        .btn-children {
          @include font-size(14px);
          line-height: 20px;
        }
      }
    }
  }
}
