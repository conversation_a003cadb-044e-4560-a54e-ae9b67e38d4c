import React, {useRef} from 'react';

import OTPPinItems from './OTPPinItems';

interface IPin {
  noOfBoxes: number;
  maxLength: number;
  onChange: (e: string) => void;
}

function OTPPin({noOfBoxes, maxLength, onChange}: IPin) {
  const elements = useRef(new Array(noOfBoxes).fill(null));
  const values = useRef(new Array(noOfBoxes).fill(''));

  const handleTextChange = React.useCallback(
    (value: string, index: number) => {
      values.current[index] = value;

      if (index < noOfBoxes - 1 && value.length === maxLength) {
        elements.current[index + 1].focus();
        elements.current[index + 1].select();
      }
      onChange(values.current.join(''));
    },
    [maxLength, onChange, noOfBoxes]
  );

  const handleBackspace = React.useCallback(
    (value: string, index: number) => {
      if (index > 0 && value.length === 0) {
        elements.current[index - 1].focus();
        elements.current[index - 1].select();
      }
      // clears the current input value and returns to the previous input box
      values.current[index] = value;
      onChange(values.current.join(''));
    },
    [onChange]
  );

  // allow paste the otp in input
  const handlePaste = React.useCallback(
    (e: React.ClipboardEvent<HTMLDivElement>) => {
      e.preventDefault();
      // getting clipboard data
      const pin = e.clipboardData.getData('Text');

      for (let i = 0; i < noOfBoxes && i < pin.length; i += 1) {
        values.current[i] = pin[i];
        elements.current[i].value = pin[i];

        elements.current[i].focus();
      }
      onChange(values.current.join(''));
    },
    [onChange, noOfBoxes]
  );
  return (
    <div className='otp-pin' data-testid='otp-pin' onPaste={handlePaste}>
      {values.current.map((item, ind) => (
        <OTPPinItems
          maxLength={maxLength}
          // eslint-disable-next-line
          key={ind}
          ref={(e) => {
            elements.current[ind] = e;
          }}
          index={ind}
          onChange={handleTextChange}
          onBackspace={handleBackspace}
        />
      ))}
    </div>
  );
}

export default OTPPin;
