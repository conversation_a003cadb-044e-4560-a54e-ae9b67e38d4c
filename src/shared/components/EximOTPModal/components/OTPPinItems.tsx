import React, {LegacyRef} from 'react';

interface IEventType {
  code?: string;
  target?: {value: string};
}

interface IPinItems {
  maxLength: number;
  onBackspace: (e: string, ind: number) => void;
  onChange: (e: string, ind: number) => void;
  index: number;
}
const OTPPinItems = React.forwardRef(
  (props: IPinItems, ref: LegacyRef<HTMLInputElement>) => {
    const {maxLength, onBackspace, onChange, index} = props;

    const handleChange = React.useCallback(
      (e: IEventType) => {
        if (e.code === 'Backspace')
          onBackspace(e.target?.value as string, index);
        else onChange(e.target?.value as string, index);
      },
      [onChange, index, onBackspace]
    );

    return (
      <input
        className='pin-input'
        data-testid={`pin-input-${index}`}
        maxLength={maxLength}
        ref={ref}
        onKeyUp={(e) => {
          handleChange(e as unknown as IEventType);
        }}
      />
    );
  }
);

export default React.memo(OTPPinItems);
