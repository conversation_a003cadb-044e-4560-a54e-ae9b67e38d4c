import {ReactNode} from 'react';

import EximButton from '../../EximButton';
import OTPPin from './OTPPin';

interface IEximOTP {
  description: ReactNode;
  resendBtnLabel: string;
  verifyBtnLabel: string;
  isVerifyBtnDisabled: boolean;
  handleResendOTP: () => void;
  handleVerifyOTP: () => void;
  onChange: (e: string) => void;
}

function EximOTP({
  description,
  resendBtnLabel,
  verifyBtnLabel,
  isVerifyBtnDisabled,
  handleResendOTP,
  handleVerifyOTP,
  onChange,
}: IEximOTP) {
  return (
    <div className='exim-otp'>
      <div className='otp-description'>{description}</div>
      <OTPPin maxLength={1} noOfBoxes={6} onChange={onChange} />
      <div className='resend-btn'>
        <EximButton
          dataTestId='resend-otp'
          variant='text'
          color='information'
          onClick={handleResendOTP}>
          {resendBtnLabel}
        </EximButton>
      </div>
      <div className='verify-btn'>
        <EximButton
          dataTestId='verify-otp'
          color='secondary'
          disabled={isVerifyBtnDisabled}
          onClick={handleVerifyOTP}>
          {verifyBtnLabel}
        </EximButton>
      </div>
    </div>
  );
}

export default EximOTP;
