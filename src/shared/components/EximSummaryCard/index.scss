@import '@utils/main.scss';

$header-colors: (
  warning: $warning,
  success: $success,
  error: $error,
  information: $information,
  tertiary-light: $tertiary-light,
);

@each $key, $val in $header-colors {
  .summary-card-wrapper.#{$key} {
    .card-wrapper {
      .card-header {
        background-color: $val;
      }
    }
  }
}

.summary-card-wrapper {
  width: 30%; // we can adjust width from here
  .card-wrapper {
    width: 100%;
    min-width: 164px;
    .card-header {
      @include margin(0);
      color: $white;
      @include rfs(5px 5px 0 0, border-radius);
      @include padding(13px 15px);
      height: fit-content;
      letter-spacing: 0.5px;
      .summary-card-title-text {
        color: $white;
        font-size: $font-size-md;
        opacity: 0.8;
        font-weight: $font-weight-regular;
      }
      .summary-card-information {
        @include flex-item(row, space-between, center);
        font-weight: $font-weight-semi-bold;
        .summary-card-price {
          line-height: 30px;
          span {
            font-size: $font-size-xxxl;
          }
        }
        .info-icon {
          @include flex-item(row, center, center);

          background-color: unset;
          svg {
            width: 22px;
            height: 22px;
          }
        }
      }
    }

    .card-content {
      @include margin(0);
      @include padding(0);
      .summary-card-items {
        background-color: $white;
        @include padding(0 0 12px);
        @include rfs(0 0 5px 5px, border-radius);
        .summary-card-item {
          @include flex-item(row, space-between, center);
          font-size: $base-font-size;
          line-height: 20px;
          @include padding(12px 15px 0);
          p {
            color: $label-color;
          }
          p:nth-child(2) {
            color: $text-color;
          }
        }
      }
    }
  }
}
