import {formatAmount} from '@common/helpers';
import Card from '@submodules/Card/Card';
import {ReactNode, memo} from 'react';

import {InfoCircular} from '../../icons';
import './index.scss';

interface SummaryTitleHeaderProps {
  title: string;
  price: number | string;
  hasInformation: boolean;
}

interface SummaryContentItemProps {
  itemId: number | string;
  type: string;
  amount: number | string;
}

interface SummaryContentItemsProps {
  items: SummaryContentItemProps[];
}

interface EximSummaryCardProps {
  header: ReactNode;
  content: ReactNode;
  variant?: 'success' | 'warning' | 'error' | 'information' | 'tertiary-light';
}

export function SummaryTitleHeader({
  title,
  price,
  hasInformation,
}: SummaryTitleHeaderProps) {
  return (
    <>
      <div className='summary-card-title-text'>{title}</div>
      <div className='summary-card-information'>
        <div className='summary-card-price'>
          <span>{formatAmount(price, 'Rs.')}</span>
        </div>
        {hasInformation && (
          <div className='info-icon'>
            <InfoCircular width={20} height={20} />
          </div>
        )}
      </div>
    </>
  );
}

export function SummaryContentItems({items}: SummaryContentItemsProps) {
  return (
    <div className='summary-card-items'>
      {items.map(({itemId, type, amount}) => (
        <div key={itemId} className='summary-card-item'>
          <p>{type}</p>
          <p>{formatAmount(amount) || '0.00'}</p>
        </div>
      ))}
    </div>
  );
}

function EximSummaryCard(props: EximSummaryCardProps) {
  const {header, content, variant} = props;

  return (
    <div className={`summary-card-wrapper ${variant}`}>
      <Card header={header} content={content} />
    </div>
  );
}

EximSummaryCard.defaultProps = {
  variant: 'warning',
};

export default memo(EximSummaryCard);
