import {render} from '@testing-library/react';

import SummaryCard, {SummaryContentItems, SummaryTitleHeader} from '.';

describe('EximSummaryCard component', () => {
  test('EximSummaryCard component should render', () => {
    render(
      <SummaryCard
        header={
          <SummaryTitleHeader
            title='Total Outward Supply'
            price={0.01}
            hasInformation
          />
        }
        content={
          <SummaryContentItems
            items={[
              {itemId: 1, type: 'CGST', amount: 0.01},
              {itemId: 2, type: 'IGST', amount: 0.01},
              {itemId: 3, type: 'CGST', amount: 0.01},
              {itemId: 4, type: 'CGST', amount: 0.01},
            ]}
          />
        }
      />
    );
    expect(document.querySelector('.summary-card-wrapper')).toBeInTheDocument();
  });
});
