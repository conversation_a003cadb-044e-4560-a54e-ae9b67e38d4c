import {fireEvent, render, screen} from '@testing-library/react';

import EximCustomDropdown from '.';

test('Should render EximCustomDropdown', () => {
  const optionsList = [
    {
      id: '1',
      value: 'green',
      label: 'Green',
    },
  ];
  render(<EximCustomDropdown onSelect={jest.fn()} optionsList={optionsList} />);
  const dropdown = screen.getByTestId('dropdown');
  fireEvent.click(dropdown);

  const dropdownValue = document.getElementsByClassName('dropdown-item');
  fireEvent.click(dropdownValue[0]);
  expect(screen.getByText('Green')).toBeInTheDocument();
});
