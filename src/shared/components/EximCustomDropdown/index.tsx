import {IRegularDropdownData} from '@common/interfaces';
import useClickOutSide from '@modules/src/it/core/hooks/useClickOutside';
import {SolidDownAngle} from '@shared/icons';
import {useEffect, useState} from 'react';

import './index.scss';

export interface IProps {
  /**
   * To provide the unique id
   */
  id?: string;

  /**
   * To indicate the dropdown option list
   */
  optionsList: IRegularDropdownData[];

  /**
   * To get the dropdown placeholder value
   */
  placeholder?: string;

  /**
   * To check the drop down validation
   */
  isInvalid?: boolean;

  /**
   * To check the drop down validation
   */
  errorMessage?: string | boolean;

  /**
   * To check the drop down validation
   */
  label?: string;

  /**
   * It indicate the checkbox in disabled mode
   */
  isRequired?: boolean;

  /**
   *  Passing data-testid from parent to the input element
   */
  dataTestId?: string;

  /**
   * Onselect handle tracking the selected options
   */
  onSelect: (data: IRegularDropdownData) => void;

  /**
   * Default value fill in the field
   */
  defaultOption?: number | null;

  /**
   * To read the selected value
   */
  readOnly?: boolean;
}

export default function EximCustomDropdown({
  id,
  optionsList,
  placeholder,
  isInvalid,
  errorMessage,
  label,
  isRequired,
  dataTestId,
  onSelect,
  defaultOption,
  readOnly,
}: IProps) {
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<number | null>(
    defaultOption || null
  );

  const availableOptionsId = optionsList?.map((opt) => opt.id);

  const handleClick = (el: IRegularDropdownData) => {
    setSelectedItem(+el.id);
    setIsDropdownVisible(false);
    onSelect(el);
  };

  const domNode = useClickOutSide(() => setIsDropdownVisible(false));

  useEffect(() => {
    if (availableOptionsId?.includes(defaultOption || ''))
      setSelectedItem(defaultOption || null);
  }, [defaultOption, availableOptionsId]);

  useEffect(() => {
    setSelectedItem(defaultOption || null);
  }, [defaultOption]);

  return (
    <div
      id={id}
      className={`select-dropdown ${isInvalid && 'is-invalid'} ${
        readOnly && 'read-only'
      }`}>
      {label && (
        <div className={`dropdown-label ${isRequired && 'star'}`}>{label}</div>
      )}
      <div className='custom-dropdown' ref={domNode}>
        {/* Selected item data */}
        <div
          role='presentation'
          className={`custom-dropdown-selection ${
            isDropdownVisible ? 'visible' : ''
          } ${isDropdownVisible && selectedItem === null ? 'disable' : ''}
          ${readOnly && 'disabled-dropdown'}
        `}
          data-testid={dataTestId}
          onClick={() => setIsDropdownVisible(!isDropdownVisible)}>
          <span className={`${selectedItem === null && 'placeholder'}`}>
            {selectedItem !== null
              ? optionsList?.[selectedItem - 1]?.label || placeholder
              : placeholder}
          </span>
          {!readOnly ? <SolidDownAngle width={10} height={7} /> : null}
        </div>
        {/* Drop down items list */}
        {isDropdownVisible && !readOnly ? (
          <div className='item-holder'>
            {optionsList?.map((el: IRegularDropdownData) => (
              <div
                role='presentation'
                key={el.id}
                className={`dropdown-item
                ${+el.id - 1 === selectedItem ? 'selected' : ''}
              `}
                onClick={() => {
                  handleClick(el);
                }}>
                {el.label}
              </div>
            ))}
          </div>
        ) : null}
      </div>
      {/* Shows errors of dropdown */}
      {isInvalid && errorMessage ? (
        <div className='dropdown-error-message'>{errorMessage}</div>
      ) : null}
    </div>
  );
}

EximCustomDropdown.defaultProps = {
  id: 'customDropdown',
  isInvalid: false,
  errorMessage: '',
  label: '',
  isRequired: false,
  dataTestId: 'dropdown',
  placeholder: '',
  defaultOption: null,
  readOnly: false,
};
