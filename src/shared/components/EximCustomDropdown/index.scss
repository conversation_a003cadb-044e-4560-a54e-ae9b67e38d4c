@import '@utils/main.scss';

.is-invalid {
  .custom-dropdown {
    .custom-dropdown-selection {
      border-color: $error !important;
    }
  }
}

.select-dropdown {
  .placeholder {
    color: $text-color;
  }

  .dropdown-error-message {
    position: fixed;
    color: $error;
    font-size: $font-size-sm;
    color: $error;
  }

  .dropdown-label {
    font-size: $font-size-sm;
    color: $label-color;
    @include margin-bottom(4px);
  }

  .star::after {
    content: '*';
    color: $error;
    font-size: 20px;
    position: relative;
    top: 4px;
    left: 3px;
  }

  .custom-dropdown {
    height: 32px;
    max-width: 100%;
    position: relative;
    background: $primary-border;
    border-radius: 5px;

    .custom-dropdown-selection {
      @include flex-item(_, space-between, center);
      border: 1px solid $input-border;
      border-radius: 5px;
      height: 100%;
      @include padding(2px 10px);
      cursor: pointer;
      position: relative;
      color: $text-color;
      @include font-size(14px);
    }

    .custom-dropdown-selection.disabled-dropdown {
      border: none;
      cursor: default;
      @include padding-left(0);
    }

    .visible {
      border-radius: 5px 5px 0 0;
      svg {
        transform: rotate(180deg);
      }
    }

    .disable {
      color: $text-disable;
    }

    .item-holder {
      z-index: 5;
      width: 100%;
      border-radius: 0 0 5px 5px;
      border: 1px solid $input-border;
      background-color: $white;
      position: absolute;
      top: 100%;
      overflow: hidden;
    }

    .dropdown-item {
      @include padding(8px 8px 8px 10px);
      display: flex;
      align-items: center;
      cursor: pointer;
      color: $text-color;
      font-size: $font-size-sm;
      border-bottom: 1px solid $input-border;

      &:hover {
        background-color: $dropdown-hover;
        color: $white;
      }
    }
  }
}
.select-dropdown.read-only .disabled-dropdown {
  border: none;
}
