import {render, screen} from '@testing-library/react';

import Tooltip from '.';

describe('Test the Tooltip component', () => {
  function renderTooltipContent() {
    return <span>This is tooltip</span>;
  }

  it('render the EximTooltip default props', () => {
    render(
      <Tooltip content={renderTooltipContent()} direction='right'>
        hover me
      </Tooltip>
    );
    const tooltip = screen.getByTestId('tooltip-wrapper');
    expect(tooltip).toBeInTheDocument();
  });
});
