@import '@utils/main.scss';

$tooltip-box-shadow: 0px 3px 6px $btn-box-shadow-hover;

@mixin tooltip($className, $bgColor, $textColor) {
  .#{$className} {
    border-radius: 0px;
    color: $white;

    @if ($className== 'secondary') {
      .content-tag {
        box-shadow: $tooltip-box-shadow;
        border: 1px solid $text-light;
      }
    }

    @if ($bgColor) {
      .content-tag {
        background: $bgColor;
        color: $textColor;
        @include font-size($font-size-sm);
        @include padding(10px 10px);
      }
    } @else {
      .content-tag {
        background: $tertiary;
        @include font-size($font-size-sm);
        @include padding(10px 10px);
      }
    }
  }
}

/* Wrapping */
.tooltip-wrapper {
  @include tooltip('tooltip-tip', _, _);
  @include tooltip('primary', $tertiary, $white);
  @include tooltip('secondary', $white, $text-color);
  @include tooltip('tertiary', $table-head-1, $white);

  .tip.primary {
    background: $tertiary;
    border: none;
  }

  .tip.secondary {
    background: $white;
    box-shadow: $tooltip-box-shadow;
    border: 1px solid $white;
    border-bottom-color: $text-mute;
    border-right-color: $text-mute;
  }

  .tip.tertiary {
    background: $table-head-1;
  }

  .tooltip-arrow.tooltip-top {
    bottom: 36px;
  }

  .tip-top {
    top: -23px;
    left: 28%;
  }
}
