import Tooltip, {TooltipProps} from '@submodules/Tooltip/Tooltip';
import {memo} from 'react';

import './index.scss';

interface EximTooltipProps extends TooltipProps {
  /** this variant is only specific for Exim hero bcz based on variant we changed the colors for tooltip here */
  variant?: 'primary' | 'secondary' | 'tertiary';
}

function EximTooltip(props: EximTooltipProps) {
  const {
    content,
    direction,
    children,
    variant,
    behavior,
    className,
    dataTestId,
  } = props;
  return (
    <Tooltip
      direction={direction}
      content={content}
      pointerClassName={variant}
      behavior={behavior}
      className={className}
      dataTestId={dataTestId}>
      {children}
    </Tooltip>
  );
}

EximTooltip.defaultProps = {
  variant: 'primary',
};

export default memo(EximTooltip);
