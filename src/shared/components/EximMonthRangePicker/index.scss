@import '@utils/main.scss';

.custom-base-date-picker {
  min-width: 180px;
  position: relative;

  .input-date-container {
    @include rfs(5px, border-radius);
    @include flex-item(_, flex-start, center);
    font-size: $font-size-sm;
    color: $title-color;
    background-color: $primary-border-light;
    cursor: pointer;
    position: relative;
    .calendar-btn {
      background: $tertiary;
      @include rfs(5px 0px 0px 5px, border-radius);
      @include padding(8px 12px 6px);
      cursor: pointer;
    }
    .date-input {
      min-width: 142px;
      @include padding-left(12px);
    }
    .date-input.placeholder {
      opacity: 0.6;
    }
    .triangle {
      position: absolute;
      top: 34px;
      left: 6px;
      z-index: 2;
      &:after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        @include margin-top(2px);
        @include margin-left(2px);
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid $white;
      }
      &:before {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border-left: 12px solid transparent;
        border-right: 12px solid transparent;
        border-bottom: 12px solid $primary-border;
      }
    }
  }
  .calendar-container {
    z-index: 1;
    position: absolute;
    width: 495px;
    height: 235px;
    border: 1px solid $primary-border;
    background-color: $white;
    @include padding(12px);
    @include rfs(5px, border-radius);
    top: 44px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 40px;

    .date-container {
      .year-container {
        @include flex-item(_, space-between, center);
        font-size: $font-size-sm;
        font-weight: $font-weight-semi-bold;
        @include padding(12px 8px);
        border-bottom: 1px solid $primary-border;
        span {
          cursor: pointer;
        }
        span.left-arrow {
          transform: rotate(180deg);
        }
        span.disabled {
          pointer-events: none;
          opacity: 0.6;
        }
      }
      .months-container,
      .years-container {
        display: grid;
        justify-items: center;
        align-items: center;
        gap: 14px;
        font-size: $font-size-sm;
        @include margin-top(28px);
        span {
          @include padding(6px 9px);
          @include rfs(5px, border-radius);
          cursor: pointer;
          color: $accordion-title;
        }
      }
      .months-container {
        grid-template-columns: repeat(4, 1fr);
        span {
          max-width: 40px;
        }
        span.month-in-range {
          background-color: $primary-border-light;
        }
        span.selected-month {
          background-color: $tertiary;
          color: $white;
        }
        span.disabled {
          background-color: transparent;
          color: $accordion-title;
          pointer-events: none;
          opacity: 0.6;
        }
      }
      .years-container {
        grid-template-columns: repeat(3, 1fr);
        span {
          min-width: 40px;
        }
        span.selected-year {
          background-color: $tertiary;
          color: $white;
        }
        span.disabled {
          pointer-events: none;
          opacity: 0.6;
        }
      }
    }
  }

  .calendar-container.bottom-left {
    left: 0;
    animation: none;
  }
  .calendar-container.bottom-right {
    right: 0;
    animation: none;
  }

  .input-date-container.disabled {
    background-color: $disable-color;
    pointer-events: none;
    .calendar-btn {
      background: $label-color;
    }
    .date-input {
      background: $disable-color;
    }
  }

  .date-range-error-message {
    position: absolute;
    top: 36px;
    font-size: $font-size-sm;
    color: $error;
  }

  .input-date-container.invalid-date {
    border: 1px solid $error;
    .calendar-btn {
      background: $error;
    }
  }
}
