import {getDateToCompare, parseDateToShow} from '@common/helpers';
import {Calendar, SolidRightTriangle} from '@shared/icons';
import {ReactNode, useEffect, useRef, useState} from 'react';

import './index.scss';

export interface DatePickerProps {
  /* To define id of the date picker */
  id: string;
  /* It defines the disable of the date picker */
  disabled?: boolean;
  /* It defines the default value of the date picker */
  calendarIcon?: ReactNode;
  /* It defines the start date of the date picker */
  minDate: string; // format: 01/2023 ==> month and year
  /* It defines the end date of the date picker */
  maxDate?: string; // format: 01/2023 ==> month and year
  /* It defines to set selected date of the date picker */
  onSelect: (start: string, end: string) => void;
  /* It defines default start date of the date picker */
  defaultStartDate?: string; // format: 01/2023 ==> month and year
  /* It defines default end date of the date picker */
  defaultEndDate?: string; // format: 01/2023 ==> month and year
  /* It defines default position the date picker */
  position?: 'bottom-right' | 'bottom-left';
  /* It defines the validation of the date */
  isInvalid?: boolean;
  /* It defines error message of the date picker */
  errorMessage?: string;
}

const months = [
  {title: 'Jan', value: '01'},
  {title: 'Feb', value: '02'},
  {title: 'Mar', value: '03'},
  {title: 'Apr', value: '04'},
  {title: 'May', value: '05'},
  {title: 'Jun', value: '06'},
  {title: 'Jul', value: '07'},
  {title: 'Aug', value: '08'},
  {title: 'Sep', value: '09'},
  {title: 'Oct', value: '10'},
  {title: 'Nov', value: '11'},
  {title: 'Dec', value: '12'},
];

function EximMonthRangePicker(props: DatePickerProps) {
  const {
    id,
    minDate,
    maxDate,
    disabled,
    onSelect,
    calendarIcon,
    defaultStartDate,
    defaultEndDate,
    position,
    isInvalid,
    errorMessage,
  } = props;

  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();

  const datePickerRef = useRef<HTMLDivElement | null>(null);

  const [minMonth, minYear] = minDate.split('/');
  const [maxMonth, maxYear] = maxDate?.split('/') || [
    `${currentMonth}`,
    `${currentYear}`,
  ];
  const [defaultFromMonth, defaultFromYear] = defaultStartDate?.split('/') || [
    '',
    '',
  ];
  const [defaultToMonth, defaultToYear] = defaultEndDate?.split('/') || [
    '',
    '',
  ];

  const [currentFromYear, setCurrentFromYear] = useState<string>(
    defaultFromYear || minYear
  );
  const [currentToYear, setCurrentToYear] = useState<string>(
    defaultToYear || maxYear
  );

  const [selectedFromMonth, setSelectedFromMonth] = useState(
    defaultFromMonth || minMonth
  );
  const [selectedToMonth, setSelectedToMonth] = useState(defaultToMonth);
  const [selectedFromYear, setSelectedFromYear] = useState(
    currentFromYear || minYear
  );
  const [selectedToYear, setSelectedToYear] = useState(currentToYear);

  const [currentFromYearRange, setCurrentFromYearRange] = useState(
    `${+currentFromYear - 8}-${currentFromYear}`
  );
  const [currentToYearRange, setCurrentToYearRange] = useState(
    `${+currentToYear - 8}-${currentToYear}`
  );

  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [isShowFromYear, setIsShowFromYear] = useState(false);
  const [isShowToYear, setIsShowToYear] = useState(false);

  const handleChangeDate = (month: string) => {
    setSelectedToMonth(month);
    setSelectedToYear(currentToYear);
    setIsDatePickerOpen(false);

    const startDate = `${selectedFromMonth}-${selectedFromYear}`;
    const endDate = `${month}-${currentToYear}`;
    onSelect(startDate, endDate);
  };

  const handlePreviousYear = (type: 'FROM_DATE' | 'TO_DATE') => {
    if (type === 'FROM_DATE') {
      if (isShowFromYear) {
        const [first, last] = currentFromYearRange.split('-').map(Number);
        if (first <= +minYear) return;
        setCurrentFromYearRange(`${first - 9}-${last - 9}`);
      } else if (+currentFromYear > +minYear) {
        setCurrentFromYear((prev) => `${Number(prev) - 1}`);
      }
    } else if (type === 'TO_DATE') {
      if (isShowToYear) {
        const [first, last] = currentToYearRange.split('-').map(Number);
        if (first <= +maxYear) return;
        setCurrentToYearRange(`${first - 9}-${last - 9}`);
      } else if (+currentToYear > +selectedFromYear) {
        setCurrentToYear((prev) => `${Number(prev) - 1}`);
      }
    }
  };
  const handleNextYear = (type: 'FROM_DATE' | 'TO_DATE') => {
    if (type === 'FROM_DATE') {
      if (isShowFromYear) {
        const [first, last] = currentFromYearRange.split('-').map(Number);
        if (last >= currentYear) return;
        setCurrentFromYearRange(`${first + 9}-${last + 9}`);
      } else if (+currentFromYear < +maxYear) {
        setCurrentFromYear((prev) => `${Number(prev) + 1}`);
      }
    } else if (type === 'TO_DATE') {
      if (isShowToYear) {
        const [first, last] = currentToYearRange.split('-').map(Number);
        if (last >= currentYear) return;
        setCurrentToYearRange(`${first + 9}-${last + 9}`);
      } else if (type === 'TO_DATE' && +currentToYear < +maxYear) {
        setCurrentToYear((prev) => `${Number(prev) + 1}`);
      }
    }
  };
  const handleShowYears = (type: 'FROM_DATE' | 'TO_DATE') => {
    if (type === 'FROM_DATE') {
      setIsShowFromYear((prev) => !prev);
    } else if (type === 'TO_DATE') {
      setIsShowToYear((prev) => !prev);
    }
  };

  const generateYears = (yearCount: number, year: number) => {
    const years = [];
    let startYear = year - yearCount;
    if (startYear < 1) startYear = 1;

    for (let i = 1; i <= yearCount; i += 1) {
      if (startYear + i <= year) {
        years.push(startYear + i);
      } else break;
    }
    return years;
  };

  const disabledArrow = (
    type: 'FROM_DATE' | 'TO_DATE',
    arrow: 'LEFT' | 'RIGHT'
  ) => {
    if (type === 'FROM_DATE') {
      if (isShowFromYear) {
        if (arrow === 'LEFT' && +currentFromYearRange.split('-')[0] <= +minYear)
          return true;
        if (
          arrow === 'RIGHT' &&
          +currentFromYearRange.split('-')[1] >= +maxYear
        )
          return true;
      } else {
        if (arrow === 'LEFT' && +currentFromYear <= +minYear) return true;
        if (arrow === 'RIGHT' && +currentFromYear >= +maxYear) return true;
      }
    }
    if (type === 'TO_DATE') {
      if (isShowToYear) {
        if (arrow === 'LEFT' && +currentToYearRange.split('-')[0] <= +minYear)
          return true;
        if (arrow === 'RIGHT' && +currentToYearRange.split('-')[1] >= +maxYear)
          return true;
      } else {
        if (arrow === 'LEFT' && +currentToYear <= +selectedFromYear)
          return true;
        if (arrow === 'RIGHT' && +currentToYear >= +maxYear) return true;
      }
    }
    return false;
  };

  // Event listener to handle clicks outside the date container
  const handleOutsideClick = (e: MouseEvent) => {
    const targetElement = e.target as HTMLElement;

    if (
      datePickerRef?.current &&
      !datePickerRef?.current?.contains(e.target as Node) &&
      !targetElement?.className?.toString()?.includes('year-text')
    ) {
      setIsDatePickerOpen(false);
    }
  };
  // Attach the event listener when the component mounts
  useEffect(() => {
    document.addEventListener('click', handleOutsideClick);
    // Clean up the event listener when the component unmounts
    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, []);

  // INFO: Update the date on UI if found any change in date
  useEffect(() => {
    if (defaultStartDate && defaultEndDate) {
      const [fromMonth, fromYear] = defaultStartDate.split('/');
      const [toMonth, toYear] = defaultEndDate.split('/');
      setSelectedFromMonth(fromMonth);
      setSelectedFromYear(fromYear);
      setSelectedToMonth(toMonth);
      setSelectedToYear(toYear);
    }
  }, [defaultStartDate, defaultEndDate]);

  return (
    <div id={id} className='custom-base-date-picker' ref={datePickerRef}>
      <div
        role='presentation'
        className={`input-date-container ${disabled ? 'disabled' : ''} ${
          isInvalid ? 'invalid-date' : ''
        }`}
        onClick={() => setIsDatePickerOpen((prev) => !prev)}>
        <button type='button' className='calendar-btn'>
          {calendarIcon}
          {isDatePickerOpen ? <span className='triangle' /> : null}
        </button>
        {selectedFromMonth &&
        selectedFromYear &&
        selectedToMonth &&
        selectedToYear ? (
          <div className='date-input'>{`${parseDateToShow(
            selectedFromMonth || '',
            selectedFromYear || ''
          )} - ${parseDateToShow(
            selectedToMonth || '',
            selectedToYear || ''
          )}`}</div>
        ) : (
          <div className='date-input placeholder'>MM YYYY - MM YYYY</div>
        )}
      </div>
      {isInvalid && errorMessage ? (
        <p className='date-range-error-message'>{errorMessage}</p>
      ) : null}

      {isDatePickerOpen ? (
        <div className={`calendar-container ${position}`}>
          <div className='date-container'>
            <div className='year-container'>
              <span
                role='presentation'
                className={`left-arrow ${
                  disabledArrow('FROM_DATE', 'LEFT') ? 'disabled' : ''
                }`}
                onClick={() => handlePreviousYear('FROM_DATE')}>
                <SolidRightTriangle />
              </span>
              <span
                role='presentation'
                onClick={() => handleShowYears('FROM_DATE')}>
                {isShowFromYear ? currentFromYearRange : currentFromYear}
              </span>
              <span
                role='presentation'
                className={`right-arrow ${
                  disabledArrow('FROM_DATE', 'RIGHT') ? 'disabled' : ''
                }`}
                onClick={() => handleNextYear('FROM_DATE')}>
                <SolidRightTriangle />
              </span>
            </div>
            {isShowFromYear ? (
              <div className='years-container'>
                {generateYears(9, +currentFromYearRange.split('-')[1])?.map(
                  (year: number) => {
                    const isDisabled =
                      year > currentYear || year < +minYear || year > +maxYear
                        ? 'disabled'
                        : '';
                    const isSelected =
                      selectedFromYear === `${year}` ? 'selected-year' : '';
                    return (
                      <span
                        role='presentation'
                        onClick={() => {
                          setSelectedFromYear(`${year}`);
                          setCurrentFromYear(`${year}`);
                          setIsShowFromYear(false);
                          if (+currentToYear < year) {
                            setCurrentToYear(`${year}`);
                          }
                        }}
                        className={`year-text ${isDisabled} ${isSelected} `}
                        key={`toDateYear${year}`}>
                        {year}
                      </span>
                    );
                  }
                )}
              </div>
            ) : (
              <div className='months-container'>
                {months.map(({title, value}) => {
                  const isSelected =
                    selectedFromMonth === value &&
                    selectedFromYear === currentFromYear
                      ? 'selected-month'
                      : '';
                  const isDisabled =
                    getDateToCompare(value, currentFromYear) <
                      getDateToCompare(minMonth, minYear) ||
                    getDateToCompare(value, currentFromYear) >
                      getDateToCompare(maxMonth, maxYear)
                      ? 'disabled'
                      : '';
                  const inRange =
                    selectedFromMonth &&
                    selectedFromMonth <= value &&
                    selectedFromYear <= currentFromYear
                      ? 'month-in-range'
                      : '';
                  return (
                    <span
                      role='presentation'
                      onClick={() => {
                        setSelectedFromMonth(value);
                        setSelectedFromYear(currentFromYear);
                        if (+currentToYear < +currentFromYear) {
                          setCurrentToYear(currentFromYear);
                        }
                        // INFO: Resetting the from date
                        setSelectedToMonth('');
                        setSelectedToYear('');
                      }}
                      className={`${isDisabled} ${isSelected} ${inRange}`}
                      key={`fromDateMonth${title}`}>
                      {title}
                    </span>
                  );
                })}
              </div>
            )}
          </div>
          <div className='date-container'>
            <div className='year-container'>
              <span
                role='presentation'
                className={`left-arrow ${
                  disabledArrow('TO_DATE', 'LEFT') ? 'disabled' : ''
                }`}
                onClick={() => handlePreviousYear('TO_DATE')}>
                <SolidRightTriangle />
              </span>
              <span
                role='presentation'
                onClick={() => handleShowYears('TO_DATE')}>
                {isShowToYear ? currentToYearRange : currentToYear}
              </span>
              <span
                role='presentation'
                className={`right-arrow ${
                  disabledArrow('TO_DATE', 'RIGHT') ? 'disabled' : ''
                }`}
                onClick={() => handleNextYear('TO_DATE')}>
                <SolidRightTriangle />
              </span>
            </div>
            {isShowToYear ? (
              <div className='years-container'>
                {generateYears(9, +currentToYearRange.split('-')[1]).map(
                  (year) => {
                    const isDisabled =
                      year > currentYear ||
                      year < +minYear ||
                      year > +maxYear ||
                      year < +selectedFromYear
                        ? 'disabled'
                        : '';
                    const isSelected =
                      selectedToYear === `${year}` ? 'selected-year' : '';
                    return (
                      <span
                        role='presentation'
                        onClick={() => {
                          setSelectedToYear(`${year}`);
                          setCurrentToYear(`${year}`);
                          setIsShowToYear(false);
                        }}
                        className={`year-text ${isDisabled} ${isSelected}`}
                        key={`toDateYear${year}`}>
                        {year}
                      </span>
                    );
                  }
                )}
              </div>
            ) : (
              <div className='months-container'>
                {months.map(({title, value}) => {
                  const isSelected =
                    selectedToMonth === value &&
                    selectedToYear === currentToYear
                      ? 'selected-month'
                      : '';
                  const isDisabled =
                    getDateToCompare(value, currentToYear) >
                      getDateToCompare(maxMonth, maxYear) ||
                    getDateToCompare(value, currentToYear) <
                      getDateToCompare(selectedFromMonth, selectedFromYear)
                      ? 'disabled'
                      : '';
                  const inRange =
                    getDateToCompare(selectedToMonth, selectedToYear) >=
                    getDateToCompare(value, currentToYear)
                      ? 'month-in-range'
                      : '';
                  return (
                    <span
                      role='presentation'
                      onClick={() => handleChangeDate(value)}
                      className={`${isDisabled} ${isSelected} ${inRange}`}
                      key={`toDateMonth${title}`}>
                      {title}
                    </span>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      ) : null}
    </div>
  );
}

EximMonthRangePicker.defaultProps = {
  disabled: false,
  maxDate: null,
  defaultStartDate: null,
  defaultEndDate: null,
  calendarIcon: <Calendar />,
  position: 'bottom-right',
  isInvalid: false,
  errorMessage: '',
};

export default EximMonthRangePicker;
