import {Api, ApiAction} from '@common/constants/index';
import {removeUndefinedKeys} from '@common/helpers';
import {
  IMoowrBomReviews,
  IMoowrJobWorkViewDtls,
  IMoowrOutwardRegViewDtls,
  IMoowrProductItem,
  IMoowrRectifySummary,
  IMoowrViewPartialOutward,
} from '@common/interfaces';
import {get, post, put} from '@core/api/axios';
import {AxiosResponse} from 'axios';

const {
  TXN,
  EDIT,
  DTLS,
  HISTORY,
  DISCARD,
  MOOWR_FILING,
  MOOWR_SERVICE,
  PARTIAL_OUTWARD_RECORDS,
  PARTIAL_OUTWARD_RECORDS_REVIEW,
  BOM_LIST,
  SAVE_BOM_REVIEW,
  SELECTION_SUMMARY,
  EXPORT,
  REPORTS,
  DOWNLOAD,
  LARGE_FILE,
  INVOICES,
  FILE_UPLOAD_TEMPLATE,
  UPLOAD,
  FILE_PROCESSING_DETAILS,
  INWARD_LIST,
  INWARD_ITEM_LIST,
  SAVE_INWARD_REVIEW,
  PROCESS_CONSUMPTION,
  CONSUMPTION_SUMMARY,
  FREEZE,
  BOM_DETAILS,
  OUTWARD_STOCK_DETAILS,
  OUTWARD_DETAILS,
  JOB_WORK_DETAILS,
  SUMMARY,
  RECON,
  STATUS,
  RECTIFICATION,
} = Api;
const {
  CREATE_MOOWR_RETURN_TXN,
  EDIT_MOOWR_RETURN_TXN,
  GET_MOOWR_RETURN_TXN_DTLS,
  DISCARD_MOOWR_RETURN_TXN,
  GET_MOOWR_RETURN_TXN_HISTORY,
  GET_PARTIAL_OUTWARD_RECORDS,
  SAVE_PARTIAL_OUTWARD_SELECTION,
  GET_MOOWR_RETURN_TXN_SELECT_SUM,
  SAVE_PARTIAL_OUTWARD_SELECTION_REVIEW,
  SAVE_BOM_REVIEW: SAVE_BOM_REVIEW_DATA,
  GET_TXN_STATUS,
  EXPORT_REPORT,
  DOWNLOAD_FILE_TEMPLATE,
  FILE_UPLOAD,
  FILE_UPLOAD_HISTORY,
  FILE_PROCESSING_DTLS,
  GET_INWARD_RECORD_LIST,
  GET_INWARD_RECORD_ITEM_LIST,
  SAVE_INWARD_REVIEW: SAVE_INWARD_REVIEW_DATA,
  PROCESS_CONSUMPTION: PROCESS_CONSUMPTION_DATA,
  CONSUMPTION_SUMMARY: CONSUMPTION_SUMMARY_DATA,
  FREEZE_TXN,
  MOOWR_GET_BOM_DETAILS,
  MOOWR_GET_INVOICES,
  MOOWR_GET_INVOICES_SUMMARY,
  DISCARD_INVALID_FILE_TXN,
  MOOWR_GET_OUTWARD_STOCK_DETAILS,
  MOOWR_GET_OUTWARD_RECORD_DETAILS,
  MOOWR_GET_JOB_WORK_DETAILS,
  EDIT_INVOICES,
  MOOWR_DELETE_INVOICES,
  DOWNLOAD_REPORT,
  RECON_SUMMARY,
  GENERATE_TRANSACTIONAL_REPORT,
  GET_RECTIFICATION_SUMM,
  RECTIFY_STOCK,
} = ApiAction;

export interface ICreateTransaction {
  txn_name: string;
  txn_type: string;
  start_prd: string;
  end_prd: string;
}

export interface IApiData {
  pan: string;
  email?: string;
  txnId?: string;
  refId?: string;
  startPeriod?: string;
  endPeriod?: string;
  selected?: boolean;
  searchKey?: string;
  searchValue?: string;
  sortBy?: string;
  sortingOrder?: 1 | -1;
  selectAll?: boolean;
  partialSave?: boolean;
  reportType?: string;
  fileId?: string;
  fileType?: string;
  invType?: string;
  errorFileId?: string;
  prodCode?: string;
  bomVersion?: number;
}

export const getProcessedDetails = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${FILE_PROCESSING_DETAILS}?action=${FILE_PROCESSING_DTLS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const downloadExcelTemplate = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${FILE_UPLOAD_TEMPLATE}${DOWNLOAD}?file-type=${data.fileType}&action=${DOWNLOAD_FILE_TEMPLATE}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const uploadFileData = async (data: IApiData, file: File) => {
  const attachedFiles = new FormData();
  attachedFiles.append(`attached_files`, file);

  const headers = {
    pan: data.pan,
    email: data.email,
    'file-type': data.fileType || '',
    'error-file-id': data.errorFileId,
    'txn-id': data.txnId,
    'uploaded-file-name': file.name,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await post(
    `${MOOWR_SERVICE}${INVOICES}${UPLOAD}?action=${FILE_UPLOAD}`,
    attachedFiles,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getUploadHistory = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${UPLOAD}${HISTORY}?page=${page}&limit=${limit}&action=${FILE_UPLOAD_HISTORY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const invoicesSummary = async (data: IApiData) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'file-type': data.fileType,
    'start-period': data.startPeriod,
    'end-period': data.endPeriod,
    'txn-id': data.txnId,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${SUMMARY}?action=${MOOWR_GET_INVOICES_SUMMARY}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getInvoices = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'txn-id': data.txnId,
    'inv-type': data.invType,
    'file-type': data.fileType,
    'start-period': data.startPeriod,
    'end-period': data.endPeriod,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}?page-no=${page}&limit=${limit}&action=${MOOWR_GET_INVOICES}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const discardInvTransaction = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${INVOICES}${DISCARD}?action=${DISCARD_INVALID_FILE_TXN}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'file-type': data.fileType as string,
        'txn-id': data.txnId as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const getOutwardStockInvDtls = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${OUTWARD_STOCK_DETAILS}?page-no=${page}&limit=${limit}&action=${MOOWR_GET_OUTWARD_STOCK_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'ref-id': data.refId,
      },
    }
  );
  return response as AxiosResponse;
};

export const getOutwardRegisterInvDtls = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${OUTWARD_DETAILS}?page-no=${page}&limit=${limit}&action=${MOOWR_GET_OUTWARD_RECORD_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        refId: data.refId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getJobWorkInvDtls = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${JOB_WORK_DETAILS}?page-no=${page}&limit=${limit}&action=${MOOWR_GET_JOB_WORK_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email as string,
        'ref-id': data.refId as string,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveEditInvoice = async (
  data: IApiData,
  body:
    | IMoowrViewPartialOutward
    | IMoowrOutwardRegViewDtls
    | IMoowrJobWorkViewDtls
) => {
  const response = await put(
    `${MOOWR_SERVICE}${INVOICES}${EDIT}?txn-id=${data.txnId}&action=${EDIT_INVOICES}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email as string,
        'file-type': data.fileType as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const deleteInvoices = async (
  data: IApiData,
  isDeleteAll: boolean,
  refIds: string[]
) => {
  const response = await put(
    `${MOOWR_SERVICE}${INVOICES}?deleteAllInvoices=${isDeleteAll}&txn-id=${data.txnId}&action=${MOOWR_DELETE_INVOICES}`,
    refIds,
    {
      headers: {
        pan: data.pan,
        email: data.email as string,
        'file-type': data.fileType as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const createTransaction = async (
  pan: string,
  payload: ICreateTransaction
) => {
  const response = await post(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}/?action=${CREATE_MOOWR_RETURN_TXN}`,
    payload,
    {headers: {pan}}
  );
  return response as AxiosResponse;
};

export const editTransaction = async (
  pan: string,
  txnId: string,
  payload: ICreateTransaction
) => {
  const response = await post(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${EDIT}?action=${EDIT_MOOWR_RETURN_TXN}`,
    payload,
    {
      headers: {pan, 'txn-id': txnId},
    }
  );
  return response as AxiosResponse;
};

export const transactionDetails = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${DTLS}?action=${GET_MOOWR_RETURN_TXN_DTLS}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const discardTransaction = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${DISCARD}?action=${DISCARD_MOOWR_RETURN_TXN}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const transactionHistory = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${HISTORY}?page-no=${page}&limit=${limit}&action=${GET_MOOWR_RETURN_TXN_HISTORY}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId as string,
        'search-key': data.searchKey as string,
        'search-value': data.searchValue as string,
        'sort-by': data.sortBy as string,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getMoowrProductList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${PARTIAL_OUTWARD_RECORDS}?page-no=${page}&limit=${limit}&action=${GET_PARTIAL_OUTWARD_RECORDS}`,
    {
      headers: {
        pan: data.pan,
        selected: data.selected as boolean,
        'txn-id': data.txnId || '',
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveMoowrProductList = async (
  data: IApiData,
  body: IMoowrProductItem[]
) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${PARTIAL_OUTWARD_RECORDS}?action=${SAVE_PARTIAL_OUTWARD_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        'select-all': data.selectAll as boolean,
        'partial-save': data.partialSave as boolean,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getSelectionSummary = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${SELECTION_SUMMARY}?action=${GET_MOOWR_RETURN_TXN_SELECT_SUM}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveSelectionSummary = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${PARTIAL_OUTWARD_RECORDS_REVIEW}?action=${SAVE_PARTIAL_OUTWARD_SELECTION_REVIEW}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const reportsExportHistory = async (pan: string) => {
  const response = await get(
    `${MOOWR_SERVICE}${REPORTS}${EXPORT}${HISTORY}?&action=${GET_TXN_STATUS}`,
    {
      headers: {
        pan,
        'latest-exports': true,
      },
    }
  );
  return response as AxiosResponse;
};

export const reportsExport = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${REPORTS}${EXPORT}?&action=${EXPORT_REPORT}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'report-type': data.reportType || '',
      },
    }
  );
  return response as AxiosResponse;
};

// INFO: This API is not in use for now, keeping it for future reference
export const reportsDownload = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${REPORTS}${DOWNLOAD}?action=${EXPORT_REPORT}`,
    {
      headers: {
        pan: data.pan,
        'file-id': data.fileId || '',
        'should-file-return': false,
        'report-type': data.reportType,
      },
    }
  );
  return response as AxiosResponse;
};

export const downloadLargeFile = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${REPORTS}${DOWNLOAD}${LARGE_FILE}?action=${DOWNLOAD_REPORT}`,
    {
      headers: {
        pan: data.pan,
        'file-id': data.fileId || '',
        'report-type': data.reportType,
        Accept: 'application/octet-stream',
      },
      responseType: 'blob',
    }
  );
  return response as Blob;
};

export const getBomList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${BOM_LIST}?page-no=${page}&limit=${limit}&action=${GET_PARTIAL_OUTWARD_RECORDS}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getBomDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    'txn-id': data.txnId || '',
    'search-key': data.searchKey || '',
    'search-value': data.searchValue || '',
    'sort-by': data.sortBy || '',
    'sorting-order': data.sortingOrder || '',
    'prod-code': data.prodCode || '',
    'bom-version': data.bomVersion || '',
  };

  const response = await get(
    `${MOOWR_SERVICE}${INVOICES}${BOM_DETAILS}?page-no=${page}&limit=${limit}&action=${MOOWR_GET_BOM_DETAILS}`,
    {
      headers,
    }
  );
  return response as AxiosResponse;
};

export const saveBomList = async (data: IApiData, body: IMoowrBomReviews[]) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${SAVE_BOM_REVIEW}?action=${SAVE_BOM_REVIEW_DATA}`,
    body,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'partial-save': data.partialSave as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

export const getInwardList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${INWARD_LIST}?page-no=${page}&limit=${limit}&action=${GET_INWARD_RECORD_LIST}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getInwardItemList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${INWARD_ITEM_LIST}?page-no=${page}&limit=${limit}&action=${GET_INWARD_RECORD_ITEM_LIST}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveInwardReview = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${SAVE_INWARD_REVIEW}?action=${SAVE_INWARD_REVIEW_DATA}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const processConsumption = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${PROCESS_CONSUMPTION}?action=${PROCESS_CONSUMPTION_DATA}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const consumptionSummary = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${CONSUMPTION_SUMMARY}?page-no=${page}&limit=${limit}&action=${CONSUMPTION_SUMMARY_DATA}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const freezeTransaction = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${MOOWR_FILING}${TXN}${FREEZE}?action=${FREEZE_TXN}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

// INFO: MOOWR Reconciliation APIs
export const createReconciliation = async (pan: string, month: string) => {
  const response = await put(
    `${MOOWR_SERVICE}${RECON}?month=${month}&action=${GENERATE_TRANSACTIONAL_REPORT}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {pan, month},
    }
  );
  return response as AxiosResponse;
};

export const getReconStatus = async (pan: string, month: string) => {
  const response = await get(
    `${MOOWR_SERVICE}${RECON}${STATUS}?month=${month}&action=${GET_TXN_STATUS}`,
    {
      headers: {pan},
    }
  );
  return response as AxiosResponse;
};

export const reconSummary = async (pan: string, month: string) => {
  const response = await get(
    `${MOOWR_SERVICE}${RECON}${SUMMARY}?month=${month}&action=${RECON_SUMMARY}`,
    {
      headers: {pan},
    }
  );
  return response as AxiosResponse;
};

export const reconDetails = async (
  data: IApiData,
  month: string,
  page: number,
  limit: number,
  reconStatus: string
) => {
  const response = await get(
    `${MOOWR_SERVICE}${RECON}${DTLS}?month=${month}&page-no=${page}&limit=${limit}&action=${RECON_SUMMARY}`,
    {
      headers: {
        pan: data.pan,
        'recon-status': reconStatus,
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const reconRectificationSummary = async (data: IApiData) => {
  const response = await get(
    `${MOOWR_SERVICE}${RECON}${RECTIFICATION}${SUMMARY}?action=${GET_RECTIFICATION_SUMM}`,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const reconRectification = async (
  data: IApiData,
  summary: {
    consumptionDtls: IMoowrRectifySummary;
    rectificationDtls: IMoowrRectifySummary;
    outwardRegDtls: IMoowrRectifySummary;
  }
) => {
  const response = await post(
    `${MOOWR_SERVICE}${RECON}${RECTIFICATION}?action=${RECTIFY_STOCK}`,
    summary,
    {
      headers: {
        pan: data.pan,
        'txn-id': data.txnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const freezeReconciliation = async (data: IApiData) => {
  const response = await put(
    `${MOOWR_SERVICE}${RECON}${FREEZE}?txn-id=${data.txnId}&action=${GENERATE_TRANSACTIONAL_REPORT}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {pan: data.pan},
    }
  );
  return response as AxiosResponse;
};
