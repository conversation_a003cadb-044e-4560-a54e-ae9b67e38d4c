import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {IMoowrTransFinalSummary} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  FINAL_SUMMARY_TRANSACTION_TABLE_HEADER,
  FINAL_SUMMARY_TRANSACTION_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {ChangeEvent} from 'react';

import './index.scss';

interface IFinalSummary {
  handleSearchQuery: (event: ChangeEvent<HTMLInputElement>) => void;
  handleShowEntries: (event: ChangeEvent<HTMLSelectElement>) => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
  handlePageChange: (pageNumber: string | number) => void;
  handleSearchKey: (value: string) => void;
  finalSummaryData: IMoowrTransFinalSummary[];
  page: number;
  searchQuery: string;
  showEntries: string;
  totalRecords: number;
  searchKey: string;
}

function FinalSummary({
  handleSearchQuery,
  handleShowEntries,
  finalSummaryData,
  page,
  searchQuery,
  showEntries,
  totalRecords,
  handlePageChange,
  handleSearchKey,
  handleSortBy,
  searchKey,
}: IFinalSummary) {
  return (
    <div className='final-summary-table-wrapper'>
      <EximTypography
        variant='h3'
        fontWeight='semi-bold'
        classNames='table-title'>
        Final Summary
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={FINAL_SUMMARY_TRANSACTION_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='final-summary-selection'>
        <TableHeader
          mainHeader={FINAL_SUMMARY_TRANSACTION_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {finalSummaryData?.length > 0 ? (
          <TableBody className='final-summary-table-body'>
            {finalSummaryData?.map((item) => (
              <TableRow key={`${item.txn_id}`}>
                <TableCell>{item.sb_no ?? '-'}</TableCell>
                <TableCell>{item.sb_date ?? '-'}</TableCell>
                <TableCell>{item.prod_code ?? '-'}</TableCell>
                <TableCell>{item.total_sale_qty ?? '-'}</TableCell>
                <TableCell>{item.total_item_qty_consumed ?? '-'}</TableCell>
                <TableCell>{item.total_item_qty_balance ?? '-'}</TableCell>
                <TableCell>{`${item.total_bcd_amt}`}</TableCell>
                <TableCell>{item.total_igst ?? '-'}</TableCell>
                <TableCell>{`${item.total_cgst}`}</TableCell>
                <TableCell>{item.total_sgst ?? '-'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={FINAL_SUMMARY_TRANSACTION_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchQuery}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={finalSummaryData as []}
        renderData={finalSummaryData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default FinalSummary;
