import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IMoowrProductItem} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {PRODUCT_SELECTION_TABLE_HEADER} from '@pages/Moowr/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import {useMemo} from 'react';

import './index.scss';

interface IProductSelectionTableProps {
  data: IMoowrProductItem[];
  isSelectedAll?: boolean;
  selectItem?: (id: string) => void;
  onSelectAll?: () => void;
  isCheckbox?: boolean;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function ProductSelectionTable({
  data,
  selectItem,
  isSelectedAll,
  onSelectAll,
  isCheckbox,
  handleSortBy,
}: IProductSelectionTableProps) {
  const tableHeader = useMemo(
    () => PRODUCT_SELECTION_TABLE_HEADER(isCheckbox || false),
    [isCheckbox]
  );

  return (
    <table className='product-selection-list-table'>
      <TableHeader
        checked={isSelectedAll}
        onChange={onSelectAll}
        mainHeader={tableHeader}
        handleSortBy={handleSortBy}
      />
      {data?.length > 0 ? (
        <TableBody>
          {data?.map((item, index) => (
            <TableRow key={`productSelection${index + 1}`}>
              {isCheckbox && selectItem && (
                <TableCell className='checkbox-td'>
                  <EximCheckbox
                    id={`${item.ref_id}`}
                    color='#2CB544'
                    size='medium'
                    checked={item.selected}
                    onChange={() => selectItem(item.ref_id)}
                  />
                </TableCell>
              )}
              <TableCell>{item.prod_code || '-'}</TableCell>
              <TableCell>{item.prod_desc || '-'}</TableCell>
              <TableCell>{item.invoice_no || '-'}</TableCell>
              <TableCell>{formatAmount(item.qty_sold || '')}</TableCell>
              <TableCell>{formatAmount(item.uqc || '')}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      ) : (
        <EmptyTable colSpan={tableHeader.length} />
      )}
    </table>
  );
}

ProductSelectionTable.defaultProps = {
  isCheckbox: false,
  isSelectedAll: false,
  onSelectAll: () => '',
  selectItem: (id: string) => '',
};
export default ProductSelectionTable;
