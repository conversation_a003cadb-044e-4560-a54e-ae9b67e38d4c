import Stripe from '@common/components/Stripe';

interface IFileStripeProps {
  status: string;
  handleRefresh: () => void;
  handleDownload: () => void;
}

export default function FileDownloadStripe({
  status,
  handleRefresh,
  handleDownload,
}: IFileStripeProps) {
  return (
    <>
      {status?.includes('COMPLETED') ? (
        <Stripe
          content='Your data is ready to download'
          variant='success'
          isBtn
          onBtnClick={handleDownload}
        />
      ) : null}
      {status?.includes('READY') || status?.includes('IN_PROGRESS') ? (
        <Stripe
          content='Data is getting generated please wait'
          variant='tertiary'
          isBtn
          btnText='Refresh'
          onBtnClick={handleRefresh}
        />
      ) : null}
      {status?.includes('FAILED') ? (
        <Stripe
          content={
            <span>
              <strong>Failed. </strong>
              <span>Please try again.</span>
            </span>
          }
          variant='primary'
        />
      ) : null}
    </>
  );
}
