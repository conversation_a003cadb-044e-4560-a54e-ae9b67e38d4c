@import '@utils/main.scss';

.moowr-transaction-header-wrapper {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
    .main-header {
      height: 53px;
      @include flex-item(row, space-between, normal);
      border-bottom: 1px solid $exim-border-line;
      .title-part {
        @include flex-item(row, normal, center, nowrap, 10px);
        @include margin(16px 18px);
        letter-spacing: 0.2px;
        .ellipsis-container {
          .ellipsis-text {
            font-size: $font-size-sm;
            font-weight: $font-weight-bold;
            color: $text-color;
          }
        }
        .inline {
          font-weight: normal;
        }

        .button-wrapper {
          .base-btn {
            padding: 0;
            font-size: $font-size-xsm;
            color: $information;
            &:not(:disabled):hover {
              box-shadow: none;
            }
          }
        }
        .inline {
          display: inline;
          @include margin-left(2px);
        }
      }

      .period {
        @include flex-item(row, normal, center, nowrap, 23px);
        @include margin-right(15px);
        height: 100%;

        .typography-container {
          color: $text-color;
        }

        .base-date-picker {
          width: auto;
          .date-input {
            @include padding(auto 12px);
          }
        }
      }
    }
  }

  // Edit MOOWR Transaction Modal
  .edit-moowr-transaction-modal {
    letter-spacing: 0.5px;
    .modal-body {
      width: 348px;
      .modal-header {
        @include flex-item(row, space-between);
        @include padding(30px 30px 0px 30px);
      }
      .modal-content {
        @include margin-top(16px);
        @include padding(0 30px 30px 30px);
      }
      .transaction-modal-title {
        font-size: $font-size-xxl;
        color: $secondary-text;
      }
    }
  }
}

// View Transaction Title Modal Style
.view-transaction-title-modal {
  .modal {
    letter-spacing: 0.2px;
    .modal-title {
      @include flex-item(_, center, center, _, 2px);
      font-size: $font-size-sm;
      span {
        font-weight: normal;
      }
    }
    .modal-body {
      width: 445px;
      .modal-content {
        min-height: 0;
        @include padding-top(0);
        align-items: baseline;
        height: max-content;
        p {
          width: 400px;
          overflow-wrap: break-word;
        }
      }
    }
  }
}
