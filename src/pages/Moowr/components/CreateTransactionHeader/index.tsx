import Ellip<PERSON><PERSON><PERSON><PERSON> from '@common/components/EllipsisChecker';
import {EximHeroDate} from '@common/constants';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import TransactionModalContent from '../TransactionModal';
import './index.scss';

interface ITransactionHeader {
  isEditBtn?: boolean;
}

function CreateTransactionHeader({isEditBtn}: ITransactionHeader) {
  const [isEditTransactionModal, setIsEditTransactionModal] = useState(false);
  const [isOpenTextModal, setIsOpenTextModal] = useState(false);

  const {
    moowr: {
      currTransactionDetails: {txnName, txnType, fromDate, toDate},
    },
  } = useSelector((state: RootState) => state);

  return (
    <div className='moowr-transaction-header-wrapper'>
      <EximPaper>
        <div className='main-header'>
          <div className='title-part'>
            <EllipsisChecker
              text={txnName}
              handleViewMore={() => setIsOpenTextModal(true)}
            />
            <span> | </span>
            <EximTypography fontWeight='bold'>
              Consumption Type
              <span className='inline'>&nbsp;- {txnType}</span>
            </EximTypography>
            {isEditBtn && (
              <>
                <span> | </span>
                <EximButton
                  variant='text'
                  onClick={() => setIsEditTransactionModal(true)}>
                  Edit
                </EximButton>
              </>
            )}
          </div>
          <div className='period'>
            <EximTypography>Import Period</EximTypography>
            <EximMonthRangePicker
              id='moowr-transaction-period'
              minDate={EximHeroDate.MIN_MONTH}
              defaultStartDate={fromDate?.split('-').join('/')}
              defaultEndDate={toDate?.split('-').join('/')}
              onSelect={() => undefined}
              disabled
            />
          </div>
        </div>
      </EximPaper>

      {/* Edit Transaction Modal */}
      <div className='edit-moowr-transaction-modal'>
        <EximModal
          isOpen={isEditTransactionModal}
          onClose={() => setIsEditTransactionModal(false)}
          onOutSideClickClose={() => setIsEditTransactionModal(false)}
          content={
            <TransactionModalContent
              onClose={() => setIsEditTransactionModal(false)}
            />
          }
          footer={false}
          header={
            <EximTypography
              classNames='transaction-modal-title'
              fontWeight='semi-bold'>
              Edit Transaction
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* View Transaction Title Modal */}
      <div className='view-transaction-title-modal'>
        <EximModal
          isOpen={isOpenTextModal}
          onClose={() => setIsOpenTextModal(false)}
          onOutSideClickClose={() => setIsOpenTextModal(false)}
          content={<p>{txnName}</p>}
          footer={false}
          header={
            <EximTypography fontWeight='bold'>Consumption Name</EximTypography>
          }
          closeIcon={<CloseIcon width={15} height={15} />}
        />
      </div>
    </div>
  );
}
CreateTransactionHeader.defaultProps = {
  isEditBtn: false,
};
export default CreateTransactionHeader;
