@import '@utils/main.scss';

.transaction-modal {
  width: 100%;

  &-part {
    @include flex-item(column, flex-start, flex-start, nowrap, 24px);
    @include margin(20px 0);
    .input-wrapper .input-group label {
      top: -22px;
    }
    .select-dropdown {
      width: 100%;
      .custom-dropdown-selection {
        background: $white;
      }
    }
    .select-date {
      width: 100%;
      .label-text {
        @include margin-bottom(4px);
        font-size: $font-size-sm;
        color: $label-color;
      }
      .base-date-picker {
        width: 100%;
      }
    }

    .custom-base-date-picker {
      width: 100%;
    }
  }

  &-actions {
    @include margin-top(32px);
    @include flex-item(row, flex-end, flex-end, nowrap, 10px);
    &-cancel,
    &-submit {
      width: 100px;
    }

    .base-btn {
      height: 32px;
      font-size: $base-font-size;
    }
  }
}
