import {
  AlertStatus,
  EximHeroDate,
  MOOWR_TRANSACTION_TYPE,
  MoowrTxnType,
  Path,
  ResponseStatus,
} from '@common/constants';
import {selectedOptionId} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {createTransaction, editTransaction} from '@pages/Moowr/api';
import {moowrActions} from '@pages/Moowr/store/reduce';
import {
  TRANSACTIONS_TYPE_DROPDOWN,
  createTransactionSchema,
} from '@pages/Moowr/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface ITransactionModalContent {
  onClose: () => void;
}

function TransactionModalContent({onClose}: ITransactionModalContent) {
  const navigate = useNavigate();
  const {
    moowr: {panNumber, currTransactionDetails},
  } = useSelector((state: RootState) => state);

  const formik = useFormik({
    initialValues: currTransactionDetails,
    validationSchema: createTransactionSchema,
    onSubmit: async (values) => {
      const payload = {
        txn_name: values.txnName,
        txn_type: values.txnType,
        start_prd: values.fromDate,
        end_prd: values.toDate,
        status: '',
      };

      if (currTransactionDetails.isTxnExist) {
        const response = await editTransaction(
          panNumber,
          currTransactionDetails.txnId,
          payload
        );
        if (response.status?.toString() === ResponseStatus.SUCCESS) {
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response.data,
              alertType: AlertStatus.SUCCESS,
            })
          );
        }
        const transactionDtls = {
          txnId: currTransactionDetails?.txnId,
          txnName: values?.txnName,
          txnType:
            MOOWR_TRANSACTION_TYPE[values?.txnType as MoowrTxnType] ||
            values?.txnType,
          fromDate: values?.fromDate,
          toDate: values?.toDate,
          isTxnExist: true,
        };
        dispatch(moowrActions.setCurrTransactionDetails(transactionDtls));
      } else {
        const response = (await createTransaction(
          panNumber,
          payload
        )) as ICustomAxiosResp;
        if (response.status?.toString() === ResponseStatus.SUCCESS) {
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          const transactionDtls = {
            txnId: response.data?.txn_id,
            txnName: values?.txnName,
            txnType:
              MOOWR_TRANSACTION_TYPE[values?.txnType as MoowrTxnType] ||
              values?.txnType,
            fromDate: values?.fromDate,
            toDate: values?.toDate,
            isTxnExist: true,
          };
          dispatch(moowrActions.setCurrTransactionDetails(transactionDtls));
          navigate(`${Path.MOOWR}${Path.TRANSACTION}${Path.PROD_SELECTION}`);
        }
      }
      onClose(); // Closing the transaction modal
    },
  });

  return (
    <form className='transaction-modal' onSubmit={formik.handleSubmit}>
      <div className='transaction-modal-part'>
        <EximInput
          label='Consumption Name'
          id='txnName'
          name='txnName'
          maxLength={64}
          onChange={formik.handleChange}
          value={formik.values.txnName}
          placeholder='Transaction Name'
          onBlur={(e) => formik.values.txnName && formik.handleBlur(e)}
          isInvalid={
            ((formik.errors.txnName && formik.touched.txnName) as boolean) ||
            false
          }
          errorMessage={
            formik.errors.txnName ? (formik.errors.txnName as string) : ''
          }
        />

        <EximCustomDropdown
          id='txnType'
          label='Consumption Type'
          placeholder='Select Type'
          onSelect={({value}) => formik.setFieldValue('txnType', value)}
          dataTestId='column-dropdown'
          optionsList={TRANSACTIONS_TYPE_DROPDOWN}
          isInvalid={
            ((formik.errors.txnType && formik.touched.txnType) as boolean) ||
            false
          }
          errorMessage={
            formik.errors.txnType ? (formik.errors.txnType as string) : ''
          }
          defaultOption={selectedOptionId(
            TRANSACTIONS_TYPE_DROPDOWN,
            formik.values.txnType
          )}
        />
        <div className='select-date'>
          <p className='label-text'>Consumption Period</p>
          <EximMonthRangePicker
            id='transaction-period'
            minDate={EximHeroDate.MIN_MONTH}
            onSelect={(startDate: string, endDate: string) => {
              formik.setFieldValue('fromDate', startDate);
              formik.setFieldValue('toDate', endDate);
            }}
            defaultStartDate={formik?.values?.fromDate?.split('-').join('/')}
            defaultEndDate={formik?.values?.toDate?.split('-').join('/')}
            isInvalid={
              ((formik.errors.toDate && formik.touched.toDate) as boolean) ||
              false
            }
            errorMessage={
              formik.errors.toDate ? (formik.errors.toDate as string) : ''
            }
          />
        </div>
      </div>
      <div className='transaction-modal-actions'>
        <div className='transaction-modal-actions-cancel'>
          <EximButton color='secondary' onClick={onClose}>
            Cancel
          </EximButton>
        </div>
        <div className='transaction-modal-actions-submit'>
          <EximButton type='submit'>
            {currTransactionDetails.isTxnExist ? 'Apply' : 'Create'}
          </EximButton>
        </div>
      </div>
    </form>
  );
}

export default TransactionModalContent;
