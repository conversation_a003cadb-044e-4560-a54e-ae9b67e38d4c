import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  EximHeroDate,
  MOOWR_FILE_ROUTE_TYPE,
  MOOWR_SALE_TYPE,
  ResponseStatus,
} from '@common/constants';
import {formatDate, selectedOptionId} from '@common/helpers';
import {
  ICustomAxiosResp,
  IInvoiceValues,
  IMoowrOutwardRegViewDtls,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOutwardRegisterInvDtls, saveEditInvoice} from '@pages/Moowr/api';
import {
  SALE_TYPE_DROPDOWN,
  VIEW_DETAILS_OUTWARD_REGISTER_TABLE_HEADER,
  VIEW_DETAILS_OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import {
  outwardInitialValues,
  outwardRegisterSchema,
} from '@pages/Moowr/utils/validations';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router';

import './index.scss';

interface IViewDetailsProps {
  isViewValidRecord: boolean;
  isEditable: boolean;
}

function OutwardRegisterViewDetails({
  isViewValidRecord,
  isEditable,
}: IViewDetailsProps) {
  const navigate = useNavigate();
  const {id} = useParams();
  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(isEditable);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [viewDetailsAllData, setViewDetailsAllData] =
    useState<IMoowrOutwardRegViewDtls>(outwardInitialValues);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const formik = useFormik({
    initialValues: viewDetailsAllData,
    validationSchema: outwardRegisterSchema,
    onSubmit: async (values: IMoowrOutwardRegViewDtls) => {
      setIsEdit(true);
      if (isEdit) {
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          fileType: MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER,
        };
        const response = (await saveEditInvoice(
          payload,
          values
        )) as ICustomAxiosResp;
        if (response?.status?.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(false);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(-1);
        }
        if (response?.status?.toString() === ResponseStatus.ERROR) {
          setViewDetailsAllData(response?.data);
          setTotalRecords(response?.data?.total_records);
          formik.setValues(response?.data);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: 'Records are not valid',
              alertType: AlertStatus.DANGER,
            })
          );
        }
      }
    },
  });

  const getViewInvoiceDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      refId: id,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getOutwardRegisterInvDtls(
      payload,
      page,
      +showEntries
    );
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setViewDetailsAllData(response?.data);
      setTotalRecords(response?.data?.total_records || 0);
      formik.setValues(response?.data);
      setIsLoading(false);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    id,
    panNumber,
    page,
    showEntries,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const getDateValue = (
    formikVal: string,
    key: keyof IMoowrOutwardRegViewDtls
  ) => {
    const apiResVal = (
      viewDetailsAllData[key] as {value: string}
    )?.value?.toString();
    if (formikVal && formikVal.length > 4) {
      return formikVal?.replaceAll('-', '/');
    }
    if (apiResVal) {
      return apiResVal?.replaceAll('-', '/');
    }
    return undefined;
  };

  // Updating `isValid` and `errorMessage`
  const handleValidation = useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const validateField = (field: IInvoiceValues, error: any) => {
      field.isValid = typeof error !== 'string';
      field.errorMessage = typeof error === 'string' ? error : null;
    };
    const {values, errors} = formik;
    Object.keys(values).forEach((key) => {
      const valueField = values[key as keyof typeof values];
      const errorField = errors[key as keyof typeof errors];
      if (
        typeof valueField === 'object' &&
        valueField !== null &&
        'value' in valueField &&
        'isValid' in valueField &&
        'errorMessage' in valueField
      ) {
        validateField(valueField, errorField);
      }

      // Handle nested arrays (e.g., products)
      if (Array.isArray(valueField) && Array.isArray(errorField)) {
        valueField.forEach((item, index) => {
          const itemErrors = errorField[index] || {};

          Object.keys(item).forEach((itemKey) => {
            const nestedField = item[itemKey as keyof typeof item];
            const nestedError = itemErrors[itemKey as keyof typeof itemErrors];

            if (
              typeof nestedField === 'object' &&
              nestedField !== null &&
              'value' in nestedField &&
              'isValid' in nestedField &&
              'errorMessage' in nestedField
            ) {
              validateField(nestedField, nestedError);
            }
          });
        });
      }
    });
    // INFO: Adding below line to avoid the warnings because formik not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.errors, formik.values]);

  useEffect(() => {
    handleValidation();
  }, [handleValidation]);

  useEffect(() => {
    getViewInvoiceDetails();
    setIsLoading(false);
  }, [getViewInvoiceDetails]);

  return isLoading ? null : (
    <form onSubmit={formik.handleSubmit}>
      <BusinessHeader>
        <div className='btn-container'>
          {/* INFO: If we are viewing valid record than no need to show edit button */}
          {!isViewValidRecord ? (
            <EximButton
              size='small'
              type='submit'
              onClick={() => !isEdit && setIsEdit(true)}>
              {`${isEdit ? 'Update' : 'Edit'}`}
            </EximButton>
          ) : null}
        </div>
      </BusinessHeader>
      <EximPaper>
        <div className='sb-view-details'>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Product Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.sup_gstin?.value &&
                        formik.errors?.sup_gstin?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Supplier GSTIN'
                    id='sup_gstin'
                    name='sup_gstin.value'
                    dataTestid='sup_gstin'
                    maxLength={8}
                    value={formik.values?.sup_gstin?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.sup_gstin?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.invoice_no?.value &&
                        formik.errors?.invoice_no?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='GST Invoice No.'
                    id='invoice_no'
                    name='invoice_no.value'
                    dataTestid='invoice_no'
                    maxLength={8}
                    value={formik.values?.invoice_no?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.invoice_no?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.sb_no?.value &&
                        formik.errors?.sb_no?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Shipping Bill No.'
                    id='sb_no'
                    name='sb_no.value'
                    dataTestid='sb_no'
                    maxLength={8}
                    value={formik.values?.sb_no?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.sb_no?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                {isEdit ? (
                  <EximTooltip
                    content={
                      formik.errors?.sale_type?.value &&
                      formik.errors?.sale_type.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximCustomDropdown
                      label='Sale Type'
                      optionsList={SALE_TYPE_DROPDOWN}
                      placeholder='Please Select Sale Type'
                      dataTestId='sale_type'
                      isInvalid={!!formik.errors?.sale_type?.value}
                      onSelect={({value}) =>
                        formik.setFieldValue('sale_type.value', value)
                      }
                      readOnly={!isEdit}
                      defaultOption={selectedOptionId(
                        SALE_TYPE_DROPDOWN,
                        formik.values?.sale_type?.value?.toString() || ''
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <>
                    <div className='product-details-title'>Sale Type</div>
                    <div className='product-details-value'>
                      {
                        MOOWR_SALE_TYPE[
                          formik.values?.sale_type
                            ?.value as keyof typeof MOOWR_SALE_TYPE
                        ]
                      }
                    </div>
                  </>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>GST Invoice Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      formik.errors?.invoice_date?.value &&
                      formik.errors?.invoice_date?.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='invoice_date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!!formik.errors?.invoice_date?.value}
                      onChange={(value) =>
                        formik.setFieldValue('invoice_date.value', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.invoice_date?.value?.toString(),
                        'invoice_date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.invoice_date?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Shipping Bill Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      formik.errors?.sb_date?.value &&
                      formik.errors?.sb_date?.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='sb_date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!!formik.errors?.sb_date?.value}
                      onChange={(value) =>
                        formik.setFieldValue('sb_date.value', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.sb_date?.value?.toString(),
                        'sb_date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.sb_date?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.ctin?.value && formik.errors?.ctin?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Customer GSTIN'
                    id='ctin'
                    name='ctin.value'
                    dataTestid='ctin'
                    maxLength={8}
                    value={formik.values?.ctin?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.ctin?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <div className='product-details-title'>LEO Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      formik.errors?.leo_date?.value &&
                      formik.errors?.leo_date?.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='leo_date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!!formik.errors?.leo_date?.value}
                      onChange={(value) =>
                        formik.setFieldValue('leo_date.value', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.leo_date?.value?.toString(),
                        'leo_date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.leo_date?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.port_of_export?.value &&
                        formik.errors?.port_of_export?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Port of Export'
                    id='port_of_export'
                    name='port_of_export.value'
                    dataTestid='port_of_export'
                    maxLength={8}
                    value={formik.values?.port_of_export?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.port_of_export?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.pos?.value && formik.errors?.pos?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='POS'
                    id='pos'
                    name='pos.value'
                    dataTestid='pos'
                    maxLength={8}
                    value={formik.values?.pos?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.pos?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.total_inv_val &&
                        formik.errors?.total_inv_val
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Invoice Value'
                    id='total_inv_val'
                    name='total_inv_val'
                    dataTestid='total_inv_val'
                    maxLength={16}
                    value={formik.values?.total_inv_val?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.total_inv_val}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
            </div>
          </div>
          <EximDivider type='dashed' />
          <div className='view-details-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleShowEntries={handleShowEntries}
              handleSearchQuery={handleSearchQuery}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={
                  VIEW_DETAILS_OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN
                }
              />
            </TableSearchFilter>
            <table className='view-details-table'>
              <TableHeader
                mainHeader={VIEW_DETAILS_OUTWARD_REGISTER_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {formik.values?.products?.length > 0 ? (
                <TableBody className='view-list-table-body'>
                  {formik.values.products?.map((item, index) => {
                    const touched = formik.touched?.products?.[index];
                    const errors = formik.errors?.products?.[index];
                    const values = formik.values.products[index];

                    return (
                      <TableRow key={`viewInvoiceDtls${index + 1}`}>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.prod_code?.value &&
                              typeof errors !== 'string' &&
                              errors?.prod_code?.value
                                ? errors.prod_code.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].prod_code.value`}
                              id={`products[${index}].prod_code.value`}
                              dataTestid='prod_code'
                              maxLength={8}
                              value={values?.prod_code?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.prod_code?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.prod_code?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.prod_desc?.value &&
                              typeof errors !== 'string' &&
                              errors?.prod_desc?.value
                                ? errors.prod_desc.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].prod_desc.value`}
                              id={`products[${index}].prod_desc.value`}
                              dataTestid='prod_desc'
                              maxLength={8}
                              value={values?.prod_desc?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.prod_desc?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.prod_desc?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.hsn?.value &&
                              typeof errors !== 'string' &&
                              errors?.hsn?.value
                                ? errors.hsn.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].hsn.value`}
                              id={`products[${index}].hsn.value`}
                              dataTestid='hsn'
                              maxLength={8}
                              value={values?.hsn?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.hsn?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.hsn?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.uqc?.value &&
                              typeof errors !== 'string' &&
                              errors?.uqc?.value
                                ? errors.uqc.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].uqc.value`}
                              id={`products[${index}].uqc.value`}
                              dataTestid='uqc'
                              maxLength={8}
                              value={values?.uqc?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.uqc?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.uqc?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.qty_sold?.value &&
                              typeof errors !== 'string' &&
                              errors?.qty_sold?.value
                                ? errors.qty_sold.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].qty_sold.value`}
                              id={`products[${index}].qty_sold.value`}
                              dataTestid='qty_sold'
                              maxLength={8}
                              value={values?.qty_sold?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.qty_sold?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.qty_sold?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.fob_or_taxable_value?.value &&
                              typeof errors !== 'string' &&
                              errors?.fob_or_taxable_value?.value
                                ? errors.fob_or_taxable_value.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].fob_or_taxable_value.value`}
                              id={`products[${index}].fob_or_taxable_value.value`}
                              dataTestid='fob_or_taxable_value'
                              maxLength={8}
                              value={
                                values?.fob_or_taxable_value?.value?.toString() ||
                                ''
                              }
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.fob_or_taxable_value?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.fob_or_taxable_value?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.tax_rt?.value &&
                              typeof errors !== 'string' &&
                              errors?.tax_rt?.value
                                ? errors.tax_rt.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].tax_rt.value`}
                              id={`products[${index}].tax_rt.value`}
                              dataTestid='tax_rt'
                              maxLength={8}
                              value={values?.tax_rt?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.tax_rt?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.tax_rt?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.igst_amt?.value &&
                              typeof errors !== 'string' &&
                              errors?.igst_amt?.value
                                ? errors.igst_amt.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].igst_amt.value`}
                              id={`products[${index}].igst_amt.value`}
                              dataTestid='igst_amt'
                              maxLength={8}
                              value={values?.igst_amt?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.igst_amt?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.igst_amt?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.cgst_amt?.value &&
                              typeof errors !== 'string' &&
                              errors?.cgst_amt?.value
                                ? errors.cgst_amt.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].cgst_amt.value`}
                              id={`products[${index}].cgst_amt.value`}
                              dataTestid='cgst_amt'
                              maxLength={8}
                              value={values?.cgst_amt?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.cgst_amt?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.cgst_amt?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.sgst_amt?.value &&
                              typeof errors !== 'string' &&
                              errors?.sgst_amt?.value
                                ? errors.sgst_amt.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].sgst_amt.value`}
                              id={`products[${index}].sgst_amt.value`}
                              dataTestid='sgst_amt'
                              maxLength={8}
                              value={values?.sgst_amt?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.sgst_amt?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.sgst_amt?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.cess_amt?.value &&
                              typeof errors !== 'string' &&
                              errors?.cess_amt?.value
                                ? errors.cess_amt.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`products[${index}].cess_amt.value`}
                              id={`products[${index}].cess_amt.value`}
                              dataTestid='cess_amt'
                              maxLength={8}
                              value={values?.cess_amt?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.cess_amt?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.cess_amt?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              ) : (
                <EmptyTable
                  colSpan={VIEW_DETAILS_OUTWARD_REGISTER_TABLE_HEADER.length}
                />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={formik.values?.products as []}
              renderData={formik.values?.products as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </div>
      </EximPaper>
    </form>
  );
}

export default OutwardRegisterViewDetails;
