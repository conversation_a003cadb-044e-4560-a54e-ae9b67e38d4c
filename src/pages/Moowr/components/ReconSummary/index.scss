@import '@utils/main.scss';

.recon-summary-container {
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;
    background-color: $white;
    box-shadow: 0px 3px 6px $box-shadow-color;
  }
  .summary-header {
    @include padding(20px);
    border-bottom: 1px solid $primary-border;
    @include flex-item(_, space-between, center);
    .summary-title {
      @include flex-item(_, flex-start, center, _, 16px);
      .filing-head {
        height: auto;
        border-bottom: none;
        padding: 0;
      }
    }
    .select-date-picker {
      @include flex-item(_, flex-start, center, _, 16px);
      .base-date-picker {
        width: 160px;
      }
    }
  }
  .summary-details {
    @include padding(20px);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    .card-container {
      position: relative;
      @include padding(20px);
      @include flex-item(column, center, center, _, 5px);
      @include rfs(5px, border-radius);
      border: 1px solid $card-label-color;
    }
  }
  .recon-details-container {
    @include margin(0 20px);
    @include padding(20px 0);
    border-top: 1px solid $primary-border;
    border-bottom: 1px solid $primary-border;
    display: grid;
    align-items: center;
    grid-template-columns: 1.5fr 1fr 1fr 1.5fr;
    gap: 20px;
    .details-card:last-child {
      width: 100%;
      @include flex-item(_, space-between, center, _, 5px);
      .button-wrapper {
        min-width: 100px;
        max-width: 115px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
      .recon-status {
        @include flex-item(_, flex-start, center, _, 8px);
        .refresh-icon {
          @include margin-top(5px);
          cursor: pointer;
        }
        .error {
          color: $error;
        }
        .success {
          color: $success;
        }
        .process {
          color: $warning;
        }
      }
      .btn-container {
        @include flex-item(_, flex-end, center, _, 20px);
      }
    }
    .summary-title {
      @include flex-item(_, flex-start, center, _, 16px);
      .filing-head {
        height: auto;
        border-bottom: none;
        padding: 0;
      }
    }
  }
  .moowr-filing {
    .btn-container {
      @include flex-item(_, space-between, center, _, 20px);
      .file-status-strip {
        color: $warning;
        font-size: $font-size-sm;
        @include flex-item(_, flex-start, center, _, 10px);
        .refresh-icon {
          @include margin-top(6px);
          cursor: pointer;
        }
        .error {
          color: $error;
        }
        .success {
          color: $success;
        }
        .process {
          color: $warning;
        }
      }
    }
    .button-wrapper {
      min-width: 100px;
      max-width: 115px;
      .base-btn {
        font-size: $font-size-sm;
        @include padding(7px 20px);
      }
    }
  }
  .avatar-wrapper {
    .avatar-container {
      .small {
        width: 28px;
        height: 28px;
      }

      .avatar-text {
        background-color: $tertiary;
        border: none;
        color: $white;
        font-size: $font-size-sm;
        font-weight: normal;
      }
    }
  }
}
