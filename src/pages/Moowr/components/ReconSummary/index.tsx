import FilingHead from '@common/components/FilingHead';
import {
  EximHeroDate,
  MoowrReconReports,
  Path,
  ResponseStatus,
} from '@common/constants';
import {downloadLargeFileData, getAlertMessage} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {
  createReconciliation,
  downloadLargeFile,
  getReconStatus,
  reconSummary,
  reportsExport,
  reportsExportHistory,
} from '@pages/Moowr/api';
import {moowrActions} from '@pages/Moowr/store/reduce';
import {RECON_SUMMARY_CARDS} from '@pages/Moowr/utils';
import EximAvatar from '@shared/components/EximAvatar';
import EximButton from '@shared/components/EximButton';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const initialSummary = {
  matchCount: 0,
  rectifiedCount: 0,
  rollbackCount: 0,
};

const initialReconStatus = {
  reconcileBy: '',
  reconcileOn: '',
  reconcileStatus: '',
};

export default function ReconSummary() {
  const navigate = useNavigate();

  const {
    moowr: {panNumber, reconMonth, reconTxnId},
  } = useSelector((state: RootState) => state);

  const [reconSummaryData, setReconSummaryData] = useState(initialSummary);
  const [reconStatus, setReconStatus] = useState(initialReconStatus);
  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleDownloadReport = async () => {
    const payload = {
      pan: panNumber,
      fileId: downloadFileId,
      reportType: MoowrReconReports.MOOWR_FILING_REPORT,
    };

    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(panNumber);
    const currentReport = data?.find(
      (el: {report_type: string}) =>
        el.report_type === MoowrReconReports.MOOWR_FILING_REPORT
    );
    setDownloadFileId(currentReport.file_id);
    setDownloadFileStatus(currentReport.status);
    setDownloadFileName(currentReport?.report_name);
  };

  const handleExportReport = async () => {
    const payload = {
      pan: panNumber,
      txnId: reconTxnId,
      reportType: MoowrReconReports.MOOWR_FILING_REPORT,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  const checkReconStatus = useCallback(async () => {
    const month = reconMonth?.split('/').join('-')?.slice(3);

    const response = await getReconStatus(panNumber, month);
    dispatch(moowrActions.setReconTxnId(response.data?.txnId));
    dispatch(moowrActions.setReconStatus(response.data?.status));
    const data = {
      reconcileBy: response?.data?.userName || '',
      reconcileOn: response?.data?.updatedAt || '',
      reconcileStatus: response?.data?.status || '',
    };
    setReconStatus(data);
  }, [panNumber, reconMonth]);

  const handleRecon = async () => {
    const month = reconMonth?.split('/').join('-')?.slice(3);
    const response = (await createReconciliation(
      panNumber,
      month
    )) as ICustomAxiosResp;

    if (response?.status?.toString() === ResponseStatus.SUCCESS) {
      getAlertMessage('success', response?.msg);
      const txnId = response.data['txn-id'];
      dispatch(moowrActions.setReconTxnId(txnId));
      checkReconStatus();
    }
  };

  const getReconSummary = useCallback(async () => {
    const month = reconMonth?.split('/').join('-')?.slice(3);
    const response = await reconSummary(panNumber, month);
    setReconSummaryData(response.data || initialSummary);
  }, [panNumber, reconMonth]);

  const getShowReconStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (
      value?.includes('RECON_IN_PROGRESS') ||
      value?.includes('READY_TO_RECON')
    )
      return <span className='process'>In Progress</span>;
    if (value?.includes('RECON_FAILED'))
      return <span className='error'>Failed</span>;
    if (value?.includes('RECON_DONE'))
      return <span className='success'>Completed</span>;
    if (value?.includes('FREEZED'))
      return <span className='error'>Freezed</span>;
    if (value?.includes('RECTIFICATION_FAILED'))
      return <span className='error'>Rectification Failed</span>;
    return null;
  };

  const getReportDownloadText = () => {
    if (downloadFileStatus === '') return null;
    if (
      downloadFileStatus?.includes('IN_PROGRESS') ||
      downloadFileStatus?.includes('READY')
    ) {
      return (
        <span className='process'>Data is getting generated please wait</span>
      );
    }
    if (downloadFileStatus?.includes('COMPLETED')) {
      return <span className='success'>Your data is ready to download</span>;
    }
    if (downloadFileStatus?.includes('FAILED')) {
      return <span className='error'>Failed, Please try again</span>;
    }

    return null;
  };

  useEffect(() => {
    getReconSummary();
  }, [getReconSummary]);

  useEffect(() => {
    checkReconStatus();
  }, [checkReconStatus]);

  return (
    <div className='recon-summary-container'>
      <EximPaper>
        <div className='summary-header'>
          <div className='summary-title'>
            <FilingHead
              filingHead='Monthly MOOWR Activity'
              onGuideClick={handleGuideClick}
              hasGuide
            />
          </div>
          <div className='select-date-picker'>
            <EximTypography>Select Period</EximTypography>
            <EximDatePicker
              id='reconDatePicker'
              minDate={EximHeroDate.MIN_DATE}
              calendarType='monthCalendar'
              onChange={(value) => {
                dispatch(moowrActions.setReconMonth(value));
              }}
              defaultValue={reconMonth}
            />
          </div>
        </div>
        <div className='summary-details'>
          {RECON_SUMMARY_CARDS.map((card) => {
            return (
              <div key={card.dataKey} className='card-container'>
                <EximTypography variant='h5'>{card.title}</EximTypography>
                <EximTypography variant='h2' fontWeight='semi-bold'>
                  {
                    reconSummaryData?.[
                      card.dataKey as keyof typeof initialSummary
                    ]
                  }
                </EximTypography>
              </div>
            );
          })}
        </div>
        <div className='recon-details-container'>
          <div className='summary-title'>
            <EximAvatar
              rounded
              firstName='1'
              lastName=''
              alt='number'
              size='small'
            />
            <FilingHead filingHead='Reconciliation (Consumption vs Actual)' />
          </div>
          <div className='details-card'>
            <EximTypography variant='h5'>
              <strong>Reconciled by :</strong> {reconStatus.reconcileBy}
            </EximTypography>
          </div>
          <div className='details-card'>
            <EximTypography variant='h5'>
              <strong>Reconcile on :</strong> {reconStatus.reconcileOn}
            </EximTypography>
          </div>
          <div className='details-card'>
            <div className='recon-status'>
              <EximTypography variant='h5'>
                <strong>Reconcile status : </strong>
                {getShowReconStatus(reconStatus.reconcileStatus)}
              </EximTypography>
              <span
                className='refresh-icon'
                role='presentation'
                onClick={checkReconStatus}>
                {reconStatus.reconcileStatus === 'READY_TO_RECON' ||
                reconStatus.reconcileStatus === 'RECON_IN_PROGRESS' ? (
                  <SolidSync />
                ) : null}
              </span>
            </div>
            <div className='btn-container'>
              {reconStatus.reconcileStatus === 'RECTIFICATION_FAILED' ||
              reconStatus.reconcileStatus === 'RECON_DONE' ||
              reconStatus.reconcileStatus === 'FREEZED' ? (
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() =>
                    navigate(`${Path.MOOWR}${Path.RECONCILIATION}`)
                  }>
                  View
                </EximButton>
              ) : (
                <EximButton
                  size='small'
                  onClick={handleRecon}
                  disabled={
                    reconStatus.reconcileStatus === 'READY_TO_RECON' ||
                    reconStatus.reconcileStatus === 'RECON_IN_PROGRESS'
                  }>
                  Reconcile
                </EximButton>
              )}
            </div>
          </div>
        </div>

        <div className='moowr-filing'>
          <div className='summary-header'>
            <div className='summary-title'>
              <EximAvatar
                rounded
                firstName='2'
                lastName=''
                alt='number'
                size='small'
              />
              <FilingHead filingHead='MOOWR Filing' />
            </div>
            <div className='btn-container'>
              <div className='file-status-strip'>
                {getReportDownloadText()}
                <span
                  className='refresh-icon'
                  role='presentation'
                  onClick={getFileStatusToDownload}>
                  {downloadFileStatus?.includes('IN_PROGRESS') ||
                  downloadFileStatus?.includes('READY') ? (
                    <SolidSync />
                  ) : null}
                </span>
              </div>
              {downloadFileStatus?.includes('COMPLETED') ? (
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={handleDownloadReport}
                  disabled={reconStatus.reconcileStatus !== 'RECON_DONE'}>
                  Download
                </EximButton>
              ) : (
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={handleExportReport}
                  disabled={
                    (reconStatus.reconcileStatus !== 'RECON_DONE' &&
                      reconStatus.reconcileStatus !== 'FREEZED') ||
                    downloadFileStatus?.includes('IN_PROGRESS') ||
                    downloadFileStatus?.includes('READY')
                  }>
                  Generate
                </EximButton>
              )}
            </div>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}
