import TableHeader from '@common/components/TableHeader';
import {downloadLargeFileData} from '@common/helpers';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  downloadLargeFile,
  reportsExport,
  reportsExportHistory,
} from '@pages/Moowr/api';
import {DOWNLOAD_REPORTS_MODAL_TABLE_HEADER} from '@pages/Moowr/utils';
import EximButton from '@shared/components/EximButton';
import {SolidSync} from '@shared/icons';
import {RootState} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IDownloadFileProps {
  txnId: string;
  onClose: () => void;
}

const initialValues = {
  MOOWR_TXN_REPORT: '',
  MOOWR_TXN_CAL_SUMM_REPORT: '',
};

export default function DownloadReportsModal({
  txnId,
  onClose,
}: IDownloadFileProps) {
  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber: pan},
  } = useSelector((state: RootState) => state);

  const [downloadFileId, setDownloadFileId] = useState(initialValues);
  const [downloadFileStatus, setDownloadFileStatus] = useState(initialValues);
  const [downloadFileName, setDownloadFileName] = useState('');

  const handleDownload = async (reportType: string) => {
    const payload = {
      pan,
      fileId: downloadFileId[reportType as keyof typeof initialValues],
      reportType,
    };

    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async (reportType: string) => {
    const {data} = await reportsExportHistory(pan);
    const currentReport = data?.find(
      (el: {report_type: string}) => el.report_type === reportType
    );
    setDownloadFileId({...downloadFileId, [reportType]: currentReport.file_id});
    setDownloadFileStatus({
      ...downloadFileStatus,
      [reportType]: currentReport.status,
    });
    setDownloadFileName(currentReport?.report_name);
  };

  const handleExportReport = async (reportType: string) => {
    const payload = {
      pan,
      email,
      txnId,
      reportType,
    };
    await reportsExport(payload);
    await getFileStatusToDownload(reportType);
  };

  return (
    <div className='download-file-table-container'>
      <table className='download-file-table'>
        <TableHeader mainHeader={DOWNLOAD_REPORTS_MODAL_TABLE_HEADER} />
        <TableBody className='download-file-tbody'>
          <TableRow>
            <TableCell>Moowr Transaction Report</TableCell>
            <TableCell className='download-btn'>
              {downloadFileStatus.MOOWR_TXN_REPORT?.includes('COMPLETED') ? (
                <EximButton
                  size='small'
                  onClick={() => handleDownload('MOOWR_TXN_REPORT')}>
                  Download
                </EximButton>
              ) : (
                <>
                  <EximButton
                    size='small'
                    color='secondary'
                    disabled={downloadFileStatus.MOOWR_TXN_REPORT?.length > 0}
                    onClick={() => handleExportReport('MOOWR_TXN_REPORT')}>
                    Generate Report
                  </EximButton>
                  {downloadFileStatus.MOOWR_TXN_REPORT?.length > 0 && (
                    <span
                      role='presentation'
                      onClick={() =>
                        getFileStatusToDownload('MOOWR_TXN_REPORT')
                      }>
                      <SolidSync />
                    </span>
                  )}
                </>
              )}
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Moowr Transaction Summary Report</TableCell>
            <TableCell className='download-btn'>
              {downloadFileStatus.MOOWR_TXN_CAL_SUMM_REPORT?.includes(
                'COMPLETED'
              ) ? (
                <EximButton
                  size='small'
                  onClick={() => handleDownload('MOOWR_TXN_CAL_SUMM_REPORT')}>
                  Download
                </EximButton>
              ) : (
                <>
                  <EximButton
                    size='small'
                    color='secondary'
                    disabled={
                      downloadFileStatus.MOOWR_TXN_CAL_SUMM_REPORT?.length > 0
                    }
                    onClick={() =>
                      handleExportReport('MOOWR_TXN_CAL_SUMM_REPORT')
                    }>
                    Generate Report
                  </EximButton>
                  {downloadFileStatus.MOOWR_TXN_CAL_SUMM_REPORT?.length > 0 && (
                    <span
                      role='presentation'
                      onClick={() =>
                        getFileStatusToDownload('MOOWR_TXN_CAL_SUMM_REPORT')
                      }>
                      <SolidSync />
                    </span>
                  )}
                </>
              )}
            </TableCell>
          </TableRow>
        </TableBody>
      </table>
    </div>
  );
}
