import UploadFiles from '@common/components/UploadFiles';
import {
  AlertStatus,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {checkFilesExtension, downloadLargeFileData} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {
  downloadLargeFile,
  reportsExport,
  reportsExportHistory,
  uploadFileData,
} from '@pages/Moowr/api';
import {moowrActions} from '@pages/Moowr/store/reduce';
import EximButton from '@shared/components/EximButton';
import GSTTypography from '@shared/components/EximTypography';
import {ArrowRight, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {ChangeEvent, memo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IErrorUploadProps {
  disabledButton: boolean;
  fileType: string;
}

function ErrorRecordsUpload({disabledButton, fileType}: IErrorUploadProps) {
  const navigate = useNavigate();
  const splitFileType = fileType?.split('_') || [];
  const temp = splitFileType?.[splitFileType.length - 1];
  splitFileType[splitFileType.length - 1] = 'ERROR';
  splitFileType?.push(temp);
  const errorFileType = splitFileType?.join('_');

  const [selectedFile, setSelectedFile] = useState<File>();
  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber: pan, invoiceErrorFileId, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const handleUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);

      if (checkFilesExtension(selectedFilesArray, [SupportedFileTypes.EXCEL])) {
        setSelectedFile(selectedFilesArray[0]);
        const payload = {
          pan,
          email,
          txnId: invoiceTxnId,
          errorFileId: invoiceErrorFileId,
          fileType,
        };
        const response = (await uploadFileData(
          payload,
          selectedFilesArray[0]
        )) as ICustomAxiosResp;
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(Path.MOOWR);
        }
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message: 'Please select the excel files only!',
            alertType: 'danger',
          })
        );
      }
    }
  };

  const handleDownload = async () => {
    const payload = {
      pan,
      email,
      fileId: downloadFileId,
      reportType: errorFileType,
    };
    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
    dispatch(moowrActions.setInvoiceErrorFileId(downloadFileId));
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(pan);
    if (errorFileType === data?.[0]?.report_type) {
      setDownloadFileId(data?.[0]?.file_id);
      setDownloadFileStatus(data?.[0]?.status);
      setDownloadFileName(data?.[0]?.report_name);
    }
  };

  const handleExportReport = async () => {
    const payload = {
      pan,
      email,
      txnId: invoiceTxnId,
      reportType: errorFileType,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  return (
    <div className='error-record-upload-container'>
      <div className='upload-note'>
        <GSTTypography fontWeight='bold' variant='h4'>
          Note:{' '}
        </GSTTypography>
        <GSTTypography variant='h4'>
          - Follow the below steps and Download the Excel containing error
          records with valid comments, rectify the error data and Reupload the
          same Excel File.
        </GSTTypography>
      </div>
      <div className='process-steps-container'>
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 1 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Download Error Records Excel
            </GSTTypography>
          </div>
          {downloadFileStatus?.includes('COMPLETED') ? (
            <EximButton
              size='small'
              color='secondary'
              disabled={disabledButton}
              onClick={handleDownload}>
              Download
            </EximButton>
          ) : (
            <div className='generate-data-btn'>
              <EximButton
                size='small'
                color='secondary'
                disabled={downloadFileStatus?.length > 0 || disabledButton}
                onClick={handleExportReport}>
                Generate Data
              </EximButton>
              {downloadFileStatus?.length > 0 && (
                <span role='presentation' onClick={getFileStatusToDownload}>
                  <SolidSync />
                </span>
              )}
            </div>
          )}
        </div>
        <ArrowRight />
        <div className='step-text'>
          <GSTTypography fontWeight='bold' variant='h4'>
            Step 2 - &nbsp;
          </GSTTypography>
          <GSTTypography fontWeight='semi-bold' variant='h4'>
            Rectify the Error records
          </GSTTypography>
        </div>
        <ArrowRight />
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 3 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Upload Validation Excel
            </GSTTypography>
          </div>

          <span>
            <UploadFiles
              title='Upload'
              accept='.xlsx'
              disabled={disabledButton}
              onChange={handleUpload}
              icon={null}
            />
            <GSTTypography>
              {selectedFile?.name ?? `No Chosen File`}
            </GSTTypography>
          </span>
        </div>
      </div>
    </div>
  );
}

export default memo(ErrorRecordsUpload);
