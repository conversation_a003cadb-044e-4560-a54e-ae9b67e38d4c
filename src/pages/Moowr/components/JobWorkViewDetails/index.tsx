import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  EximHeroDate,
  MOOWR_FILE_ROUTE_TYPE,
  ResponseStatus,
} from '@common/constants';
import {formatDate, selectedOptionId} from '@common/helpers';
import {
  ICustomAxiosResp,
  IInvoiceValues,
  IMoowrJobWorkViewDtls,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getJobWorkInvDtls, saveEditInvoice} from '@pages/Moowr/api';
import {
  PROCESSING_TYPE_DROPDOWN,
  VIEW_DETAILS_JOB_WORK_TABLE_HEADER,
  VIEW_DETAILS_JOB_WORK_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import {
  jobWorkInitialValues,
  jobWorkSchema,
} from '@pages/Moowr/utils/validations';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router';

import './index.scss';

interface IViewDetailsProps {
  isViewValidRecord: boolean;
  isEditable: boolean;
}

function JobWorkViewDetails({
  isViewValidRecord,
  isEditable,
}: IViewDetailsProps) {
  const navigate = useNavigate();
  const {id} = useParams();
  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(isEditable);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [viewDetailsAllData, setViewDetailsAllData] =
    useState<IMoowrJobWorkViewDtls>(jobWorkInitialValues);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const formik = useFormik({
    initialValues: viewDetailsAllData,
    validationSchema: jobWorkSchema,
    onSubmit: async (values: IMoowrJobWorkViewDtls) => {
      setIsEdit(true);
      if (isEdit) {
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          fileType: MOOWR_FILE_ROUTE_TYPE.JOB_WORK_REGISTER,
        };

        const response = (await saveEditInvoice(
          payload,
          values
        )) as ICustomAxiosResp;
        if (response?.status?.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(false);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(-1);
        }
        if (response?.status?.toString() === ResponseStatus.ERROR) {
          setViewDetailsAllData(response?.data);
          setTotalRecords(response?.data?.total_records);
          formik.setValues(response?.data);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: 'Records are not valid',
              alertType: AlertStatus.DANGER,
            })
          );
        }
      }
    },
  });

  const getViewInvoiceDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      refId: id,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getJobWorkInvDtls(payload, page, +showEntries);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setViewDetailsAllData(response?.data);
      setTotalRecords(response?.data.total_records);
      formik.setValues(response?.data);
      setIsLoading(false);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    id,
    panNumber,
    page,
    showEntries,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const getDateValue = (
    formikVal: string,
    key: keyof IMoowrJobWorkViewDtls
  ) => {
    const apiResVal = (
      viewDetailsAllData[key] as {value: string}
    )?.value?.toString();
    if (formikVal && formikVal.length > 4) {
      return formikVal?.replaceAll('-', '/');
    }
    if (apiResVal) {
      return apiResVal?.replaceAll('-', '/');
    }
    return undefined;
  };

  // Updating `isValid` and `errorMessage`
  const handleValidation = useCallback(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const validateField = (field: IInvoiceValues, error: any) => {
      field.isValid = typeof error !== 'string';
      field.errorMessage = typeof error === 'string' ? error : null;
    };
    const {values, errors} = formik;
    Object.keys(values).forEach((key) => {
      const valueField = values[key as keyof typeof values];
      const errorField = errors[key as keyof typeof errors];
      if (
        typeof valueField === 'object' &&
        valueField !== null &&
        'value' in valueField &&
        'isValid' in valueField &&
        'errorMessage' in valueField
      ) {
        validateField(valueField, errorField);
      }

      // Handle nested arrays (e.g., products)
      if (Array.isArray(valueField) && Array.isArray(errorField)) {
        valueField.forEach((item, index) => {
          const itemErrors = errorField[index] || {};

          Object.keys(item).forEach((itemKey) => {
            const nestedField = item[itemKey as keyof typeof item];
            const nestedError = itemErrors[itemKey as keyof typeof itemErrors];

            if (
              typeof nestedField === 'object' &&
              nestedField !== null &&
              'value' in nestedField &&
              'isValid' in nestedField &&
              'errorMessage' in nestedField
            ) {
              validateField(nestedField, nestedError);
            }
          });
        });
      }
    });
    // INFO: Adding below line to avoid the warnings because formik not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formik.errors, formik.values]);

  useEffect(() => {
    handleValidation();
  }, [handleValidation]);

  useEffect(() => {
    getViewInvoiceDetails();
    setIsLoading(false);
  }, [getViewInvoiceDetails]);

  return isLoading ? null : (
    <form onSubmit={formik.handleSubmit}>
      <BusinessHeader>
        <div className='btn-container'>
          {/* INFO: If we are viewing valid record than no need to show edit button */}
          {!isViewValidRecord ? (
            <EximButton
              size='small'
              type='submit'
              onClick={() => !isEdit && setIsEdit(true)}>
              {`${isEdit ? 'Update' : 'Edit'}`}
            </EximButton>
          ) : null}
        </div>
      </BusinessHeader>
      <EximPaper>
        <div className='sb-view-details'>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Job Work Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                {isEdit ? (
                  <EximTooltip
                    content={
                      formik.errors?.type_of_Processing?.value &&
                      formik.errors?.type_of_Processing.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximCustomDropdown
                      label='Type Of Processing'
                      optionsList={PROCESSING_TYPE_DROPDOWN}
                      placeholder='Please Select Type'
                      dataTestId='type_of_Processing'
                      isInvalid={!!formik.errors?.type_of_Processing?.value}
                      onSelect={({value}) =>
                        formik.setFieldValue('type_of_Processing.value', value)
                      }
                      readOnly={!isEdit}
                      defaultOption={selectedOptionId(
                        PROCESSING_TYPE_DROPDOWN,
                        formik.values?.type_of_Processing?.value?.toString() ||
                          ''
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <>
                    <div className='product-details-title'>
                      Type Of Processing
                    </div>
                    <div className='product-details-value'>
                      {formik.values?.type_of_Processing?.value}
                    </div>
                  </>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      formik.errors?.date?.value && formik.errors?.date?.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!!formik.errors?.date?.value}
                      onChange={(value) =>
                        formik.setFieldValue('date.value', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.date?.value?.toString(),
                        'date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(formik.values?.date?.value?.toString() || '')}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.time?.value && formik.errors?.time?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Time'
                    id='time'
                    name='time.value'
                    dataTestid='time'
                    maxLength={8}
                    value={formik.values?.time?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.time?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.ewb?.value && formik.errors?.ewb?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='E-Bay Bill'
                    id='ewb'
                    name='ewb.value'
                    dataTestid='ewb'
                    maxLength={8}
                    value={formik.values?.ewb?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.ewb?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.delivery_chalan?.value &&
                        formik.errors?.delivery_chalan?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Delivery Challan'
                    id='delivery_chalan'
                    name='delivery_chalan.value'
                    dataTestid='delivery_chalan'
                    maxLength={8}
                    value={formik.values?.delivery_chalan?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.delivery_chalan?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
            </div>
          </div>
          <div className='hr-line'>
            <EximDivider type='dashed' />
          </div>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Job Worker Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.job_worker_name_address?.value &&
                        formik.errors?.job_worker_name_address?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Name & Address'
                    id='job_worker_name_address'
                    name='job_worker_name_address.value'
                    dataTestid='job_worker_name_address'
                    maxLength={8}
                    value={formik.values?.job_worker_name_address?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.job_worker_name_address?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.errors?.job_worker_gstin?.value &&
                        formik.errors?.job_worker_gstin?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='GSTIN'
                    id='job_worker_gstin'
                    name='job_worker_gstin.value'
                    dataTestid='job_worker_gstin'
                    maxLength={8}
                    value={formik.values?.job_worker_gstin?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!formik.errors?.job_worker_gstin?.value}
                    onChange={formik.handleChange}
                  />
                </EximTooltip>
              </div>
            </div>
          </div>
          <EximDivider type='dashed' />
          <div className='view-details-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleShowEntries={handleShowEntries}
              handleSearchQuery={handleSearchQuery}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={VIEW_DETAILS_JOB_WORK_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='view-details-table'>
              <TableHeader
                mainHeader={VIEW_DETAILS_JOB_WORK_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {formik.values?.jw_item_list?.length > 0 ? (
                <TableBody className='view-list-table-body'>
                  {formik.values.jw_item_list?.map((item, index) => {
                    const touched = formik.touched?.jw_item_list?.[index];
                    const errors = formik.errors?.jw_item_list?.[index];
                    const values = formik.values.jw_item_list[index];

                    return (
                      <TableRow key={`viewInvoiceDtls${index + 1}`}>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.item_code?.value &&
                              typeof errors !== 'string' &&
                              errors?.item_code?.value
                                ? errors.item_code.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`jw_item_list[${index}].item_code.value`}
                              id={`jw_item_list[${index}].item_code.value`}
                              dataTestid='item_code'
                              maxLength={8}
                              value={values?.item_code?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.item_code?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.item_code?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.item_desc?.value &&
                              typeof errors !== 'string' &&
                              errors?.item_desc?.value
                                ? errors.item_desc.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`jw_item_list[${index}].item_desc.value`}
                              id={`jw_item_list[${index}].item_desc.value`}
                              dataTestid='item_desc'
                              maxLength={8}
                              value={values?.item_desc?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.item_desc?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.item_desc?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.item_qty?.value &&
                              typeof errors !== 'string' &&
                              errors?.item_qty?.value
                                ? errors.item_qty.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`jw_item_list[${index}].item_qty.value`}
                              id={`jw_item_list[${index}].item_qty.value`}
                              dataTestid='item_qty'
                              maxLength={8}
                              value={values?.item_qty?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.item_qty?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.item_qty?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.uqc?.value &&
                              typeof errors !== 'string' &&
                              errors?.uqc?.value
                                ? errors.uqc.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`jw_item_list[${index}].uqc.value`}
                              id={`jw_item_list[${index}].uqc.value`}
                              dataTestid='uqc'
                              maxLength={8}
                              value={values?.uqc?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.uqc?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.uqc?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                        <TableCell className='editable-td'>
                          <EximTooltip
                            className='input-tooltip-text'
                            content={
                              isEdit &&
                              touched?.value?.value &&
                              typeof errors !== 'string' &&
                              errors?.value?.value
                                ? errors.value.value
                                : null
                            }
                            direction='top'
                            variant='secondary'>
                            <EximInput
                              name={`jw_item_list[${index}].value.value`}
                              id={`jw_item_list[${index}].value.value`}
                              dataTestid='value'
                              maxLength={8}
                              value={values?.value?.value?.toString() || ''}
                              disabled={!isEdit}
                              isInvalid={
                                !!(
                                  touched?.value?.value &&
                                  typeof errors !== 'string' &&
                                  errors?.value?.value
                                )
                              }
                              onChange={formik.handleChange}
                              onBlur={formik.handleBlur}
                            />
                          </EximTooltip>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              ) : (
                <EmptyTable
                  colSpan={VIEW_DETAILS_JOB_WORK_TABLE_HEADER.length}
                />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={formik.values?.jw_item_list as []}
              renderData={formik.values?.jw_item_list as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </div>
      </EximPaper>
    </form>
  );
}

export default JobWorkViewDetails;
