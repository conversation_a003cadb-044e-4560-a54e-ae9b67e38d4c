import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ReactNode} from 'react';

import './index.scss';

interface ICreateTransactionSubHeader {
  step: string;
  children: ReactNode;
  subTitle: string;
  isSecondSub?: boolean;
  secondSubTitle?: string;
}

function CreateTransactionSubHeader(props: ICreateTransactionSubHeader) {
  const {step, children, subTitle, secondSubTitle, isSecondSub} = props;

  return (
    <div className='create-transaction-sub-header-container'>
      <EximPaper>
        <div className='create-transaction-sub-header'>
          <div className='title-part'>
            <EximTypography fontWeight='semi-bold'>{`Step ${step} of 4`}</EximTypography>
            <EximTypography> | </EximTypography>
            <EximTypography fontWeight='semi-bold'>{subTitle}</EximTypography>
            {isSecondSub && (
              <>
                <EximTypography> | </EximTypography>
                <EximTypography fontWeight='semi-bold'>
                  {secondSubTitle}
                </EximTypography>
              </>
            )}
          </div>
          <div className='action-buttons-wrapper'>{children}</div>
        </div>
      </EximPaper>
    </div>
  );
}

CreateTransactionSubHeader.defaultProps = {
  secondSubTitle: '',
  isSecondSub: false,
};

export default CreateTransactionSubHeader;
