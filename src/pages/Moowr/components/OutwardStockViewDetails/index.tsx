import BusinessHeader from '@common/components/BusinessHeader';
import TableHeader from '@common/components/TableHeader';
import {
  AlertStatus,
  EximHeroDate,
  MOOWR_FILE_ROUTE_TYPE,
  MOOWR_SALE_TYPE,
  ResponseStatus,
} from '@common/constants';
import {formatDate, selectedOptionId} from '@common/helpers';
import {
  ICustomAxiosResp,
  IInvoiceValues,
  IMoowrViewPartialOutward,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOutwardStockInvDtls, saveEditInvoice} from '@pages/Moowr/api';
import {
  SALE_TYPE_DROPDOWN,
  VIEW_DETAILS_OUTWARD_STOCK_TABLE_HEADER,
} from '@pages/Moowr/utils';
import {outwardStockSchema} from '@pages/Moowr/utils/validations';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {FormikTouched, useFormik} from 'formik';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router';

import './index.scss';

interface IViewDetailsProps {
  isViewValidRecord: boolean;
  isEditable: boolean;
}

function OutwardStockViewDetails({
  isViewValidRecord,
  isEditable,
}: IViewDetailsProps) {
  const navigate = useNavigate();
  const {id} = useParams();
  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const initialInvValues: IMoowrViewPartialOutward = {
    ref_id: '',
    selected: false,
    sale_type: {value: '', isValid: true, errorMessage: ''},
    invoice_no: {value: '', isValid: true, errorMessage: ''},
    invoice_date: {value: '', isValid: true, errorMessage: ''},
    prod_code: {value: '', isValid: true, errorMessage: ''},
    prod_desc: {value: '', isValid: true, errorMessage: ''},
    hsn: {value: '', isValid: true, errorMessage: ''},
    uqc: {value: '', isValid: true, errorMessage: ''},
    qty_sold: {value: '', isValid: true, errorMessage: ''},
  };

  const initialTouched: FormikTouched<IMoowrViewPartialOutward> = {
    ref_id: false,
    selected: false,
    sale_type: {value: false},
    invoice_no: {value: false},
    invoice_date: {value: false},
    prod_code: {value: false},
    prod_desc: {value: false},
    hsn: {value: false},
    uqc: {value: false},
    qty_sold: {value: false},
  };

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(isEditable);
  const [viewDetailsAllData, setViewDetailsAllData] =
    useState<IMoowrViewPartialOutward>(initialInvValues);

  const formik = useFormik({
    initialValues: viewDetailsAllData,
    validationSchema: outwardStockSchema,
    validateOnChange: false,
    validateOnBlur: false,
    initialTouched,
    onSubmit: async (values: IMoowrViewPartialOutward) => {
      if (isEdit) {
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          fileType: MOOWR_FILE_ROUTE_TYPE.PARTIAL_OUTWARD_REGISTER,
        };
        const response = (await saveEditInvoice(
          payload,
          values
        )) as ICustomAxiosResp;
        if (response?.status?.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(false);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(-1);
        }
        if (response?.status?.toString() === ResponseStatus.ERROR) {
          setViewDetailsAllData(response?.data);
          formik.setValues(response?.data);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: 'Records are not valid',
              alertType: AlertStatus.DANGER,
            })
          );
        }
      }
    },
  });
  const {values, errors, touched, setFieldValue, validateField} = formik;

  const handleFieldChange = (
    fieldName: keyof IMoowrViewPartialOutward,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    formik.validateField(fieldName);
    const fieldValue = event.target.value;
    setFieldValue(fieldName, {
      ...(values[fieldName] as IInvoiceValues),
      value: fieldValue,
    });

    formik.setTouched({
      ...touched,
      [fieldName]: {
        ...(touched[fieldName] as {value: boolean}),
        value: true,
      },
    });
    validateField(fieldName);
  };

  const getInvoiceDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      refId: id,
      email,
    };
    const response = await getOutwardStockInvDtls(payload, 1, 5);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setViewDetailsAllData(response?.data);
      formik.setValues(response?.data);
      // INFO: Reforming the errors
      const formattedErrors = Object.entries(response?.data).reduce(
        (errorsObj: {[key: string]: {[key: string]: string}}, [key, value]) => {
          if (
            value &&
            typeof value === 'object' &&
            !(value as {isValid: boolean}).isValid &&
            (value as {errorMessage: string}).errorMessage
          ) {
            errorsObj[key] = {
              value: (value as {errorMessage: string}).errorMessage,
            };
          }
          return errorsObj;
        },
        {}
      );
      formik.setErrors(formattedErrors);
      setIsLoading(false);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, panNumber, email]);

  const getDateValue = (
    formikVal: string,
    key: keyof IMoowrViewPartialOutward
  ) => {
    const apiResVal = (
      viewDetailsAllData[key] as {value: string}
    )?.value?.toString();
    if (formikVal && formikVal.length > 4) {
      return formikVal?.replaceAll('-', '/');
    }
    if (apiResVal) {
      return apiResVal?.replaceAll('-', '/');
    }
    return undefined;
  };

  // Updating `isValid` and `errorMessage`
  const handleValidation = useCallback(() => {
    Object.keys(values).forEach((key) => {
      const field = values[key as keyof typeof values];
      const error = errors[key as keyof typeof errors];
      if (
        typeof field === 'object' &&
        field !== null &&
        'isValid' in field &&
        'errorMessage' in field
      ) {
        const typedField = field as IInvoiceValues;
        const errorMessage =
          typeof error === 'object' && error !== null ? error.value : undefined;

        typedField.isValid = !errorMessage;
        typedField.errorMessage = errorMessage || null;
      }
    });
  }, [errors, values]);

  useEffect(() => {
    handleValidation();
  }, [handleValidation]);

  useEffect(() => {
    getInvoiceDetails();
    setIsLoading(false);
  }, [getInvoiceDetails]);

  useEffect(() => {
    if (touched.sale_type?.value) {
      validateField('sale_type');
    }
    if (touched.invoice_no?.value) {
      validateField('invoice_no');
    }
    if (touched.invoice_date?.value) {
      validateField('invoice_date');
    }
    if (touched.prod_code?.value) {
      validateField('prod_code');
    }
    if (touched.prod_desc?.value) {
      validateField('prod_desc');
    }
    if (touched.hsn?.value) {
      validateField('hsn');
    }
    if (touched.uqc?.value) {
      validateField('uqc');
    }
    if (touched.qty_sold?.value) {
      validateField('qty_sold');
    }
  }, [touched, validateField]);

  return isLoading ? null : (
    <form onSubmit={formik.handleSubmit}>
      <BusinessHeader>
        <div className='btn-container'>
          {/* INFO: If we are viewing valid record than no need to show edit button */}
          {!isViewValidRecord ? (
            <EximButton
              size='small'
              type='submit'
              onClick={() => !isEdit && setIsEdit(true)}>
              {`${isEdit ? 'Update' : 'Edit'}`}
            </EximButton>
          ) : null}
        </div>
      </BusinessHeader>
      <EximPaper>
        <div className='sb-view-details'>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Product Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                {isEdit ? (
                  <EximTooltip
                    content={
                      errors?.sale_type?.value && errors?.sale_type.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximCustomDropdown
                      label='Sale Type'
                      optionsList={SALE_TYPE_DROPDOWN}
                      placeholder='Please Select Sale Type'
                      dataTestId='sale_type'
                      isInvalid={!!errors?.sale_type?.value}
                      onSelect={({value}) => {
                        setFieldValue('sale_type.value', value);
                        formik.setFieldTouched('sale_type.value', true);
                        formik.validateField('sale_type');
                      }}
                      readOnly={!isEdit}
                      defaultOption={selectedOptionId(
                        SALE_TYPE_DROPDOWN,
                        values?.sale_type?.value?.toString() || ''
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <>
                    <div className='product-details-title'>Sale Type</div>
                    <div className='product-details-value'>
                      {
                        MOOWR_SALE_TYPE[
                          values?.sale_type
                            ?.value as keyof typeof MOOWR_SALE_TYPE
                        ]
                      }
                    </div>
                  </>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? errors?.invoice_no?.value && errors?.invoice_no?.value
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Invoice No'
                    id='invoice_no'
                    name='invoice_no.value'
                    dataTestid='invoice_no'
                    maxLength={8}
                    value={values?.invoice_no?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!!errors?.invoice_no?.value}
                    onChange={(e) => handleFieldChange('invoice_no', e)}
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Invoice Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      errors?.invoice_date?.value && errors?.invoice_date?.value
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='invoice_date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!!errors?.invoice_date?.value}
                      onChange={(value) => {
                        setFieldValue('invoice_date.value', value);
                        formik.setFieldTouched('invoice_date.value', true);
                        formik.validateField('invoice_date');
                      }}
                      defaultValue={getDateValue(
                        values?.invoice_date?.value?.toString(),
                        'invoice_date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(values?.invoice_date?.value?.toString() || '')}
                  </div>
                )}
              </div>
            </div>
          </div>
          <EximDivider type='dashed' />
          <div className='view-details-table-container'>
            <table className='view-details-table'>
              <TableHeader
                mainHeader={VIEW_DETAILS_OUTWARD_STOCK_TABLE_HEADER}
              />
              <TableBody className='view-list-table-body'>
                <TableRow>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      content={
                        isEdit
                          ? errors?.prod_code?.value && errors?.prod_code?.value
                          : null
                      }
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='prod_code'
                        name='prod_code.value'
                        dataTestid='prod_code'
                        maxLength={20}
                        disabled={!isEdit}
                        value={values?.prod_code?.value?.toString()}
                        isInvalid={!!errors?.prod_code?.value}
                        onChange={(e) => handleFieldChange('prod_code', e)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      content={
                        isEdit
                          ? errors?.prod_desc?.value && errors?.prod_desc?.value
                          : null
                      }
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='prod_desc'
                        name='prod_desc.value'
                        dataTestid='prod_desc'
                        maxLength={200}
                        disabled={!isEdit}
                        value={values?.prod_desc?.value?.toString()}
                        isInvalid={!!errors?.prod_desc?.value}
                        onChange={(e) => handleFieldChange('prod_desc', e)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      content={
                        isEdit ? errors?.hsn?.value && errors?.hsn?.value : null
                      }
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='hsn'
                        name='hsn.value'
                        dataTestid='hsn'
                        maxLength={17}
                        disabled={!isEdit}
                        value={values?.hsn?.value?.toString()}
                        isInvalid={!!errors?.hsn?.value}
                        onChange={(e) => handleFieldChange('hsn', e)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      content={
                        isEdit ? errors?.uqc?.value && errors?.uqc?.value : null
                      }
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='uqc'
                        name='uqc.value'
                        dataTestid='uqc'
                        maxLength={17}
                        disabled={!isEdit}
                        value={values?.uqc?.value?.toString()}
                        isInvalid={!!errors?.uqc?.value}
                        onChange={(e) => handleFieldChange('uqc', e)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      content={
                        isEdit
                          ? errors?.qty_sold?.value && errors?.qty_sold?.value
                          : null
                      }
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='qty_sold'
                        name='qty_sold.value'
                        dataTestid='qty_sold'
                        maxLength={15}
                        disabled={!isEdit}
                        value={values?.qty_sold?.value?.toString()}
                        isInvalid={!!errors?.qty_sold?.value}
                        onChange={(e) => handleFieldChange('qty_sold', e)}
                      />
                    </EximTooltip>
                  </TableCell>
                </TableRow>
              </TableBody>
            </table>
          </div>
        </div>
      </EximPaper>
    </form>
  );
}

export default OutwardStockViewDetails;
