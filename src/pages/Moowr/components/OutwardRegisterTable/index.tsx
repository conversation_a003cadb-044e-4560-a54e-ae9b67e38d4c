import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  MOOWR_FILE_ROUTE_TYPE,
  MOOWR_ROUTE_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {ICustomAxiosResp, IMoowrViewOutwardRegister} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {deleteInvoices, getInvoices} from '@pages/Moowr/api';
import {
  OUTWARD_REGISTER_TABLE_HEADER,
  OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const {MOOWR, VIEW_DETAILS} = Path;

interface ITableProps {
  isValidRecord: boolean;
  startPeriod: string;
  endPeriod: string;
  setRefIdList: (value: string[]) => void;
  getInvoicesSummary: () => void;
  isDeleteAllClicked: boolean;
}

function OutwardRegisterTable({
  isValidRecord,
  startPeriod,
  endPeriod,
  setRefIdList,
  getInvoicesSummary,
  isDeleteAllClicked,
}: ITableProps) {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber, invoiceTxnId, isLastTransactionInvalid},
  } = useSelector((state: RootState) => state);

  const TABLE_HEADER = useMemo(
    () => OUTWARD_REGISTER_TABLE_HEADER(!isValidRecord),
    [isValidRecord]
  );

  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [invoicesData, setInvoicesData] = useState<IMoowrViewOutwardRegister[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handleSearchKey,
    handlePageChange,
    handleShowEntries,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleSingleSelect = (id: string) => {
    const updatedInvoices = invoicesData.map((item) =>
      item.ref_id === id ? {...item, selected: !item.selected} : item
    );
    const totalSelected = updatedInvoices.filter(
      (item) => item.selected
    ).length;
    setIsSelectedAll(totalSelected === updatedInvoices.length);
    setInvoicesData(updatedInvoices);
    // Storing selected id to delete the invoices
    const selectedIds = updatedInvoices
      .filter((obj) => obj.selected === true)
      .map((obj) => obj.ref_id);
    setRefIdList(selectedIds);
  };

  const handleSelectAll = () => {
    const allSelected = invoicesData.every((item) => item.selected);
    const updatedInvoices = invoicesData.map(
      (value: IMoowrViewOutwardRegister) => ({
        ...value,
        selected: !allSelected,
      })
    );
    setInvoicesData(updatedInvoices);
    setIsSelectedAll(!allSelected);
    // Storing selected id to delete the invoices
    const selectedIds = updatedInvoices
      .filter((obj) => obj.selected === true)
      .map((obj) => obj.ref_id);
    setRefIdList(selectedIds);
  };

  const getInvoicesData = useCallback(async () => {
    const payload = {
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      pan: panNumber,
      email,
      invType: isValidRecord ? 'VALID' : 'INVALID',
      fileType: MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    // INFO: If last transaction has invalid records the we need to fetch the data using txnId else period
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await getInvoices(payload, page, +showEntries);
    setInvoicesData(data?.records);
    setTotalRecords(data?.total_records);
    setIsSelectedAll(false); // Reset the value on API call
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    invoiceTxnId,
    isLastTransactionInvalid,
    startPeriod,
    endPeriod,
    email,
    panNumber,
    isValidRecord,
    showEntries,
    page,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleViewInvoice = (id: string) => {
    navigate(
      `${MOOWR}${VIEW_DETAILS}${MOOWR_ROUTE_TYPE.OUTWARD_REGISTER}/${id}`,
      {
        state: {
          isViewValidRecord: isValidRecord,
          isEditable: false,
          fileType: MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER,
        },
      }
    );
  };

  const handleEditInvoice = (id: string) => {
    navigate(
      `${MOOWR}${VIEW_DETAILS}${MOOWR_ROUTE_TYPE.OUTWARD_REGISTER}/${id}`,
      {
        state: {
          isViewValidRecord: isValidRecord,
          isEditable: true,
          fileType: MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER,
        },
      }
    );
  };

  const handleDeleteInvoice = async (id: string) => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      fileType: MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER,
    };
    const response = (await deleteInvoices(payload, false, [
      id,
    ])) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getInvoicesSummary(); // Updating the records count after delete record
      getInvoicesData(); // Updating the data after delete record
    }
  };

  useEffect(() => {
    getInvoicesData();
  }, [getInvoicesData, isDeleteAllClicked]);

  return (
    <div className='outward-register-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='invoices-table'>
        <TableHeader
          mainHeader={TABLE_HEADER}
          checked={isSelectedAll}
          onChange={handleSelectAll}
          handleSortBy={handleSortBy}
        />
        {invoicesData?.length > 0 ? (
          <TableBody className='invoices-tbody'>
            {invoicesData?.map((item: IMoowrViewOutwardRegister) => (
              <TableRow key={item.ref_id}>
                {!isValidRecord ? (
                  <TableCell className='checkbox-td'>
                    <EximCheckbox
                      id={`${item.ref_id}`}
                      name='invoiceRecord'
                      color='#2CB544'
                      size='medium'
                      checked={item.selected}
                      onChange={() => handleSingleSelect(item.ref_id)}
                    />
                  </TableCell>
                ) : null}
                <TableCell>{item.sale_type || '-'}</TableCell>
                <TableCell>{item.sb_no || '-'}</TableCell>
                <TableCell>{item.sb_date || '-'}</TableCell>
                <TableCell>{item.invoice_no || '-'}</TableCell>
                <TableCell>{item.invoice_date || '-'}</TableCell>
                <TableCell>{item.total_sale_qty || '-'}</TableCell>
                <TableCell>{item.total_fob_or_taxable_value || '-'}</TableCell>
                <TableCell>{item.total_taxes || '-'}</TableCell>
                <TableCell>
                  <TableActions
                    isViewIcon
                    isEditIcon
                    isDeleteIcon
                    isEditIconDisabled={isValidRecord}
                    isDeleteIconDisabled={isValidRecord}
                    viewToolTipText='View Invoice'
                    editToolTipText={!isValidRecord ? 'Edit Invoice' : ''}
                    deleteToolTipText={!isValidRecord ? 'Delete Invoice' : ''}
                    handleView={() => handleViewInvoice(item.ref_id)}
                    handleEdit={() => handleEditInvoice(item.ref_id)}
                    handleDelete={() => handleDeleteInvoice(item.ref_id)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={invoicesData as []}
        renderData={invoicesData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default OutwardRegisterTable;
