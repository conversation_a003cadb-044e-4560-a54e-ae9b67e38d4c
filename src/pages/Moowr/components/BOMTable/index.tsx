import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  MOOWR_FILE_ROUTE_TYPE,
  MOOWR_ROUTE_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {
  IBOMListTable,
  ICustomAxiosResp,
  IMoowrViewBomList,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {deleteInvoices, getInvoices} from '@pages/Moowr/api';
import {
  BOM_LIST_TABLE_HEADER,
  BOM_LIST_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState, dispatch} from '@store';
import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const {MOOWR, VIEW_DETAILS} = Path;

interface ITableProps {
  isValidRecord: boolean;
  startPeriod: string;
  endPeriod: string;
  setRefIdList: (value: string[]) => void;
  getInvoicesSummary: () => void;
  isDeleteAllClicked: boolean;
}

function BOMTable({
  isValidRecord,
  getInvoicesSummary,
  setRefIdList,
  isDeleteAllClicked,
  startPeriod,
  endPeriod,
}: ITableProps) {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {invoiceTxnId, panNumber, isLastTransactionInvalid},
  } = useSelector((state: RootState) => state);

  const TABLE_HEADER = useMemo(
    () => BOM_LIST_TABLE_HEADER(!isValidRecord),
    [isValidRecord]
  );

  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [BOMList, setBOMList] = useState<IMoowrViewBomList[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handleSearchKey,
    handlePageChange,
    handleShowEntries,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleSingleSelect = (id: string) => {
    const updateBomList = BOMList.map((item) =>
      item.bom_ref_id === id ? {...item, selected: !item.selected} : item
    );
    const totalSelected = updateBomList.filter((item) => item.selected).length;
    setIsSelectedAll(totalSelected === updateBomList.length);
    setBOMList(updateBomList);
    // Storing selected id to delete the invoices
    const selectedIds = updateBomList
      .filter((obj) => obj.selected === true)
      .map((obj) => obj.bom_ref_id);
    setRefIdList(selectedIds);
  };

  const handleSelectAll = () => {
    const allSelected = BOMList.every((item) => item.selected);
    const updateBomList = BOMList.map((value: IMoowrViewBomList) => ({
      ...value,
      selected: !allSelected,
    }));
    setBOMList(updateBomList);
    setIsSelectedAll(!allSelected);
    // Storing selected id to delete the invoices
    const selectedIds = updateBomList
      .filter((obj) => obj.selected === true)
      .map((obj) => obj.bom_ref_id);
    setRefIdList(selectedIds);
  };

  const getBOMList = useCallback(async () => {
    const payload = {
      txnId: '',
      pan: panNumber,
      email,
      startPeriod: '',
      endPeriod: '',
      invType: isValidRecord ? 'VALID' : 'INVALID',
      fileType: MOOWR_FILE_ROUTE_TYPE.BOM_STATEMENT,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    // INFO: If last transaction has invalid records the we need to fetch the data using txnId
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    }
    const {data} = await getInvoices(payload, page, +showEntries);
    setBOMList(data?.records);
    setTotalRecords(data?.total_records);
    // INFO: Adding the invalid records refId to delete the all invalid records
    if (!isValidRecord) {
      const selectedIds = data?.records?.map(
        (obj: IBOMListTable) => obj['bom-ref-id']
      );
      setRefIdList(selectedIds);
    } else {
      setRefIdList([]);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    invoiceTxnId,
    panNumber,
    email,
    isValidRecord,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleViewLog = (prodCode: string, bomVersion: string) => {
    const version = bomVersion?.split('-')[1];
    navigate(`${MOOWR}${VIEW_DETAILS}${MOOWR_ROUTE_TYPE.BOM_STATEMENT}`, {
      state: {prodCode, bomVersion: version || 1},
    });
  };

  const handleDeleteLog = async (id: string) => {
    const payload = {
      pan: panNumber,
      fileType: MOOWR_FILE_ROUTE_TYPE.BOM_STATEMENT,
      email,
      txnId: invoiceTxnId,
    };
    const response = (await deleteInvoices(payload, false, [
      id,
    ])) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getBOMList(); // Updating the data after delete record
      getInvoicesSummary(); // Updating the records count after delete record
    }
  };

  useEffect(() => {
    getBOMList();
  }, [getBOMList, isDeleteAllClicked]);

  return (
    <div className='bom-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        {/* TODO: Keeping it for future */}
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={BOM_LIST_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='bom-list-table'>
        <TableHeader
          mainHeader={TABLE_HEADER}
          handleSortBy={handleSortBy}
          checked={isSelectedAll}
          onChange={handleSelectAll}
        />
        {BOMList?.length > 0 ? (
          <TableBody className='bom-list-tbody'>
            {BOMList?.map((item: IMoowrViewBomList, index: number) => (
              <TableRow key={`${item.bom_ref_id}${index + 1}`}>
                {!isValidRecord ? (
                  <TableCell className='checkbox-td'>
                    <EximCheckbox
                      id={item?.bom_ref_id}
                      name='invoiceRecord'
                      color='#2CB544'
                      size='medium'
                      checked={item.selected}
                      onChange={() => handleSingleSelect(item.bom_ref_id)}
                    />
                  </TableCell>
                ) : null}
                <TableCell>{item?.prod_code?.value}</TableCell>
                <TableCell>{item?.prod_desc?.value}</TableCell>
                <TableCell>{item?.total_items}</TableCell>
                <TableCell>{item?.bom_version}</TableCell>
                <TableCell>
                  <TableActions
                    isViewIcon
                    isDeleteIcon
                    isDeleteIconDisabled={isValidRecord}
                    viewToolTipText='View Invoice'
                    deleteToolTipText={!isValidRecord ? 'Delete Invoice' : ''}
                    handleView={() =>
                      handleViewLog(
                        item.prod_code.value.toString(),
                        item.bom_version
                      )
                    }
                    handleDelete={() => handleDeleteLog(item.bom_ref_id)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={BOMList as []}
        renderData={BOMList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default memo(BOMTable);
