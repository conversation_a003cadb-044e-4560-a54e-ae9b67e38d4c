@import '@utils/main.scss';

.view-bom-review-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    .main-header {
      border: none;
    }
  }

  .view-details-card-container {
    @include margin-top(20px);
    display: grid;
    grid-template-columns: 1fr 3fr 1fr 1fr;
    gap: 20px;
    .card-wrapper {
      width: 100%;
      .card-content {
        @include margin(0);
      }
    }
    .card-wrapper:nth-child(3) {
      max-height: 88px;
      .card-content {
        max-height: 88px;
      }
    }
  }

  .view-bom-review-table-container {
    @include padding(20px);
    @include margin(20px auto 32px);
    .view-bom-review-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .view-bom-review-tbody {
        .error-text-td {
          color: $error;
        }
      }
    }

    .table-search-container,
    .table-footer {
      @include padding(10px auto);
    }
  }
}
