import {REGEXP} from '@common/constants';
import * as Yup from 'yup';

// INFO: Create Transaction Form Schema
export const createTransactionSchema = Yup.object().shape({
  txnName: Yup.string()
    .required('Please enter the transaction name.')
    .max(300, 'Transaction name length should be 300 characters.')
    .matches(REGEXP.alphanumeric, 'Special characters are not allowed.'),
  txnType: Yup.string().required('Please select the transaction type.'),
  toDate: Yup.string().required('Please select the transaction period.'),
});

export const TRANSACTIONS_TYPE_DROPDOWN = [
  {id: '1', value: 'MOOWR_FG_TXN', label: 'Finished Goods Sales'},
  {id: '2', value: 'MOOWR_RETURN_TXN', label: 'Return / Trade'},
  {id: '3', value: 'MOOWR_WASTAGE_TXN', label: 'Wastage'},
];

export const CONSUMPTION_LIST_TABLE_HEADER = [
  {title: 'Consumption Name', width: '35%'},
  {title: 'Consumption Period', width: '25%'},
  {title: 'Consumption Type', width: '25%'},
  {title: 'Status', width: '15%'},
];

export const PRODUCT_SELECTION_TABLE_HEADER = (isCheckbox: boolean) => {
  const tableHeader = [
    {title: 'Product Code', width: '10%'},
    {title: 'Product Name / Description', width: '30%'},
    {title: 'Invoice No / Sales Order No', width: '30%'},
    {title: 'QTY', width: '12%'},
    {title: 'UQC', width: '16%'},
  ];
  if (isCheckbox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};

export const PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod-code', label: 'Product Code'},
  {id: '2', value: 'prod-desc', label: 'Product Name / Description'},
  {id: '3', value: 'inv-no', label: 'Invoice No / Sales Order No'},
];

export const PRODUCT_OVERALL_SELECTION_SUMMARY = [
  {title: 'Particulars', width: '55%'},
  {title: 'Unique Products', width: '45%'},
];

export const INWARD_REVIEW_SELECTION_SUMMARY_TABLE_HEADER = [
  {title: 'Particulars', width: '25%'},
  {title: 'BOE', width: '25%'},
  {title: 'Item', width: '25%'},
];

export const INWARD_REVIEW_BOE_TABLE_HEADER = [
  {
    title: 'Bill of Entry No / Invoice No',
    width: 'auto',
    sortingKey: 'boe_no',
  },
  {
    title: 'Bill of Entry Date / Invoice Date',
    width: 'auto',
    sortingKey: 'boe_date',
  },
  {title: 'Unique Items', width: 'auto'},
  {title: 'Total Item Quantity', width: 'auto'},
  {title: 'Total Assessable Value', width: 'auto'},
];
export const INWARD_REVIEW_BOE_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'boe_no', label: 'Bill of Entry No / Invoice No'},
  {id: '2', value: 'boe_date', label: 'Bill of Entry Date / Invoice Date'},
];

export const INWARD_REVIEW_ITEM_LIST_TABLE_HEADER = [
  {title: 'Bill Of entry no.', width: '12%', sortingKey: 'boe_no'},
  {title: 'Item code', width: '12%', sortingKey: 'item_code'},
  {
    title: 'Item Name / Description',
    width: '40%',
    sortingKey: 'item_desc',
  },
  {title: 'Total Value', width: '12%'},
  {title: 'Total Purchase', width: '12%'},
];
export const INWARD_REVIEW_ITEM_LIST_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'boe_no', label: 'Bill Of entry no.'},
  {id: '2', value: 'item_code', label: 'Item code'},
  {id: '2', value: 'item_desc', label: 'Item Name / Description'},
];

export const CONSUMPTION_REVIEW_TABLE_HERDER = [
  {title: 'Particulars', width: '15%'},
  {title: 'Invoices', width: '13%'},
  {title: 'Unique Product', width: '13%'},
  {title: 'Total Product Qty', width: '13%'},
  {title: 'BOE', width: '13%'},
  {title: 'Unique Items', width: '13%'},
];

export const FINAL_SUMMARY_TRANSACTION_TABLE_HEADER = [
  {
    title: 'Proforma Invoice/ Sales Order/ Tax Invoice No',
    width: '7%',
    sortingKey: 'invoice_no',
  },
  {
    title: 'Proforma Invoice /Sales Order/ Tax Invoice Date',
    width: '7%',
    sortingKey: 'invoice_date',
  },
  {title: 'Product/FG Code', width: '7%', sortingKey: 'prod_code'},
  {title: 'Total Sales/Export Qty.', width: '7%', sortingKey: 'total_sale_qty'},
  {
    title: 'Total Item Qty Consumed',
    width: '15%',
    sortingKey: 'total_item_qty_consumed',
  },
  {
    title: 'Total Balance Item Qty.',
    width: '10%',
    sortingKey: 'total_item_qty_balance',
  },
  {
    title: 'BCD amount for Consumption',
    width: '10%',
    sortingKey: 'total_bcd_amt',
  },
  {
    title: 'IGST amount for Consumption',
    width: '7%',
    sortingKey: 'total_igst',
  },
  {
    title: 'CGST amount for Consumption',
    width: '8%',
    sortingKey: 'total_cgst',
  },
  {
    title: 'SGST amount for Consumption',
    width: '8%',
    sortingKey: 'total_sgst',
  },
];
export const FINAL_SUMMARY_TRANSACTION_TABLE_SEARCH_DROPDOWN = [
  {
    id: '1',
    value: 'invoice_no',
    label: 'Proforma Invoice/ Sales Order/ Tax Invoice No',
  },
  {
    id: '2',
    value: 'invoice_date',
    label: 'Proforma Invoice /Sales Order/ Tax Invoice Date',
  },
  {id: '3', value: 'prod_code', label: 'Product/FG Code'},
];

export const DOWNLOAD_REPORTS_MODAL_TABLE_HEADER = [
  {title: 'Reports', width: '70%'},
  {title: 'Action', width: '30%'},
];

export const OUTWARD_STOCK_TABLE_HEADER = (isCheckBox: boolean) => {
  const tableHeader = [
    {title: 'Invoice / Shipping Bill No.', sortingKey: 'invoice_no'},
    {title: 'Invoice Date / Shipping Bill Date', sortingKey: 'invoice_date'},
    {title: 'Product Code', sortingKey: 'prod_code'},
    {title: 'Product Description'},
    {title: 'HSN'},
    {title: 'UQC'},
    {title: 'Qty. Exported / Sold'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckBox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};
export const OUTWARD_STOCK_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'invoice_date', label: 'Invoice Date'},
  {id: '2', value: 'invoice_no', label: 'Invoice No.'},
  {id: '3', value: 'prod_code', label: 'Product Code'},
];

export const INWARD_REGISTER_TABLE_HEADER = (isCheckBox: boolean) => {
  const tableHeader = [
    {title: 'Purchase Type', sortingKey: 'purchase_type'},
    {title: 'Bill of Entry No. / Invoice No.', sortingKey: 'boe_no'},
    {title: 'Bill of Entry Date / Invoice Date', sortingKey: 'boe_date'},
    {title: 'Supplier GSTN'},
    {title: 'Total Unique Items'},
    {title: 'Assessable / Taxable Value'},
    {title: 'Total Duty (BCD + SWS + Additional)'},
    {title: 'Total Taxes (IGST + CGST + SGST + CESS)QC'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckBox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};
export const INWARD_REGISTER_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'purchase_type', label: 'Purchase Type'},
  {id: '2', value: 'boe_no', label: 'Bill of Entry No.'},
  {id: '3', value: 'boe_date', label: 'Bill of Entry Date'},
];

export const BOM_LIST_TABLE_HEADER = (isCheckBox: boolean) => {
  const tableHeader = [
    {title: 'Product code', width: '20%', sortingKey: 'prod_code'},
    {
      title: 'Description & Technical Characteristics',
      width: '40%',
    },
    {title: 'Total Items', width: '10%'},
    {title: 'BOM Version', width: '14%', sortingKey: 'bom_version'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckBox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};
export const BOM_LIST_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod_code', label: 'Product Code'},
  {id: '2', value: 'bom_version', label: 'BOM Version'},
];
export const BOM_REVIEW_VIEW_DETAILS_TABLE_HEADER = [
  {title: 'Item Code', width: 'auto', sortingKey: 'item_code'},
  {title: 'Item Name / Description', width: 'auto'},
  {
    title: 'Procurement Type',
    width: 'auto',
    sortingKey: 'procurement_type',
  },
  {title: 'Item Quantity', width: 'auto', sortingKey: 'item_qty'},
  {title: 'UQC', width: '10%'},
];
export const BOM_REVIEW_VIEW_DETAILS_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'item_code', label: 'Item Code'},
  {id: '2', value: 'item_qty', label: 'Item Quantity'},
  {id: '3', value: 'procurement_type', label: 'Procurement Type'},
];

export const OUTWARD_REGISTER_TABLE_HEADER = (isCheckBox: boolean) => {
  const tableHeader = [
    {title: 'Sale Type', sortingKey: 'sale_type'},
    {title: 'Shipping Bill No. / Invoice No.', sortingKey: 'sb_no'},
    {title: 'Shipping Bill Date / Invoice Date', sortingKey: 'sb_date'},
    {title: 'Invoice No.'},
    {title: 'Invoice Date'},
    {title: 'Qty. Exported / Sold'},
    {title: 'FOB value / Taxable value'},
    {title: 'Total Taxes (IGST + CGST + SGST + CESS)QC'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckBox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};
export const OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'sale_type', label: 'Sales Type'},
  {id: '2', value: 'sb_no', label: 'Shipping Bill No.'},
  {id: '3', value: 'sb_date', label: 'Shipping Bill Date'},
];

export const JOB_WORK_TABLE_HEADER = (isCheckBox: boolean) => {
  const tableHeader = [
    {title: 'Type Of Processing', sortingKey: 'type_of_Processing'},
    {title: 'Date', sortingKey: 'date'},
    {title: 'Qty.'},
    {title: 'Value'},
    {title: 'Delivery challan/Job work Id'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckBox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};
export const JOB_WORK_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'type_of_Processing', label: 'Type Of Processing'},
  {id: '2', value: 'date', label: 'Date'},
];

export const VIEW_DETAILS_OUTWARD_STOCK_TABLE_HEADER = [
  {title: 'Product code', width: 'auto'},
  {title: 'Product Description'},
  {title: 'HSN'},
  {title: 'UQC'},
  {title: 'Qty. Exported / Sold'},
];
export const VIEW_DETAILS_OUTWARD_REGISTER_TABLE_HEADER = [
  {title: 'Product code', width: 'auto', sortingKey: 'prod_code'},
  {title: 'Product Description', sortingKey: 'prod_desc'},
  {title: 'HSN'},
  {title: 'UQC'},
  {title: 'Qty. Exported / Sold'},
  {title: 'FOB value / Taxable value'},
  {title: 'Tax Rate'},
  {title: 'IGST Amount'},
  {title: 'CGST Amount'},
  {title: 'SGST Amount'},
  {title: 'CESS Amount'},
];
export const VIEW_DETAILS_OUTWARD_REGISTER_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod_code', label: 'Product Code'},
  {id: '2', value: 'prod_desc', label: 'Product Description'},
];

export const VIEW_DETAILS_JOB_WORK_TABLE_HEADER = [
  {title: 'Item code', sortingKey: 'item_code'},
  {title: 'Item Description', sortingKey: 'item_desc'},
  {title: 'Qty.'},
  {title: 'UQC'},
  {title: 'Value'},
];
export const VIEW_DETAILS_JOB_WORK_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'item_code', label: 'Item Code'},
  {id: '2', value: 'item_desc', label: 'Item Description'},
];

export const SALE_TYPE_DROPDOWN = [
  {id: '1', value: 'EXPORT_MFG', label: 'Export (Mfg)'},
  {id: '2', value: 'DOMESTIC_MFG', label: 'Domestic (Mfg)'},
  {id: '3', value: 'EXPORT_TRAD', label: 'Export (trading)'},
  {id: '4', value: 'DOMESTIC_TRAD', label: 'Domestic (trading)'},
];

export const PROCESSING_TYPE_DROPDOWN = [
  {id: '1', value: 'Job work outward', label: 'Job work outward'},
  {id: '2', value: 'Job work inward', label: 'Job work inward'},
  {id: '3', value: 'Other activity', label: 'Other activity'},
];

export const RECON_MATCH_MISS_MATCH_INVOICES_MAIN_TABLE_HEADER = [
  {title: 'Outward Plan Details', colspan: 5, width: '50%'},
  {title: 'Outward Registered Details', colspan: 5, width: '50%'},
];

export const RECON_MISSING_INVOICES_MAIN_TABLE_HEADER = [
  {
    title: 'Outward Plan (Missing in Outward Register)',
    colspan: 5,
    width: '50%',
  },
  {
    title: 'Outward Register (Missing in Outward Plan)',
    colspan: 5,
    width: '50%',
  },
];

export const RECON_INVOICES_SUB_TABLE_HEADER = (isOutwardPlan: boolean) => {
  const tableHeader = [
    {
      title: 'Invoice No.',
      width: '7%',
      sortingKey: isOutwardPlan ? 'consReportInvNo' : 'gstInvNo',
    },
    {
      title: 'Date',
      sortingKey: isOutwardPlan ? 'consReportInvDate' : 'gstInvDate',
    },
    {
      title: 'Product Code',
      sortingKey: isOutwardPlan ? 'consReportProdCode' : 'prodCode',
    },
    {
      title: 'Product Quantity',
      width: '10%',
      sortingKey: isOutwardPlan ? 'consReportQtySold' : 'qtySold',
    },
  ];

  if (isOutwardPlan) {
    return [
      {title: 'Consumption Name', sortingKey: 'consumptionName'},
      ...tableHeader,
    ];
  }
  return [{title: 'Shipping Bill', sortingKey: 'sbNo'}, ...tableHeader];
};

export const RECON_INVOICE_SEARCH_TYPE_DROPDOWN = [
  {id: '1', value: 'consumptionName', label: 'Consumption Name'},
  {id: '2', value: 'consReportInvNo', label: 'Invoice No.'},
  {id: '3', value: 'shippingBillNo', label: 'Shipping Bill'},
  {id: '4', value: 'prodCode', label: 'Product Code'},
];

export const ROLLBACK_RECTIFY_MAIN_TABLE_HEADER = [
  {title: 'Outward Plan', colspan: 3, width: '33%'},
  {title: 'Consider for Rectification', colspan: 3, width: '33%'},
  {title: 'Unmatched Invoices', colspan: 3, width: '33%'},
];

export const ROLLBACK_RECTIFY_SUB_TABLE_HEADER = [
  {title: 'No. of Invoices', width: '11%'},
  {title: 'No. of Product', width: '11%'},
  {title: 'Total Product Qty', width: '11%'},
  {title: 'No. of Invoices', width: '11%'},
  {title: 'No. of Product', width: '11%'},
  {title: 'Total Product Qty', width: '11%'},
  {title: 'No. of Invoices', width: '11%'},
  {title: 'No. of Product', width: '11%'},
  {title: 'Total Product Qty', width: '11%'},
];

export const RECON_SUMMARY_CARDS = [
  {
    type: 'MATCH',
    title: 'Match',
    dataKey: 'matchCount',
  },
  {
    type: 'RECTIFY',
    title: 'Rectify',
    dataKey: 'rectifiedCount',
  },
  {
    type: 'ROLL_BACK',
    title: 'Roll Back',
    dataKey: 'rollbackCount',
  },
];
