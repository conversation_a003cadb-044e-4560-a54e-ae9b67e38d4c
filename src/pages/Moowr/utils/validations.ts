import {REGEXP} from '@common/constants';
import {IInvoiceValues} from '@common/interfaces';
import {isAfter, parse} from 'date-fns';
import * as Yup from 'yup';

const getDynamicRegX = (specialCharAllowed: string) => {
  const escapedChars = specialCharAllowed.replace(
    /[-/\\^$*+?.()|[\]{}]/g,
    '\\$&'
  );
  return new RegExp(`^[a-zA-Z0-9${escapedChars}]*$`);
};

export const outwardStockSchema = Yup.object({
  sale_type: Yup.object({
    value: Yup.string().nullable().required('Sale type is required'),
  }),
  invoice_no: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Invoice number is required')
      .min(1, 'Minimum length should be 1')
      .max(7, 'Maximum length should be 7')
      .matches(REGEXP.gstInvoiceNo, 'Invalid invoice number'),
  }),
  invoice_date: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Invoice date is required')
      .test(
        'not-future-date',
        'Date cannot be in the future',
        function checkDate(value) {
          if (!value) return false;
          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
          return !isAfter(parsedDate, new Date());
        }
      ),
  }),
  prod_code: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Product code is required')
      .matches(
        getDynamicRegX('/&_'),
        'Special character are not allowed except & / _'
      ),
  }),
  prod_desc: Yup.object({
    value: Yup.string().nullable().required('Product description is required'),
  }),
  hsn: Yup.object({
    value: Yup.string()
      .nullable()
      .required('HSN is required')
      .matches(REGEXP.alphanumeric, 'Special character are not allowed'),
  }),
  uqc: Yup.object({
    value: Yup.string()
      .nullable()
      .required('UQC is required')
      .matches(
        getDynamicRegX('-'),
        'Special character are not allowed except -'
      ),
  }),
  qty_sold: Yup.object({
    value: Yup.string().nullable().required('Quantity sold is required'),
  }),
});

export const outwardInitialValues = {
  sale_type: {value: '', isValid: true, errorMessage: ''},
  sb_no: {value: '', isValid: true, errorMessage: ''},
  sb_date: {value: '', isValid: true, errorMessage: ''},
  leo_date: {value: '', isValid: true, errorMessage: ''},
  port_of_export: {value: '', isValid: true, errorMessage: ''},
  sup_gstin: {value: '', isValid: true, errorMessage: ''},
  invoice_no: {value: '', isValid: true, errorMessage: ''},
  invoice_date: {value: '', isValid: true, errorMessage: ''},
  ctin: {value: '', isValid: true, errorMessage: ''},
  pos: {value: '', isValid: true, errorMessage: ''},
  total_inv_val: 0,
  products: [
    {
      prod_code: {value: '', isValid: true, errorMessage: ''},
      prod_desc: {value: '', isValid: true, errorMessage: ''},
      hsn: {value: '', isValid: true, errorMessage: ''},
      uqc: {value: '', isValid: true, errorMessage: ''},
      qty_sold: {value: 0, isValid: true, errorMessage: ''},
      fob_or_taxable_value: {value: 0, isValid: true, errorMessage: ''},
      tax_rt: {value: '', isValid: true, errorMessage: ''},
      igst_amt: {value: 0, isValid: true, errorMessage: ''},
      cgst_amt: {value: '', isValid: true, errorMessage: ''},
      sgst_amt: {value: '', isValid: true, errorMessage: ''},
      cess_amt: {value: '', isValid: true, errorMessage: ''},
    },
  ],
};

const outwardProductsSchema = Yup.object({
  prod_code: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Product code is required')
      .matches(
        getDynamicRegX('/&_'),
        'Special character are not allowed except & / _'
      ),
  }),
  prod_desc: Yup.object({
    value: Yup.string().nullable().required('Product description is required'),
  }),
  hsn: Yup.object({
    value: Yup.string()
      .nullable()
      .required('HSN is required')
      .matches(REGEXP.alphanumeric, 'Special character are not allowed'),
  }),
  uqc: Yup.object({
    value: Yup.string()
      .nullable()
      .required('UQC is required')
      .matches(
        getDynamicRegX('-'),
        'Special character are not allowed except -'
      ),
  }),
  qty_sold: Yup.object({
    value: Yup.number()
      .nullable()
      .required('Quantity sold is required')
      .positive('Quantity must be positive'),
  }),
  fob_or_taxable_value: Yup.object({
    value: Yup.number()
      .nullable()
      .required('Taxable value is required')
      .positive('Taxable value must be positive'),
  }),
  tax_rt: Yup.object({
    value: Yup.number()
      .nullable()
      .required('Tax rate is required')
      .positive('Tax rate must be positive'),
  }),
  igst_amt: Yup.object({
    value: Yup.number()
      .nullable()
      .required('IGST amount is required')
      .positive('IGST amount must be positive'),
  }),
  cgst_amt: Yup.object({
    value: Yup.number()
      .nullable()
      .required('CGST amount is required')
      .positive('CGST amount must be positive'),
  }),
  sgst_amt: Yup.object({
    value: Yup.number()
      .nullable()
      .required('SGST amount is required')
      .positive('SGST amount must be positive'),
  }),
  cess_amt: Yup.object({
    value: Yup.string()
      .nullable()
      .matches(REGEXP.numbersWithZero, 'Invalid amount'),
  }),
});

export const outwardRegisterSchema = Yup.object({
  sup_gstin: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Supplier GSTIN is required')
      .matches(REGEXP.validateGstin, 'Invalid GSTIN'),
  }),
  invoice_no: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Invoice number is required')
      .min(3, 'Minimum length should be 3')
      .max(7, 'Maximum length should be 7')
      .matches(REGEXP.gstInvoiceNo, 'Invalid invoice number'),
  }),
  sale_type: Yup.object({
    value: Yup.string().nullable().required('Sale type is required'),
  }),
  sb_no: Yup.object({
    value: Yup.string()
      .nullable()
      .min(3, 'Minimum length should be 3')
      .max(7, 'Maximum length should be 7')
      .matches(REGEXP.alphanumeric, 'Special character are not allowed'),
  }),
  sb_date: Yup.object({
    value: Yup.string()
      .nullable()
      .test('not-future-date', 'Date cannot be in the future', (value) => {
        if (!value) return false;
        const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
        return !isAfter(parsedDate, new Date());
      }),
  }),
  invoice_date: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Invoice date is required')
      .test(
        'not-future-date',
        'Date cannot be in the future',
        function checkDate(value) {
          if (!value) return false;
          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
          return !isAfter(parsedDate, new Date());
        }
      )
      .test('required', 'Please enter the leo date', function checkVal(value) {
        const options = this.options as {
          from: {value: {sale_type: IInvoiceValues}}[];
        };
        const saleType = options?.from[1]?.value?.sale_type?.value;
        return !(
          saleType?.toString().includes('EXPORT') &&
          (value === undefined || value?.length === 0)
        );
      }),
  }),
  leo_date: Yup.object({
    value: Yup.string()
      .nullable()
      .test(
        'not-future-date',
        'Date cannot be in the future',
        function checkDate(value) {
          if (!value) return false;
          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
          return parsedDate && !isAfter(parsedDate, new Date());
        }
      )
      .test('required', 'Please enter the leo date', function checkVal(value) {
        const options = this.options as {
          from: {value: {sale_type: IInvoiceValues}}[];
        };
        const saleType = options?.from[1]?.value?.sale_type?.value;
        return !(
          saleType?.toString().includes('EXPORT') &&
          (value === undefined || value?.length === 0)
        );
      }),
  }),
  port_of_export: Yup.object({
    value: Yup.string()
      .nullable()
      .matches(REGEXP.alphanumeric, 'Special character are not allowed')
      .test(
        'required',
        'Please enter the port of export',
        function checkVal(value) {
          const options = this.options as {
            from: {value: {sale_type: IInvoiceValues}}[];
          };
          const saleType = options?.from[1]?.value?.sale_type?.value;
          return !(
            saleType?.toString().includes('EXPORT') &&
            (value === undefined || value?.length === 0)
          );
        }
      )
      .test('not-required', 'Not required', function checkVal(value) {
        const options = this.options as {
          from: {value: {sale_type: IInvoiceValues}}[];
        };
        const saleType = options?.from[1]?.value?.sale_type?.value;
        return !(
          saleType?.toString().includes('DOMESTIC') &&
          value !== undefined &&
          value !== null &&
          value?.length > 0
        );
      }),
  }),
  ctin: Yup.object({
    value: Yup.string()
      .nullable()
      .matches(REGEXP.validateGstin, 'Invalid GSTIN'),
  }),
  pos: Yup.object({
    value: Yup.string()
      .nullable()
      .matches(REGEXP.alphanumeric, 'Special character are not allowed'),
  }),
  total_inv_val: Yup.string()
    .nullable()
    .matches(REGEXP.numbersWithZero, 'Invalid amount')
    .required('Invoice value is required'),
  products: Yup.array().of(outwardProductsSchema),
});

export const jobWorkInitialValues = {
  type_of_Processing: {value: '', isValid: true, errorMessage: ''},
  date: {value: '', isValid: true, errorMessage: ''},
  time: {value: '', isValid: true, errorMessage: ''},
  ewb: {value: '', isValid: true, errorMessage: ''},
  delivery_chalan: {value: '', isValid: true, errorMessage: ''},
  job_worker_name_address: {value: '', isValid: true, errorMessage: ''},
  job_worker_gstin: {value: '', isValid: true, errorMessage: ''},
  jw_item_list: [
    {
      item_code: {value: '', isValid: true, errorMessage: ''},
      item_desc: {value: '', isValid: true, errorMessage: ''},
      item_qty: {value: '', isValid: true, errorMessage: ''},
      uqc: {value: '', isValid: true, errorMessage: ''},
      value: {value: 0, isValid: true, errorMessage: ''},
    },
  ],
  total_item_count: 0,
};

const jobWorkItemsSchema = Yup.object({
  item_code: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Item code is required')
      .matches(
        getDynamicRegX('/&_'),
        'Special character are not allowed except & / _'
      ),
  }),
  item_desc: Yup.object({
    value: Yup.string().nullable().required('Item description is required'),
  }),
  item_qty: Yup.object({
    value: Yup.number()
      .nullable()
      .required('Quantity is required')
      .positive('Quantity must be positive'),
  }),
  uqc: Yup.object({
    value: Yup.string()
      .nullable()
      .required('UQC is required')
      .matches(
        getDynamicRegX('-'),
        'Special character are not allowed except -'
      ),
  }),
  value: Yup.object({
    value: Yup.number()
      .nullable()
      .required('Value is required')
      .positive('Value must be positive'),
  }),
});

export const jobWorkSchema = Yup.object({
  type_of_Processing: Yup.object({
    value: Yup.string().nullable().required('Processing type is required'),
  }),
  date: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Date is required')
      .test(
        'not-future-date',
        'Date cannot be in the future',
        function checkDate(value) {
          if (!value) return false;
          const parsedDate = parse(value, 'dd-MM-yyyy', new Date());
          return !isAfter(parsedDate, new Date());
        }
      ),
  }),
  time: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Time is required')
      .matches(
        REGEXP.time,
        'Invalid Time, format should be HH:MM or HH::MM:SS'
      ),
  }),
  ewb: Yup.object({
    value: Yup.string()
      .nullable()
      .length(12, 'Length must be exactly 12 characters')
      .matches(REGEXP.alphanumeric, 'Special character are not allowed')
      .test('not-required', 'Not required', function checkVal(value) {
        const options = this.options as {
          from: {value: {type_of_Processing: IInvoiceValues}}[];
        };
        const processingType =
          options?.from[1]?.value?.type_of_Processing?.value;
        return !(
          processingType?.toString().includes('Other') &&
          value !== undefined &&
          value !== null &&
          value?.length > 0
        );
      }),
  }),
  delivery_chalan: Yup.object({
    value: Yup.string()
      .nullable()
      .required('Delivery challan is required')
      .matches(REGEXP.alphanumeric, 'Special character are not allowed'),
  }),
  job_worker_name_address: Yup.object({
    value: Yup.string()
      .nullable()
      .test('not-required', 'Not required', function checkVal(value) {
        const options = this.options as {
          from: {value: {type_of_Processing: IInvoiceValues}}[];
        };
        const processingType =
          options?.from[1]?.value?.type_of_Processing?.value;
        return !(
          processingType?.toString().includes('Other') &&
          value !== undefined &&
          value !== null &&
          value?.length > 0
        );
      })
      .test(
        'required',
        'Please enter the name & address',
        function checkVal(value) {
          const options = this.options as {
            from: {value: {type_of_Processing: IInvoiceValues}}[];
          };
          const processingType =
            options?.from[1]?.value?.type_of_Processing?.value;
          return !(
            !processingType?.toString().includes('Other') &&
            (value === undefined || value?.length === 0)
          );
        }
      ),
  }),
  job_worker_gstin: Yup.object({
    value: Yup.string()
      .nullable()
      .matches(REGEXP.validateGstin, 'Invalid GSTIN')
      .test('not-required', 'Not required', function checkVal(value) {
        const options = this.options as {
          from: {value: {type_of_Processing: IInvoiceValues}}[];
        };
        const processingType =
          options?.from[1]?.value?.type_of_Processing?.value;
        return !(
          processingType?.toString().includes('Other') &&
          value !== undefined &&
          value !== null &&
          value?.length > 0
        );
      }),
  }),
  jw_item_list: Yup.array().of(jobWorkItemsSchema),
});
