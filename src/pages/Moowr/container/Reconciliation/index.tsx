import BusinessHeader from '@common/components/BusinessHeader';
import ConfirmationModal from '@common/components/ConfirmationModal';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {
  AlertStatus,
  HelmetTitle,
  MoowrReconReports,
  MoowrReconType,
  Path,
  ResponseStatus,
} from '@common/constants';
import {downloadLargeFileData, getAlertMessage} from '@common/helpers';
import {ICustomAxiosResp, IMoowrReconciliationInv} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {
  downloadLargeFile,
  freezeReconciliation,
  reconDetails,
  reportsExport,
  reportsExportHistory,
} from '@pages/Moowr/api';
import FileDownloadStripe from '@pages/Moowr/components/FileDownloadStripe';
import {moowrActions} from '@pages/Moowr/store/reduce';
import EximActionableDropdown from '@shared/components/EximActionableDropdown';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTabs from '@shared/components/EximTabs';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import ReconInvoicesTable from './ReconInvoicesTable';
import RectifyRollbackModal from './RectifyRollbackModal';
import './index.scss';

function Reconciliation() {
  const navigate = useNavigate();
  const {
    moowr: {
      reconMonth,
      panNumber,
      reconTxnId,
      reconStatus,
      reconActiveTab: activeTab = MoowrReconType.MATCH,
    },
  } = useSelector((state: RootState) => state);

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [isOpenFreezeModal, setIsOpenFreezeModal] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [reconData, setReconData] = useState<IMoowrReconciliationInv[]>([]);
  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');
  const [reportType, setReportType] = useState('');

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleConfirmFreeze = async () => {
    setIsOpenFreezeModal(false);
    const payload = {
      pan: panNumber,
      txnId: reconTxnId,
    };

    const response = (await freezeReconciliation(payload)) as ICustomAxiosResp;
    if (response.status?.toString() === ResponseStatus.SUCCESS) {
      getAlertMessage(AlertStatus.SUCCESS, response.msg);
      navigate(Path.MOOWR);
    }
  };

  const handleDownloadReport = async () => {
    const payload = {
      pan: panNumber,
      fileId: downloadFileId,
      reportType,
    };

    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async (report: string) => {
    const {data} = await reportsExportHistory(panNumber);
    const currentReport = data?.find(
      (el: {report_type: string}) => el.report_type === report
    );
    setDownloadFileId(currentReport?.file_id);
    setDownloadFileStatus(currentReport?.status);
    setDownloadFileName(currentReport?.report_name);
  };

  const handleExportReport = async (report: string) => {
    setReportType(report);
    const payload = {
      pan: panNumber,
      txnId: reconTxnId,
      reportType: report,
    };
    await reportsExport(payload);
    await getFileStatusToDownload(report);
  };
  const getReconData = useCallback(async () => {
    const month = reconMonth?.split('/').join('-')?.slice(3);
    const payload = {
      pan: panNumber,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await reconDetails(
      payload,
      month,
      page,
      +showEntries,
      activeTab.toUpperCase()
    );
    setReconData(data?.records);
    setTotalRecords(data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    panNumber,
    debouncedValue,
    sortBy,
    sortingOrder,
    activeTab,
  ]);

  useEffect(() => {
    getReconData();
  }, [getReconData]);

  return (
    <>
      <Helmet title={HelmetTitle.UPLOAD_AND_PROCESS} />
      <div className='moowr-recon-container'>
        <NavigationSubHeader
          hasTitle
          leftArrowRoute='#'
          hasLeftArrow
          isNavigate
          leftArrowText='Invoice Level Reconciliation Details'
        />
        <BusinessHeader />

        {downloadFileStatus ? (
          <FileDownloadStripe
            status={downloadFileStatus}
            handleRefresh={() => getFileStatusToDownload(reportType)}
            handleDownload={handleDownloadReport}
          />
        ) : null}

        <EximPaper>
          <div className='recon-tables-container'>
            <div className='tables-header-container'>
              <EximTypography variant='h3' fontWeight='semi-bold'>
                Invoice Level Reconciliation Details
              </EximTypography>
              <div className='btn-container'>
                <EximButton
                  className='rectify-rollback-btn'
                  size='small'
                  color='secondary'
                  disabled={
                    reconStatus === 'FREEZED' ||
                    reconStatus === 'RECTIFICATION_FAILED'
                  }
                  onClick={() => setIsOpenFreezeModal(true)}>
                  Freeze
                </EximButton>
                <EximButton
                  className='rectify-rollback-btn'
                  size='small'
                  disabled={reconStatus === 'FREEZED'}
                  onClick={() => setIsOpenModal(true)}>
                  Rectify/Roll Back
                </EximButton>
                <EximActionableDropdown
                  id='reports-actionable-dropdown'
                  placeholder='Reports'
                  optionList={[
                    {
                      id: '1',
                      value: MoowrReconReports.MOOWR_RECON_REPORT,
                      label: 'Reconciliation Report',
                    },
                    {
                      id: '2',
                      value: MoowrReconReports.MOOWR_RECON_CONSUMPTION_REPORT,
                      label: 'Final Consumption Report',
                      disabled: true,
                    },
                  ]}
                  onClick={(value: string) => handleExportReport(value)}
                />
              </div>
            </div>

            <div className='recon-tabs-container'>
              <EximTabs
                onChange={(id: string | number) => {
                  dispatch(moowrActions.setReconActiveTab(id.toString()));
                }}
                activeTab={activeTab}
                border={false}
                iconPosition='right'
                values={[
                  {
                    id: MoowrReconType.MATCH,
                    tabName: MoowrReconType.MATCH,
                    tabContent: (
                      <ReconInvoicesTable
                        type={MoowrReconType.MATCH}
                        reconData={reconData}
                        totalRecords={totalRecords}
                        page={page}
                        searchKey={searchKey}
                        searchValue={searchValue}
                        showEntries={showEntries}
                        handlePageChange={handlePageChange}
                        handleShowEntries={handleShowEntries}
                        handleSearchKey={handleSearchKey}
                        handleSearchQuery={handleSearchQuery}
                        handleSortBy={handleSortBy}
                      />
                    ),
                  },
                  {
                    id: MoowrReconType.MISMATCH,
                    tabName: MoowrReconType.MISMATCH,
                    tabContent: (
                      <ReconInvoicesTable
                        type={MoowrReconType.MISMATCH}
                        reconData={reconData}
                        totalRecords={totalRecords}
                        page={page}
                        searchKey={searchKey}
                        searchValue={searchValue}
                        showEntries={showEntries}
                        handlePageChange={handlePageChange}
                        handleShowEntries={handleShowEntries}
                        handleSearchKey={handleSearchKey}
                        handleSearchQuery={handleSearchQuery}
                        handleSortBy={handleSortBy}
                      />
                    ),
                  },
                  {
                    id: MoowrReconType.MISSING,
                    tabName: MoowrReconType.MISSING,
                    tabContent: (
                      <ReconInvoicesTable
                        type={MoowrReconType.MISSING}
                        reconData={reconData}
                        totalRecords={totalRecords}
                        page={page}
                        searchKey={searchKey}
                        searchValue={searchValue}
                        showEntries={showEntries}
                        handlePageChange={handlePageChange}
                        handleShowEntries={handleShowEntries}
                        handleSearchKey={handleSearchKey}
                        handleSearchQuery={handleSearchQuery}
                        handleSortBy={handleSortBy}
                      />
                    ),
                  },
                ]}
              />
            </div>
          </div>
        </EximPaper>
      </div>

      {/* Rectify Rollback Modal */}
      <div className='rectify-rollback-modal'>
        <EximModal
          isOpen={isOpenModal}
          onClose={() => setIsOpenModal(false)}
          onOutSideClickClose={() => setIsOpenModal(false)}
          content={
            <RectifyRollbackModal
              getReconData={getReconData}
              onClose={() => setIsOpenModal(false)}
            />
          }
          header={
            <EximTypography variant='h2' fontWeight='semi-bold'>
              Rectify/Roll Back
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>

      <ConfirmationModal
        isOpen={isOpenFreezeModal}
        onClose={() => {
          setIsOpenFreezeModal(false);
        }}
        handleConfirm={handleConfirmFreeze}
        title='Freeze Reconciliation'
        content='Are you sure you want to freeze the reconciliation?'
        primaryBtnText='Freeze'
        secondaryBtnText='Cancel'
      />
    </>
  );
}

export default Reconciliation;
