import TableHeader from '@common/components/TableHeader';
import {AlertStatus} from '@common/constants';
import {getAlertMessage} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {reconRectification, reconRectificationSummary} from '@pages/Moowr/api';
import {
  ROLLBACK_RECTIFY_MAIN_TABLE_HEADER,
  ROLLBACK_RECTIFY_SUB_TABLE_HEADER,
} from '@pages/Moowr/utils';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

interface IRectifyRollbackModalProps {
  getReconData: () => void;
  onClose: () => void;
}

const initialSummary = {
  consumptionDtls: {
    invCount: 0,
    prodCount: 0,
    prodQty: 0,
  },
  rectificationDtls: {
    invCount: 0,
    prodCount: 0,
    prodQty: 0,
  },
  outwardRegDtls: {
    invCount: 0,
    prodCount: 0,
    prodQty: 0,
  },
};

export default function RectifyRollbackModal({
  onClose,
  getReconData,
}: IRectifyRollbackModalProps) {
  const {
    moowr: {panNumber, reconTxnId},
  } = useSelector((state: RootState) => state);

  const [summary, setSummary] = useState(initialSummary);

  const handleSubmit = async () => {
    const payload = {
      txnId: reconTxnId,
      pan: panNumber,
    };
    const response = (await reconRectification(
      payload,
      summary
    )) as ICustomAxiosResp;

    getAlertMessage(AlertStatus.SUCCESS, response.msg);

    getReconData(); // Updating the data
    onClose();
  };

  const getSummaryData = useCallback(async () => {
    const payload = {
      txnId: reconTxnId,
      pan: panNumber,
    };
    const {data} = await reconRectificationSummary(payload);
    setSummary(data);
  }, [panNumber, reconTxnId]);

  useEffect(() => {
    getSummaryData();
  }, [getSummaryData]);

  return (
    <div className='rectify-modal-container'>
      <div className='rectify-table-container'>
        <table className='rectify-rollback-table'>
          <thead>
            <tr>
              <th colSpan={9} className='rectify-table-title'>
                Rectification Summary
              </th>
            </tr>
          </thead>
          <TableHeader
            mainHeader={ROLLBACK_RECTIFY_MAIN_TABLE_HEADER}
            subHeader={ROLLBACK_RECTIFY_SUB_TABLE_HEADER}
          />
          <TableBody>
            <TableRow>
              <TableCell>{summary.outwardRegDtls.invCount}</TableCell>
              <TableCell>{summary.outwardRegDtls.prodCount}</TableCell>
              <TableCell>{summary.outwardRegDtls.prodQty}</TableCell>
              <TableCell>{summary.rectificationDtls.invCount}</TableCell>
              <TableCell>{summary.rectificationDtls.prodCount}</TableCell>
              <TableCell>{summary.rectificationDtls.prodQty}</TableCell>
              <TableCell>{summary.consumptionDtls.invCount}</TableCell>
              <TableCell>{summary.consumptionDtls.prodCount}</TableCell>
              <TableCell>{summary.consumptionDtls.prodQty}</TableCell>
            </TableRow>
          </TableBody>
        </table>
        <EximTypography variant='h4' fontWeight='semi-bold'>
          Invoices from the Outward Plan and Outward Register have been matched.
          Do you want to save the rectified invoices?
        </EximTypography>
        <EximTypography variant='h4' fontWeight='semi-bold'>
          {`Once saved, all rectified invoices will move to the 'Matched' section,and unmatched invoices will be included in the next reconciliation cycle.`}
        </EximTypography>
      </div>
      <div className='btn-container'>
        <EximButton size='small' color='secondary' onClick={onClose}>
          No
        </EximButton>
        <EximButton size='small' onClick={handleSubmit}>
          Yes
        </EximButton>
      </div>
    </div>
  );
}
