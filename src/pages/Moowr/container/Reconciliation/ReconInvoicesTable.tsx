import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {MoowrReconType} from '@common/constants';
import {IMoowrReconciliationInv} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  RECON_INVOICES_SUB_TABLE_HEADER,
  RECON_INVOICE_SEARCH_TYPE_DROPDOWN,
  RECON_MATCH_MISS_MATCH_INVOICES_MAIN_TABLE_HEADER,
  RECON_MISSING_INVOICES_MAIN_TABLE_HEADER,
} from '@pages/Moowr/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {ChangeEvent, useMemo} from 'react';

import './index.scss';

interface IProps {
  type: string;
  reconData: IMoowrReconciliationInv[];
  totalRecords: number;
  page: number;
  searchKey: string;
  searchValue: string;
  showEntries: string;
  handlePageChange: (page: number | string) => void;
  handleShowEntries: (entries: ChangeEvent<HTMLSelectElement>) => void;
  handleSearchKey: (key: string) => void;
  handleSearchQuery: (query: ChangeEvent<HTMLInputElement>) => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function ReconInvoicesTable({
  type,
  reconData,
  totalRecords,
  page,
  searchKey,
  searchValue,
  showEntries,
  handlePageChange,
  handleShowEntries,
  handleSearchKey,
  handleSearchQuery,
  handleSortBy,
}: IProps) {
  const subHeader = useMemo(
    () => [
      ...RECON_INVOICES_SUB_TABLE_HEADER(true),
      ...RECON_INVOICES_SUB_TABLE_HEADER(false),
    ],
    []
  );

  return (
    <div className='match-invoices-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={RECON_INVOICE_SEARCH_TYPE_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='match-invoices-table'>
        <TableHeader
          mainHeader={
            type === MoowrReconType.MISSING
              ? RECON_MISSING_INVOICES_MAIN_TABLE_HEADER
              : RECON_MATCH_MISS_MATCH_INVOICES_MAIN_TABLE_HEADER
          }
          subHeader={subHeader}
          handleSortBy={handleSortBy}
        />
        {reconData?.length > 0 ? (
          <TableBody className='match-invoices-body'>
            {reconData?.map((item: IMoowrReconciliationInv) => (
              <TableRow key={`${item.id}`}>
                <TableCell>{item.consumptionName}</TableCell>
                <TableCell>{item.consReportInvNo}</TableCell>
                <TableCell>{item.consReportInvDate}</TableCell>
                <TableCell>{item.consReportProdCode}</TableCell>
                <TableCell>{item.consReportQtySold}</TableCell>
                <TableCell>{item.sbNo}</TableCell>
                <TableCell>{item.gstInvNo}</TableCell>
                <TableCell>{item.gstInvDate}</TableCell>
                <TableCell>{item.prodCode}</TableCell>
                <TableCell>{item.qtySold}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={subHeader.length + 1} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={reconData as []}
        renderData={reconData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default ReconInvoicesTable;
