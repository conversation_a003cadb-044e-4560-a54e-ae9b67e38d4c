@import '@utils/main.scss';

.moowr-recon-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .recon-tables-container {
    @include margin-top(16px);

    .tables-header-container {
      @include flex-item(_, space-between, center);
      @include padding(20px);
      border-bottom: 1px solid $primary-border;

      .btn-container {
        @include flex-item(_, flex-end, center, _, 16px);
        .button-wrapper {
          min-width: 182px;
        }
        .button-wrapper.rectify-rollback-btn {
          min-width: 100px;
          .base-btn {
            height: 32px;
            font-size: $font-size-sm;
            @include padding(7px 16px);
          }
        }
      }
    }

    // INFO: Recon Tabs Style
    .recon-tabs-container {
      @include padding(20px);
      .tabs-wrapper {
        .tab {
          border-bottom: 1px solid $primary-border;
          .tab-button {
            background-color: transparent;
            letter-spacing: 0.4px;
            @include padding(16px);
            min-width: 100px;
          }
          .tab-horizontal-primary {
            @include margin-bottom(-17px);
            border-bottom: 3px solid $tertiary;
          }

          .button-content-rightIcon {
            color: $text-color;
            font-weight: normal;
            font-size: $font-size-sm;
          }
        }
      }

      .match-invoices-table-container {
        .table-search-container {
          @include margin(24px 0 20px 0);
        }
        .match-invoices-table {
          border-spacing: 0;
          width: 100%;
          thead tr:first-child > th:nth-child(n) {
            text-align: center;
          }
          thead tr:first-child > th:nth-child(3),
          thead tr:nth-child(2) > th:nth-child(n + 6) {
            background-color: $table-head-4;
          }
          tbody tr > td:nth-child(n + 7) {
            background-color: $table-cell-color;
          }
        }

        .middle-content-div {
          @include flex-item(_, space-between, center, _, 16px);
        }
      }
    }
  }
}

// Discard Transaction Modal Style
.rectify-rollback-modal {
  letter-spacing: 0.2px;
  .modal-title h2 {
    color: $secondary-text;
    font-size: $font-size-xl;
  }
  .modal-body {
    width: 65%;
    .modal-header {
      align-items: flex-start;
      @include padding(32px 40px 12px 40px);
    }
    .modal-content {
      @include padding(0px 40px 16px 40px);
      .rectify-modal-container {
        width: 100%;
        .rectify-table-container {
          width: 100%;
          .rectify-rollback-table {
            border-spacing: 0;
            width: 100%;
            @include margin-bottom(30px);
            .rectify-table-title {
              @include padding(10px);
              background-color: $table-head-primary;
              color: $white;
            }
            thead tr:first-child > th:nth-child(n) {
              text-align: center;
            }
            thead tr:first-child > th:nth-child(2),
            thead tr:nth-child(2) > th:nth-child(n + 4):not(:nth-child(n + 7)) {
              background-color: $table-head-4;
            }
          }
        }

        .btn-container {
          @include flex-item(_, flex-end, center, _, 16px);
          @include margin-top(40px);
          .button-wrapper {
            min-width: 100px;
            .base-btn {
              height: 32px;
              font-size: $font-size-sm;
              @include padding(7px 16px);
            }
          }
        }
      }
    }
  }
}
