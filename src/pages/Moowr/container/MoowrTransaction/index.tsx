import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {moowrTransactionBreadcrumbs} from '@common/components/NavigationSubHeader/config';
import {HelmetTitle, MoowrTransactionSteps, Path} from '@common/constants';
import CreateTransactionHeader from '@pages/Moowr/components/CreateTransactionHeader';
import {useCallback, useEffect} from 'react';
import {useNavigate, useParams} from 'react-router';

import BOMReview from '../BOMReview';
import ConsumptionReport from '../ConsumptionReport';
import InwardReview from '../InwardReview';
import ProductSelectionDetails from '../ProductSelectionDetails';
import ProductSelectionSummary from '../ProductSelectionSummary';
import './index.scss';

function DbkClaim() {
  const {transactionStep} = useParams();
  const navigate = useNavigate();

  const getTransactionStep = useCallback(() => {
    switch (transactionStep) {
      case MoowrTransactionSteps.STEP_1_1:
        return <ProductSelectionDetails />;
      case MoowrTransactionSteps.STEP_1_2:
        return <ProductSelectionSummary />;
      case MoowrTransactionSteps.STEP_2:
        return <BOMReview />;
      case MoowrTransactionSteps.STEP_3:
        return <InwardReview />;
      case MoowrTransactionSteps.STEP_4:
        return <ConsumptionReport />;
      default:
        return null;
    }
  }, [transactionStep]);

  useEffect(() => {
    if (
      transactionStep &&
      !Object.values(MoowrTransactionSteps)?.includes(
        transactionStep as unknown as MoowrTransactionSteps
      )
    ) {
      navigate(`${Path.PAGE_NOT_FOUND}`);
    }
  }, [transactionStep, navigate]);

  return (
    <div className='moowr-transaction-wrapper'>
      {/* Navbar */}
      <NavigationSubHeader
        hasLeftArrow
        hasTitle
        hasGuide
        isNavigate
        leftArrowRoute={Path.MOOWR}
        leftArrowText={HelmetTitle.MOOWR}
        breadCrumbData={moowrTransactionBreadcrumbs}
      />

      {/* Date period section */}
      <CreateTransactionHeader />

      {/* Render the Step based on the param */}
      {getTransactionStep()}
    </div>
  );
}

export default DbkClaim;
