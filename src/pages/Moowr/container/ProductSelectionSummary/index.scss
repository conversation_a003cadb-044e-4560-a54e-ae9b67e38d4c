@import '@utils/main.scss';

.product-selection-summary-container {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .table-search-container {
      @include margin-top(20px);
    }
  }
  .prod-summary-table-wrapper {
    @include padding(20px);
    @include margin-top(20px);
    color: $text-color;
    .prod-summary-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
    }
    &:last-child {
      @include margin-bottom(20px);
    }
  }

  .create-transaction-sub-header {
    .action-buttons-wrapper {
      .buttons {
        display: flex;
        gap: 20px;
        .button-wrapper {
          width: 131px;
          .contained[class^='secondary'] {
            color: $tertiary;
          }
        }
        .edit-btn {
          width: 86px;
        }
      }
    }
  }

  .selected-product-container {
    @include padding(20px);
    @include margin-bottom(20px);
  }
}
