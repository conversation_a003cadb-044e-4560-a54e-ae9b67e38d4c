import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IDbkClaimOverallSummary} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getSelectionSummary} from '@pages/Moowr/api';
import {PRODUCT_OVERALL_SELECTION_SUMMARY} from '@pages/Moowr/utils';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {memo, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function OverallSelectionSummary() {
  const {
    moowr: {
      panNumber,
      currTransactionDetails: {txnId},
    },
  } = useSelector((state: RootState) => state);

  const [overallSummary, setOverallSummary] = useState<
    IDbkClaimOverallSummary[]
  >([]);

  useEffect(() => {
    (async () => {
      const payload = {
        txnId,
        pan: panNumber,
      };
      const response = await getSelectionSummary(payload);
      setOverallSummary([
        {
          particulars: 'Total Available',
          product: response.data.total_prod_available.toString(),
        },
        {
          particulars: 'Considered for Transactional report',
          product: response.data.total_prod_considered.toString(),
        },
      ]);
    })();
  }, [txnId, panNumber]);
  return (
    <EximPaper>
      <div className='prod-summary-table-wrapper'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          Overall Selection Summary
        </EximTypography>
        <table className='prod-summary-table'>
          <TableHeader mainHeader={PRODUCT_OVERALL_SELECTION_SUMMARY} />
          {overallSummary?.length > 0 ? (
            <TableBody>
              {overallSummary?.map((item) => (
                <TableRow key={`${item.particulars}`}>
                  <TableCell>{item.particulars}</TableCell>
                  <TableCell>
                    {formatAmount(item.product || '') || '-'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable colSpan={PRODUCT_OVERALL_SELECTION_SUMMARY.length} />
          )}
        </table>
      </div>
    </EximPaper>
  );
}

export default memo(OverallSelectionSummary);
