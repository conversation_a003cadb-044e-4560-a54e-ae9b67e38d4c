import Stripe from '@common/components/Stripe';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  MOOWR_TRANSACTION_REPORT_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {downloadLargeFileData} from '@common/helpers';
import {IMoowrProductItem} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN} from '@pages/DutyDrawback/utils';
import {
  downloadLargeFile,
  getMoowrProductList,
  reportsExport,
  reportsExportHistory,
  saveSelectionSummary,
} from '@pages/Moowr/api';
import CreateTransactionSubHeader from '@pages/Moowr/components/CreateTransactionSubHeader';
import FileDownloadStripe from '@pages/Moowr/components/FileDownloadStripe';
import ProductSelectionTable from '@pages/Moowr/components/ProductSelectionTable';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import OverallSelectionSummary from './OverallSelectionSummary';
import './index.scss';

function ProductSelectionSummary() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber,
      currTransactionDetails: {txnId, transactionCalcStatus},
    },
  } = useSelector((state: RootState) => state);

  const [productList, setProductList] = useState<IMoowrProductItem[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();

  const debouncedValue = useDebounce(searchValue, 300);

  const getProductList = useCallback(async () => {
    const payload = {
      txnId,
      pan: panNumber,
      selected: true,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getMoowrProductList(payload, page, +showEntries);
    setProductList(response?.data?.records);
    setTotalRecords(response?.data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    txnId,
    page,
    panNumber,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleNextBtn = async () => {
    const payload = {
      pan: panNumber,
      txnId,
    };
    const data = await saveSelectionSummary(payload);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      navigate(`${Path.MOOWR}${Path.TRANSACTION}${Path.BOM_REVIEW}`);
    }
  };

  const handleEditBtn = () => {
    navigate(`${Path.MOOWR}${Path.TRANSACTION}${Path.PROD_SELECTION}`);
  };

  const handleDownload = async () => {
    const payload = {
      pan: panNumber,
      email,
      fileId: downloadFileId,
      reportType:
        MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_PARTIAL_OUTWARD_REGISTER,
    };
    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(panNumber);
    if (
      MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_PARTIAL_OUTWARD_REGISTER ===
      data?.[0]?.report_type
    ) {
      setDownloadFileId(data?.[0]?.file_id);
      setDownloadFileStatus(data?.[0]?.status);
      setDownloadFileName(data?.[0]?.report_name);
    }
  };

  const handleExportReport = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId,
      reportType:
        MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_PARTIAL_OUTWARD_REGISTER,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  useEffect(() => {
    getProductList();
  }, [getProductList]);

  return (
    <div className='product-selection-summary-container'>
      <CreateTransactionSubHeader step='1.2' subTitle='Product Summary'>
        <div className='buttons'>
          <EximButton
            onClick={handleEditBtn}
            color='secondary'
            disabled={transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT'}
            className='edit-btn'>
            Edit
          </EximButton>
          <EximButton onClick={handleExportReport} color='secondary'>
            Export Report
          </EximButton>
          <EximButton
            size='small'
            disabled={transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT'}
            onClick={handleNextBtn}>
            Save & Next
          </EximButton>
        </div>
      </CreateTransactionSubHeader>
      <FileDownloadStripe
        status={downloadFileStatus}
        handleRefresh={getFileStatusToDownload}
        handleDownload={handleDownload}
      />
      <Stripe
        content={`This page displays the Summary details for the Shipping Bills, it's Products and Local Sales`}
        variant='info'
      />
      <OverallSelectionSummary />
      <EximPaper>
        <div className='selected-product-container'>
          <EximTypography variant='h3' fontWeight='semi-bold'>
            Products list
          </EximTypography>
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <ProductSelectionTable
            data={productList}
            handleSortBy={handleSortBy}
          />
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={productList as []}
            renderData={productList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ProductSelectionSummary;
