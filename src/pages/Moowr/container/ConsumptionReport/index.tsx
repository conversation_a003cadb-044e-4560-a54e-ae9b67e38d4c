import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {
  ICustomAxiosResp,
  IMoowrCalcSummary,
  IMoowrTransFinalSummary,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  consumptionSummary,
  freezeTransaction,
  getSelectionSummary,
  processConsumption,
} from '@pages/Moowr/api';
import CreateTransactionSubHeader from '@pages/Moowr/components/CreateTransactionSubHeader';
import DownloadReportsModal from '@pages/Moowr/components/DownloadReportsModal';
import FinalSummary from '@pages/Moowr/components/FinalSummary';
import {CONSUMPTION_REVIEW_TABLE_HERDER} from '@pages/Moowr/utils';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function ConsumptionReport() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber: pan,
      currTransactionDetails: {txnId, transactionCalcStatus},
    },
  } = useSelector((state: RootState) => state);

  const [summaryCalculationData, setSummaryCalculationData] = useState<
    IMoowrCalcSummary[]
  >([]);

  const [finalSummaryData, setFinalSummaryData] = useState<
    IMoowrTransFinalSummary[]
  >([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDownloadReport, setIsDownloadReport] = useState<boolean>(false);
  const [isFreezModal, setIsFreezModal] = useState<boolean>(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const calculationProcess = async () => {
    const payload = {
      pan,
      email,
      txnId,
    };
    const response = (await processConsumption(payload)) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      setTimeout(() => navigate(`${Path.MOOWR}`), 1000);
    }
  };

  const handleFreezeTransaction = async () => {
    const payload = {
      pan,
      email,
      txnId,
    };
    const response = (await freezeTransaction(payload)) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          message: response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      setTimeout(() => navigate(`${Path.MOOWR}`), 1000);
    }
    setIsFreezModal(false); // Closing the Modal
  };

  const handleEdit = () => {
    navigate(`${Path.MOOWR}${Path.TRANSACTION}${Path.PROD_SELECTION}`);
  };

  const summaryCalculation = useCallback(async () => {
    const payload = {
      pan,
      email,
      txnId,
    };
    const {data} = await getSelectionSummary(payload);
    const formatData = [
      {
        title: 'Total Available (Quantity)',
        invoices: data?.total_inv_available,
        uniqueProd: data?.total_prod_available,
        prodQty: data?.total_prod_qty_available,
        boe: data?.total_boe_considered,
        uniqueItems: data?.total_item_available,
      },
      {
        title: 'Considered for Transactions',
        invoices: data?.total_inv_considered,
        uniqueProd: data?.total_prod_considered,
        prodQty: data?.total_prod_qty_considered,
        boe: data?.total_boe_considered,
        uniqueItems: data?.total_item_considered,
      },
    ];
    setSummaryCalculationData(formatData);
  }, [email, txnId, pan]);

  const getConsumptionSummary = useCallback(async () => {
    const headers = {
      txnId,
      pan,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await consumptionSummary(headers, page, +showEntries);
    setFinalSummaryData(data?.records);
    setTotalRecords(data?.total_records);

    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    txnId,
    email,
    page,
    pan,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    summaryCalculation();
  }, [transactionCalcStatus, summaryCalculation]);

  useEffect(() => {
    if (
      transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT' ||
      transactionCalcStatus === 'FREEZE_TXN' ||
      transactionCalcStatus === 'FREEZED'
    ) {
      getConsumptionSummary();
    }
  }, [transactionCalcStatus, getConsumptionSummary]);

  return (
    <div className='calculation-summary-container'>
      <CreateTransactionSubHeader step='4' subTitle='Consumption Report Review'>
        {transactionCalcStatus !== 'CONSUMPTION_CAL_CMPLT' &&
        transactionCalcStatus !== 'FREEZE_TXN' &&
        transactionCalcStatus !== 'FREEZED' ? (
          <EximButton size='small' onClick={calculationProcess}>
            Start Process
          </EximButton>
        ) : (
          <>
            <EximButton
              size='small'
              color='secondary'
              onClick={() => setIsDownloadReport(true)}>
              Download Reports
            </EximButton>
            <EximButton
              size='small'
              disabled={
                transactionCalcStatus !== 'CONSUMPTION_CAL_CMPLT' &&
                (transactionCalcStatus === 'FREEZE_TXN' ||
                  transactionCalcStatus === 'FREEZED')
              }
              onClick={() => setIsFreezModal(true)}>
              Freeze
            </EximButton>
          </>
        )}
      </CreateTransactionSubHeader>

      <EximPaper>
        <div className='calculation-summary-table-container'>
          <div className='table-title-section'>
            <EximTypography
              variant='h3'
              fontWeight='semi-bold'
              classNames='table-title'>
              Consumption Calculation Summary
            </EximTypography>
            <EximButton
              size='small'
              color='secondary'
              disabled={
                transactionCalcStatus === 'FREEZE_TXN' ||
                transactionCalcStatus === 'FREEZED'
              }
              onClick={handleEdit}>
              Edit Selections
            </EximButton>
          </div>
          <table className='calculation-summary-table'>
            <TableHeader mainHeader={CONSUMPTION_REVIEW_TABLE_HERDER} />
            {summaryCalculationData?.length > 0 ? (
              <TableBody className='calculation-summary-tbody'>
                {summaryCalculationData?.map((item) => (
                  <TableRow key={`${item.title}`}>
                    <TableCell>{item.title}</TableCell>
                    <TableCell>{item.invoices || '-'}</TableCell>
                    <TableCell>{item.uniqueProd || '-'}</TableCell>
                    <TableCell>{item.prodQty || '-'}</TableCell>
                    <TableCell>{item.boe || '-'}</TableCell>
                    <TableCell>{item.uniqueItems || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={CONSUMPTION_REVIEW_TABLE_HERDER.length} />
            )}
          </table>
          {transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT' ||
          transactionCalcStatus === 'FREEZE_TXN' ||
          transactionCalcStatus === 'FREEZED' ? (
            <FinalSummary
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}
              finalSummaryData={finalSummaryData}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              handlePageChange={handlePageChange}
              handleSortBy={handleSortBy}
              handleSearchKey={handleSearchKey}
              searchKey={searchKey}
              page={page}
            />
          ) : null}
        </div>
      </EximPaper>

      {/* Download Reports Modal */}
      <div className='download-report-modal'>
        <EximModal
          isOpen={isDownloadReport}
          onClose={() => setIsDownloadReport(false)}
          onOutSideClickClose={() => setIsDownloadReport(false)}
          content={
            <DownloadReportsModal
              txnId={txnId}
              onClose={() => setIsDownloadReport(false)}
            />
          }
          footer={false}
          header='Download Reports'
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* Process Transaction Modal */}
      <div className='transaction-claim-modal'>
        <EximModal
          isOpen={isFreezModal}
          onClose={() => setIsFreezModal(false)}
          onOutSideClickClose={() => setIsFreezModal(false)}
          content={
            <div className='freez-modal-container'>
              <EximTypography variant='h4'>
                Are you sure, you want to Process Consumption Claim?
              </EximTypography>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsFreezModal(false)}>
                  No
                </EximButton>
                <EximButton size='small' onClick={handleFreezeTransaction}>
                  Yes
                </EximButton>
              </span>
            </div>
          }
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Process Consumption Claim
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>
    </div>
  );
}

export default ConsumptionReport;
