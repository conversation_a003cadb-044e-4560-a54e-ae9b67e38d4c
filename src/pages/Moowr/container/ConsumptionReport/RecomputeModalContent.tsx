import TableHeader from '@common/components/TableHeader';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {RECOMPUTE_ROLLBACK_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximTypography from '@shared/components/EximTypography';
import {ExclamationTriangle} from '@shared/icons';

interface IRecomputeProps {
  isChecked: boolean;
  setIsChecked: (val: boolean) => void;
  onClose: () => void;
  handleRecomputeAndFreeze: () => void;
  airDisqualificationCount: number;
  shortFallDisqualificationCount: number;
}

function RecomputeModalContent({
  isChecked,
  setIsChecked,
  onClose,
  handleRecomputeAndFreeze,
  airDisqualificationCount,
  shortFallDisqualificationCount,
}: IRecomputeProps) {
  return (
    <div className='recompute-freez-modal-container'>
      <ExclamationTriangle />
      <EximTypography variant='h4' fontWeight='bold'>
        {airDisqualificationCount + shortFallDisqualificationCount}{' '}
        Not-Qualified Products found!
      </EximTypography>

      <table className='rollback-table'>
        <TableHeader mainHeader={RECOMPUTE_ROLLBACK_TABLE_HEADER} />
        <TableBody>
          <TableRow>
            <TableCell>Not- qualified due to Shortfall in Quantity</TableCell>
            <TableCell>{shortFallDisqualificationCount}</TableCell>
            <TableCell>
              <EximCheckbox
                id='rollback_1'
                name='shortfallQty'
                color='#878787'
                size='medium'
                onChange={() => undefined}
                checked
                disabled
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell>Not- qualified due to 4/5Th Rule</TableCell>
            <TableCell>{airDisqualificationCount}</TableCell>
            <TableCell>
              <EximCheckbox
                id='rollback_2'
                name='fourthFifthRule'
                color='#2CB544'
                size='medium'
                onChange={() => setIsChecked(!isChecked)}
                checked={isChecked}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </table>

      <EximTypography variant='h5'>
        Selected products will be rollback. Are you sure, you want to continue.
      </EximTypography>

      <span className='btn-container'>
        <EximButton size='small' color='secondary' onClick={onClose}>
          Cancel
        </EximButton>
        <EximButton size='small' onClick={handleRecomputeAndFreeze}>
          Continue
        </EximButton>
      </span>
    </div>
  );
}

export default RecomputeModalContent;
