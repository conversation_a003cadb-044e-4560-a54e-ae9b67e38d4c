@import '@utils/main.scss';

.calculation-summary-container {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
    @include margin(0);
  }
  .calculation-summary-table-container {
    @include padding(20px);
    @include margin(15px auto 32px);
    .table-title-section {
      @include flex-item(row, space-between);
      .button-wrapper {
        width: 131px;
        .base-btn {
          @include padding(7px 0);
          font-size: $base-font-size;
        }
      }
    }
    .table-title {
      color: $text-color;
    }
    .calculation-summary-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;

      thead {
        tr {
          th:nth-child(2),
          th:nth-child(3),
          th:nth-child(4) {
            background-color: $table-head-4;
          }
          th:nth-child(5),
          th:nth-child(6) {
            background-color: $table-head-2;
          }
        }
      }
    }
  }

  .auto-recompute {
    @include flex-item(row, space-between);
    color: $text-color;
    .button-wrapper {
      width: 109px;
      .base-btn {
        @include padding(7px 0);
        font-size: $base-font-size;
      }
    }
    @include padding(8px 16.5px);
    background-color: $default-background;
    border: 1px solid $default-border;
    box-shadow: $alert-box-shadow;
    border-radius: 5px;
    @include margin-top(15px);
  }

  .download-report-modal {
    .modal-body {
      width: 611px;
    }
    .modal-title {
      font-size: $font-size-xxl;
      color: $secondary-text;
      font-weight: $font-weight-semi-bold;
    }
    .modal-header {
      @include padding-bottom(8px);
      @include padding-top(24px);
    }
    .modal-content {
      padding-top: 0;
    }
  }

  // Freez Claim Modal Style
  .transaction-claim-modal {
    letter-spacing: 0.2px;
    .modal-title h2 {
      color: $secondary-text;
    }
    .modal-body {
      width: 535px;
      .modal-header {
        align-items: flex-start;
        @include padding(32px 40px 12px 40px);
      }
      .modal-content {
        @include padding(0px 40px 16px 40px);
        .freez-modal-container {
          width: 100%;
          .btn-container {
            @include flex-item(_, flex-end, center, _, 16px);
            @include margin-top(40px);
            .button-wrapper {
              min-width: 100px;
              .base-btn {
                height: 32px;
                font-size: $font-size-sm;
                @include padding(7px 16px);
              }
            }
          }
        }
      }
    }
  }
}
