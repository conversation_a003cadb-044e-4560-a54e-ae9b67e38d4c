import EmptyTable from '@common/components/EmptyTable';
import Stripe from '@common/components/Stripe';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  MOOWR_FILE_ROUTE_TYPE,
  MOOWR_TRANSACTION_REPORT_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {
  downloadLargeFileData,
  formatAmount,
  selectedOptionId,
} from '@common/helpers';
import {
  ICustomAxiosResp,
  IMoowrBomReviews,
  IMoowrMissingBom,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  BOM_REVIEW_TABLE_HEADER,
  BOM_REVIEW_TABLE_SEARCH_DROPDOWN,
  MISSING_BOM_REVIEW_TABLE_HEADER,
} from '@pages/DutyDrawback/utils';
import {
  downloadLargeFile,
  getBomList,
  reportsExport,
  reportsExportHistory,
  saveBomList,
} from '@pages/Moowr/api';
import CreateTransactionSubHeader from '@pages/Moowr/components/CreateTransactionSubHeader';
import FileDownloadStripe from '@pages/Moowr/components/FileDownloadStripe';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const {
  MOOWR,
  BOM_REVIEW,
  UPLOAD_PROCESS,
  TRANSACTION,
  VIEW_DETAILS,
  INWARD_REVIEW,
} = Path;

function BOMReview() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber,
      currTransactionDetails: {txnId, transactionCalcStatus},
    },
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [bomReviewsData, setBomReviewsData] = useState<IMoowrBomReviews[]>([]);
  const [missingBomList, setMissingBomList] = useState<IMoowrMissingBom[]>([]);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [isEditBomVersion, setIsEditBomVersion] = useState(false);
  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleNextBtn = async () => {
    const payload = {
      pan: panNumber,
      partialSave: false,
      txnId,
    };
    const data = await saveBomList(payload, bomReviewsData);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      navigate(`${MOOWR}${TRANSACTION}${INWARD_REVIEW}`);
    }
  };

  const handleViewReview = (prodCode: string, bomVersion: string) => {
    const version = bomVersion?.split('-')[1];
    navigate(`${MOOWR}${TRANSACTION}${BOM_REVIEW}${VIEW_DETAILS}`, {
      state: {prodCode, bomVersion: version},
    });
  };

  const handleChangeBomVersion = (value: string, prodIndex: number) => {
    const updatedData = bomReviewsData.map((product, index) => {
      if (index === prodIndex) {
        const updatedBomList = product.bom_version_dtls_list.map((bom) => ({
          ...bom,
          selected: bom.ref_id === value,
        }));

        return {
          ...product,
          bom_version_dtls_list: updatedBomList,
        };
      }

      return product;
    });
    setBomReviewsData(updatedData);
  };

  const handleEditBomVersion = async () => {
    const payload = {
      pan: panNumber,
      partialSave: true,
      txnId,
    };
    if (isEditBomVersion) {
      const resp = (await saveBomList(
        payload,
        bomReviewsData
      )) as ICustomAxiosResp;
      if (resp.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: resp.status,
            message: resp.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
    }
    setIsEditBomVersion((prev) => !prev);
  };

  const getReviewsData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      txnId,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getBomList(payload, page, +showEntries);
    setTotalRecords(data?.total_records);
    setBomReviewsData(data?.data?.bom_dtls);
    if (data?.data?.error_bom_dtls) {
      setMissingBomList(data?.data?.error_bom_dtls);
    } else {
      setMissingBomList([]);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    txnId,
    panNumber,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleDownload = async () => {
    const payload = {
      pan: panNumber,
      email,
      fileId: downloadFileId,
      reportType: MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOM_STATEMENT,
    };
    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(panNumber);
    if (
      MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOM_STATEMENT ===
      data?.[0]?.report_type
    ) {
      setDownloadFileId(data?.[0]?.file_id);
      setDownloadFileStatus(data?.[0]?.status);
      setDownloadFileName(data?.[0]?.report_name);
    }
  };

  const handleExportReport = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId,
      reportType: MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOM_STATEMENT,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  useEffect(() => {
    getReviewsData();
  }, [getReviewsData]);

  return (
    <div className='bom-review-container'>
      <CreateTransactionSubHeader
        step='2'
        subTitle='Bill of Materials (BOM) - Review'>
        <>
          <EximButton
            size='small'
            color='secondary'
            onClick={handleExportReport}>
            Export Report
          </EximButton>
          <EximButton
            size='small'
            onClick={handleNextBtn}
            disabled={
              missingBomList.length > 0 ||
              transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT'
            }>
            Save & Next
          </EximButton>
        </>
      </CreateTransactionSubHeader>
      <FileDownloadStripe
        status={downloadFileStatus}
        handleRefresh={getFileStatusToDownload}
        handleDownload={handleDownload}
      />
      <Stripe
        content='This Page displays the list of BOMs for the selected products'
        variant='info'
      />
      {missingBomList.length > 0 ? (
        <Stripe
          content={
            <span>
              <strong>Validation Error!</strong> : BOM for one or more selected
              products is unavailable. Please upload the valid BOM to Proceed.
            </span>
          }
          variant='primary'
          isBtn
          btnText='View Details'
          onBtnClick={() => setIsOpenModal(true)}
        />
      ) : null}
      <EximPaper>
        <div className='bom-review-table-container'>
          <TableSearchFilter
            btnProps={{
              text: isEditBomVersion ? 'Save' : 'Edit',
              color: 'secondary',
              onClick: handleEditBomVersion,
            }}
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={BOM_REVIEW_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <table className='bom-review-table'>
            <TableHeader
              mainHeader={BOM_REVIEW_TABLE_HEADER}
              handleSortBy={handleSortBy}
            />
            {bomReviewsData?.length > 0 ? (
              <TableBody className='bom-review-tbody'>
                {bomReviewsData?.map((item: IMoowrBomReviews, index) => {
                  const selectedBom = item.bom_version_dtls_list.find(
                    (el) => el.selected
                  ) || {bom_version: '', ref_id: ''};

                  const availableVersion = item.bom_version_dtls_list?.map(
                    (el, indx) => {
                      return {
                        id: indx + 1,
                        value: el.ref_id,
                        label: el.bom_version,
                      };
                    }
                  );

                  return (
                    <TableRow key={`${selectedBom.ref_id}${index + 1}`}>
                      <TableCell>{item.prod_code}</TableCell>
                      <TableCell>{item.prod_desc}</TableCell>
                      <TableCell>
                        {formatAmount(item.total_item_count)}
                      </TableCell>
                      {isEditBomVersion ? (
                        <TableCell className='edit-version'>
                          <EximCustomDropdown
                            placeholder='Search By Column'
                            onSelect={({value}) =>
                              handleChangeBomVersion(value, index)
                            }
                            dataTestId='column-dropdown'
                            optionsList={availableVersion}
                            defaultOption={selectedOptionId(
                              availableVersion,
                              selectedBom.bom_version
                            )}
                          />
                        </TableCell>
                      ) : (
                        <TableCell>{selectedBom.bom_version}</TableCell>
                      )}
                      <TableCell>
                        <TableActions
                          isViewIcon
                          viewToolTipText='View Details'
                          handleView={() =>
                            handleViewReview(
                              item.prod_code,
                              selectedBom.bom_version
                            )
                          }
                        />
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            ) : (
              <EmptyTable colSpan={BOM_REVIEW_TABLE_HEADER.length} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={bomReviewsData as []}
            renderData={bomReviewsData as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>

      {/* Missing BOM files Modal */}
      <div className='missing-bom-modal'>
        <EximModal
          isOpen={isOpenModal}
          onClose={() => setIsOpenModal(false)}
          onOutSideClickClose={() => setIsOpenModal(false)}
          content={
            <div className='missing-bom-container'>
              <table>
                <TableHeader mainHeader={MISSING_BOM_REVIEW_TABLE_HEADER} />
                <TableBody className='missing-bom-table-body'>
                  {missingBomList.map((item, index) => (
                    <TableRow key={`missingBom${index + 1}`}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{item.prod_code}</TableCell>
                      <TableCell>{item.prod_desc}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </table>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenModal(false)}>
                  No
                </EximButton>
                <EximButton
                  size='small'
                  onClick={() =>
                    navigate(`${MOOWR}${UPLOAD_PROCESS}/bom`, {
                      state: {fileType: MOOWR_FILE_ROUTE_TYPE.BOM_STATEMENT},
                    })
                  }>
                  Upload
                </EximButton>
              </span>
            </div>
          }
          footer={false}
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Missing BOM Files
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
        />
      </div>
    </div>
  );
}

export default BOMReview;
