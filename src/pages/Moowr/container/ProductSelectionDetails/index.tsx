import AllFileSelectionStripe from '@common/components/AllFileSelectionStripe';
import Stripe from '@common/components/Stripe';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path, ResponseStatus} from '@common/constants';
import {IMoowrProductItem} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import {getMoowrProductList, saveMoowrProductList} from '@pages/Moowr/api';
import CreateTransactionSubHeader from '@pages/Moowr/components/CreateTransactionSubHeader';
import ProductSelectionTable from '@pages/Moowr/components/ProductSelectionTable';
import {PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN} from '@pages/Moowr/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function ProductSelectionDetails() {
  const navigate = useNavigate();

  const {
    moowr: {
      panNumber,
      currTransactionDetails: {txnId},
    },
  } = useSelector((state: RootState) => state);

  const [page, setPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [sortBy, setSortBy] = useState<string>('');
  const [sortingOrder, setSortingOrder] = useState<1 | -1>(-1);
  const [productList, setProductList] = useState<IMoowrProductItem[]>([]);
  const [isSelectedAll, setIsSelectedAll] = useState<boolean>(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedFileCount, setSelectedFileCount] = useState(0);

  const debouncedValue = useDebounce(searchValue, 300);

  const getProductList = useCallback(async () => {
    const payload = {
      txnId,
      pan: panNumber,
      selected: false,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getMoowrProductList(payload, page, +showEntries);
    setProductList(response?.data?.records);
    setTotalRecords(response?.data?.total_records);
    const isAllValueSelected = response.data.records.every(
      (item: IMoowrProductItem) => item.selected === true
    );
    setIsSelectedAll(isAllValueSelected);
    setSelectedFileCount(
      response.data.records?.filter((el: IMoowrProductItem) => el?.selected)
        .length
    );
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    txnId,
    page,
    panNumber,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleNextBtn = async (isPageAction: boolean) => {
    const payload = {
      txnId,
      pan: panNumber,
      partialSave: isPageAction,
      selectAll: selectedFileCount === totalRecords,
    };

    const data = await saveMoowrProductList(payload, productList);
    if (data.status.toString() === ResponseStatus.SUCCESS && !isPageAction) {
      navigate(`${Path.MOOWR}${Path.TRANSACTION}${Path.PRODUCT_SUMMARY}`);
    }
  };

  const handlePageChange = (pageNumber: string | number) => {
    handleNextBtn(true);
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
    setPage(1);
  };

  const handleSortBy = (sortKey: string, order: 'asc' | 'desc') => {
    setSortBy(sortKey);
    setSortingOrder(order === 'asc' ? 1 : -1);
  };

  const toggleSelectItem = (id: string) => {
    if (productList.length > 0) {
      const updatedList = productList.map((item: IMoowrProductItem) =>
        item.ref_id === id ? {...item, selected: !item.selected} : item
      );
      setProductList(updatedList);

      const selectedItems = updatedList.filter((item) => item.selected).length;
      const isAllSelected = selectedItems === productList.length;
      setIsSelectedAll(isAllSelected);
      setSelectedFileCount(selectedItems);
    }
  };

  const handleHeaderToggle = () => {
    if (productList.length) {
      const updatedList = productList.map((value: IMoowrProductItem) => ({
        ...value,
        selected: !isSelectedAll,
      }));
      setProductList(updatedList);
      setIsSelectedAll(!isSelectedAll);
      setSelectedFileCount(isSelectedAll ? 0 : productList.length);
    }
  };

  // INFO: Below function to select all the records that present in backend
  const handleToggleSelectAll = (type: 'CLEAR_ALL' | 'SELECT_ALL') => {
    if (type === 'SELECT_ALL') {
      setProductList(
        productList.map((value: IMoowrProductItem) => ({
          ...value,
          selected: true,
        }))
      );
      setIsSelectedAll(true);
      setSelectedFileCount(totalRecords);
    } else if (type === 'CLEAR_ALL') {
      setProductList(
        productList.map((value: IMoowrProductItem) => ({
          ...value,
          selected: false,
        }))
      );
      setIsSelectedAll(false);
      setSelectedFileCount(0);
    }
  };

  useEffect(() => {
    getProductList();
  }, [getProductList]);

  useEffect(() => {
    if (txnId && productList.length) {
      const isAllValueSelected = productList.every(
        (item) => item.selected === true
      );
      if (isAllValueSelected) setIsSelectedAll(true);
    }
  }, [txnId, productList]);

  return (
    <div className='product-selection-details'>
      <CreateTransactionSubHeader step='1.1' subTitle='Product Selection'>
        <div className='next-button'>
          <EximButton onClick={() => handleNextBtn(false)}>Next</EximButton>
        </div>
      </CreateTransactionSubHeader>
      <Stripe
        content='This page displays the Shipping bills based on the date range specified during claim creation.'
        variant='info'
      />
      <EximPaper>
        <div className='sb-table-wrapper'>
          {productList.length > 0 && totalRecords > +showEntries ? (
            <AllFileSelectionStripe
              selectedFileCount={selectedFileCount}
              totalRecords={totalRecords}
              handleToggleSelectAll={handleToggleSelectAll}
            />
          ) : null}
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => setSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <ProductSelectionTable
            data={productList}
            selectItem={(id) => toggleSelectItem(id)}
            isSelectedAll={isSelectedAll}
            onSelectAll={handleHeaderToggle}
            handleSortBy={handleSortBy}
            isCheckbox
          />
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={productList as []}
            renderData={productList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ProductSelectionDetails;
