import Stripe from '@common/components/Stripe';
import {
  MOOWR_TRANSACTION_REPORT_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {downloadLargeFileData} from '@common/helpers';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import {
  downloadLargeFile,
  reportsExport,
  reportsExportHistory,
  saveInwardReview,
} from '@pages/Moowr/api';
import CreateTransactionSubHeader from '@pages/Moowr/components/CreateTransactionSubHeader';
import FileDownloadStripe from '@pages/Moowr/components/FileDownloadStripe';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState, dispatch} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import OverallSelectionSummary from './OverallSelectionSummary';
import SelectedBoeItemsList from './SelectedBoeItemList';
import SelectedItemList from './SelectedItemList';
import './index.scss';

const {MOOWR, TRANSACTION, CONSUMPTION_CAL} = Path;

function InwardReview() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber: pan,
      currTransactionDetails: {txnId, transactionCalcStatus},
    },
  } = useSelector((state: RootState) => state);

  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  const handleNextBtn = async () => {
    const payload = {
      pan,
      email,
      txnId,
    };
    const data = await saveInwardReview(payload);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(dutyDrawbackActions.setClaimCalculationStatus(''));
      navigate(`${MOOWR}${TRANSACTION}${CONSUMPTION_CAL}`);
    }
  };

  const handleDownload = async () => {
    const payload = {
      pan,
      email,
      fileId: downloadFileId,
      reportType: MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOE_RECORDS,
    };

    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(pan);
    if (
      MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOE_RECORDS ===
      data?.[0]?.report_type
    ) {
      setDownloadFileId(data?.[0]?.file_id);
      setDownloadFileStatus(data?.[0]?.status);
      setDownloadFileName(data?.[0]?.report_name);
    }
  };

  const handleExportReport = async () => {
    const payload = {
      pan,
      email,
      txnId,
      reportType: MOOWR_TRANSACTION_REPORT_TYPE.SELECTED_BOE_RECORDS,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  return (
    <div className='inward-review'>
      <CreateTransactionSubHeader step='3' subTitle='Inward Review'>
        <EximButton onClick={handleExportReport} color='secondary'>
          Export Report
        </EximButton>
        <EximButton
          disabled={transactionCalcStatus === 'CONSUMPTION_CAL_CMPLT'}
          onClick={handleNextBtn}>
          Save & Next
        </EximButton>
      </CreateTransactionSubHeader>
      <FileDownloadStripe
        status={downloadFileStatus}
        handleRefresh={getFileStatusToDownload}
        handleDownload={handleDownload}
      />
      <Stripe
        content='This page displays the Summary details for Bill of Entries'
        variant='info'
      />
      <OverallSelectionSummary />
      <EximPaper>
        <SelectedBoeItemsList />
        <SelectedItemList />
      </EximPaper>
    </div>
  );
}

export default InwardReview;
