import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IDbkClaimOverallSummary} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getSelectionSummary} from '@pages/Moowr/api';
import {INWARD_REVIEW_SELECTION_SUMMARY_TABLE_HEADER} from '@pages/Moowr/utils';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function OverallSelectionSummary() {
  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber: pan,
      currTransactionDetails: {txnId},
    },
  } = useSelector((state: RootState) => state);

  const [overallSummary, setOverallSummary] = useState<
    IDbkClaimOverallSummary[]
  >([]);

  useEffect(() => {
    (async () => {
      const payload = {
        txnId,
        pan,
        email,
      };
      const response = await getSelectionSummary(payload);
      setOverallSummary([
        {
          particulars: 'Total Available',
          billOfEntries: response.data?.total_inv_considered?.toString(),
          item: response.data?.total_item_considered?.toString(),
        },
      ]);
    })();
  }, [txnId, email, pan]);

  return (
    <EximPaper>
      <div className='boe-ps-table-wrapper'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          Overall Selection Summary
        </EximTypography>
        <table className='boe-product-selection'>
          <TableHeader
            mainHeader={INWARD_REVIEW_SELECTION_SUMMARY_TABLE_HEADER}
          />
          {overallSummary?.length > 0 ? (
            <TableBody>
              {overallSummary?.map((item) => (
                <TableRow key={`${item.particulars}`}>
                  <TableCell>{item.particulars}</TableCell>
                  <TableCell>
                    {formatAmount(item.billOfEntries || '-')}
                  </TableCell>
                  <TableCell>{formatAmount(item.item || '-')}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable
              colSpan={INWARD_REVIEW_SELECTION_SUMMARY_TABLE_HEADER.length}
            />
          )}
        </table>
      </div>
    </EximPaper>
  );
}

export default OverallSelectionSummary;
