import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount} from '@common/helpers';
import {IMoowrInwardItemList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getInwardItemList} from '@pages/Moowr/api';
import {
  INWARD_REVIEW_ITEM_LIST_TABLE_HEADER,
  INWARD_REVIEW_ITEM_LIST_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function SelectedItemList() {
  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber: pan,
      currTransactionDetails: {txnId},
    },
  } = useSelector((state: RootState) => state);

  const [boeItemList, setBoeItemList] = useState<IMoowrInwardItemList[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const payload = {
        pan,
        email,
        txnId,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const {data} = await getInwardItemList(payload, page, +showEntries);
      setBoeItemList(data?.records);
      setTotalRecords(data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    pan,
    email,
    txnId,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='dbk-claim-boe-summary-container'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Items List
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={INWARD_REVIEW_ITEM_LIST_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='boe-item-table'>
        <TableHeader
          mainHeader={INWARD_REVIEW_ITEM_LIST_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {boeItemList?.length > 0 ? (
          <TableBody>
            {boeItemList?.map((item: IMoowrInwardItemList, index: number) => (
              <TableRow key={`itemSelected${index + 1}`}>
                <TableCell>{item.purchase_inv_no}</TableCell>
                <TableCell>{item.item_code}</TableCell>
                <TableCell>{item.item_desc}</TableCell>
                <TableCell>{formatAmount(item.total_val)}</TableCell>
                <TableCell>{formatAmount(item.total_imported_qty)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={INWARD_REVIEW_ITEM_LIST_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={boeItemList as []}
        renderData={boeItemList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedItemList;
