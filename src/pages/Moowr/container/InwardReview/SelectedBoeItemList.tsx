import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount, formatDate} from '@common/helpers';
import {IMoowrInwardBoeList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getInwardList} from '@pages/Moowr/api';
import {
  INWARD_REVIEW_BOE_TABLE_HEADER,
  INWARD_REVIEW_BOE_TABLE_SEARCH_DROPDOWN,
} from '@pages/Moowr/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function SelectedBoeItemsList() {
  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber: pan,
      currTransactionDetails: {txnId},
    },
  } = useSelector((state: RootState) => state);

  const [boeListData, setBoeListData] = useState<IMoowrInwardBoeList[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const headers = {
        txnId,
        pan,
        email,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const response = await getInwardList(headers, page, +showEntries);
      setBoeListData(response.data.records);
      setTotalRecords(response.data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    txnId,
    email,
    pan,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='boe-summary-table-wrapper'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Bill of Entries List
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={INWARD_REVIEW_BOE_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='dbk-boe-list-table'>
        <TableHeader
          mainHeader={INWARD_REVIEW_BOE_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {boeListData?.length > 0 ? (
          <TableBody>
            {boeListData?.map((item, index) => (
              <TableRow key={`BOE${index + 1}`}>
                <TableCell>{item.purchase_inv_no}</TableCell>
                <TableCell>{formatDate(item.purchase_inv_date)}</TableCell>
                <TableCell>
                  {formatAmount(item.total_unique_item_code)}
                </TableCell>
                <TableCell>{formatAmount(item.total_balance_qty)}</TableCell>
                <TableCell>{formatAmount(item.total_assessable_val)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={INWARD_REVIEW_BOE_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={boeListData as []}
        renderData={boeListData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedBoeItemsList;
