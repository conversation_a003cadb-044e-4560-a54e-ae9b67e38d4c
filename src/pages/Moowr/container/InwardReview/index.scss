@import '@utils/main.scss';

.inward-review {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .table-search-container {
      @include margin-top(20px);
    }
  }

  .dbk-sub-header {
    .action-buttons-wrapper {
      .buttons {
        display: flex;
        gap: 20px;
        .button-wrapper {
          width: 131px;
          .contained[class^='secondary'] {
            color: $tertiary;
          }
        }
        .edit-btn {
          width: 86px;
        }
      }
    }
  }

  .boe-ps-table-wrapper {
    @include padding(20px);
    @include margin-bottom(20px);
  }

  .boe-summary-table-wrapper,
  .dbk-claim-boe-summary-container {
    @include padding(20px);
  }
  .dbk-claim-boe-summary-container {
    @include margin-bottom(20px);
  }

  .boe-product-selection,
  .dbk-boe-list-table,
  .boe-item-table {
    @include margin-top(16px);
    width: 100%;
    border-spacing: 0;
  }
}
