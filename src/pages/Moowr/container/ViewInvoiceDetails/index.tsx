import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {MOOWR_FILE_ROUTE_TYPE} from '@common/constants';
import JobWorkViewDetails from '@pages/Moowr/components/JobWorkViewDetails';
import OutwardRegisterViewDetails from '@pages/Moowr/components/OutwardRegisterViewDetails';
import OutwardStockViewDetails from '@pages/Moowr/components/OutwardStockViewDetails';
import {useLocation} from 'react-router-dom';

import './index.scss';

function ViewInvoiceDetails() {
  const location = useLocation();
  const {isViewValidRecord, isEditable, fileType} = (location.state ?? {}) as {
    isViewValidRecord: boolean;
    isEditable: boolean;
    fileType: string;
  };

  return (
    <div className='view-details-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText={
          isViewValidRecord ? 'View Details' : 'Error Records View Details'
        }
      />

      <div className='view-details-component'>
        {fileType === MOOWR_FILE_ROUTE_TYPE.PARTIAL_OUTWARD_REGISTER ? (
          <OutwardStockViewDetails
            isEditable={isEditable}
            isViewValidRecord={isViewValidRecord}
          />
        ) : null}
        {fileType === MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER ? (
          <OutwardRegisterViewDetails
            isEditable={isEditable}
            isViewValidRecord={isViewValidRecord}
          />
        ) : null}
        {fileType === MOOWR_FILE_ROUTE_TYPE.JOB_WORK_REGISTER ? (
          <JobWorkViewDetails
            isEditable={isEditable}
            isViewValidRecord={isViewValidRecord}
          />
        ) : null}
      </div>
    </div>
  );
}

export default ViewInvoiceDetails;
