import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import UploadFiles from '@common/components/UploadFiles';
import Helmet from '@common/components/utils/Helmet';
import {
  AlertStatus,
  HelmetTitle,
  MoowrUploadProcessTitle,
  MoowrUploadProcessTitleType,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {
  UtcToIstFormat,
  checkFilesExtension,
  downloadFile,
} from '@common/helpers';
import {ICustomAxiosResp, IMoorFileUploadHistory} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  UPLOAD_DATA_TABLE_HEADER,
  UPLOAD_HISTORY_TABLE_HEADER,
  UPLOAD_HISTORY_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import {
  downloadExcelTemplate,
  getUploadHistory,
  uploadFileData,
} from '@pages/Moowr/api';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDivider from '@shared/components/EximDivider';
import EximPaper from '@shared/components/EximPaper';
import {RootState, dispatch} from '@store';
import format from 'date-fns/format';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function UploadAndProcess() {
  const navigate = useNavigate();
  const location = useLocation();
  const {fileType} = (location.state ?? {}) as {
    fileType: string;
  };

  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber},
  } = useSelector((state: RootState) => state);

  const [uploadHistoryData, setUploadHistoryData] = useState<
    IMoorFileUploadHistory[]
  >([]);

  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedExcelFile, setSelectedExcelFile] = useState<File>();

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleFileSelect = (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);
      if (checkFilesExtension(selectedFilesArray, [SupportedFileTypes.EXCEL])) {
        setSelectedExcelFile(selectedFilesArray[0]);
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message: 'Please select the Excel file only!',
            alertType: 'danger',
          })
        );
      }
    }
  };

  const handleDownloadExcelTemplate = async () => {
    const payload = {
      pan: panNumber,
      fileType,
      email,
    };
    const {data} = await downloadExcelTemplate(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const getUploadHistoryData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      fileType,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getUploadHistory(payload, page, +showEntries);
    setUploadHistoryData(data?.records);
    setTotalRecords(data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    panNumber,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleFileUpload = async () => {
    const payload = {
      pan: panNumber,
      fileType,
      email,
    };
    if (selectedExcelFile) {
      const response = (await uploadFileData(
        payload,
        selectedExcelFile
      )) as ICustomAxiosResp;
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
        navigate(`${Path.MOOWR}`);
      }
    }
  };

  useEffect(() => {
    getUploadHistoryData();
  }, [getUploadHistoryData]);

  return (
    <>
      <Helmet title={HelmetTitle.UPLOAD_AND_PROCESS} />
      <div className='moowr-upload-process-container'>
        <NavigationSubHeader
          hasTitle
          leftArrowRoute='#'
          hasLeftArrow
          isNavigate
          leftArrowText={`Upload ${
            MoowrUploadProcessTitle[fileType as MoowrUploadProcessTitleType]
          }`}
        />
        <BusinessHeader />
        <BusinessSubHeader
          handleFileUpload={handleFileUpload}
          isFileSelected={!!selectedExcelFile}
          handleDownloadExcelTemplate={handleDownloadExcelTemplate}
          title={
            MoowrUploadProcessTitle[fileType as MoowrUploadProcessTitleType]
          }
        />
        <EximDivider type='solid' text='Upload Data' textAlign='left' />

        <EximPaper>
          <div className='upload-data-container'>
            <table className='upload-data-table'>
              <TableHeader mainHeader={UPLOAD_DATA_TABLE_HEADER} />
              <TableBody className='upload-data-tbody'>
                <TableRow>
                  <TableCell>{selectedExcelFile?.name || '-'}</TableCell>
                  <TableCell>
                    {selectedExcelFile
                      ? format(new Date(), 'dd MMM yyyy hh:mm a')
                      : ''}
                  </TableCell>
                  <TableCell>-</TableCell>
                  <TableCell>
                    <UploadFiles
                      title='Browse File'
                      accept='.xlsx'
                      onChange={handleFileSelect}
                    />
                  </TableCell>
                </TableRow>
              </TableBody>
            </table>
          </div>
        </EximPaper>

        <EximDivider type='solid' text='Upload History' textAlign='left' />

        <EximPaper elevation={2} variant='elevation'>
          <div className='upload-history-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={UPLOAD_HISTORY_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='upload-history-table'>
              <TableHeader
                mainHeader={UPLOAD_HISTORY_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {uploadHistoryData?.length > 0 ? (
                <TableBody className='upload-history-tbody'>
                  {uploadHistoryData.map((item: IMoorFileUploadHistory) => (
                    <TableRow key={item.txn_id}>
                      <TableCell>{item.file_name}</TableCell>
                      <TableCell>
                        {UtcToIstFormat(item.last_updated_date) || '-'}
                      </TableCell>
                      <TableCell>{item.last_updated_by || '-'}</TableCell>
                      <TableCell>{item.processing_status}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              ) : (
                <EmptyTable colSpan={UPLOAD_HISTORY_TABLE_HEADER.length} />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={uploadHistoryData as []}
              renderData={uploadHistoryData as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>
      </div>
    </>
  );
}

export default UploadAndProcess;
