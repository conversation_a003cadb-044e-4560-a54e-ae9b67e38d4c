@import '@utils/main.scss';

.moowr-upload-process-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .subscription-header-left {
    color: $text-color;
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .sub-header-wrapper {
      @include flex-item(_, space-between, center);
      @include padding(10px 16px);

      .btn-container {
        @include flex-item(_, flex-end, center, _, 20px);
        display: flex;
        align-items: center;

        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }

  // Upload Data & Upload History Table Style
  .upload-data-container,
  .upload-history-container {
    @include padding(30px 36px);
    @include margin(16px auto 32px);
    .upload-data-table,
    .upload-history-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
    }
    .upload-data-table {
      .file-input-container {
        max-width: max-content;
        margin: auto;
        .file-button {
          @include padding(2px 12px);
        }
      }
      thead th:last-child {
        text-align: center;
      }
    }
  }
}
