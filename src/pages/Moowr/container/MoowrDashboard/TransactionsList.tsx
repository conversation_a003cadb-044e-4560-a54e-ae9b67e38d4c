import <PERSON>lip<PERSON><PERSON><PERSON><PERSON> from '@common/components/EllipsisChecker';
import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {MOOWR_TRANSACTION_TYPE, MoowrTxnType} from '@common/constants';
import {ITransactionList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {transactionHistory} from '@pages/Moowr/api';
import {CONSUMPTION_LIST_TABLE_HEADER} from '@pages/Moowr/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

interface ITransactionsListProps {
  transactionStatus: string;
}

export default function TransactionsList({
  transactionStatus,
}: ITransactionsListProps) {
  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber},
  } = useSelector((state: RootState) => state);

  const [transactionNameToShow, setTransactionNameToShow] = useState('');
  const [isOpenTextModal, setIsOpenTextModal] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [transactionList, setTransactionList] = useState<ITransactionList[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getTransactionHistory = useCallback(async () => {
    const payload = {
      pan: panNumber,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await transactionHistory(payload, page, +showEntries);
    setTransactionList(data?.records);
    setTotalRecords(data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    panNumber,
    email,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getTransactionHistory();
  }, [getTransactionHistory, transactionStatus]);

  if (transactionList.length === 0) return null;

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('SELECT_IP'))
      return <span className='process'>In Progress</span>;
    if (value?.includes('SELECT_REVIEW_IP'))
      return <span className='process'>Review In Progress</span>;
    if (value?.includes('CMPLT') || value?.includes('COMPLETED'))
      return <span className='success'>Completed</span>;
    if (value?.includes('SELECT_FAILED'))
      return <span className='error'>Failed</span>;
    if (value?.includes('FREEZED'))
      return <span className='error'>Freezed</span>;
    if (value?.includes('FREEZE_TXN'))
      return <span className='process'>Freeze In Progress</span>;
    if (value?.includes('DISCARDED'))
      return <span className='error'>Discarded</span>;
    if (
      value?.includes('READY_TO_CAL') ||
      value?.includes('CAL_IP') ||
      value?.includes('READY_FOR_CONSUMPTION_CAL')
    )
      return <span className='process'>Calculation In Progress</span>;
    return null;
  };

  return (
    <div className='transaction-list-container'>
      <div className='transaction-list-table-container'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          Consumption List
        </EximTypography>
        <TableSearchFilter
          isInputDisabled={!searchKey}
          handleShowEntries={handleShowEntries}
          handleSearchQuery={handleSearchQuery}>
          <EximCustomDropdown
            placeholder='Search By Column'
            onSelect={({value}) => handleSearchKey(value)}
            dataTestId='column-dropdown'
            optionsList={[]}
          />
        </TableSearchFilter>
        <table className='transaction-history-table'>
          <TableHeader
            mainHeader={CONSUMPTION_LIST_TABLE_HEADER}
            handleSortBy={handleSortBy}
          />
          {transactionList?.length > 0 ? (
            <TableBody className='transaction-history-tbody'>
              {transactionList?.map((item: ITransactionList) => (
                <TableRow key={item.txn_id}>
                  <TableCell>
                    <EllipsisChecker
                      text={item.txn_name}
                      handleViewMore={() => {
                        setIsOpenTextModal(true);
                        setTransactionNameToShow(item.txn_name);
                      }}
                    />
                  </TableCell>
                  <TableCell>{`${item.start_prd} to ${item.end_prd}`}</TableCell>
                  <TableCell>
                    {MOOWR_TRANSACTION_TYPE[item.txn_type as MoowrTxnType]}
                  </TableCell>
                  <TableCell className={`status-td ${item.status}`}>
                    {getShowStatus(item.status)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable colSpan={10} />
          )}
        </table>
        <TableFooter
          page={page}
          searchQuery={searchValue}
          showEntries={showEntries}
          totalRecords={totalRecords}
          searchData={transactionList as []}
          renderData={transactionList as []}
          handlePageChange={handlePageChange}
          hasBackendPagination
        />
      </div>

      {/* View Transaction Text Modal */}
      <div className='view-transaction-text-modal'>
        <EximModal
          isOpen={isOpenTextModal}
          onClose={() => setIsOpenTextModal(false)}
          onOutSideClickClose={() => setIsOpenTextModal(false)}
          content={<p>{transactionNameToShow}</p>}
          footer={false}
          header={
            <EximTypography fontWeight='bold'>Transaction Name</EximTypography>
          }
          closeIcon={<CloseIcon width={15} height={15} />}
        />
      </div>
    </div>
  );
}
