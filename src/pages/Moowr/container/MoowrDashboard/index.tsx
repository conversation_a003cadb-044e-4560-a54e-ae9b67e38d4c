import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {HelmetTitle, Path} from '@common/constants';
import SummaryCards from '@pages/DutyDrawback/components/SummaryCards';
import ReconSummary from '@pages/Moowr/components/ReconSummary';
import EximDivider from '@shared/components/EximDivider';

import DataUploadValidate from './DataUploadValidate';
import Transactions from './Transactions';
import './index.scss';

function MoowrDashboard() {
  return (
    <>
      <Helmet title={HelmetTitle.MOOWR} />
      <div className='moowr-dashboard-wrapper'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          leftArrowRoute={Path.DASHBOARD}
          leftArrowText='EXIM MOOWR Dashboard'
        />
        <BusinessHeader />

        <EximDivider type='solid' text='Summary' textAlign='left' />
        <SummaryCards />

        <EximDivider
          type='solid'
          text='Data Upload & Validate'
          textAlign='left'
        />
        <DataUploadValidate />

        <EximDivider type='solid' text='Consumption' textAlign='left' />
        <Transactions />

        <ReconSummary />
      </div>
    </>
  );
}

export default MoowrDashboard;
