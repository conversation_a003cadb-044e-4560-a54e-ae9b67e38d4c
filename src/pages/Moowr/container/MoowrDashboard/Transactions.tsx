import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {
  AlertStatus,
  MoowrTransactionStatus,
  Path,
  ResponseStatus,
} from '@common/constants';
import {ICustomAxiosResp, ISubTransaction} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {discardTransaction, transactionDetails} from '@pages/Moowr/api';
import TransactionModalContent from '@pages/Moowr/components/TransactionModal';
import {initialTxnDtls, moowrActions} from '@pages/Moowr/store/reduce';
import EximAvatar from '@shared/components/EximAvatar';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon, CloseIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import TransactionsList from './TransactionsList';
import './index.scss';

const {MOOWR, TRANSACTION} = Path;

export default function Transactions() {
  const navigate = useNavigate();
  const {
    moowr: {
      panNumber,
      currTransactionDetails: {txnId, transactionCalcStatus},
    },
  } = useSelector((state: RootState) => state);

  const [txnDetailsList, setTxnDetailsList] = useState<ISubTransaction[]>([]);
  const [transactionStatus, setTransactionStatus] = useState('');
  const [isOpenCreateTxnModal, setIsOpenCreateTxnModal] = useState(false);
  const [isOpenDiscardTxnModal, setIsOpenDiscardTxnModal] = useState(false);

  const getTransactionDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      txnId: '',
    };
    const {data} = await transactionDetails(payload);
    if (data && data?.status !== MoowrTransactionStatus.DISCARDED) {
      setTransactionStatus(data.status);
      setTxnDetailsList(data?.sub_txn_dtls);
      const transactionDtls = {
        txnId: data?.txn_id,
        txnName: data?.txn_name,
        txnType: data?.txn_type,
        fromDate: data?.start_prd,
        toDate: data?.end_prd,
        isTxnExist: true,
      };
      dispatch(moowrActions.setCurrTransactionDetails(transactionDtls));
      dispatch(moowrActions.setTransactionCalcStatus(data?.status));
    } else {
      setTransactionStatus('DISCARDED');
      setTxnDetailsList([]);
      dispatch(moowrActions.setCurrTransactionDetails(initialTxnDtls));
    }
  }, [panNumber]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleViewDetails = (type: string, status: string) => {
    if (
      transactionCalcStatus === MoowrTransactionStatus.CONSUMPTION_CAL_CMPLT
    ) {
      if (type === 'PROD_SELECTION') {
        navigate(`${MOOWR}${TRANSACTION}${Path.PRODUCT_SUMMARY}`);
      } else {
        navigate(`${MOOWR}${TRANSACTION}${Path[type as keyof typeof Path]}`);
      }
    } else {
      navigate(`${MOOWR}${TRANSACTION}${Path[type as keyof typeof Path]}`);
    }
  };

  const handleDiscardTransaction = async () => {
    const payload = {
      pan: panNumber,
      txnId,
    };
    const resp = (await discardTransaction(payload)) as ICustomAxiosResp;
    if (resp.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(moowrActions.setCurrTransactionDetails(initialTxnDtls));
      getTransactionDetails();
      dispatch(
        alertActions.setAlertMsg({
          code: resp.statusCode,
          message: resp.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
    setIsOpenDiscardTxnModal(false);
  };

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('SELECT_IP'))
      return <span className='process'>In Progress</span>;
    if (value?.includes('SELECT_REVIEW_IP'))
      return <span className='process'>Review In Progress</span>;
    if (value?.includes('CMPLT') || value?.includes('COMPLETED'))
      return <span className='success'>Completed</span>;
    if (value?.includes('SELECT_FAILED'))
      return <span className='error'>Failed</span>;
    if (value?.includes('FREEZED'))
      return <span className='error'>Freezed</span>;
    if (value?.includes('FREEZE_TXN'))
      return <span className='process'>Freeze In Progress</span>;
    if (value?.includes('DISCARDED'))
      return <span className='error'>Discarded</span>;
    if (
      value?.includes('READY_TO_CAL') ||
      value?.includes('CAL_IP') ||
      value?.includes('READY_FOR_CONSUMPTION_CAL')
    )
      return <span className='process'>Calculation In Progress</span>;
    return null;
  };

  useEffect(() => {
    getTransactionDetails();
  }, [getTransactionDetails]);

  return (
    <div className='data-upload-step-container'>
      <EximPaper>
        <FilingHead
          filingHead='Consumption'
          onGuideClick={handleGuideClick}
          hasGuide>
          {transactionStatus === MoowrTransactionStatus.DISCARDED ||
          transactionStatus === MoowrTransactionStatus.FREEZED ? (
            <EximButton
              onClick={() => {
                setIsOpenCreateTxnModal(true);
                dispatch(
                  moowrActions.setCurrTransactionDetails(initialTxnDtls)
                );
              }}>
              Create
            </EximButton>
          ) : (
            <EximButton
              onClick={() => setIsOpenDiscardTxnModal(true)}
              color='tertiary'
              className='discard-claim-btn'>
              Discard
            </EximButton>
          )}
        </FilingHead>
        <div className='filing-step-container'>
          {txnDetailsList?.map((txn: ISubTransaction, index) => {
            return (
              <FilingStep
                key={txn.sub_txn_name}
                stepIcon={
                  <span>
                    {(txn?.status && txn?.status?.includes('CMPLT')) ||
                    txn?.status?.includes('COMPLETED') ? (
                      <CheckIcon fill='#2CB445' />
                    ) : (
                      <EximAvatar
                        rounded
                        firstName={`${index + 1}`}
                        lastName=''
                        alt='number'
                        size='small'
                      />
                    )}
                  </span>
                }
                stepEndIcon={
                  <span className='info-icons'>
                    <InfoCircular fill='#4379B5' width={13} height={13} />
                  </span>
                }
                filingName={txn.sub_txn_name}
                btnName='View Details'
                btnDisable={
                  !txn.txn_id ||
                  transactionStatus === MoowrTransactionStatus.DISCARDED ||
                  transactionStatus === MoowrTransactionStatus.FREEZE_TXN ||
                  transactionStatus ===
                    MoowrTransactionStatus.READY_FOR_CONSUMPTION_CAL ||
                  (index !== 3 &&
                    transactionStatus === MoowrTransactionStatus.FREEZED)
                }
                onBtnClick={() =>
                  handleViewDetails(txn.sub_txn_type, txn.status)
                }
                status={getShowStatus(txn.status)}
                statusIcon={
                  txn.status?.includes(
                    MoowrTransactionStatus.READY_FOR_CONSUMPTION_CAL
                  ) ||
                  (index === 3 &&
                    txn.status?.includes(MoowrTransactionStatus.FREEZE_TXN)) ? (
                    <span onClick={getTransactionDetails} role='presentation'>
                      <SolidSync />
                    </span>
                  ) : null
                }
                recentUpdate={txn.last_updated_date || '-'}
                updatedBy={txn.last_updated_by || '-'}
              />
            );
          })}
        </div>
        {/* INFO: All Transaction List Table */}
        <TransactionsList transactionStatus={transactionStatus} />
      </EximPaper>

      {/* Create Transaction Modal */}
      <div className='create-transaction-modal'>
        <EximModal
          isOpen={isOpenCreateTxnModal}
          onClose={() => setIsOpenCreateTxnModal(false)}
          onOutSideClickClose={() => setIsOpenCreateTxnModal(false)}
          content={
            <TransactionModalContent
              onClose={() => setIsOpenCreateTxnModal(false)}
            />
          }
          footer={false}
          header={
            <EximTypography
              classNames='transaction-modal-title'
              fontWeight='semi-bold'>
              Consumption
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* Discard Transaction Modal */}
      <div className='discard-transaction-modal'>
        <EximModal
          isOpen={isOpenDiscardTxnModal}
          onClose={() => setIsOpenDiscardTxnModal(false)}
          onOutSideClickClose={() => setIsOpenDiscardTxnModal(false)}
          content={
            <div className='discard-modal-container'>
              <EximTypography variant='h4'>
                Are you sure, you want to discard ongoing consumption?
              </EximTypography>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenDiscardTxnModal(false)}>
                  No
                </EximButton>
                <EximButton size='small' onClick={handleDiscardTransaction}>
                  Yes
                </EximButton>
              </span>
            </div>
          }
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Discard Current Consumption
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>
    </div>
  );
}
