import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {
  EximHeroDate,
  MOOWR_ROUTE_TYPE,
  MoowrRouteType,
  MoowrUploadProcessTitle,
  MoowrUploadProcessTitleType,
  Path,
} from '@common/constants';
import {
  formatDateWithTime,
  getCurrentMonthAndYear,
  getLastMonthAndYear,
} from '@common/helpers';
import {IMoowrInputFilesUploadStatus} from '@common/interfaces';
import {getProcessedDetails} from '@pages/Moowr/api';
import {moowrActions} from '@pages/Moowr/store/reduce';
import EximAvatar from '@shared/components/EximAvatar';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const formatDataToRender = (data: IMoowrInputFilesUploadStatus[]) => {
  const [first, sec, third, forth, ...restOfData] = data;
  return [forth, sec, first, third, ...restOfData];
};

export default function DataUploadValidate() {
  const navigate = useNavigate();
  const [processingStatus, setProcessingStatus] = useState<
    IMoowrInputFilesUploadStatus[]
  >([]);

  const {
    auth: {
      userData: {email},
    },
    moowr: {panNumber},
  } = useSelector((state: RootState) => state);

  const getProcessingDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      fileType: 'ALL',
      email,
    };
    const {data} = await getProcessedDetails(payload);
    const formatData = formatDataToRender(data);
    setProcessingStatus(formatData);
  }, [email, panNumber]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleUpload = async (type: MoowrRouteType) => {
    navigate(`${Path.MOOWR}${Path.UPLOAD_PROCESS}${MOOWR_ROUTE_TYPE[type]}`, {
      state: {fileType: type},
    });
  };

  const handleViewDetails = (
    type: MoowrRouteType,
    txnId: string,
    status: string
  ) => {
    dispatch(moowrActions.setInvoiceTxnId(txnId));
    dispatch(moowrActions.setInvoicesCardActive(true));
    dispatch(
      moowrActions.setInvoicesPeriod({
        startPeriod: getLastMonthAndYear(),
        endPeriod: getCurrentMonthAndYear(),
      })
    );
    // INFO: Track the last transaction status to pass dynamic header based on the last transaction in the invoices details API
    dispatch(
      moowrActions.setIsLastTransactionInvalid(status.includes('Invalid'))
    );
    navigate(`${Path.MOOWR}${Path.VIEW_INVOICES}${MOOWR_ROUTE_TYPE[type]}`, {
      state: {fileType: type},
    });
  };

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('In Progress'))
      return <span className='process'>{value}</span>;
    if (
      value?.includes('Failed') ||
      value?.includes('Invalid') ||
      value?.includes('Discarded')
    )
      return <span className='error'>{value}</span>;
    if (value?.includes('Completed'))
      return <span className='success'>{value}</span>;
    return null;
  };

  useEffect(() => {
    getProcessingDetails();
  }, [getProcessingDetails]);

  return (
    <div className='data-upload-step-container'>
      <EximPaper>
        <FilingHead
          filingHead='Input Files'
          onGuideClick={handleGuideClick}
          hasGuide>
          <div className='date-container'>
            <EximTypography>Select Period</EximTypography>
            <EximDatePicker
              id='input-date'
              minDate={EximHeroDate.MIN_DATE}
              calendarType='monthCalendar'
              onChange={(value) => undefined}
            />
          </div>
        </FilingHead>
        <div className='filing-step-container'>
          {processingStatus.map((item, index) => (
            <FilingStep
              key={`uploadAndView${index + 1}`}
              stepIcon={
                <span>
                  {item.processing_status?.includes('Completed') ? (
                    <CheckIcon fill='#2CB445' />
                  ) : (
                    <EximAvatar
                      rounded
                      firstName={`${index + 1}`}
                      lastName=''
                      alt='number'
                      size='small'
                    />
                  )}
                </span>
              }
              statusIcon={
                item.processing_status?.includes('In Progress') ? (
                  <span onClick={getProcessingDetails} role='presentation'>
                    <SolidSync />
                  </span>
                ) : null
              }
              stepEndIcon={
                <span className='info-icons'>
                  <InfoCircular fill='#4379B5' width={13} height={13} />
                </span>
              }
              filingName={
                MoowrUploadProcessTitle[
                  item.file_type as MoowrUploadProcessTitleType
                ]
              }
              btnName='Upload'
              btnDisable={false}
              onBtnClick={() => handleUpload(item.file_type as MoowrRouteType)}
              secondBtnName='View'
              secondBtnDisable={!item.file_name}
              status={
                item.processing_status
                  ? getShowStatus(item.processing_status)
                  : null
              }
              recentUpdate={
                item?.file_name
                  ? `Last Uploaded on ${formatDateWithTime(
                      item?.last_updated_date,
                      false
                    )}`
                  : ''
              }
              updatedBy={
                (item?.last_updated_by && `By ${item?.last_updated_by}`) || ''
              }
              onSecondBtnClick={() =>
                handleViewDetails(
                  item.file_type as MoowrRouteType,
                  item.txn_id,
                  item.processing_status
                )
              }
            />
          ))}
        </div>
      </EximPaper>
    </div>
  );
}
