@import '@utils//main.scss';

.moowr-dashboard-wrapper {
  @include padding(0 20px);

  .business-container {
    .business-add-button {
      color: $white;
      min-width: 100px;

      .button-wrapper {
        .btn-children {
          font-size: $font-size-sm;
        }
        .base-btn {
          @include padding(10px 16px);
        }
      }

      .base-btn {
        @include padding(8px 10px);
        @include margin(0);
      }
    }
  }
  .product-head {
    font-size: $font-size-lg;
    color: $title-color;
  }
  .divider {
    hr {
      @include margin(28px 0);
    }
  }
}

// data upload steps style
.data-upload-step-container {
  @include margin-top(20px);
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;

    .filing-step-container {
      @include padding(0 16px);
      & > div:last-child {
        border-bottom: none;
      }
    }
    .date-container {
      @include flex-item(row, flex-end, center, _, 10px);
    }
  }

  .button-wrapper {
    .base-btn.discard-claim-btn {
      background-color: $table-head-primary;
      color: $white;
      &:hover {
        background-color: $table-head-primary;
        color: $white;
      }
    }
  }

  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .filing-head {
    .children-item {
      .button-wrapper {
        .base-btn {
          min-width: 114px;
          @include padding(7px 16px);
        }
      }
    }
  }

  .filing-step {
    .details-container {
      .filing-type {
        .avatar-wrapper {
          .avatar-container {
            .small {
              width: 28px;
              height: 28px;
            }

            .avatar-text {
              background-color: $tertiary;
              border: none;
              color: $white;
              font-size: $font-size-sm;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
  .error {
    color: $error;
    .error-count {
      @include margin-left(4px);
      color: $text-color;
    }
    .error-count-space {
      @include margin-left(10px);
    }
    font-size: $font-size-sm;
  }
  .success {
    color: $success;
    font-size: $font-size-sm;
  }
  .process {
    color: $warning;
    font-size: $font-size-sm;
  }

  // Transaction Modal
  .create-transaction-modal {
    letter-spacing: 0.5px;
    .modal-body {
      width: 348px;
      .modal-header {
        @include flex-item(row, space-between);
        @include padding(30px 30px 0px 30px);
      }
      .modal-content {
        @include margin-top(16px);
        @include padding(0 30px 30px 30px);
      }
      .transaction-modal-title {
        font-size: $font-size-xxl;
        color: $secondary-text;
      }
    }
  }

  // Discard Transaction Modal Style
  .discard-transaction-modal {
    letter-spacing: 0.2px;
    .modal-title h2 {
      color: $secondary-text;
    }
    .modal-body {
      width: 535px;
      .modal-header {
        align-items: flex-start;
        @include padding(32px 40px 12px 40px);
      }
      .modal-content {
        @include padding(0px 40px 16px 40px);
        .discard-modal-container {
          width: 100%;
          .btn-container {
            @include flex-item(_, flex-end, center, _, 16px);
            @include margin-top(40px);
            .button-wrapper {
              min-width: 100px;
              .base-btn {
                height: 32px;
                font-size: $font-size-sm;
                @include padding(7px 16px);
              }
            }
          }
        }
      }
    }
  }
}

// Transaction List Table Style
.transaction-list-container {
  border-top: 1px solid $primary-border;
  .table-search-container {
    @include margin-top(20px);
  }
  .transaction-list-table-container {
    @include padding(20px 16px);
    .transaction-history-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .transaction-history-tbody {
        .status-td {
          color: $error;
        }
      }
    }
  }
}

// View Transaction Text Modal Style
.view-transaction-text-modal {
  .modal {
    letter-spacing: 0.2px;
    .modal-title {
      @include flex-item(_, center, center, _, 2px);
      font-size: $font-size-sm;
      span {
        font-weight: normal;
      }
    }
    .modal-body {
      width: 445px;
      .modal-content {
        min-height: 0;
        @include padding-top(0);
        align-items: baseline;
        height: max-content;
        p {
          width: 400px;
          overflow-wrap: break-word;
        }
      }
    }
  }
}
