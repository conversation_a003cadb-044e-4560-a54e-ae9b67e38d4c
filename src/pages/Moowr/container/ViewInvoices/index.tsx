import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import RecordsCard from '@common/components/RecordsCard';
import {
  AlertStatus,
  EximHeroDate,
  MOOWR_FILE_ROUTE_TYPE,
  MoowrUploadProcessTitle,
  MoowrUploadProcessTitleType,
  ResponseStatus,
} from '@common/constants';
import {downloadLargeFileData} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {
  deleteInvoices,
  downloadLargeFile,
  invoicesSummary,
  reportsExport,
  reportsExportHistory,
} from '@pages/Moowr/api';
import BOMTable from '@pages/Moowr/components/BOMTable';
import ErrorRecordsUpload from '@pages/Moowr/components/ErrorRecordsUpload';
import FileDownloadStripe from '@pages/Moowr/components/FileDownloadStripe';
import InwardRegisterTable from '@pages/Moowr/components/InwardRegisterTable';
import JobWorkTable from '@pages/Moowr/components/JobWorkTable';
import OutwardRegisterTable from '@pages/Moowr/components/OutwardRegisterTable';
import OutwardStockTable from '@pages/Moowr/components/OutwardStockTable';
import {moowrActions} from '@pages/Moowr/store/reduce';
import EximButton from '@shared/components/EximButton';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ErrorIcon, SuccessIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router-dom';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

interface IInvoiceCount {
  valid_inv_count: number;
  invalid_inv_count: number;
}

// It is only for this page
const CARD_TYPE = {
  SUCCESS: 'success',
  ERROR: 'error',
};

function ViewInvoices() {
  const location = useLocation();
  const {fileType} = (location.state ?? {}) as {
    fileType: string;
  };

  const {
    auth: {
      userData: {email},
    },
    moowr: {
      panNumber,
      invoiceTxnId,
      invoicesCardActive,
      isLastTransactionInvalid,
      invoicesPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [invoiceCount, setInvoiceCount] = useState<IInvoiceCount>();
  const [isDeleteAllClicked, setIsDeleteAllClicked] = useState(false);
  // Below two state to store the refId of error records for deleting
  const [refIdsToDelete, setRefIdsToDelete] = useState<string[]>([]);

  const [downloadFileId, setDownloadFileId] = useState('');
  const [downloadFileStatus, setDownloadFileStatus] = useState('');
  const [downloadFileName, setDownloadFileName] = useState('');

  const handleViewRecord = (type: string) => {
    dispatch(moowrActions.setInvoicesCardActive(type === CARD_TYPE.SUCCESS));
    // Resetting the values on change the records type
    setRefIdsToDelete([]);
  };

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    dispatch(
      moowrActions.setInvoicesPeriod({
        startPeriod: startDate,
        endPeriod: endDate,
      })
    );
  };

  const getInvoicesSummary = useCallback(async () => {
    const payload = {
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      pan: panNumber,
      email,
      fileType,
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else if (fileType !== MOOWR_FILE_ROUTE_TYPE.BOM_STATEMENT) {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await invoicesSummary(payload);
    setInvoiceCount(data);
  }, [
    panNumber,
    email,
    fileType,
    invoiceTxnId,
    startPeriod,
    endPeriod,
    isLastTransactionInvalid,
  ]);

  const handleDeleteAll = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      fileType,
    };
    const response = (await deleteInvoices(
      payload,
      refIdsToDelete.length === 0,
      refIdsToDelete
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setRefIdsToDelete([]); // Empty the array after deleting the data
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }

    // Updating the data after deleting the all invalid records
    setIsDeleteAllClicked((prev) => !prev);
    getInvoicesSummary();
  };

  const disableDeleteAllBtn = () => {
    if (
      (refIdsToDelete.length === 0 && invoiceCount?.valid_inv_count !== 0) ||
      invoiceCount?.invalid_inv_count === 0
    ) {
      return true;
    }

    return false;
  };

  const handleDownloadInvoices = async () => {
    const payload = {
      pan: panNumber,
      fileId: downloadFileId,
      reportType: fileType,
    };

    const response = (await downloadLargeFile(payload)) as Blob;
    downloadLargeFileData(response, downloadFileName);
  };

  const getFileStatusToDownload = async () => {
    const {data} = await reportsExportHistory(panNumber);
    if (fileType === data?.[0]?.report_type) {
      setDownloadFileId(data?.[0]?.file_id);
      setDownloadFileStatus(data?.[0]?.status);
      setDownloadFileName(data?.[0]?.report_name);
    }
  };

  const handleExportReport = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      reportType: fileType,
    };
    await reportsExport(payload);
    await getFileStatusToDownload();
  };

  useEffect(() => {
    getInvoicesSummary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className='invoice-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText={`${
          MoowrUploadProcessTitle[fileType as MoowrUploadProcessTitleType]
        }`}
      />
      <BusinessHeader>
        <div className='btn-children-container'>
          <EximTypography>Period</EximTypography>
          <EximMonthRangePicker
            id='invoiceDatePicker'
            minDate={EximHeroDate.MIN_MONTH}
            onSelect={handleSelectPeriod}
            defaultStartDate={startPeriod.split('-').join('/')}
            defaultEndDate={endPeriod.split('-').join('/')}
            disabled={isLastTransactionInvalid}
          />
        </div>
      </BusinessHeader>
      <BusinessSubHeader fileType={fileType} />

      {invoicesCardActive ? (
        <FileDownloadStripe
          status={downloadFileStatus}
          handleRefresh={getFileStatusToDownload}
          handleDownload={handleDownloadInvoices}
        />
      ) : null}
      <div className='records-card-container'>
        <RecordsCard
          title='Valid records'
          icon={<SuccessIcon />}
          recordType='success'
          handleViewRecord={() => handleViewRecord(CARD_TYPE.SUCCESS)}
          isActive={invoicesCardActive}
          recordCount={invoiceCount?.valid_inv_count || 0}>
          <EximButton
            onClick={handleExportReport}
            disabled={
              !invoicesCardActive ||
              invoiceCount?.valid_inv_count === 0 ||
              downloadFileStatus !== ''
            }
            size='small'
            color='secondary'
            className='export-excel-btn'
            dataTestId='export-excel-btn'>
            Generate Data
          </EximButton>
        </RecordsCard>
        <RecordsCard
          title='Error records'
          icon={<ErrorIcon />}
          recordType='error'
          handleViewRecord={() => handleViewRecord(CARD_TYPE.ERROR)}
          isActive={!invoicesCardActive}
          recordCount={invoiceCount?.invalid_inv_count || 0}>
          <EximButton
            onClick={handleDeleteAll}
            disabled={invoicesCardActive || disableDeleteAllBtn()}
            size='small'
            color='secondary'
            className='delete-all-error-records-btn'
            dataTestId='delete-all-error-records-btn'>
            Delete All
          </EximButton>
        </RecordsCard>
      </div>
      <div className='view-invoices-container'>
        <EximPaper>
          {!invoicesCardActive ? (
            <ErrorRecordsUpload
              fileType={fileType}
              disabledButton={invoiceCount?.invalid_inv_count === 0}
            />
          ) : null}
          {fileType === MOOWR_FILE_ROUTE_TYPE.PARTIAL_OUTWARD_REGISTER ? (
            <OutwardStockTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setRefIdList={setRefIdsToDelete}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileType === MOOWR_FILE_ROUTE_TYPE.INWARD_REGISTER ? (
            <InwardRegisterTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setRefIdList={setRefIdsToDelete}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileType === MOOWR_FILE_ROUTE_TYPE.BOM_STATEMENT ? (
            <BOMTable
              isDeleteAllClicked={isDeleteAllClicked}
              setRefIdList={setRefIdsToDelete}
              isValidRecord={invoicesCardActive}
              getInvoicesSummary={getInvoicesSummary}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileType === MOOWR_FILE_ROUTE_TYPE.OUTWARD_REGISTER ? (
            <OutwardRegisterTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setRefIdList={setRefIdsToDelete}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileType === MOOWR_FILE_ROUTE_TYPE.JOB_WORK_REGISTER ? (
            <JobWorkTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setRefIdList={setRefIdsToDelete}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
        </EximPaper>
      </div>
    </div>
  );
}

export default ViewInvoices;
