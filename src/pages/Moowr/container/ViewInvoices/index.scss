@import '@utils/main.scss';

.view-invoices-container {
  @include margin(24px auto 32px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }
}
.invoice-container {
  @include padding(0 20px);
  .records-card-container {
    @include flex-item(_, space-between, center, _, 32px);
    margin-top: 40px;
    .records-card {
      width: 50%;
      background-color: $white;
      .records-card-left {
        flex: 1;
      }
    }
    .delete-all-error-records-btn,
    .export-excel-btn {
      @include padding(7.5px 28px);
      font-size: $font-size-sm;
      font-weight: $font-weight-semi-bold;
    }
  }
  .btn-children-container {
    @include flex-item(_, _, center, _, 12px);
    .sub-header-container {
      margin: 0;
      .paper-wrapper-rounded {
        box-shadow: none;
        padding: 0;
        .btn-container {
          padding: 0;
        }
      }
    }
    .base-date-picker {
      width: auto;
      .react-datepicker__portal {
        left: -77px;
      }
    }
  }
  .error-record-upload-container {
    @include margin-top(24px);
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .btn-container {
      @include flex-item(_, flex-end, center, _, 20px);
      @include padding(10px 16px);

      .button-wrapper {
        width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }
}
