import Helmet from '@common/components/utils/Helmet';
import {
  AlertStatus,
  HelmetTitle,
  Path,
  ResponseStatus,
} from '@common/constants';
import {IAccountSetupAddress} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TextField} from '@mui/material';
import FreeTrialSidebar from '@pages/Auth/components/FreeTrialSidebar';
import {authActions} from '@pages/Auth/store/reducer';
import {addressDetailsSchema, gstinDetailsSchema} from '@pages/Dashboard/utils';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import getGstInApi, {createBillingSubscription} from '@subscription/api';
import GstinInfo from '@subscription/components/GstinInfo';
import {subscriptionActions} from '@subscription/store/reducer';
import {useFormik} from 'formik';
import {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import AddressDetails from './components/AddressDetails';
import './index.scss';

export const initialsAddressDetails = {
  address1: '',
  address2: '',
  pincode: '',
  city: '',
  state: '',
  country: '',
  billingAddress1: '',
  billingAddress2: '',
  billingPincode: '',
  billingCity: '',
  billingState: '',
  billingCountry: '',
  sameAsBilling: false,
};

export default function AccountSetup() {
  const navigate = useNavigate();
  const [showSidebar, setShowSidebar] = useState(false);

  const handleClose = () => {
    setShowSidebar(true);
  };

  const handleClearAll = () => {
    setShowSidebar(false);
  };
  const {
    subscription: {
      gstInDetails: {businessName, tradeName, panNumber},
    },
  } = useSelector((state: RootState) => state);

  const [validatedGstin, setValidatedGstin] = useState(false);
  const [disable, setDisable] = useState(false);

  const addressFormik = useFormik<IAccountSetupAddress>({
    initialValues: initialsAddressDetails,
    validationSchema: addressDetailsSchema(),
    onSubmit: async (values) => {
      // INFO: No noed to implement this function
    },
  });

  const getGstInDetailsData = async (gstInNumber: string) => {
    const res = await getGstInApi({
      gstInNumber,
      productName: '',
    });
    if (res.status.toString() === ResponseStatus.SUCCESS) {
      const {
        lgnm,
        dty,
        tradeNam,
        sts,
        gstin: gstNumber,
        ctj,
        stj,
        ctb,
        rgdt,
      } = res.data;
      const panId = gstNumber.substring(2, 12);

      const gstinDetails = {
        businessName: lgnm,
        taxPayerType: dty,
        tradeName: tradeNam || lgnm,
        status: sts,
        gstin: gstNumber,
        city: ctj,
        stj,
        constitutionOfBusiness: ctb,
        registrationDate: rgdt,
        panNumber: panId,
      };
      const organisationGstPayload = {
        gstin: gstNumber,
        tradeName: tradeNam || lgnm,
        legalName: tradeNam || lgnm,
      };
      dispatch(subscriptionActions.getGstInDetails(gstinDetails));
      dispatch(subscriptionActions.addGstDetails(organisationGstPayload));
      dispatch(subscriptionActions.addPanNumberAndOrgName({panId, lgnm}));
      setDisable(false);
      setValidatedGstin(true);
    }
  };

  const formik = useFormik({
    initialValues: {
      gstin: '',
    },
    validationSchema: gstinDetailsSchema(),
    onSubmit: async (values) => {
      getGstInDetailsData(values.gstin);
    },
  });

  const submitForActivation = async (isFree: boolean) => {
    addressFormik.submitForm();
    const hasErrors = await addressFormik.validateForm();
    if (Object.keys(hasErrors).length > 0) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: 'Please fill all the required fields.',
          alertType: AlertStatus.DANGER,
        })
      );
      return;
    }

    const orgPayload = {
      organizationDetails: {
        orgName: businessName,
        pan: panNumber,
        orgRole: 'ORG_MASTER_ADMIN',
        gstinDetails: {
          gstin: formik.values.gstin,
          tradeName,
          legalName: businessName,
        },
        registeredAddressBean: {
          addressLine1: addressFormik.values.address1,
          addressLine2: addressFormik.values.address2,
          city: addressFormik.values.city,
          state: addressFormik.values.state,
          pinCode: addressFormik.values.pincode,
          country: addressFormik.values.country,
        },
        billingAddressBean: {
          addressLine1: addressFormik.values.billingAddress1,
          addressLine2: addressFormik.values.billingAddress2,
          city: addressFormik.values.billingCity,
          state: addressFormik.values.billingState,
          pinCode: addressFormik.values.billingPincode,
          country: addressFormik.values.billingCountry,
        },
      },
      partnerDetails: {
        partnerCode: 'RGSTH001',
        companyName: '',
      },
    };

    if (isFree) {
      handleClose();
    } else {
      const response = await createBillingSubscription(orgPayload);
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: response.status || response.status,
            message: response.data.message,
            alertType: AlertStatus.SUCCESS,
          })
        );
        dispatch(authActions.setIsNewUser(false));
        navigate(Path.DASHBOARD);
      }
    }
  };
  const clearAllDetails = async () => {
    setValidatedGstin(false);
    // INFO: Resetting the Submission Settings Form values
    addressFormik.resetForm();

    formik.setValues({
      ...formik.values,
      gstin: '',
    });

    dispatch(subscriptionActions.getGstInDetails({}));
    dispatch(subscriptionActions.addGstDetails({}));
    dispatch(subscriptionActions.addPanNumberAndOrgName({}));

    dispatch(subscriptionActions.setPartnerDetails({}));

    window.location.reload();
  };

  return (
    <>
      <Helmet title={HelmetTitle.ACCOUNT_SETUP} />

      <div className='account-setup-container'>
        <div className='account-setup-form'>
          <div className='table-title'>
            <EximTypography fontWeight='semi-bold' variant='h3'>
              Billing Company Details
            </EximTypography>
          </div>

          <form onSubmit={formik.handleSubmit} noValidate>
            <div className='multi-column-details'>
              <TextField
                className='common-input'
                id='gstin'
                required
                label='Billing GSTIN'
                name='gstin'
                value={formik.values.gstin}
                onBlur={formik.handleBlur}
                size='small'
                error={formik.touched.gstin && Boolean(formik.errors.gstin)}
                helperText={formik.touched.gstin && formik.errors.gstin}
                onChange={formik.handleChange}
              />
              <EximButton size='small' type='submit' color='tertiary'>
                {disable ? 'Edit Details' : 'Get Details'}
              </EximButton>
            </div>
            {validatedGstin ? <GstinInfo /> : null}
          </form>
        </div>
        {validatedGstin ? (
          <AddressDetails addressFormik={addressFormik} />
        ) : null}

        {validatedGstin ? (
          <div className='account-setup-form'>
            <div className='save-details'>
              <EximButton
                type='reset'
                color='secondary'
                onClick={() => clearAllDetails()}
                size='small'>
                Clear All
              </EximButton>

              <EximButton
                size='small'
                onClick={() => {
                  submitForActivation(true);
                }}
                type='submit'>
                Start Free Trial
              </EximButton>

              <div className='activate-button'>
                <EximButton
                  size='small'
                  onClick={() => submitForActivation(false)}
                  type='submit'>
                  Register Account
                </EximButton>
              </div>
            </div>
          </div>
        ) : null}
        {showSidebar && (
          <FreeTrialSidebar
            onClose={() => setShowSidebar(false)}
            onClearAll={handleClearAll}
            gstin={formik.values.gstin}
            addressFormik={addressFormik}
            partnerDetails={{
              partnerCode: 'RGSTH001',
              companyName: '',
            }}
          />
        )}
      </div>
    </>
  );
}
