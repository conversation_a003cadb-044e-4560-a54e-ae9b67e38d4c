import {ResponseStatus, STATES} from '@common/constants';
import {IAccountSetupAddress} from '@common/interfaces';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import {partnerCodeSchema} from '@pages/Dashboard/utils';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {dispatch} from '@store';
import {verifyPartner} from '@subscription/api';
import {subscriptionActions} from '@subscription/store/reducer';
import {FormikProps, useFormik} from 'formik';
import {useState} from 'react';

interface AddressDetailsProps {
  addressFormik: FormikProps<IAccountSetupAddress>;
}

export default function AddressDetails({addressFormik}: AddressDetailsProps) {
  const STATES_NAME: string[] = Object.values(STATES);

  const [partnerName, setPartnerName] = useState('');
  const [isEdit, setIsEdit] = useState(false);

  const formik = useFormik({
    initialValues: {partnerCode: ''},
    validationSchema: partnerCodeSchema(),
    onSubmit: async (values) => {
      if (!isEdit) {
        const response = await verifyPartner(values.partnerCode);
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(true);
          setPartnerName(response.data?.companyName);
          dispatch(subscriptionActions.setPartnerDetails(response.data));
        }
      } else {
        setIsEdit(false);
      }
    },
  });

  const handleSameAsBillingChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const isChecked = event.target.checked;
    addressFormik.setFieldValue('sameAsBilling', isChecked);

    if (isChecked) {
      addressFormik.setValues({
        ...addressFormik.values,
        billingAddress1: addressFormik.values.address1,
        billingAddress2: addressFormik.values.address2,
        billingPincode: addressFormik.values.pincode,
        billingCity: addressFormik.values.city,
        billingState: addressFormik.values.state,
        billingCountry: addressFormik.values.country,
        sameAsBilling: true,
      });
    } else {
      addressFormik.setValues({
        ...addressFormik.values,
        billingAddress1: '',
        billingAddress2: '',
        billingPincode: '',
        billingCity: '',
        billingState: '',
        billingCountry: '',
        sameAsBilling: false,
      });
    }
  };

  return (
    <div>
      <form className='account-setup-form'>
        <div className='table-title'>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            Address Details
          </EximTypography>
        </div>
        <div className='address-label'>
          <EximTypography fontWeight='semi-bold' variant='h4'>
            Registered Details
          </EximTypography>
        </div>
        <div className='single-column-details'>
          <TextField
            className='common-input'
            id='address1'
            label='Address Line 1'
            size='small'
            name='address1'
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.address1 &&
              Boolean(addressFormik.errors.address1)
            }
            helperText={
              addressFormik.touched.address1 && addressFormik.errors.address1
            }
            value={addressFormik.values.address1}
            onChange={addressFormik.handleChange}
          />
          <TextField
            className='common-input'
            id='address2'
            label='Address Line 2'
            size='small'
            name='address2'
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.address2 &&
              Boolean(addressFormik.errors.address2)
            }
            helperText={
              addressFormik.touched.address2 && addressFormik.errors.address2
            }
            value={addressFormik.values.address2}
            onChange={addressFormik.handleChange}
          />
        </div>
        <div className='multi-column-details address'>
          <TextField
            className='common-input'
            id='pincode'
            label='Pincode'
            size='small'
            name='pincode'
            value={addressFormik.values.pincode}
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.pincode &&
              Boolean(addressFormik.errors.pincode)
            }
            helperText={
              addressFormik.touched.pincode && addressFormik.errors.pincode
            }
            onChange={addressFormik.handleChange}
          />
          <TextField
            className='common-input'
            id='city'
            label='City'
            size='small'
            name='city'
            value={addressFormik.values.city}
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.city && Boolean(addressFormik.errors.city)
            }
            helperText={addressFormik.touched.city && addressFormik.errors.city}
            onChange={addressFormik.handleChange}
          />
        </div>
        <div className='multi-column-details address'>
          <FormControl sx={{minWidth: 120}} size='small'>
            <InputLabel id='demo-select-small-label'>Country</InputLabel>
            <Select
              className='common-input'
              labelId='demo-select-small-label'
              id='demo-select-small'
              label='Country'
              value={addressFormik.values.country}
              onBlur={addressFormik.handleBlur}
              error={
                addressFormik.touched.country &&
                Boolean(addressFormik.errors.country)
              }
              onChange={(e) =>
                addressFormik.setFieldValue('country', e.target.value)
              }>
              <MenuItem value=''>
                <em>State</em>
              </MenuItem>
              <MenuItem value='india'>India</MenuItem>
            </Select>
            {addressFormik.touched.country && addressFormik.errors.country && (
              <div className='dropdown-error'>
                {addressFormik.errors.country}
              </div>
            )}
          </FormControl>
          <FormControl sx={{minWidth: 120}} size='small'>
            <InputLabel id='demo-select-small-label'>State</InputLabel>
            <Select
              className='common-input'
              labelId='demo-select-small-label'
              id='demo-select-small'
              label='State'
              value={addressFormik.values.state}
              onChange={(e) =>
                addressFormik.setFieldValue('state', e.target.value)
              }
              onBlur={addressFormik.handleBlur}
              error={
                addressFormik.touched.state &&
                Boolean(addressFormik.errors.state)
              }>
              <MenuItem value=''>
                <em>None</em>
              </MenuItem>
              {STATES_NAME.map((value: string, index) => (
                <MenuItem key={value} value={index.toString().padStart(2, '0')}>
                  {value}
                </MenuItem>
              ))}
            </Select>
            {addressFormik.touched.state && addressFormik.errors.state && (
              <div className='dropdown-error'>{addressFormik.errors.state}</div>
            )}
          </FormControl>
        </div>
        <div className='address-label'>
          <FormControlLabel
            control={
              <Checkbox
                checked={Boolean(addressFormik.values.sameAsBilling)}
                onChange={handleSameAsBillingChange}
                name='sameAsBilling'
              />
            }
            label='Same As Register Address'
          />
        </div>
        <div className='address-label'>
          <EximTypography fontWeight='semi-bold' variant='h4'>
            Business Address
          </EximTypography>
        </div>
        <div className='single-column-details'>
          <TextField
            className='common-input'
            id='billingAddress1'
            label='Address Line 1'
            size='small'
            name='billingAddress1'
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.billingAddress1 &&
              Boolean(addressFormik.errors.billingAddress1)
            }
            helperText={
              addressFormik.touched.billingAddress1 &&
              addressFormik.errors.billingAddress1
            }
            value={addressFormik.values.billingAddress1}
            onChange={addressFormik.handleChange}
            disabled={addressFormik.values.sameAsBilling}
          />
          <TextField
            className='common-input'
            id='billingAddress2'
            label='Address Line 2'
            size='small'
            name='billingAddress2'
            value={addressFormik.values.billingAddress2}
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.billingAddress2 &&
              Boolean(addressFormik.errors.billingAddress2)
            }
            helperText={
              addressFormik.touched.billingAddress2 &&
              addressFormik.errors.billingAddress2
            }
            onChange={addressFormik.handleChange}
            disabled={addressFormik.values.sameAsBilling}
          />
        </div>
        <div className='multi-column-details address'>
          <TextField
            className='common-input'
            id='billingPincode'
            label='Pincode'
            type='number'
            size='small'
            name='billingPincode'
            value={addressFormik.values.billingPincode}
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.billingPincode &&
              Boolean(addressFormik.errors.billingPincode)
            }
            helperText={
              addressFormik.touched.billingPincode &&
              addressFormik.errors.billingPincode
            }
            onChange={addressFormik.handleChange}
            disabled={addressFormik.values.sameAsBilling}
          />

          <TextField
            className='common-input'
            id='billingCity'
            label='City'
            size='small'
            name='billingCity'
            value={addressFormik.values.billingCity}
            onBlur={addressFormik.handleBlur}
            error={
              addressFormik.touched.billingCity &&
              Boolean(addressFormik.errors.billingCity)
            }
            helperText={
              addressFormik.touched.billingCity &&
              addressFormik.errors.billingCity
            }
            onChange={addressFormik.handleChange}
            disabled={addressFormik.values.sameAsBilling}
          />
        </div>
        <div className='multi-column-details address'>
          <FormControl sx={{minWidth: 120}} size='small'>
            <InputLabel id='demo-select-small-label'>Country</InputLabel>
            <Select
              className='common-input'
              labelId='demo-select-small-label'
              id='billingCountry'
              name='billingCountry'
              label='Country'
              value={addressFormik.values.billingCountry}
              onBlur={addressFormik.handleBlur}
              error={
                addressFormik.touched.city &&
                Boolean(addressFormik.errors.billingCountry)
              }
              onChange={(e) =>
                addressFormik.setFieldValue('billingCountry', e.target.value)
              }
              disabled={addressFormik.values.sameAsBilling}>
              <MenuItem value=''>
                <em>Country</em>
              </MenuItem>
              <MenuItem value='india'>India</MenuItem>
            </Select>
            {addressFormik.touched.billingCountry &&
              addressFormik.errors.billingCountry && (
                <div className='dropdown-error'>
                  {addressFormik.errors.billingCountry}
                </div>
              )}
          </FormControl>
          <FormControl sx={{minWidth: 120}} size='small'>
            <InputLabel id='demo-select-small-label'>State</InputLabel>
            <Select
              className='common-input'
              labelId='demo-select-small-label'
              id='billingState'
              label='State'
              name='billingState'
              value={addressFormik.values.billingState}
              onBlur={addressFormik.handleBlur}
              error={
                addressFormik.touched.billingState &&
                Boolean(addressFormik.errors.billingState)
              }
              onChange={(e) =>
                addressFormik.setFieldValue('billingState', e.target.value)
              }
              disabled={addressFormik.values.sameAsBilling}>
              <MenuItem value=''>
                <em> None</em>
              </MenuItem>
              {STATES_NAME.map((value: string, index) => (
                <MenuItem key={value} value={index.toString().padStart(2, '0')}>
                  {value}
                </MenuItem>
              ))}
            </Select>
            {addressFormik.touched.billingState &&
              addressFormik.errors.billingState && (
                <div className='dropdown-error'>
                  {addressFormik.errors.billingState}
                </div>
              )}
          </FormControl>
        </div>
      </form>
      <div className='account-setup-form'>
        <div className='table-title'>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            Partner Details
          </EximTypography>
        </div>
        <div className='partner-container'>
          <div>
            <div>
              <EximTypography variant='h4' fontWeight='semi-bold'>
                Partner Name :
                {partnerName ? (
                  <span className='partner-name'>{` ${partnerName}`}</span>
                ) : null}
              </EximTypography>
            </div>
            <div className='partner-details'>
              <TextField
                className='common-input'
                id='partnerCode'
                disabled={isEdit}
                label='Partner Code'
                size='small'
                name='partnerCode'
                value={formik.values.partnerCode}
                onBlur={formik.handleBlur}
                error={
                  formik.touched.partnerCode &&
                  Boolean(formik.errors.partnerCode)
                }
                helperText={
                  formik.touched.partnerCode && formik.errors.partnerCode
                }
                onChange={formik.handleChange}
              />
              <EximButton
                id='partner-code'
                variant='text'
                size='small'
                color='information'
                onClick={formik.submitForm}>
                {isEdit ? 'Edit' : 'Verify'}
              </EximButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
