@import '@utils/main.scss';
.account-setup-container {
  @include padding(0px 50px 0px 50px);
  position: relative;
  .account-setup-form {
    width: 100%;
    @include rfs(16px, border-radius);
    @include margin-top(20px);
    background-color: $white;
    .table-title {
      width: 100%;
      height: 70px;
      @include padding(23px 0 23px 20px);
      @include rfs(16px 16px 0 0, border-radius);
      background: $table-title-bg;
    }
    .address-label {
      @include margin(20px);
      .form-check {
        .label span {
          font-size: $font-size-xsm;
        }
      }
    }
    & > form {
      @include padding(20px 20px 32px 20px);
      background-color: $white;
      @include rfs(0 0 16px 16px, border-radius);
      .common-input {
        .MuiFormHelperText-root {
          position: absolute;
          top: 35px;
        }
      }
    }
    .multi-column-details {
      width: 60%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      row-gap: 24px;
      column-gap: 24px;

      .button-wrapper {
        width: 100px;
      }
      .dropdown-error {
        color: $error-msg-color;
        font-size: $font-size-xsm;
      }
    }
    .gstin-info {
      @include margin-top(20px);
    }
    .tin-column-details-div {
      @include flex-item(_, flex-start, center, _, 24px);
      @include margin-top(24px);
      .multi-column-details {
        width: 60%;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        row-gap: 24px;
        column-gap: 24px;

        .button-wrapper {
          width: 82px;
        }
      }
    }
    .multi-column-details.address {
      @include margin(20px 0 0 20px);
    }
    .multi-column-details.address:last-child {
      @include padding-bottom(20px);
    }
    .single-column-details {
      width: 60%;
      display: grid;
      background-color: $white;
      @include margin(10px 0 0 20px);
      row-gap: 24px;
      column-gap: 20px;
    }
    .partner-container {
      @include padding(10px 20px 32px 20px);
      .partner-name {
        color: $label-color;
        font-size: $font-size-sm;
      }
      .partner-details {
        @include flex-item(_, flex-start, center, _, 24px);
        width: 50%;
        background-color: $white;
        @include rfs(0 0 16px 16px, border-radius);
        @include padding-top(20px);
        .common-input {
          .MuiFormHelperText-root {
            position: absolute;
            top: 35px;
          }
        }
        .input-wrapper {
          @include margin-right(15px);
        }
        .MuiTypography-root {
          color: $information-light;
          @include padding(8px 0px 0px 0px);
        }
        .verified {
          @include flex-item(_, center, center, _);
          @include margin(8px);
          color: $success;
        }
        .partnerName {
          @include flex-item(_, center, center, _);
          @include margin(8px);
        }
      }
    }

    .save-details {
      width: 100%;
      height: 79px;
      background-color: $white;
      font-size: $font-size-sm;
      @include flex-item(_, flex-end, center, _, 20px);
      @include margin(10px 0 20px 0);
      @include padding(20px 20px 20px 20px);
      @include rfs(16px 16px 16px 16px, border-radius);

      .activate-button {
        .button-wrapper {
          height: -webkit-fill-available;
        }
        .primary {
          background-color: $information-light;
        }
      }
    }
  }
}
