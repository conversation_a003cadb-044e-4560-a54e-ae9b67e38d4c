import UploadFiles from '@common/components/UploadFiles';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {
  MbToBytes,
  checkFilesExtension,
  formatAmount,
  getAllFilesSize,
  getWordFromUrl,
} from '@common/helpers';
import {alertActions} from '@core/api/store/alertReducer';
import {uploadFiles} from '@pages/DataExtractor/api';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {ChangeEvent} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

import './index.scss';

interface IBusinessSubHeaderProps {
  totalUploadedFile: number | string;
  getUploadLogsData: () => void;
}

function BusinessSubHeader({
  totalUploadedFile,
  getUploadLogsData,
}: IBusinessSubHeaderProps) {
  const navigate = useNavigate();
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1).toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const data = {
    pan: panNumber,
    fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
    email,
  };
  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);
      // Set the maximum allowed size limit (20 MB in bytes)
      const limit = MbToBytes(20);
      const allFileSize = getAllFilesSize(selectedFilesArray);

      if (checkFilesExtension(selectedFilesArray, [SupportedFileTypes.PDF])) {
        if (allFileSize > limit) {
          dispatch(
            alertActions.setAlertMsg({
              code: 400,
              message: 'File size can not be more the 20MB',
              alertType: 'danger',
            })
          );
        } else {
          const response = await uploadFiles(data, selectedFilesArray);
          if (response.status.toString() === ResponseStatus.SUCCESS) {
            getUploadLogsData(); // Calling function to update the upload logs data
          }
        }
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message: 'Please select the pdf files only!',
            alertType: 'danger',
          })
        );
      }
    }
  };
  const handleAdvanceExport = () => {
    const period = {
      startPeriod: '',
      endPeriod: '',
    };
    // setting the default date
    dispatch(dataExtractorActions.setAdvanceExportPeriod(period));
    dispatch(dataExtractorActions.setIsApplyClickedOnAdvanceExport(false));
    navigate(
      `${Path.DATA_EXTRACTOR}${Path.ADVANCE_EXPORT}/${fileType.toLowerCase()}`
    );
  };
  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='sub-header-wrapper'>
          <EximTypography variant='h4'>
            Total PDF Upload Count:- {formatAmount(totalUploadedFile)}
          </EximTypography>
          <div className='btn-container'>
            <UploadFiles onChange={handleFileUpload} accept='.pdf' multiple />
            <EximButton
              size='small'
              color='tertiary'
              disabled={totalUploadedFile === 0}
              dataTestId='advance-export'
              onClick={handleAdvanceExport}>
              Advance Export
            </EximButton>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}

export default BusinessSubHeader;
