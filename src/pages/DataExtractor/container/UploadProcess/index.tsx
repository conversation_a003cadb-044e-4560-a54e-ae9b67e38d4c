import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import Helmet from '@common/components/utils/Helmet';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  HelmetTitle,
} from '@common/constants';
import {IUploadLogs} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {getUploadedLogs} from '@pages/DataExtractor/api';
import UploadLogsTable from '@pages/DataExtractor/components/UploadLogsTable';
import {UPLOAD_LOGS_TABLE_SEARCH_DROPDOWN} from '@pages/DataExtractor/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useParams} from 'react-router';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function UploadAndProcess() {
  const {fileType} = useParams();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const [uploadLogsData, setUploadLogsData] = useState<IUploadLogs[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const allUploadedFilesCount =
    uploadLogsData?.length > 0
      ? uploadLogsData?.reduce((acc, curr) => acc + curr['total-file-count'], 0)
      : 0;

  const getUploadLogsData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType?.toUpperCase() as FileType],
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getUploadedLogs(payload, page, +showEntries);
    setUploadLogsData(data?.['file-upload-history']);
    setTotalRecords(data?.['total-records']);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    fileType,
    panNumber,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getUploadLogsData();
  }, [getUploadLogsData]);

  return (
    <>
      <Helmet title={HelmetTitle.UPLOAD_AND_PROCESS} />
      <div className='upload-process-container'>
        <NavigationSubHeader
          hasTitle
          leftArrowRoute='#'
          hasLeftArrow
          leftArrowText={`Upload & Process -
            ${
              fileType?.toUpperCase() === 'SB'
                ? 'Shipping Bill'
                : 'Bill Of Entry'
            }`}
          isNavigate
        />
        <BusinessHeader />
        <BusinessSubHeader
          totalUploadedFile={allUploadedFilesCount ?? 0}
          getUploadLogsData={getUploadLogsData}
        />

        <EximPaper>
          <div className='upload-logs-container'>
            <EximTypography fontWeight='bold' variant='h4'>
              Upload Logs
            </EximTypography>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={UPLOAD_LOGS_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <UploadLogsTable
              uploadLogs={uploadLogsData ?? []}
              getUploadLogsData={getUploadLogsData}
              handleSortBy={handleSortBy}
            />
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={uploadLogsData as []}
              renderData={uploadLogsData as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>
      </div>
    </>
  );
}

export default UploadAndProcess;
