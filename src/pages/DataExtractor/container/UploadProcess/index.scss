@import '@utils/main.scss';

.upload-process-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .subscription-header-left {
    color: $text-color;
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .sub-header-wrapper {
      @include flex-item(_, space-between, center);
      @include padding(10px 16px);

      .btn-container {
        @include flex-item(_, flex-end, center, _, 20px);
        display: flex;
        align-items: center;

        .base-btn {
          background-color: $table-head-primary;
          font-size: $font-size-sm;
          color: $white;
          @include padding(7px 20px);
          &:hover {
            background-color: $table-head-primary;
            color: $white;
          }
        }
      }
    }
  }
}
