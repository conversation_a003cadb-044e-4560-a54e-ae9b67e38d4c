import BusinessHeader from '@common/components/BusinessHeader';
import DeleteConfirmationModal from '@common/components/DeleteConfirmationModal';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import RecordsCard from '@common/components/RecordsCard';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import Helmet from '@common/components/utils/Helmet';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  HelmetTitle,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile, formatAmount, getWordFromUrl} from '@common/helpers';
import {IUploadDetails, IUploadFileDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {
  deleteAllFailedPdfFiles,
  exportReports,
  getUploadedDetails,
} from '@pages/DataExtractor/api';
import ExportAs from '@pages/DataExtractor/components/ExportAs';
import UploadDetailsTable from '@pages/DataExtractor/components/UploadDetailsTable';
import {UPLOAD_DETAILS_TABLE_SEARCH_DROPDOWN} from '@pages/DataExtractor/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {ErrorIcon, SuccessIcon} from '@shared/icons';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useParams} from 'react-router';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function ViewUploadDetails() {
  const {id: txnId} = useParams();
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 2).toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const [uploadDetails, setUploadDetails] = useState<IUploadDetails>();
  const [uploadFileDetails, setUploadFileDetails] = useState<
    IUploadFileDetails[]
  >([]);

  const [invoicesCardActive, setInvoicesCardActive] = useState(true);
  const [activeCardRecords, setActiveCardRecords] = useState(0);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleViewRecord = (typeOfRecords: 'success' | 'error') => {
    if (typeOfRecords === 'success') setInvoicesCardActive(true);
    else setInvoicesCardActive(false);
  };

  const getUploadDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      failedFiles: !invoicesCardActive,
      txnId,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getUploadedDetails(payload, page, +showEntries);
    setUploadDetails(data);
    setUploadFileDetails(data['file-details']);
    const currentCount = invoicesCardActive
      ? data?.['processing-details']?.completed
      : data?.['processing-details']?.failed;
    setActiveCardRecords(currentCount || 0);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    panNumber,
    email,
    txnId,
    invoicesCardActive,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  // TODO: Below functionality not needed for now
  const handleExportExcel = async (exportType: string) => {
    const payload = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      exportFormat: exportType,
      txnId,
      email,
    };

    const {data} = await exportReports(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const handleDeleteAllFailedFiles = async () => {
    const payload = {
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      txnId,
    };
    await deleteAllFailedPdfFiles(payload);
    setIsOpenDeleteModal(false);
    getUploadDetails();
  };

  useEffect(() => {
    getUploadDetails();
  }, [getUploadDetails]);

  return (
    <>
      <Helmet title={HelmetTitle.VIEW_UPLOAD_DETAILS} />
      <div className='upload-details-container'>
        <NavigationSubHeader
          hasTitle
          leftArrowRoute='#'
          hasLeftArrow
          leftArrowText='Upload Details'
          isNavigate
        />
        <BusinessHeader />
        <BusinessSubHeader
          totalUploadedFile={formatAmount(
            uploadDetails?.['total-file-count'] || 0
          )}
          uploadDate={uploadDetails?.['last-updated-date'] || ''}
          uploadBy={uploadDetails?.['last-updated-by'] || ''}
        />

        <div className='records-card-container'>
          <RecordsCard
            title='Processed Files'
            icon={<SuccessIcon />}
            recordType='success'
            handleViewRecord={handleViewRecord}
            isActive={invoicesCardActive}
            recordCount={uploadDetails?.['processing-details']?.completed || 0}>
            <ExportAs
              handleExportType={handleExportExcel}
              isDropdown={fileType === 'BOE'}
              isDisabled={
                !invoicesCardActive ||
                !uploadDetails?.['processing-details'].completed
              }
            />
          </RecordsCard>
          <RecordsCard
            title='Failed Files'
            icon={<ErrorIcon />}
            recordType='error'
            handleViewRecord={handleViewRecord}
            isActive={!invoicesCardActive}
            recordCount={uploadDetails?.['processing-details']?.failed || 0}>
            <EximButton
              onClick={() => setIsOpenDeleteModal(true)}
              disabled={
                invoicesCardActive ||
                !uploadDetails?.['processing-details'].failed
              }
              size='small'
              color='secondary'
              className='delete-all-error-records-btn'
              dataTestId='delete-all-error-records-btn'>
              Delete All
            </EximButton>
          </RecordsCard>
        </div>

        <EximPaper>
          <div className='upload-details-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={UPLOAD_DETAILS_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <UploadDetailsTable
              uploadDetails={uploadFileDetails}
              getUploadDetails={getUploadDetails}
              handleSortBy={handleSortBy}
            />
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={activeCardRecords}
              searchData={uploadFileDetails as []}
              renderData={uploadFileDetails as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isOpenDeleteModal}
          onClose={() => {
            setIsOpenDeleteModal(false);
          }}
          handleConfirm={handleDeleteAllFailedFiles}
        />
      </div>
    </>
  );
}

export default ViewUploadDetails;
