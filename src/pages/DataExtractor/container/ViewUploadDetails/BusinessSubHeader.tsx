import {formatAmount, formatDateWithTime} from '@common/helpers';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';

import './index.scss';

interface IBusinessSubHeaderProps {
  totalUploadedFile: number | string;
  uploadDate: string;
  uploadBy: string;
}

function BusinessSubHeader({
  totalUploadedFile,
  uploadDate,
  uploadBy,
}: IBusinessSubHeaderProps) {
  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='sub-header-wrapper'>
          <EximTypography variant='h4'>
            Total PDF Upload Count -{' '}
            <span>{formatAmount(totalUploadedFile)}</span>
          </EximTypography>
          <span>|</span>
          <EximTypography variant='h4'>
            Processed Date & Time -{' '}
            <span>{formatDateWithTime(uploadDate)}</span>
          </EximTypography>
          <span>|</span>
          <EximTypography variant='h4'>
            Uploaded By - <span>{uploadBy}</span>
          </EximTypography>
        </div>
      </EximPaper>
    </div>
  );
}

export default BusinessSubHeader;
