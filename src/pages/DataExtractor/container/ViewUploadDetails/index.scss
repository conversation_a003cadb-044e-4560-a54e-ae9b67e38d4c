@import '@utils/main.scss';

.upload-details-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .subscription-header-left {
    color: $text-color;
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .sub-header-wrapper {
      @include flex-item(_, _, center, _, 12px);
      @include padding(10px 16px);
      text-transform: capitalize;

      .typography-container {
        span {
          font-weight: $font-weight-semi-bold;
          @include margin-left(2px);
          letter-spacing: 0.4px;
        }
      }
    }
  }

  .records-card-container {
    @include flex-item(_, space-between, center, _, 32px);
    margin-top: 40px;
    .records-card {
      width: 50%;
      .records-card-left {
        flex: 1;
      }
    }
    .delete-all-error-records-btn {
      .base-btn {
        min-width: 115px;
        height: 33px;
        font-size: $font-size-sm;
        font-weight: $font-weight-semi-bold;
      }
    }
  }
}
