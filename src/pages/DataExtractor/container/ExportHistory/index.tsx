import BusinessHeader from '@common/components/BusinessHeader';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@common/components/EllipsisChecker';
import EmptyTable from '@common/components/EmptyTable';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import Helmet from '@common/components/utils/Helmet';
import {
  AlertStatus,
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  HelmetTitle,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile, formatDateWithTime} from '@common/helpers';
import {IExportHistoryData, ITableCommonHeader} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  exportReports,
  getExportData,
  updateRemark,
} from '@pages/DataExtractor/api';
import {
  EXPORT_HISTORY_TABLE_HEADER,
  EXPORT_HISTORY_TABLE_SEARCH_DROPDOWN,
} from '@pages/DataExtractor/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {format, parse} from 'date-fns';
import {ChangeEvent, useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useParams} from 'react-router-dom';

import './index.scss';

function ExportHistory() {
  const {queryParam} = useParams();
  const fileTypeCaps = queryParam?.toUpperCase() as FileType;

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const tableHeader: ITableCommonHeader[] = useMemo(() => {
    if (fileTypeCaps === 'SB' || fileTypeCaps === 'BOE') {
      return EXPORT_HISTORY_TABLE_HEADER(fileTypeCaps);
    }
    return [];
  }, [fileTypeCaps]);

  const [exportRecords, setExportRecords] = useState<IExportHistoryData[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [remarkText, setRemarkText] = useState('');
  const [isErrorMsg, setIsErrorMsg] = useState(false);
  const [exportFileId, setExportFileId] = useState('');
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [isOpenTextModal, setIsOpenTextModal] = useState(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleGetExportData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps],
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getExportData(payload, page, +showEntries);
    setExportRecords(data?.records || []);
    setTotalRecords(data?.['total-records']);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    panNumber,
    fileTypeCaps,
    email,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleChangeRemark = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const {value} = event.target;
    setRemarkText(value);
    if (value?.length > 250) {
      setIsErrorMsg(true);
    } else {
      setIsErrorMsg(false);
    }
  };

  const handleDownload = async (exportId: string) => {
    const payload = {
      exportId,
      email,
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps],
    };
    const {data} = await exportReports(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const getExportPeriod = (period: string) => {
    const [startPeriod, endPeriod] = period.split(' to ');
    const lengthOfDate = startPeriod.split('-').length - 1;

    const startMonth = startPeriod.split('-')[lengthOfDate - 1];
    const startYear = startPeriod.split('-')[lengthOfDate];

    const endMonth = endPeriod.split('-')[lengthOfDate - 1];
    const endYear = endPeriod.split('-')[lengthOfDate];

    const startDate = format(
      parse(`${startMonth}-01-${startYear}`, 'MM-dd-yyyy', new Date()),
      'MMM yyyy'
    );
    const endDate = format(
      parse(`${endMonth}-01-${endYear}`, 'MM-dd-yyyy', new Date()),
      'MMM yyyy'
    );

    return `${startDate} - ${endDate}`;
  };

  const handleUpdateRemark = async () => {
    const payload = {
      pan: panNumber,
      email,
      exportId: exportFileId,
      remark: remarkText,
    };
    const response = await updateRemark(payload);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      handleGetExportData(); // Calling function to get the updated data
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
      setIsOpenModal(false);
    }
  };

  useEffect(() => {
    handleGetExportData();
  }, [handleGetExportData]);

  return (
    <>
      <Helmet title={HelmetTitle.DATA_EXTRACTOR} />
      <div className='export-history-container'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          leftArrowRoute='#'
          leftArrowText='Export History'
          isNavigate
        />
        <BusinessHeader />
        <EximPaper>
          <div className='export-history-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleShowEntries={handleShowEntries}
              handleSearchQuery={handleSearchQuery}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={EXPORT_HISTORY_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='export-table'>
              <TableHeader
                mainHeader={tableHeader}
                handleSortBy={handleSortBy}
              />
              {exportRecords.length > 0 ? (
                <TableBody className='export-tbody'>
                  {exportRecords?.map((item, index) => (
                    <TableRow key={`exportHistory${index + 1}`}>
                      <TableCell>
                        {formatDateWithTime(item['export-time'])}
                      </TableCell>
                      <TableCell>
                        {item['export-period'] &&
                          getExportPeriod(item['export-period'])}
                      </TableCell>
                      <TableCell>{item['file-count']}</TableCell>
                      <TableCell>{item['exported-by']}</TableCell>
                      <TableCell className='remark-text-td'>
                        <EllipsisChecker
                          text={item.remark}
                          handleViewMore={() => {
                            setIsOpenTextModal(true);
                            setRemarkText(item.remark);
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <TableActions
                          isDownloadIcon
                          isEditIcon
                          downloadToolTipText='Download'
                          editToolTipText='Edit Remark'
                          handleDownload={() =>
                            handleDownload(item['export-id'])
                          }
                          handleEdit={() => {
                            setIsOpenModal(true);
                            setExportFileId(item['export-id']);
                            setRemarkText(item.remark);
                            setIsErrorMsg(false); // Reset the value
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              ) : (
                <EmptyTable colSpan={tableHeader.length} />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={exportRecords as []}
              renderData={exportRecords as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>
      </div>

      {/* Update Remark Text Modal */}
      <div className='update-remark-text-modal'>
        <EximModal
          isOpen={isOpenModal}
          onClose={() => setIsOpenModal(false)}
          onOutSideClickClose={() => setIsOpenModal(false)}
          content={
            <div className='remark-text-area'>
              <textarea
                name='remark'
                placeholder='Enter Remark'
                value={remarkText}
                onChange={handleChangeRemark}
                rows={5}
                maxLength={251}
              />
              {isErrorMsg && (
                <p className='err-msg'>
                  Remark can not be more than 250 character
                </p>
              )}
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenModal(false)}>
                  Cancel
                </EximButton>
                <EximButton
                  size='small'
                  onClick={handleUpdateRemark}
                  disabled={isErrorMsg}>
                  Update
                </EximButton>
              </span>
            </div>
          }
          footer={false}
          header={
            <EximTypography
              classNames='remark-text-modal-title'
              fontWeight='bold'>
              Add/Edit remark for current export
            </EximTypography>
          }
          closeIcon={null}
        />
      </div>

      {/* View Remark Text Modal */}
      <div className='view-remark-text-modal'>
        <EximModal
          isOpen={isOpenTextModal}
          onClose={() => setIsOpenTextModal(false)}
          onOutSideClickClose={() => setIsOpenTextModal(false)}
          content={<p>{remarkText}</p>}
          footer={false}
          header={
            <EximTypography fontWeight='bold'>Remark Text</EximTypography>
          }
          closeIcon={<CloseIcon width={15} height={15} />}
        />
      </div>
    </>
  );
}

export default ExportHistory;
