@import '@utils//main.scss';

.export-history-container {
  @include padding(0 20px);
  .business-details-card {
    margin-bottom: 20px;
  }
  .paper-wrapper-rounded {
    border: none;
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .export-history-table-container {
    @include padding(24px 26px);
    @include margin-bottom(32px);

    .export-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .export-tbody {
        .remark-text-td {
          .ellipsis-text {
            width: 32px;
          }
        }
      }
    }
  }
}

// Update Remark Modal Style
.export-history-container + .update-remark-text-modal {
  letter-spacing: 0.2px;
  .modal-title {
    @include flex-item(_, center, center, _, 2px);
    font-size: $font-size-sm;
    span {
      font-weight: normal;
      font-style: italic;
    }
  }
  .modal-body {
    width: 436px;
    .modal-content {
      padding-top: 0;
    }
    .remark-text-area {
      position: relative;
      width: 100%;
      textarea {
        width: 100%;
        outline: none;
        border: 1px solid $input-border;
        border-radius: 5px;
        @include padding(7px 10px);
        resize: none;
      }
      .btn-container {
        @include flex-item(_, flex-end, center, _, 16px);
        @include margin-top(20px);
        .button-wrapper {
          min-width: 100px;
          .base-btn {
            height: 32px;
            font-size: $font-size-sm;
            @include padding(7px 16px);
          }
        }
      }
      .err-msg {
        position: absolute;
        bottom: 40px;
        font-size: $font-size-xsm;
        color: $error;
      }
    }
  }
}

// View Remark Modal Style
.view-remark-text-modal {
  .modal {
    letter-spacing: 0.2px;
    .modal-title {
      @include flex-item(_, center, center, _, 2px);
      font-size: $font-size-sm;
      span {
        font-weight: normal;
      }
    }
    .modal-body {
      width: 445px;
      .modal-content {
        min-height: 0;
        @include padding-top(0);
        align-items: baseline;
        height: max-content;
        p {
          width: 400px;
          overflow-wrap: break-word;
        }
      }
    }
  }
}
