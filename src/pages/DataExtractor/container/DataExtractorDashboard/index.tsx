import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {HelmetTitle, Path} from '@common/constants';
import UploadBlock from '@pages/DataExtractor/components/UploadBlock';

import './index.scss';

function DataExtractorUpload() {
  return (
    <>
      <Helmet title={HelmetTitle.DATA_EXTRACTOR} />
      <div className='dashboard-wrapper'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          leftArrowRoute={Path.DASHBOARD}
          leftArrowText={HelmetTitle.DATA_EXTRACTOR}
        />
        <BusinessHeader />
        <UploadBlock />
        {/* filing head and cabinet here */}
      </div>
    </>
  );
}

export default DataExtractorUpload;
