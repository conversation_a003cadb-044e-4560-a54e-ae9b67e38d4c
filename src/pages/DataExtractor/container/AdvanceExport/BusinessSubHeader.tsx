import {
  DATA_EXTRACTOR_FILE_TYPE,
  EximHeroDate,
  FileType,
} from '@common/constants';
import {
  formatAmount,
  getCurrentMonthAndYear,
  getLastMonthAndYear,
  getWordFromUrl,
} from '@common/helpers';
import {getProcessedFiles} from '@pages/DataExtractor/api';
import ExportAs from '@pages/DataExtractor/components/ExportAs';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {memo, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router';

import './index.scss';

interface IBusinessSubHeaderProps {
  isExporting: boolean;
  selectedFileCount: string | number;
  handleApply: () => void;
  onAdvanceExport: (type: string) => Promise<void>;
}

function BusinessSubHeader({
  selectedFileCount,
  onAdvanceExport,
  handleApply,
  isExporting,
}: IBusinessSubHeaderProps) {
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1)?.toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {
      panNumber,
      advanceExportPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [processingFileCount, setProcessingFileCount] = useState(0);

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    const period = {
      startPeriod: startDate,
      endPeriod: endDate,
    };
    // setting the selected date while onChange
    dispatch(dataExtractorActions.setAdvanceExportPeriod(period));
  };

  const handleSyncFiles = useCallback(async () => {
    const payload = {
      startPeriod: startPeriod || getLastMonthAndYear(),
      endPeriod: endPeriod || getCurrentMonthAndYear(),
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      searchKey: '',
      searchValue: '',
      sortBy: '',
      sortingOrder: undefined,
    };
    const {data} = await getProcessedFiles(payload, 1, 5);
    setProcessingFileCount(data['in-progress-files']);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [email, panNumber, fileType]);

  useEffect(() => {
    handleSyncFiles();
  }, [handleSyncFiles]);

  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='sub-header-wrapper'>
          {!(processingFileCount === 0) && (
            <div className='processing-wrapper'>
              <EximTypography variant='h4'>
                Document Under Process:- &nbsp;
                <strong>{formatAmount(processingFileCount)}</strong>
              </EximTypography>
              <span
                onClick={handleSyncFiles}
                role='presentation'
                className='refresh-icon'>
                <SolidSync />
              </span>
            </div>
          )}
          <div className='btn-container'>
            <EximTypography>
              {`Select ${
                fileType === 'SB' ? 'Shipping Bill' : 'Bill Of Entry'
              } Period`}
            </EximTypography>
            <EximMonthRangePicker
              id='advanceExportDate'
              minDate={EximHeroDate.MIN_MONTH}
              onSelect={handleSelectPeriod}
              defaultStartDate={startPeriod.split('-').join('/')}
              defaultEndDate={endPeriod.split('-').join('/')}
            />
            <EximButton
              size='small'
              color='secondary'
              disabled={!startPeriod || !endPeriod}
              dataTestId='advance-export'
              onClick={handleApply}>
              Apply
            </EximButton>
            <ExportAs
              fileCount={Number(selectedFileCount)}
              handleExportType={onAdvanceExport}
              isDropdown={fileType === 'BOE'}
              isExporting={isExporting}
              isDisabled={selectedFileCount === 0}
            />
          </div>
        </div>
      </EximPaper>
    </div>
  );
}

export default memo(BusinessSubHeader);
