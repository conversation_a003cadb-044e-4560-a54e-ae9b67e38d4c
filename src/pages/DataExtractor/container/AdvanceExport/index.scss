@import '@utils/main.scss';

.advance-export-container {
  @include padding(0 20px);
  .business-details-card {
    .paper-wrapper-rounded {
      .business-header-actions {
        .button-wrapper {
          .base-btn {
            @include padding(6px 24px 8px 22px);
            font-size: $font-size-sm;
          }
        }
      }
    }
  }
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .subscription-header-left {
    color: $text-color;
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .sub-header-wrapper {
      @include flex-item(_, space-between, center);
      @include padding(10px 16px);
      .processing-wrapper {
        @include flex-item(_, space-between, flex-start);
        background-color: $secondary-background;
        @include padding(10px 15px 8px);
        @include rfs(5px, border-radius);
        .refresh-icon {
          @include margin-left(15px);
          :hover {
            cursor: pointer;
          }
        }
      }
      .btn-container {
        @include flex-item(_, flex-end, center, _, 20px);
        @include margin-left('auto');
        .base-date-picker {
          width: auto;
        }
        .base-btn {
          font-size: $font-size-sm;
          height: 32px;
          min-width: 130px;
        }
      }
    }
  }

  .process-documents-container {
    .process-documents-table {
      @include margin-bottom(18px);
    }
  }
}
// Remark Modal Style
.advance-export-container + .remark-text-modal {
  letter-spacing: 0.2px;
  .modal-title {
    @include flex-item(_, center, center, _, 2px);
    font-size: $font-size-sm;
    span {
      font-weight: normal;
      font-style: italic;
    }
  }
  .modal-body {
    width: 436px;
    .modal-content {
      padding-top: 0;
    }
    .remark-text-area {
      width: 100%;
      textarea {
        width: 100%;
        outline: none;
        border: 1px solid $input-border;
        border-radius: 5px;
        @include padding(7px 10px);
        resize: none;
      }
      .err-msg {
        position: absolute;
        bottom: 64px;
        font-size: $font-size-xsm;
        color: $error;
      }
      .btn-container {
        @include flex-item(_, flex-end, center, _, 16px);
        @include margin-top(20px);
        .button-wrapper {
          min-width: 100px;
          .base-btn {
            height: 32px;
            font-size: $font-size-sm;
            @include padding(7px 16px);
          }
        }
      }
    }
  }
}
