import AllFileSelectionStripe from '@common/components/AllFileSelectionStripe';
import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Stripe from '@common/components/Stripe';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import Helmet from '@common/components/utils/Helmet';
import {
  AlertStatus,
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  HelmetTitle,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {
  downloadFile,
  formatSelectedPeriod,
  getCurrentMonthAndYear,
  getLastMonthAndYear,
} from '@common/helpers';
import {
  ICustomAxiosResp,
  IExportHistoryData,
  IProcessedFile,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {
  exportReports,
  getExportData,
  getProcessedFiles,
  reportsGenerate,
} from '@pages/DataExtractor/api';
import ProcessedDocumentsTable from '@pages/DataExtractor/components/ProcessedDocumentsTable';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import {PROCESS_DOCUMENTS_TABLE_SEARCH_DROPDOWN} from '@pages/DataExtractor/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {ChangeEvent, useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function AdvanceExport() {
  const navigate = useNavigate();
  const {fileType} = useParams();
  const fileTypeCaps = fileType?.toUpperCase() as FileType;

  const tableHeadSearchDropdown = useMemo(
    () => PROCESS_DOCUMENTS_TABLE_SEARCH_DROPDOWN(fileTypeCaps as FileType),
    [fileTypeCaps]
  );

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {
      panNumber,
      isApplyClickedOnAdvanceExport,
      advanceExportPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [isOpenModal, setIsOpenModal] = useState(false);
  const [remarkText, setRemarkText] = useState('');
  const [isErrorMsg, setIsErrorMsg] = useState(false);
  const [exportFileType, setExportFileType] = useState('');
  const [isInitialRender, setIsInitialRender] = useState(true);
  const [exportingDetails, setExportingDetails] =
    useState<IExportHistoryData>();

  const [processedFiles, setProcessedFiles] = useState<IProcessedFile[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedFileCount, setSelectedFileCount] = useState(0);
  const [isSelectedAll, setIsSelectedAll] = useState(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleChangeRemark = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const {value} = event.target;
    setRemarkText(value);
    if (value?.length > 250) {
      setIsErrorMsg(true);
    } else {
      setIsErrorMsg(false);
    }
  };

  const handleGetProcessedFiles = useCallback(async () => {
    dispatch(dataExtractorActions.setIsApplyClickedOnAdvanceExport(true));
    const payload = {
      startPeriod: startPeriod || getLastMonthAndYear(),
      endPeriod: endPeriod || getCurrentMonthAndYear(),
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps as FileType],
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getProcessedFiles(payload, page, +showEntries);
    const newData = data?.['file-details']
      ? data['file-details']?.map((value: IProcessedFile) => ({
          ...value,
          isSelected: true,
        }))
      : [];
    setProcessedFiles(newData);
    setIsSelectedAll(newData.length > 0); // Reset the value on API call
    setTotalRecords(data?.['total-records']);
    setSelectedFileCount(
      newData?.filter((el: IProcessedFile) => el.isSelected).length
    );
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    panNumber,
    email,
    page,
    showEntries,
    fileTypeCaps,
    startPeriod,
    endPeriod,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleDownloadFile = async () => {
    const payload = {
      exportId: exportingDetails?.['export-id'],
      email,
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps],
    };
    const {data} = await exportReports(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const handleRefresh = async () => {
    const payload = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps],
      email,
    };
    const {data} = await getExportData(payload, page, +showEntries);
    setExportingDetails(data?.records[0]);
    if (data?.records[0].status === 'Completed') {
      setIsInitialRender(false);
    }
  };

  const handleSaveAndExport = async () => {
    const fileId: string[] = processedFiles
      .filter((item) => item.isSelected)
      .map((item) => item['file-id']);

    const payload = {
      startPeriod,
      endPeriod,
      pan: panNumber,
      email,
      fileId,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileTypeCaps],
      exportFormat: exportFileType,
      remark: remarkText,
    };
    const isAllRecordSelected = selectedFileCount === totalRecords;

    const response = (await reportsGenerate(
      payload,
      isAllRecordSelected
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      // INFO: Initial call to get the current status
      handleRefresh();
    }
    setIsOpenModal(false);
  };

  const toggleSelectItem = (id: string) => {
    const index = processedFiles.findIndex(
      (value: IProcessedFile) => value['file-id'] === id
    );
    processedFiles[index].isSelected = !processedFiles[index].isSelected;
    const totalChecks = processedFiles.reduce(
      (acc, value) => acc + Number(value.isSelected),
      0
    );
    setIsSelectedAll(totalChecks === processedFiles.length);
    setProcessedFiles(processedFiles);
    setSelectedFileCount(totalChecks);
  };
  const handleHeaderToggle = () => {
    setProcessedFiles(
      processedFiles.map((value: IProcessedFile) => ({
        ...value,
        isSelected: !isSelectedAll,
      }))
    );
    setIsSelectedAll(!isSelectedAll);
    setSelectedFileCount(isSelectedAll ? 0 : processedFiles.length);
  };
  const handleExportHistory = () => {
    navigate(
      `${Path.DATA_EXTRACTOR}${Path.EXPORT_HISTORY}/${fileType?.toLowerCase()}`
    );
  };

  // TODO: Need to integrate the API once available
  // INFO: Below function to select all the records that present in backend
  const handleToggleSelectAll = (type: 'CLEAR_ALL' | 'SELECT_ALL') => {
    if (type === 'SELECT_ALL') {
      setProcessedFiles(
        processedFiles.map((value: IProcessedFile) => ({
          ...value,
          isSelected: true,
        }))
      );
      setIsSelectedAll(true);
      setSelectedFileCount(totalRecords);
    } else if (type === 'CLEAR_ALL') {
      setProcessedFiles(
        processedFiles.map((value: IProcessedFile) => ({
          ...value,
          isSelected: false,
        }))
      );
      setIsSelectedAll(false);
      setSelectedFileCount(0);
    }
  };

  const handleAdvanceExport = async (exportType: string) => {
    setExportFileType(exportType);
    setIsOpenModal(true);
  };

  useEffect(() => {
    if (isApplyClickedOnAdvanceExport) {
      handleGetProcessedFiles();
    }
  }, [handleGetProcessedFiles, isApplyClickedOnAdvanceExport]);

  return (
    <>
      <Helmet title={HelmetTitle.DATA_EXTRACTOR} />
      <div className='advance-export-container'>
        <NavigationSubHeader
          hasTitle
          leftArrowRoute='#'
          hasLeftArrow
          leftArrowText={`
            Advance Export - ${
              fileTypeCaps === 'SB' ? 'Shipping Bill' : 'Bill of Entry'
            }`}
          isNavigate
        />
        <BusinessHeader>
          <EximButton
            size='small'
            color='tertiary'
            dataTestId='export-history'
            onClick={handleExportHistory}>
            Export History
          </EximButton>
        </BusinessHeader>
        <BusinessSubHeader
          selectedFileCount={selectedFileCount}
          handleApply={handleGetProcessedFiles}
          onAdvanceExport={handleAdvanceExport}
          isExporting={exportingDetails?.status === 'In Progress'}
        />
        {exportingDetails?.status === 'Completed' && !isInitialRender ? (
          <Stripe
            content={`Your data is ready to download | ${
              exportingDetails?.['file-count']
            } document/s for ${
              fileTypeCaps === 'SB' ? 'shipping bill' : 'bill of entry'
            } period from ${formatSelectedPeriod(
              startPeriod
            )} to ${formatSelectedPeriod(endPeriod)}`}
            variant='success'
            isBtn
            onBtnClick={handleDownloadFile}
          />
        ) : null}
        {exportingDetails?.status === 'In Progress' ? (
          <Stripe
            content={
              <span>
                {`Data from ${
                  exportingDetails?.['file-count']
                } document/s for the ${
                  fileTypeCaps === 'SB' ? 'shipping bill' : 'bill of entry'
                } period from ${formatSelectedPeriod(
                  startPeriod
                )} to ${formatSelectedPeriod(endPeriod)} is `}
                <strong>Exporting. </strong>
                <span>Please wait.</span>
              </span>
            }
            variant='tertiary'
            isBtn
            btnText='Refresh'
            onBtnClick={handleRefresh}
          />
        ) : null}
        {exportingDetails?.status === 'Failed' ? (
          <Stripe
            content={
              <span>
                {`Exporting `}
                <strong>Failed. </strong>
                <span>Please try again.</span>
              </span>
            }
            variant='primary'
          />
        ) : null}
        <EximPaper>
          <div className='process-documents-container'>
            {processedFiles.length > 0 && totalRecords > +showEntries ? (
              <AllFileSelectionStripe
                selectedFileCount={selectedFileCount}
                totalRecords={totalRecords}
                handleToggleSelectAll={handleToggleSelectAll}
              />
            ) : null}
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={tableHeadSearchDropdown}
              />
            </TableSearchFilter>
            <ProcessedDocumentsTable
              selectItem={(id) => toggleSelectItem(id)}
              isSelectedAll={isSelectedAll}
              onSelectAll={handleHeaderToggle}
              processedFiles={processedFiles}
              handleSortBy={handleSortBy}
            />
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={processedFiles as []}
              renderData={processedFiles as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>
      </div>

      {/* Add Remark Text Modal */}
      <div className='remark-text-modal'>
        <EximModal
          isOpen={isOpenModal}
          onClose={() => setIsOpenModal(false)}
          onOutSideClickClose={() => setIsOpenModal(false)}
          content={
            <div className='remark-text-area'>
              <textarea
                name='remark'
                placeholder='Enter Remark'
                onChange={handleChangeRemark}
                maxLength={251}
                rows={5}
              />
              {isErrorMsg && (
                <p className='err-msg'>
                  Remark can not be more than 250 character
                </p>
              )}
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenModal(false)}>
                  Cancel
                </EximButton>
                <EximButton
                  size='small'
                  onClick={handleSaveAndExport}
                  disabled={isErrorMsg}>
                  Save & Continue
                </EximButton>
              </span>
            </div>
          }
          header={
            <>
              <EximTypography
                classNames='remark-text-modal-title'
                fontWeight='bold'>
                Would you like to add a remark for current export?
              </EximTypography>
              <span>(Optional)</span>
            </>
          }
          closeIcon={null}
          footer={null}
        />
      </div>
    </>
  );
}

export default AdvanceExport;
