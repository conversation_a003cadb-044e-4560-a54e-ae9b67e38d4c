import * as constants from '@common/constants';

export const UPLOAD_LOGS_TABLE_HEADER = [
  {
    title: 'Last Upload Date & Time',
    width: 'auto',
    sortingKey: 'last-updated-date',
  },
  {title: 'PDF Upload Count', width: 'auto'},
  {title: 'Upload By', width: 'auto', sortingKey: 'last-updated-by'},
  {title: 'Status', width: 'auto', sortingKey: 'processing-status'},
  {title: 'Action', width: 'auto'},
];

export const UPLOAD_LOGS_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'last-updated-date', label: 'Last Upload Date and Time'},
  {id: '2', value: 'last-updated-by', label: 'Upload By'},
  {id: '3', value: 'processing-status', label: 'Status'},
];

export const UPLOAD_DETAILS_TABLE_HEADER = [
  {title: 'File Name', width: 'auto', sortingKey: 'file-name'},
  {title: 'Action', width: '15%'},
];

export const UPLOAD_DETAILS_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'file-name', label: 'File Name'},
];

export const EXPORT_HISTORY_TABLE_HEADER = (type: 'SB' | 'BOE') => {
  const text = type === 'SB' ? 'Shipping Bill' : 'Bill of Entry';
  return [
    {title: 'Export Date and Time', with: '20%', sortingKey: 'export-time'},
    {title: `${text} Export Period`, with: '15%'},
    {title: 'Export File Quantity', with: '15%'},
    {title: 'Exported by', with: '15%', sortingKey: 'exported-by'},
    {title: 'Remark', with: '15%', sortingKey: 'remark'},
    {title: 'Action', with: '15%'},
  ];
};

export const EXPORT_HISTORY_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'export-time', label: 'Export Date and Time'},
  {id: '2', value: 'exported-by', label: 'Exported By'},
  {id: '3', value: 'remark', label: 'Remark'},
];

export const PROCESS_DOCUMENTS_TABLE_HEADER = (
  fileType: constants.FileType
) => {
  if (fileType === 'SB') {
    return [
      {title: 'checkbox', width: '5%'},
      {title: 'File Name', width: '25%'},
      {title: `${fileType} No.`, width: '12%', sortingKey: 'sb-no'},
      {title: `${fileType} Date`, width: '13%', sortingKey: 'sb-date'},
      {title: 'Upload Date & Time', width: '13%'},
      {title: 'Processed Date & Time', width: '13%'},
      {title: 'Upload By', width: '16%'},
    ];
  }
  return [
    {title: 'checkbox', width: '5%'},
    {title: 'File Name', width: '25%'},
    {title: 'Type', width: '10%', sortingKey: 'be-type'},
    {title: `${fileType} No.`, width: '12%', sortingKey: 'boe-no'},
    {title: `${fileType} Date`, width: '13%', sortingKey: 'boe-date'},
    {title: 'Upload Date & Time', width: '13%'},
    {title: 'Processed Date & Time', width: '13%'},
    {title: 'Upload By', width: '16%'},
  ];
};

export const PROCESS_DOCUMENTS_TABLE_SEARCH_DROPDOWN = (
  fileType: constants.FileType
) => {
  if (fileType === 'SB') {
    return [
      {id: '1', value: 'sb-no', label: 'SB No.'},
      {id: '2', value: 'sb-date', label: 'SB Date'},
    ];
  }
  return [
    {id: '1', value: 'boe-no', label: 'BOE No.'},
    {id: '2', value: 'boe-date', label: 'BOE Date'},
    {id: '3', value: 'be-type', label: 'Type'},
  ];
};
