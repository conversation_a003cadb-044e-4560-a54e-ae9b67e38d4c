import {Api, ApiAction} from '@common/constants/index';
import {removeUndefinedKeys} from '@common/helpers';
import {_delete, get, post, put} from '@core/api/axios';
import {AxiosResponse} from 'axios';

const {
  PDF_READER,
  FILES,
  PDF,
  UPLOAD,
  HISTORY,
  PROCESSING_DETAILS,
  DETAILS,
  REPORTS,
  GENERATE,
  EXPORT,
  EDIT_REMARK,
  PROCESSED_FILES,
} = Api;
const {
  FILE_UPLOAD_HISTORY,
  PDF_FILE_UPLOAD,
  GET_PDF_FILE,
  FILE_PROCESSING_DTLS,
  FILE_UPLOAD_DETAILS,
  DELETE_PDF_FILE,
  EXPORT_REPORT,
  GET_PROCESSED_FILES,
  ADVANCE_EXPORT,
  GET_FILE_EXPORT_HISTORY,
} = ApiAction;

interface IApiData {
  pan: string;
  email: string;
  fileType?: string;
  txnId?: string;
  failedFiles?: boolean;
  fileId?: string | string[];
  startPeriod?: string;
  endPeriod?: string;
  reportGenId?: string | null;
  exportId?: string;
  remark?: string;
  exportFormat?: string;
  searchKey?: string;
  searchValue?: string;
  sortBy?: string;
  sortingOrder?: 1 | -1;
}

export const getUploadedLogs = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${PDF_READER}${FILES}${UPLOAD}${HISTORY}?page-no=${page}&limit=${limit}&action=${FILE_UPLOAD_HISTORY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const uploadFiles = async (data: IApiData, files: File[]) => {
  const attachedFiles = new FormData();
  files.forEach((file: File) => {
    attachedFiles.append(`attachedFiles`, file);
  });
  const response = await post(
    `${PDF_READER}${FILES}${UPLOAD}${PDF}?action=${PDF_FILE_UPLOAD}`,
    attachedFiles,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getProcessingDetails = async (data: IApiData) => {
  const response = await get(
    `${PDF_READER}${FILES}${PROCESSING_DETAILS}?action=${FILE_PROCESSING_DTLS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const getUploadedDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${PDF_READER}${FILES}${UPLOAD}${DETAILS}?page-no=${page}&limit=${limit}&failed-files=${data.failedFiles}&action=${FILE_UPLOAD_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'txn-id': data.txnId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getPdfData = async (data: IApiData) => {
  const response = await get(
    `${PDF_READER}${FILES}${PDF}?action=${GET_PDF_FILE}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-id': data.fileId,
        'file-type': data.fileType,
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const deletePdfFile = async (data: IApiData) => {
  const response = await _delete(
    `${PDF_READER}${FILES}${PDF}?action=${DELETE_PDF_FILE}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-id': data.fileId,
        'file-type': data.fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const deleteAllFailedPdfFiles = async (data: IApiData) => {
  const response = await _delete(
    `${PDF_READER}${FILES}${PDF}?action=${DELETE_PDF_FILE}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'txn-id': data.txnId,
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getProcessedFiles = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${PDF_READER}${FILES}${PROCESSED_FILES}${DETAILS}?page-no=${page}&limit=${limit}&action=${GET_PROCESSED_FILES}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'start-period': data.startPeriod,
        'end-period': data.endPeriod,
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder || -1,
      },
    }
  );
  return response as AxiosResponse;
};

export const exportProcessedFiles = async (data: IApiData) => {
  const payload = {
    description: data.remark,
    'file-id-list': data.fileId,
  };
  const response = await put(
    `${PDF_READER}${REPORTS}${EXPORT}?action=${ADVANCE_EXPORT}`,
    payload,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'start-period': data.startPeriod || '',
        'end-period': data.endPeriod || '',
        'report-type': data.fileType || '',
        'export-format': data.exportFormat || '',
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const reportsGenerate = async (
  data: IApiData,
  isAllRecordSelected: boolean
) => {
  const payload = {
    description: data.remark,
    'file-id-list': data.fileId,
  };
  // INFO: if user want to export all the records, then no need to send selected file ids
  if (isAllRecordSelected) delete payload['file-id-list'];

  const response = await put(
    `${PDF_READER}${REPORTS}${GENERATE}?action=${EXPORT_REPORT}`,
    payload,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'start-period': data.startPeriod || '',
        'end-period': data.endPeriod || '',
        'report-type': data.fileType || '',
        'export-format': data.exportFormat || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getExportData = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'report-type': data.fileType,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${PDF_READER}${REPORTS}${EXPORT}${HISTORY}?page-no=${page}&limit=${limit}&action=${GET_FILE_EXPORT_HISTORY}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};
export const exportReports = async (data: IApiData) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'report-type': data.fileType as string,
    'report-generation-id': data.exportId as string,
    'export-format': data.exportFormat as string,
    'txn-id': data.txnId as string,
    'should-return-file': false,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await put(
    `${PDF_READER}${REPORTS}${EXPORT}?action=${EXPORT_REPORT}`,
    {},
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const updateRemark = async (data: IApiData) => {
  const payload = {
    description: data.remark,
    'file-id-list': [],
  };
  const response = await put(
    `${PDF_READER}${REPORTS}${EDIT_REMARK}?action=${GET_FILE_EXPORT_HISTORY}`,
    payload,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'report-generation-id': data.exportId as string,
      },
    }
  );
  return response as AxiosResponse;
};
