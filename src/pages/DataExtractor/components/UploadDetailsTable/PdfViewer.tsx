import {base64To<PERSON>rrayBuffer} from '@common/helpers';
import {useEffect, useState} from 'react';

export default function PdfViewer({pdfBase64Data}: {pdfBase64Data: string}) {
  const [pdfBlobUrl, setPdfBlobUrl] = useState('');

  useEffect(() => {
    const decodedArrayBuffer = base64ToArrayBuffer(pdfBase64Data);

    // Create a Blob from ArrayBuffer
    const blob = new Blob([decodedArrayBuffer], {
      type: 'application/pdf',
    });

    // Create Blob URL
    const blobUrl = URL.createObjectURL(blob);
    setPdfBlobUrl(blobUrl);

    // Clean up URL when the component unmounts
    return () => URL.revokeObjectURL(blobUrl);
  }, [pdfBase64Data]);

  return (
    <iframe
      src={pdfBlobUrl}
      width='100%'
      height='90%'
      title='PDF Preview'
      style={{border: 'none'}}
    />
  );
}
