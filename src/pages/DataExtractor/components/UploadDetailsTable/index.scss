@import '@utils/main.scss';

.upload-details-container {
  .upload-details-table-container {
    @include padding(20px);
    @include margin(25px auto 32px);
    .upload-details-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
    }

    .table-search-container,
    .table-footer {
      @include padding(10px auto);
    }
  }

  .pdf-view-modal {
    .modal {
      .modal-body {
        border: none;
        height: 90vh;
        width: 65%;
        .modal-header {
          @include padding(11px 30px);
          background: $table-head-primary;
          color: $white;
          .modal-title {
            font-size: $font-size-13;
            letter-spacing: 0.2px;
          }
        }
        .modal-content {
          min-height: none;
          height: 100%;
          @include padding-top(0);
        }
      }
    }
  }
}
