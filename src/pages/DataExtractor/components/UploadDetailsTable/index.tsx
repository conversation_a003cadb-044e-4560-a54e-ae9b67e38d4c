import DeleteConfirmationModal from '@common/components/DeleteConfirmationModal';
import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableHeader from '@common/components/TableHeader';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile, getWordFromUrl} from '@common/helpers';
import {IUploadFileDetails} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {deletePdfFile, getPdfData} from '@pages/DataExtractor/api';
import {UPLOAD_DETAILS_TABLE_HEADER} from '@pages/DataExtractor/utils';
import EximModal from '@shared/components/EximModal';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {memo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router';

import PdfViewer from './PdfViewer';
import './index.scss';

interface IUploadDetailsTableProps {
  uploadDetails: IUploadFileDetails[];
  getUploadDetails: () => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function UploadDetailsTable({
  uploadDetails,
  getUploadDetails,
  handleSortBy,
}: IUploadDetailsTableProps) {
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 2).toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const [isOpen, setIsOpen] = useState(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState(false);
  const [fileIdToDelete, setFileIdToDelete] = useState('');
  const [pdfPreviewData, setPreviewPdfData] = useState('');

  const handleViewFile = async (id: string) => {
    const payload = {
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      fileId: id,
    };

    const {data} = await getPdfData(payload);
    setPreviewPdfData(data['file-data']);
    setIsOpen(true);
  };

  const handleDownloadPdf = async (id: string) => {
    const payload = {
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      fileId: id,
    };

    const {data} = await getPdfData(payload);
    downloadFile(data['file-data'], data['file-name'], SupportedFileTypes.PDF);
  };

  const handleDeleteFile = async () => {
    const payload = {
      pan: panNumber,
      email,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      fileId: fileIdToDelete,
    };
    await deletePdfFile(payload);
    setIsOpenDeleteModal(false);
    setFileIdToDelete('');
    getUploadDetails();
  };

  return (
    <>
      <table className='upload-details-table'>
        <TableHeader
          mainHeader={UPLOAD_DETAILS_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {uploadDetails?.length > 0 ? (
          <TableBody className='upload-details-tbody'>
            {uploadDetails?.map((item: IUploadFileDetails, index: number) => (
              <TableRow key={`${item['file-id']}${index + 1}`}>
                <TableCell>{item['file-name']}</TableCell>
                <TableCell>
                  <TableActions
                    isViewIcon
                    isDownloadIcon
                    isDeleteIcon
                    viewToolTipText='View File'
                    downloadToolTipText='Download File'
                    deleteToolTipText='Delete File'
                    handleView={() => handleViewFile(item['file-id'])}
                    handleDownload={() => handleDownloadPdf(item['file-id'])}
                    handleDelete={() => {
                      setFileIdToDelete(item['file-id']);
                      setIsOpenDeleteModal(true);
                    }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={UPLOAD_DETAILS_TABLE_HEADER.length} />
        )}
      </table>
      {isOpen && (
        <div className='pdf-view-modal'>
          <EximModal
            isOpen={isOpen}
            header='Preview'
            closeIcon={<CloseIcon width={16} height={16} fill='#fff' />}
            content={
              <PdfViewer pdfBase64Data={pdfPreviewData} />
              // TODO: Keeping for reference
              // <iframe
              //   src={`data:application/pdf;base64,${pdfPreviewData}`}
              //   width='100%'
              //   height='90%'
              //   title='PDF Preview'
              //   style={{border: 'none'}}
              // />
            }
            isCloseIconVisible
            onClose={() => setIsOpen(false)}
            onOutSideClickClose={() => setIsOpen(false)}
            footer={null}
          />
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isOpenDeleteModal}
        onClose={() => {
          setIsOpenDeleteModal(false);
        }}
        handleConfirm={handleDeleteFile}
      />
    </>
  );
}

export default memo(UploadDetailsTable);
