@import '@utils/main.scss';

.upload-logs-container {
  @include padding(20px);
  @include margin(16px auto 32px);
  .typography-container {
    @include margin-bottom(36px);
    & > h4 {
      font-size: $font-size-lg;
    }
  }
  .upload-logs-table {
    @include margin-top(16px);
    width: 100%;
    border-spacing: 0;
    .upload-logs-tbody {
      .error {
        font-weight: $font-weight-semi-bold;
        color: $error;
      }
      .success {
        font-weight: $font-weight-semi-bold;
        color: $success;
      }
      .process {
        font-weight: $font-weight-semi-bold;
        color: $primary;
      }

      .upload-by {
        text-transform: capitalize;
      }

      // Style for children TableCell buttons
      .child-btn-container {
        display: flex;
        gap: 10px;
        .button-wrapper {
          .base-btn {
            height: 24px;
          }
        }
      }
    }
  }
  .table-search-container,
  .table-footer {
    @include padding(10px auto);
  }
}
