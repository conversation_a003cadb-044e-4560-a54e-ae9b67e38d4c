import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableHeader from '@common/components/TableHeader';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  FileType,
  Path,
  SupportedFileTypes,
  UploadLogsStatus,
} from '@common/constants';
import {
  calculatePercentage,
  downloadFile,
  formatDateWithTime,
  getObjKeyValue,
  getWordFromUrl,
} from '@common/helpers';
import {IUploadLogs} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {exportReports} from '@pages/DataExtractor/api';
import {UPLOAD_LOGS_TABLE_HEADER} from '@pages/DataExtractor/utils';
import {SolidSync} from '@shared/icons';
import {RootState} from '@store';
import {memo} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

import ExportAs from '../ExportAs';
import './index.scss';

const {IN_PROGRESS, FAILED, COMPLETED, DELETED} = UploadLogsStatus;

function StatusTd(obj: {[key: string]: number}, totalNumber: number) {
  const {key, value} = getObjKeyValue(obj);

  if (key === FAILED)
    return <span className='error'>{`${value} Failed `}</span>;
  if (key === IN_PROGRESS)
    return (
      <span className='process'>{`Processing (${calculatePercentage(
        totalNumber,
        value
      )})`}</span>
    );
  if (key === COMPLETED) return <span className='success'>Completed</span>;
  if (key === DELETED)
    return <span className='error'>{`${value} Deleted`}</span>;
}

interface IUploadAndProcessProps {
  uploadLogs: IUploadLogs[];
  getUploadLogsData: () => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function UploadLogsTable({
  uploadLogs,
  getUploadLogsData,
  handleSortBy,
}: IUploadAndProcessProps) {
  const navigate = useNavigate();
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1).toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const handleViewLog = (id: string) => {
    navigate(
      `${Path.DATA_EXTRACTOR}${
        Path.VIEW_UPLOAD_DETAILS
      }/${fileType.toLowerCase()}/${id}`
    );
  };

  const handleDownloadLog = async (exportType: string, id: string) => {
    const payload = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE[fileType as FileType],
      exportFormat: exportType,
      email,
      txnId: id,
    };
    const {data} = await exportReports(payload);
    downloadFile(data['file-data'], data['file-name'], SupportedFileTypes.ZIP);
  };

  return (
    <table className='upload-logs-table'>
      <TableHeader
        mainHeader={UPLOAD_LOGS_TABLE_HEADER}
        handleSortBy={handleSortBy}
      />
      {uploadLogs?.length > 0 ? (
        <TableBody className='upload-logs-tbody'>
          {uploadLogs?.map((item: IUploadLogs, index: number) => (
            <TableRow key={`${item['txn-id']}${index + 1}`}>
              <TableCell>
                {formatDateWithTime(item['last-updated-date'])}
              </TableCell>
              <TableCell>{item['total-file-count']}</TableCell>
              <TableCell className='upload-by'>
                {item['last-updated-by']}
              </TableCell>
              <TableCell>
                {StatusTd(item['processing-details'], item['total-file-count'])}
                {item?.['error-message'] && (
                  <span className='error'>
                    &nbsp;({item?.['error-message']})
                  </span>
                )}
              </TableCell>
              <TableCell>
                <TableActions
                  isViewIcon
                  viewToolTipText='View Details'
                  isViewIconDisabled={
                    getObjKeyValue(item['processing-details']).key ===
                    IN_PROGRESS
                  }
                  handleView={() => handleViewLog(item['txn-id'])}>
                  <span className='child-btn-container'>
                    {getObjKeyValue(item['processing-details']).key ===
                    IN_PROGRESS ? (
                      <span
                        role='presentation'
                        onClick={() => getUploadLogsData()}>
                        <SolidSync />
                      </span>
                    ) : null}
                    <ExportAs
                      handleExportType={(exportType: string) =>
                        handleDownloadLog(exportType, item['txn-id'])
                      }
                      isDropdown={fileType === 'BOE'}
                      isDisabled={
                        getObjKeyValue(item['processing-details']).key !==
                        COMPLETED
                      }
                    />
                  </span>
                </TableActions>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      ) : (
        <EmptyTable colSpan={UPLOAD_LOGS_TABLE_HEADER.length} />
      )}
    </table>
  );
}

export default memo(UploadLogsTable);
