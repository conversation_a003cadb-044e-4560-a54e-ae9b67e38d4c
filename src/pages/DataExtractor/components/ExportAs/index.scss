@import '@utils/main.scss';

.export-as-container {
  width: max-content;

  .dropdown-container {
    width: 100%;
    position: relative;

    .button-wrapper {
      .base-btn {
        .btn-children {
          gap: 10px;
        }
      }
    }

    .bottom-radius-none {
      border-radius: 5px 5px 0 0;
    }
    .select-btn-container {
      width: 100%;
      border-left: 1px solid $input-border;
      border-right: 1px solid $input-border;
      position: absolute;
      z-index: 1;
      background: $white;
      border-radius: 0 0 5px 5px;
      @include flex-item(column);
      span {
        width: 100%;
        @include padding(7px 0px 7px 20px);
        font-size: $font-size-sm;
        border-bottom: 1px solid $input-border;
        cursor: pointer;
        &:hover {
          background: $secondary;
        }
      }
    }
  }

  .base-btn {
    font-size: $font-size-sm;
    height: 32px;
  }
  svg {
    rect {
      display: none;
    }
  }
}
