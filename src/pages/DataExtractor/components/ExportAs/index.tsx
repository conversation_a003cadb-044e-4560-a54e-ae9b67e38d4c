import EximButton from '@shared/components/EximButton';
import {
  DownloadIcon,
  ProcessCircle,
  SolidDownAngle,
  SolidUpIcon,
} from '@shared/icons';
import {useEffect, useRef, useState} from 'react';

import './index.scss';

const EXPORT_TYPE_DROPDOWN = [
  {id: '1', value: 'DEFAULT', label: 'Default'},
  {id: '2', value: 'DUTY_DRAWBACK', label: 'Duty Drawback'},
  {id: '3', value: 'MOOWR', label: 'MOOWR'},
];

interface IDropDownExportProps {
  isDropdown: boolean;
  isDisabled: boolean;
  fileCount?: number;
  handleExportType: (exportType: string, id?: string) => void;
}

interface IExportAsProps extends IDropDownExportProps {
  isExporting?: boolean;
}

function DropDownExport({
  isDropdown,
  isDisabled,
  fileCount,
  handleExportType,
}: IDropDownExportProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return isDropdown ? (
    <div className='dropdown-container' ref={dropdownRef}>
      <EximButton
        size='small'
        className={`${isOpen ? 'bottom-radius-none' : ''}`}
        disabled={isDisabled}
        onClick={() => setIsOpen((prev) => !prev)}>
        <DownloadIcon fill='#fffff' />
        {`Export as ${fileCount === 0 ? '' : `(${fileCount})`}`}
        {isOpen ? (
          <SolidUpIcon width={10} height={7} fill='#fff' />
        ) : (
          <SolidDownAngle width={10} height={7} fill='#fff' />
        )}
      </EximButton>
      {isOpen ? (
        <div className='select-btn-container'>
          {EXPORT_TYPE_DROPDOWN.map((item) => (
            <span
              key={item.value}
              role='presentation'
              onClick={() => {
                handleExportType(item.value);
                setIsOpen(false);
              }}>
              {item.label}
            </span>
          ))}
        </div>
      ) : null}
    </div>
  ) : (
    <EximButton
      size='small'
      disabled={isDisabled}
      onClick={() => handleExportType('DEFAULT')}>
      <DownloadIcon fill='#fffff' />
      {`Export ${fileCount === 0 ? '' : `(${fileCount})`}`}
    </EximButton>
  );
}

function ExportAs({
  isExporting,
  isDropdown,
  isDisabled,
  fileCount,
  handleExportType,
}: IExportAsProps) {
  return (
    <div className='export-as-container'>
      {isExporting ? (
        <EximButton disabled>
          <ProcessCircle />
          Exporting
        </EximButton>
      ) : (
        <DropDownExport
          fileCount={fileCount}
          handleExportType={handleExportType}
          isDropdown={isDropdown}
          isDisabled={isDisabled}
        />
      )}
    </div>
  );
}

ExportAs.defaultProps = {
  isExporting: false,
  fileCount: 0,
};
DropDownExport.defaultProps = {
  fileCount: 0,
};

export default ExportAs;
