import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {FileType} from '@common/constants';
import {formatDateWithTime, getWordFromUrl} from '@common/helpers';
import {IProcessedFile} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {PROCESS_DOCUMENTS_TABLE_HEADER} from '@pages/DataExtractor/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import {memo, useMemo} from 'react';
import {useLocation} from 'react-router';

import './index.scss';

interface IProcessFilesTable {
  processedFiles: IProcessedFile[];
  isSelectedAll: boolean;
  selectItem: (id: string) => void;
  onSelectAll: () => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}
function ProcessedDocumentsTable({
  processedFiles,
  isSelectedAll,
  selectItem,
  onSelectAll,
  handleSortBy,
}: IProcessFilesTable) {
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1).toUpperCase();
  const tableHead = useMemo(
    () => PROCESS_DOCUMENTS_TABLE_HEADER(fileType as FileType),
    [fileType]
  );

  return (
    <table className='process-documents-table'>
      <TableHeader
        mainHeader={tableHead}
        checked={isSelectedAll}
        onChange={onSelectAll}
        handleSortBy={handleSortBy}
      />
      {processedFiles?.length > 0 ? (
        <TableBody className='process-documents-tbody'>
          {processedFiles?.map((item: IProcessedFile) => (
            <TableRow key={`${item['file-id']}`}>
              <TableCell className='checkbox-td'>
                <EximCheckbox
                  id={`${item['file-id']}`}
                  name='processRecord'
                  color='#2CB544'
                  size='medium'
                  checked={item.isSelected}
                  onChange={() => selectItem(item['file-id'])}
                />
              </TableCell>
              <TableCell>{item['file-name']}</TableCell>
              {fileType === 'BOE' ? (
                <TableCell>{item['be-type']}</TableCell>
              ) : null}
              <TableCell>
                {fileType === 'SB'
                  ? item['sb-no'] ?? '-'
                  : item['boe-no'] ?? '-'}
              </TableCell>
              <TableCell>
                {formatDateWithTime(
                  fileType === 'SB'
                    ? item['sb-date'] ?? undefined
                    : item['boe-date'] ?? undefined,
                  false
                )}
              </TableCell>
              <TableCell>{formatDateWithTime(item['upload-date'])}</TableCell>
              <TableCell>
                {formatDateWithTime(item['last-updated-date'])}
              </TableCell>
              <TableCell>{item['last-updated-by']}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      ) : (
        <EmptyTable colSpan={tableHead.length} />
      )}
    </table>
  );
}

export default memo(ProcessedDocumentsTable);
