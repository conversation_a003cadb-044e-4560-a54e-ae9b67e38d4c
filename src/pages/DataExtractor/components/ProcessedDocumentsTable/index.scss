@import '@utils/main.scss';

.process-documents-container {
  @include padding(24px 0);
  @include margin(16px auto 32px);
  .process-documents-table {
    @include padding(0 32px);
    @include margin-top(16px);
    width: 100%;
    border-spacing: 0;
    .process-documents-tbody {
      .error {
        font-weight: $font-weight-semi-bold;
        color: $error;
      }
      .success {
        font-weight: $font-weight-semi-bold;
        color: $success;
      }
      .process {
        font-weight: $font-weight-semi-bold;
        color: $primary;
      }
    }
  }
  .table-search-container,
  .table-footer {
    @include padding(0 32px);
  }
}
