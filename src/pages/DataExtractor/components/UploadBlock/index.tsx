import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {
  DATA_EXTRACTOR_FILE_TYPE,
  Path,
  UploadLogsStatus,
} from '@common/constants';
import {formatDateWithTime, getObjKeyValue} from '@common/helpers';
import {IUploadLogs} from '@common/interfaces';
import {getProcessingDetails} from '@pages/DataExtractor/api';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import EximAvatar from '@shared/components/EximAvatar';
import EximPaper from '@shared/components/EximPaper';
import {CheckIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const {FAILED, IN_PROGRESS, COMPLETED, DELETED} = UploadLogsStatus;

export default function UploadBlock() {
  const navigate = useNavigate();
  const [SBData, setSBData] = useState<IUploadLogs>();
  const [BOEData, setBOEData] = useState<IUploadLogs>();
  const {
    auth: {
      userData: {email},
    },
    dataExtractor: {panNumber},
  } = useSelector((state: RootState) => state);

  const handleGetProcessingDetails = useCallback(async () => {
    const data = {
      pan: panNumber,
      fileType: DATA_EXTRACTOR_FILE_TYPE.ALL,
      email,
    };
    const response = await getProcessingDetails(data);
    if (response.data.length) {
      if (response.data.length === 1) {
        if (response.data[0]['file-type'] === DATA_EXTRACTOR_FILE_TYPE.BOE) {
          setBOEData(response.data[0]);
        }
        if (response.data[0]['file-type'] === DATA_EXTRACTOR_FILE_TYPE.SB) {
          setSBData(response.data[0]);
        }
      }
      if (response.data.length === 2) {
        if (response.data[0]['file-type'] === DATA_EXTRACTOR_FILE_TYPE.BOE) {
          setBOEData(response.data[0]);
          setSBData(response.data[1]);
        } else if (
          response.data[0]['file-type'] === DATA_EXTRACTOR_FILE_TYPE.SB
        ) {
          setBOEData(response.data[1]);
          setSBData(response.data[0]);
        }
      }
    }
  }, [panNumber, email]);

  useEffect(() => {
    handleGetProcessingDetails();
  }, [handleGetProcessingDetails]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleUpload = (type: 'SB' | 'BOE') => {
    navigate(
      `${Path.DATA_EXTRACTOR}${Path.UPLOAD_PROCESS}/${type.toLowerCase()}`
    );
  };

  const handleAdvanceExport = (type: 'SB' | 'BOE') => {
    const period = {
      startPeriod: '',
      endPeriod: '',
    };
    // setting the default date
    dispatch(dataExtractorActions.setAdvanceExportPeriod(period));
    dispatch(dataExtractorActions.setIsApplyClickedOnAdvanceExport(false));
    navigate(
      `${Path.DATA_EXTRACTOR}${Path.ADVANCE_EXPORT}/${type.toLowerCase()}`
    );
  };

  const getShowStatus = (object: IUploadLogs | undefined) => {
    if (object === undefined) return null;
    const {key, value} = getObjKeyValue(object['processing-details']);
    if (key === IN_PROGRESS) return <span className='process'>Processing</span>;
    if (key === FAILED)
      return (
        <span className='error'>
          Failed
          <span className='error-count'>:</span>
          <span className='error-count error-count-space'>{value}</span>
        </span>
      );
    if (key === COMPLETED) return <span className='success'>Completed</span>;
    if (key === DELETED)
      return (
        <span className='error'>
          Deleted
          <span className='error-count'>:</span>
          <span className='error-count error-count-space'>{value}</span>
        </span>
      );
    return null;
  };

  return (
    <div className='upload-block'>
      <EximPaper>
        <FilingHead
          filingHead='Data Extractor (PDF to Excel)'
          onGuideClick={handleGuideClick}
          hasGuide
        />
        <div className='filing-step-container'>
          <FilingStep
            stepIcon={
              <span>
                {SBData &&
                getObjKeyValue(SBData['processing-details']).key ===
                  COMPLETED ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='1'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            statusIcon={
              SBData &&
              getObjKeyValue(SBData['processing-details']).key ===
                IN_PROGRESS && (
                <span onClick={handleGetProcessingDetails} role='presentation'>
                  <SolidSync />
                </span>
              )
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='Shipping Bills (SB)'
            btnName='Upload & Process'
            btnDisable={false}
            onBtnClick={() => handleUpload('SB')}
            secondBtnName='Advance Export'
            secondBtnDisable={!SBData}
            status={SBData ? getShowStatus(SBData) : null}
            recentUpdate={
              SBData?.['last-updated-date']
                ? `Last Uploaded on ${formatDateWithTime(
                    SBData?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              (SBData?.['last-updated-by'] &&
                `By ${SBData?.['last-updated-by']}`) ||
              '-'
            }
            onSecondBtnClick={() => handleAdvanceExport('SB')}
          />
          <FilingStep
            stepIcon={
              <span>
                {BOEData &&
                getObjKeyValue(BOEData['processing-details']).key ===
                  COMPLETED ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='2'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            statusIcon={
              BOEData &&
              getObjKeyValue(BOEData['processing-details']).key ===
                IN_PROGRESS && (
                <span onClick={handleGetProcessingDetails} role='presentation'>
                  <SolidSync />
                </span>
              )
            }
            filingName='Bill of Entry (BOE)'
            btnName='Upload & Process'
            btnDisable={false}
            onBtnClick={() => handleUpload('BOE')}
            secondBtnName='Advance Export'
            secondBtnDisable={!BOEData}
            status={BOEData ? getShowStatus(BOEData) : null}
            recentUpdate={
              BOEData?.['last-updated-date']
                ? `Last Uploaded on ${formatDateWithTime(
                    BOEData?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              (BOEData?.['last-updated-by'] &&
                `By ${BOEData?.['last-updated-by']}`) ||
              '-'
            }
            onSecondBtnClick={() => handleAdvanceExport('BOE')}
          />
        </div>
      </EximPaper>
    </div>
  );
}
