@import '@utils/main.scss';

.upload-block {
  @include margin-top(20px);
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;

    .filing-step-container {
      @include padding(0 16px);
      & > div:last-child {
        border-bottom: none;
      }
    }
  }

  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .filing-step {
    .details-container {
      .filing-type {
        .avatar-wrapper {
          .avatar-container {
            .small {
              width: 28px;
              height: 28px;
            }

            .avatar-text {
              background-color: $tertiary;
              border: none;
              color: $white;
              font-size: $font-size-sm;
              font-weight: normal;
            }
          }
        }
      }
    }
    .button-container {
      .base-btn {
        @include padding(7px 20px);
      }
      & > .button-wrapper:last-child {
        .base-btn {
          background-color: $table-head-primary;
          color: $white;
          &:hover {
            background-color: $table-head-primary;
            color: $white;
          }
        }
      }
    }
  }
  .error {
    color: $error;
    .error-count {
      @include margin-left(4px);
      color: $text-color;
    }
    .error-count-space {
      @include margin-left(10px);
    }
    font-size: $font-size-sm;
  }
  .success {
    color: $success;
    font-size: $font-size-sm;
  }
  .process {
    color: $warning;
    font-size: $font-size-sm;
  }
}
