import {Api, ApiAction, ApiVersion} from '@common/constants';
import {
  ICreateBillingSubscription,
  ICreateSaveAndSubscribeLater,
  ICreateSubscription,
} from '@common/interfaces';
import {get, post} from '@core/api/axios';
import {AxiosResponse} from 'axios';

export interface IApiData {
  productName?: string;
  gstInNumber?: string;
  planCode?: string;
  pageNo?: number;
  limit?: number;
  primaryPan?: string;
  pan?: string;
  subscriptionId?: string;
  product?: string;
}

const {
  PLANS,
  EXIM_SUBSCRIPTION_SERVICE,
  GSTIN_DETAILS,
  ADDONS,
  VERIFY_PARTNER_API,
  SUBSCRIPTIONS,
  BILLING_SUBSCRIPTIONS,
  SAVE_LATER,
  RENEW,
  FREE_SUBSCRIPTIONS,
} = Api;
const {
  GET_PLANS,
  GET_GSTIN_DETAILS,
  GET_ADDONS,
  VERIFY_PARTNER,
  CREATE_SUB,
  RENEW_SUB,
} = ApiAction;

// INFO: Below URL is common for all the API in entire file
const COMMON_BASE_URL = `${EXIM_SUBSCRIPTION_SERVICE}${ApiVersion.V1}`;

const getGstInApi = async (data: IApiData) => {
  const response = await get(`${COMMON_BASE_URL}${GSTIN_DETAILS}`, {
    headers: {
      gstin: data.gstInNumber as string,
      productName: data.productName as string,
      action: GET_GSTIN_DETAILS,
    },
  });
  return response as AxiosResponse;
};

// TODO : add the data values in url once flow complete
export const getSubscriptionPlans = async (data: IApiData) => {
  const response = await get(
    `${COMMON_BASE_URL}${PLANS}?productName=${data.productName}`,
    {
      headers: {
        action: GET_PLANS,
      },
    }
  );
  return response as AxiosResponse;
};

// TODO : add the data values in url once flow complete
export const getSubscriptionAddons = async (data: IApiData) => {
  const response = await get(
    `${COMMON_BASE_URL}${ADDONS}?productName=${data.productName}&planCode=${data.planCode}`,
    {
      headers: {
        action: GET_ADDONS,
      },
    }
  );
  return response as AxiosResponse;
};
export const verifyPartner = async (partnerCode: string) => {
  const response = await get(`${COMMON_BASE_URL}${VERIFY_PARTNER_API}`, {
    headers: {
      partnerCode,
      action: VERIFY_PARTNER,
    },
  });
  return response as AxiosResponse;
};

export const createSubscription = async (data: ICreateSubscription) => {
  const response = await post(`${COMMON_BASE_URL}${SUBSCRIPTIONS}`, data, {
    headers: {
      action: CREATE_SUB,
    },
  });
  return response as AxiosResponse;
};

export const renewSubscription = async (data: ICreateSubscription) => {
  const response = await post(
    `${COMMON_BASE_URL}${SUBSCRIPTIONS}${RENEW}`,
    data,
    {
      headers: {
        action: RENEW_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const createSaveAndSubscribeLater = async (
  data: ICreateSaveAndSubscribeLater
) => {
  const response = await post(
    `${COMMON_BASE_URL}${SUBSCRIPTIONS}${SAVE_LATER}`,
    data,
    {
      headers: {
        action: CREATE_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const createBillingSubscription = async (
  data: ICreateBillingSubscription
) => {
  const response = await post(
    `${COMMON_BASE_URL}${BILLING_SUBSCRIPTIONS}`,
    data,
    {
      headers: {
        action: CREATE_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBusinessDetails = async (data: IApiData) => {
  const response = await get(
    `${COMMON_BASE_URL}${Api.ORGANIZATIONS}?page-no=${data.pageNo}&limit=${data.limit}`,
    {
      headers: {
        action: ApiAction.GET_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const getSubscriptionDetails = async (data: IApiData) => {
  const response = await get(
    `${COMMON_BASE_URL}${Api.SUBSCRIPTIONS}${Api.NEW}`,
    {
      headers: {
        pan: data.pan,
        primaryPan: data.primaryPan,
        subscriptionId: data.subscriptionId,
        product: data.product,
        action: ApiAction.GET_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const createFreeSubscription = async (
  data: ICreateBillingSubscription
) => {
  const response = await post(`${COMMON_BASE_URL}${FREE_SUBSCRIPTIONS}`, data, {
    headers: {
      action: CREATE_SUB,
    },
  });
  return response as AxiosResponse;
};

export default getGstInApi;
