import {REGEXP} from '@common/constants';
import * as yup from 'yup';

export const validationSchemaGstInSchema = yup.object().shape({
  gstin: yup
    .string()
    .required('Please enter GSTIN number.')
    .test('valid-gstin', 'Please enter a valid GSTIN.', (value) =>
      REGEXP.validateGstinWithFixedZ.test(value || '')
    ),
});

export const validationSchemaIecFormDetails = yup.object().shape({
  iecCode: yup
    .string()
    .min(10, 'IEC Code should not be less than 10 characters.')
    .max(10, 'IEC Code should not be more than 10 characters.')
    .matches(REGEXP.alphanumericValue, 'Must be a 10-digit alphanumeric value.')
    .required('Please enter IEC Code.')
    .test(
      'no-spaces',
      'Please enter valid IEC Code',
      (value) => !REGEXP.whiteSpace.test(value || '')
    ),
});
export const validationSchemaIceFormDetails = yup.object().shape({
  iecCode: yup
    .string()
    .min(10, 'IEC Code should not be less than 10 characters.')
    .max(10, 'IEC Code should not be more than 10 characters.')
    .matches(REGEXP.alphanumericValue, 'Must be a 10-digit alphanumeric value.')
    .required('Please enter IEC Code.')
    .test(
      'no-spaces',
      'Please enter valid IEC Code',
      (value) => !REGEXP.whiteSpace.test(value || '')
    ),
  iceGateUserName: yup
    .string()
    .required('Please enter ICE Gate UserName.')
    .test(
      'no-spaces',
      'Please enter valid ICE Gate UserName',
      (value) => !REGEXP.whiteSpace.test(value || '')
    ),
  iceGatePassword: yup
    .string()
    .required('Please enter ICE Gate Password.')
    .test(
      'no-spaces',
      'Please enter valid ICE Gate Password',
      (value) => !REGEXP.whiteSpace.test(value || '')
    ),
});
