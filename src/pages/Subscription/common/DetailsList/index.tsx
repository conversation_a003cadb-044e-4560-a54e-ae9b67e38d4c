import EximTypography from '@shared/components/EximTypography';

import './index.scss';

interface IDetailsList {
  title: string;
  value: string;
}

function DetailsList(props: IDetailsList) {
  const {title, value} = props;
  return (
    <div className='section-details-list'>
      <EximTypography variant='caption' fontWeight='semi-bold'>
        {title}
      </EximTypography>
      <EximTypography variant='h6' fontWeight='regular'>
        {value}
      </EximTypography>
    </div>
  );
}

export default DetailsList;
