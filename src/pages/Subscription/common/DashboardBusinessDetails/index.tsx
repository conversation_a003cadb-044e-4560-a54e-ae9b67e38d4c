import {EximProducts, SubscriptionStatus} from '@common/constants';
import {ICommonScrollableData} from '@common/interfaces';
import EximTypography from '@shared/components/EximTypography';
import {NavigateAngle} from '@shared/icons/NavigateAngle';

import './index.scss';

interface IBusinessDetailsProps {
  options: ICommonScrollableData[];
  productData: boolean;
  selectedData: string;
  onSelect: (value: string, orgName: string, tinId: number) => void;
}

export default function DashboardBusinessDetails({
  options,
  onSelect,
  productData,
  selectedData,
}: IBusinessDetailsProps) {
  const translateProductName = (productName: string) => {
    if (productName === EximProducts.DATA_EXTRACTOR) {
      return 'Data Extractor';
    }
    if (productName === EximProducts.DUTY_DRAWBACK) {
      return 'Duty Drawback';
    }
    if (productName === EximProducts.MOOWR) {
      return 'Moowr';
    }
    if (productName === EximProducts.EBRC) {
      return 'EBRC';
    }

    return '';
  };

  const translateSubscriptionName = (status: string) => {
    if (status === SubscriptionStatus.PAID) {
      return 'Active Subscription';
    }
    if (status === SubscriptionStatus.FREE) {
      return 'FREE';
    }
    if (status === SubscriptionStatus.PENDING_PAYMENT) {
      return 'Pending Payment';
    }
    if (status === SubscriptionStatus.PENDING_ACTIVATION) {
      return 'Pending Activation';
    }
    if (status === SubscriptionStatus.EXPIRED) {
      return 'Expired';
    }
    if (status === SubscriptionStatus.NON_SUBSCRIBED) {
      return 'No Subscribed';
    }
    if (status === SubscriptionStatus.INACTIVE) {
      return 'Inactive';
    }
    if (status === SubscriptionStatus.PAYMENT_COMPLETED) {
      return 'Payment Completed';
    }
    return '';
  };

  return (
    <div className='business-cards-container'>
      {options.map((item, index) =>
        productData ? (
          <div
            key={item.value + item.title}
            className={`business-detail${
              selectedData === item.title
                ? ' business-details-selected'
                : ' business-details-unselected'
            }`}
            role='presentation'
            onClick={() =>
              onSelect(item.subscriptionId || '', item.title, item.tinId || 0)
            }>
            <div>
              <EximTypography variant='h5' fontWeight='semi-bold'>
                {translateProductName(item.title)}
              </EximTypography>
              <p className={`status ${item?.value?.toLowerCase()}`}>
                {translateSubscriptionName(item.value)}
              </p>
            </div>
            <span className='rotate-180-deg'>
              <NavigateAngle width={15} height={15} />
            </span>
          </div>
        ) : (
          <div
            key={item.value + item.title}
            className={`business-detail${
              selectedData === item.value ? ' business-details-selected' : ''
            }`}
            role='presentation'
            onClick={() => onSelect(item?.value, item.title, item.tinId || 0)}>
            <div>
              {item.isPrimary ? <p className='tin-status'>Primary</p> : null}
              <EximTypography variant='h5' fontWeight='semi-bold'>
                {item.title}
              </EximTypography>
              <p className='tin-number'>PAN: {item.value}</p>
            </div>
            <span className='rotate-180-deg'>
              <NavigateAngle width={15} height={15} />
            </span>
          </div>
        )
      )}
    </div>
  );
}
