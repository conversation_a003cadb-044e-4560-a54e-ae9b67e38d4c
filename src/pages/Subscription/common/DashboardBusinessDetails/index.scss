@import '@utils/main.scss';
.main-container {
  background-color: #f5f5f5 !important;
  .business-cards-container {
    width: 100%;
    @include rfs(7px 0px 0px 0px, border-radius);
    background-color: $white;
    max-height: 410px;
    overflow-y: auto;
    -ms-overflow-style: none;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
    .tin-status {
      font-size: $font-size-xxsm;
      color: $information-light;
      @include padding(0px 0px 4px 0px);
    }
    .tin-number {
      font-size: $font-size-xxsm;
      color: $tertiary;
      @include padding(4px 0px 0px 0px);
    }
    .business-detail {
      @include flex-item(_, space-between, center, _);
      @include padding(12px 15px 13px 20px);
      border-bottom: 1px solid $accordion-border;
      cursor: pointer;
      &:last-child {
        border: none;
      }
      .status {
        color: $submitted-status-color;
        font-size: $font-size-xxsm;
        @include margin(5px 0px 0px 0px);
        @include padding(0px 0px 4px 0px);
      }
      .status.paid,
      .status.active.subscription {
        color: $success-color;
      }
      .status.expired {
        color: $error-color;
      }
      .status.inactive {
        color: $error-color;
      }
      .status.pending {
        color: $in-process-color;
      }
      .rotate-180-deg {
        transform: rotate(180deg);
      }
    }
    .business-details-selected {
      background-color: $body-bg-color;
    }
    .business-details-unselected {
      background-color: $white;
    }
    .disabled-product {
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}
