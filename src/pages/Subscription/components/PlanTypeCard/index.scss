@import '@utils/main.scss';

.plan-type-container {
  height: 239px;
  width: 100%;
  @include margin-top(20px);
  .paper-wrapper-rounded {
    @include flex-item(row, flex-start, center, nowrap);

    .plan-card-icon {
      height: 100%;
      @include padding(62px 65px 55px 38px);
      border-right: 1px solid $avatar-border;
    }

    .plan-box {
      height: 100%;
      @include padding(25px 25px);
      max-width: 240px;
      @include flex-item(column, center, center, nowrap);
      text-align: center;
      border-right: 1px solid $avatar-border;
      .plan-title {
        font-size: $font-size-xl;
      }
      .plan-description {
        color: $label-color;
        min-height: 120px;
      }

      .top-button-wrapper {
        width: 110px;
        height: 34px;
        .base-btn {
          font-size: $font-size-sm;

          &[class~='medium'] {
            @include padding(8px 37px);
          }
        }

        .base-btn.free-plan-btn {
          background-color: $information;
        }
      }
    }

    .plan-box:last-child {
      border-right: none;
    }
  }
}
