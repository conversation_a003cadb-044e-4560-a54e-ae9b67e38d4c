import planCard from '@assets/images/planCard.svg';
import {
  PLAN_TYPE,
  Path,
  ResponseStatus,
  SubscriptionStatus,
} from '@common/constants';
import {IPlanDetails} from '@common/interfaces';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {getSubscriptionAddons, getSubscriptionPlans} from '@subscription/api';
import {subscriptionActions} from '@subscription/store/reducer';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

export default function PlanTypeCard() {
  const navigate = useNavigate();
  const {
    subscription: {
      existingPlanName,
      existingPlanStatus,
      productDetails: {productName},
    },
  } = useSelector((state: RootState) => state);

  const [allPlans, setAllPlans] = useState<IPlanDetails[]>([]);

  const getData = useCallback(async () => {
    const payload = {
      productName,
    };
    const res = await getSubscriptionPlans(payload);
    if (res.status.toString() === ResponseStatus.SUCCESS) {
      setAllPlans(res.data);
    }
  }, [productName]);

  useEffect(() => {
    getData();
  }, [getData]);

  const handleSelectPlan = async (plan: IPlanDetails) => {
    dispatch(
      subscriptionActions.setSubscriptionPlanData({...plan, productName})
    );
    const payload = {
      productName,
      planCode: plan.code,
    };
    const res = await getSubscriptionAddons(payload);
    if (res.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(subscriptionActions.setSubscriptionAllAddons(res.data));
      navigate(`${Path.SUBSCRIPTION}${Path.SETUP_PROCESS_SELECT_ADDON}`);
    }
  };

  return (
    <div className='plan-type-container'>
      <EximPaper elevation={2} variant='elevation'>
        <div className='plan-card-icon'>
          <img src={planCard} alt='plan card icon' />
        </div>
        {allPlans.map((plan, index) => (
          <div className='plan-box' key={`plan${index + 1}`}>
            <EximTypography classNames='plan-title' fontWeight='semi-bold'>
              {plan.name}
            </EximTypography>
            <EximTypography
              variant='h5'
              classNames='plan-description'
              fontWeight='normal'>
              {plan.description}
            </EximTypography>
            <div className='top-button-wrapper'>
              <EximButton
                disabled={
                  plan.name.includes(PLAN_TYPE.FREE) &&
                  plan.name === existingPlanName &&
                  existingPlanStatus !== SubscriptionStatus.EXPIRED
                }
                className={`${
                  plan.name.includes(PLAN_TYPE.FREE) && 'free-plan-btn'
                }  `}
                onClick={() => handleSelectPlan(plan)}>
                Select
              </EximButton>
            </div>
          </div>
        ))}
      </EximPaper>
    </div>
  );
}
