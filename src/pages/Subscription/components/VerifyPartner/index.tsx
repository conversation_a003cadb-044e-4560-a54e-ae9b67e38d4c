import {AlertStatus, Path, REGEXP, ResponseStatus} from '@common/constants';
import {IPartnerDetailsResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import {RootState, dispatch} from '@store';
import {
  createSubscription,
  renewSubscription,
  verifyPartner,
} from '@subscription/api';
import {subscriptionActions} from '@subscription/store/reducer';
import {AxiosResponse} from 'axios';
import {ChangeEvent, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';

import PartnerDetails from './PartnerDetails';
import './index.scss';

function VerifyPartner() {
  const navigate = useNavigate();
  const {
    subscription: {
      organizationDetails,
      partnerDetails,
      productDetails,
      isRenewOrUpgradePlan,
    },
  } = useSelector((state: RootState) => state);

  const [partnerCode, setPartnerCode] = useState<string>('');
  const [isPartnerCode, setIsPartnerCode] = useState<boolean>(false);
  const [inputErrorMsg, setInpErrorMsg] = useState<string>('');
  const [partnerDetailsData, setPartnerDetailsData] =
    useState<IPartnerDetailsResp>({
      companyName: '',
      partnerCode: '',
    });

  const handleChangePartnerCode = (e: ChangeEvent<HTMLInputElement>) => {
    const {value} = e.target;
    setPartnerCode(value?.toUpperCase());
    if (!REGEXP.partnerCode.test(value?.toUpperCase())) {
      setInpErrorMsg('Please enter valid partner Id');
    } else {
      setInpErrorMsg('');
    }
  };

  const handleVerifyPartnerCode = async (e: ChangeEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (partnerCode === '') {
      setInpErrorMsg('Please enter partner Id');
    } else {
      const response: AxiosResponse = await verifyPartner(partnerCode);
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        const {data} = response;

        setPartnerDetailsData({
          companyName: data?.companyName ? data?.companyName : 'Company',
          partnerCode: data?.partnerCode,
        });
        dispatch(subscriptionActions.setPartnerCode(data.partnerCode));
        setIsPartnerCode(true);
      }
    }
  };

  const handleEditPartnerCode = () => {
    setIsPartnerCode(false);
    dispatch(subscriptionActions.setPartnerCode(''));
    setPartnerDetailsData({companyName: '', partnerCode: ''});
  };

  const handleActiveSubscription = async () => {
    const {code} = productDetails.planDetails;

    const data = {
      organizationDetails,
      partnerDetails,
      productDetails: {
        ...productDetails,
        planDetails: {code},
      },
    };
    if (partnerCode) {
      // INFO: Need to call renew subscription API for renew and upgrade plan
      const response = isRenewOrUpgradePlan
        ? await renewSubscription(data)
        : await createSubscription(data);
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: response?.status,
            message: response?.data,
            alertType: AlertStatus.SUCCESS,
          })
        );
        navigate(Path.DASHBOARD);
      }
    }
  };

  return (
    <div
      className='verify-partner-container'
      data-testid='verify-partner-container'>
      <div
        className={`${
          isPartnerCode ? 'verify-partner verify-toggle' : 'verify-partner'
        }`}>
        <form onSubmit={handleVerifyPartnerCode} className='partner-input'>
          <EximInput
            dataTestid='partner-code-input'
            placeholder='Enter Code'
            type='text'
            label='Partner / Account manager code'
            value={partnerCode}
            errorMessage={inputErrorMsg}
            onChange={handleChangePartnerCode}
            isInvalid={inputErrorMsg.length !== 0}
            disabled={isPartnerCode || partnerDetailsData?.companyName !== ''}
            isRequired
          />
          {isPartnerCode || partnerDetailsData?.companyName !== '' ? (
            <div className='edit-code-btn'>
              <EximButton
                variant='text'
                size='small'
                onClick={handleEditPartnerCode}>
                Edit Code
              </EximButton>
            </div>
          ) : (
            <EximButton
              disabled={partnerDetailsData?.companyName !== ''}
              type='submit'
              color='secondary'
              size='small'>
              Verify
            </EximButton>
          )}
        </form>
        {isPartnerCode || partnerDetailsData?.companyName !== '' ? (
          <PartnerDetails
            partnerCode={partnerCode}
            partnerDetailsData={partnerDetailsData}
          />
        ) : null}
      </div>
      {isPartnerCode || partnerDetailsData?.companyName !== '' ? (
        <EximButton
          color='primary'
          size='small'
          onClick={handleActiveSubscription}>
          Submit For Activation
        </EximButton>
      ) : null}
    </div>
  );
}

export default VerifyPartner;
