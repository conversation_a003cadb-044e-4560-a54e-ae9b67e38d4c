@import '@utils/main.scss';

.verify-partner-container {
  width: 304px;

  .verify-partner {
    width: 100%;
    background: $white;
    box-shadow: $product-subscription-box-shadow;
    border: 1px solid $card-label-color;
    @include rfs(5px, border-radius);
    @include padding(35px 24px 27px);
    .partner-details-verify {
      @include flex-item(_, _, center, _, _);
      .verify-btn {
        .base-btn {
          @include padding(1.45px 5.95px);
          @include rfs(2px, border-radius);
        }
      }
      .verify-username {
        font-size: $font-size-sm;
        color: $text-color;
      }
    }

    .partner-input {
      @include flex-item(_, _, _, _, 12px);

      .input-container {
        @include margin-top(12px);

        .input-group.disabled:hover {
          border: none;
        }

        input {
          @include rfs(5px, border-radius);
          font-size: $font-size-sm;
          color: $text-light;
          &:disabled {
            background-color: $disable-color;
          }
        }

        label {
          top: -37px;

          span {
            font-size: $font-size-xsm;
          }
        }
      }

      .button-wrapper {
        .base-btn {
          @include padding(7px 14px);
          font-size: $font-size-sm;
          @include margin(22px 0 0 0);
        }

        .disabled {
          border: none;
          box-shadow: none;
          border: none;
          cursor: not-allowed;
        }
      }

      .edit-code-btn {
        .base-btn {
          min-width: 60px;
          @include padding(0);

          &:hover {
            box-shadow: none;
          }
        }
      }
    }

    .partner-details-container {
      @include padding-top(20px);

      & > p {
        font-size: $font-size-xsm;
        color: $label-color;
        @include margin-bottom(10px);
      }

      & > div {
        @include flex-item(_, _, center, _, 16px);

        p {
          font-size: $font-size-sm;
          color: $text-color;
        }

        .base-btn {
          @include margin(0);
          @include padding(0 7px 1px);
          font-size: $font-size-xsm;

          &:hover {
            background-color: $success;
            box-shadow: none;
            cursor: text;
          }
        }
      }
    }

    .validation-partner {
      @include flex-item(column, _, _, _, 20px);
      @include padding(16px 0 5px);
      @include margin-top(20px);
      border-top: 1px solid $primary-border;

      p {
        font-weight: $font-weight-bold;
        font-size: $font-size-sm;
        color: $text-color;
        letter-spacing: 0.4px;
      }

      .validation-radio-btn {
        @include flex-item(column, _, _, _, _);

        label {
          font-size: $font-size-xsm;
          color: $text-color;
          letter-spacing: 0.3px;
        }
      }
    }

    .validation-type {
      @include flex-item(_, _, center, _, _);

      .input-wrapper {
        @include margin(0);
        width: 185px;

        .error-message {
          @include margin(0);
          font-size: $font-size-sm;
          color: $error;
        }

        .input-normal-wrapper {
          .input-container {
            .disabled {
              cursor: not-allowed;
            }
          }
        }
      }

      .base-btn {
        @include padding(7.5px 15px);
        font-size: $font-size-sm;
        @include margin(0);
        width: 100px;
        position: relative;
        left: -4px;
      }

      .otp-resend-section {
        color: $error;
        font-size: $font-size-xsm;

        .otp-second {
          position: relative;
          left: 10px;
        }
      }
    }

    // changing the underline animation color for "Edit Code" button
    & .edit-code-btn {
      .animation-underline {
        &::before {
          background: $primary;
        }
      }
    }

    .validation-type + .button-wrapper {
      position: relative;
      top: 16px;
      width: 61px;

      .base-btn {
        @include padding(0);
        font-size: $font-size-xsm;
        box-shadow: none;
        margin-top: 0;

        &:hover {
          box-shadow: none;
          border: none;
        }
      }
    }
  }
  .verify-toggle {
    @include padding(42.5px 24px 27px);
  }

  .button-wrapper {
    .base-btn {
      @include margin(0);
      @include margin-top(16px);
      @include padding(8px);
      font-size: $font-size-sm;
    }
  }
}
