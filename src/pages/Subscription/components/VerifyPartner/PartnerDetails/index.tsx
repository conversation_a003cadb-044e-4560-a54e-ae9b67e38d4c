import {IPartnerDetailsProps} from '@common/interfaces';
import EximButton from '@shared/components/EximButton';
import {SolidCheckCircle} from '@shared/icons';

export default function PartnerDetails(props: IPartnerDetailsProps) {
  const {partnerDetailsData, partnerCode} = props;

  const isVerified = partnerCode || partnerDetailsData?.companyName !== '';

  return (
    <div className='partner-details-container'>
      <p>Partner/Account Manager Name</p>
      {isVerified ? (
        <div className='partner-details-verify'>
          <p className='verify-username'>{`${partnerDetailsData?.companyName}`}</p>
          <EximButton className='verify-btn' color='success' size='small'>
            Verified
            <SolidCheckCircle />
          </EximButton>
        </div>
      ) : null}
    </div>
  );
}
