@import '@utils/main.scss';

.add-on-cards {
  @include flex-item(_, flex-start, center, wrap, 64px);
  .outlined-paper {
    border: none;
  }
  .paper-wrapper-rounded {
    width: 304px;
    height: 233px;
    margin: 0;
    @include padding(30px 24px);
    @include flex-item(column, space-between, _);
    box-shadow: $product-subscription-box-shadow;
    .typography-variant-body1 {
      font-size: $font-size-xsm;
      color: $label-color;
    }

    .card-controls {
      height: 30px;
      @include flex-item(row, space-between, flex-end);
      .button-wrapper {
        width: fit-content;
        .btn-children {
          font-size: $font-size-xsm;
          text-decoration: underline;
        }
        .base-btn {
          margin: 0;
          padding: 0;
        }
        .base-btn:not(:disabled):hover {
          box-shadow: none;
        }
      }

      .checkbox-container {
        margin: 0;
        padding: 0;
        width: 30px;
        height: 30px;
        .checkbox-large {
          @include flex-item();
          width: 30px;
          height: 30px;

          &::after {
            width: 8px;
            height: 18px;
            @include margin(-4px 2px 0);
          }
        }
      }

      .modal-body {
        width: 358px;
        border-radius: 0;
        @include padding(16px);
        .modal-header {
          width: 300px;
        }

        .modal-title {
          @include font-size(18px);
          color: $text-color;
          font-weight: normal;
        }

        .modal-content {
          @include padding(0 24px 24px);
          font-size: $font-size-xsm;
          min-height: 16px;
          height: fit-content;
        }

        svg {
          path {
            fill: $black;
          }
        }
      }

      .modal {
        &:before {
          opacity: 0;
        }
      }
    }
  }
}
