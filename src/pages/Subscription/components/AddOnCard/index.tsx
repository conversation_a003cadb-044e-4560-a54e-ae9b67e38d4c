import {ISubscriptionAddon} from '@common/interfaces';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {subscriptionActions} from '@subscription/store/reducer';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function AddOnCard() {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const {
    subscription: {subscriptionSummaryAddon},
  } = useSelector((state: RootState) => state);

  const toggleAddon = (itemCode: string) => {
    const newList: Array<ISubscriptionAddon> = subscriptionSummaryAddon?.map(
      (item: ISubscriptionAddon) =>
        item.addonCode === itemCode
          ? {...item, purchased: !item.purchased}
          : {...item}
    );
    dispatch(subscriptionActions.setSubscriptionAllAddons(newList));
  };

  const handleIsOpen = (state: boolean) => {
    setIsOpen(state);
  };

  return (
    <div className='add-on-cards'>
      {subscriptionSummaryAddon.length > 0 &&
        subscriptionSummaryAddon?.map((item: ISubscriptionAddon) => (
          <EximPaper key={item.addonCode}>
            <EximTypography variant='h3'>{item.name}</EximTypography>
            <EximTypography variant='body1'>{item.description}</EximTypography>
            <div className='card-controls'>
              <EximButton
                dataTestId={`btn-${item.addonCode}`}
                onClick={() => handleIsOpen(true)}
                size='small'
                variant='text'
                color='information'>
                Know More
              </EximButton>
              {/* GST Modal */}
              <EximModal
                isOpen={isOpen}
                header={item.name}
                content={item.description}
                footer=''
                onClose={() => handleIsOpen(false)}
                onOutSideClickClose={() => handleIsOpen(false)}
              />
              <EximCheckbox
                id='addService'
                name='addService'
                color='#2CB544'
                size='large'
                dataTestId={item.addonCode}
                checked={item.purchased}
                onChange={() => toggleAddon(item.addonCode)}
              />
            </div>
          </EximPaper>
        ))}
    </div>
  );
}

export default AddOnCard;
