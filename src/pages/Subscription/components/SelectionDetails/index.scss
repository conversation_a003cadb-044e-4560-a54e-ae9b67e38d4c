@import '@utils/main.scss';

/*TOP SECTION SCSS*/
.subscription-top {
  @include margin-top(8px);

  .subscription-main-header {
    width: 100%;
    @include padding-bottom(30px);
    @include flex-item(row, space-between, flex-start, wrap);

    .account-selection-details {
      @include flex-item(row, flex-start, flex-start, wrap, 30px);
      @include margin-top(15px);
      & > div {
        min-width: 110px;
      }
    }

    .typography-container .typography-variant-h4 {
      @include font-size($font-size-md);
      color: $heading-color;
    }

    .product-details {
      .selected-product {
        width: inherit;
        height: 32px;
        @include rfs(18px, border-radius);
        border: 2px solid $quaternary;
        @include flex-item();
        color: $quaternary;
        @include font-size($font-size-sm);
        @include margin-top(27px);
        @include padding(0 5px);
      }
    }
  }
}

.divider {
  .divider-center {
    border: none;
    border-bottom: 1px solid $product-header-border;
  }
}
