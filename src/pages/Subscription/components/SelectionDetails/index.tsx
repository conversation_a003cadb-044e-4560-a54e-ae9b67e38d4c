import EximDivider from '@shared/components/EximDivider';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import DetailsList from '@subscription/common/DetailsList';
import {memo} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function SelectionDetails() {
  const {
    subscription: {
      subscriptionType: {subscribeAs, type},
      organizationDetails: {
        gstinDetails: {gstin, tradeName},
        iecDetails: {iecCode},
        pan,
      },
      productDetails: {productName},
    },
  } = useSelector((state: RootState) => state);

  const {account, business} = {
    // ? Info: Registered Business & PAN Based are by default values
    account: [
      {title: 'Subscribed As', value: subscribeAs || 'Registered Business'},
      {title: 'Type', value: type || 'PAN Based'},
    ],
    business: [
      {title: 'Trade Name', value: tradeName || '-'},
      {title: 'PAN No.', value: pan || '-'},
      {title: 'Billing GSTIN', value: gstin || '-'},
      {title: 'IEC Number', value: iecCode || '-'},
    ],
  };
  return (
    <>
      <div className='subscription-top'>
        <div className='subscription-main-header'>
          <div className='account-details'>
            <EximTypography variant='h4' fontWeight='semi-bold'>
              Account Selection Done:
            </EximTypography>
            <div className='account-selection-details'>
              {account &&
                account.map((item) => (
                  <div key={item.title}>
                    <DetailsList title={item.title} value={item.value} />
                  </div>
                ))}
            </div>
          </div>
          <div className='business-details'>
            <EximTypography variant='h4' fontWeight='semi-bold'>
              Business Details:
            </EximTypography>
            <div className='account-selection-details'>
              {business.length &&
                business.map((item) => (
                  <div key={item.title}>
                    <DetailsList
                      title={item.title}
                      value={String(item.value)}
                    />
                  </div>
                ))}
            </div>
          </div>
          <div className='product-details'>
            <EximTypography variant='h4' fontWeight='semi-bold'>
              Selected Product:
            </EximTypography>
            <div className='selected-product'>{productName}</div>
          </div>
        </div>
      </div>
      <EximDivider type='solid' />
    </>
  );
}

export default memo(SelectionDetails);
