@import '@utils/main.scss';

.subscription-plans {
  .paper-wrapper-rounded {
    @include margin(35px 0 31px 0);
    border: none;
    box-shadow: $product-subscription-box-shadow;
  }
  .product-details {
    @include flex-item(row, space-between, center, nowrap, 30px);
    @include padding(16px 30px);
    filter: drop-shadow($product-subscription-box-shadow);
    img {
      width: 85px;
      height: 85px;
      @include padding(20px);
      border: 2px solid $avatar-border;
      @include rfs(5px, border-radius);
    }

    .product-data {
      width: 100%;
      @include flex-item(row, flex-start, center, nowrap, 30px);
    }

    .product-details-data {
      @include flex-item(row, flex-start, flex-start, nowrap, 56px);

      .typography-variant-body1 {
        color: $label-color;
        @include font-size(12px);
        @include margin(0 0 14px 0);
        &.regular {
          font-weight: $font-weight-semi-bold;
        }
      }

      .plan-type {
        display: flex;
      }

      .typography-variant-body2 {
        display: inline-block;
        color: $text-color;
        font-size: $font-size-md;
      }

      .button-wrapper {
        width: 38px;
        @include margin(0 0 0 10px);
        .base-btn {
          margin: 0;
          padding: 0;
          height: 20px;
          align-items: flex-end;

          &:not(:disabled):hover {
            box-shadow: none;
          }
        }

        .base-btn[class~='small'] {
          @include font-size(12px);
        }
      }
    }
  }
  .add-on {
    @include margin(0 60px 0 30px);
    @include padding(16px 0);
    border-bottom: 2px solid $avatar-border;
    @include flex-item(row, flex-start, center, nowrap, 30px);
    .add-file-icon {
      width: 72px;
      height: 72px;
      @include padding(20px);
      border: 2px solid $avatar-border;
      @include rfs(5px, border-radius);
    }

    .add-on-details {
      @include flex-item(row, space-between, center, nowrap);
      width: 100%;
      .typography-variant-body1 {
        color: $label-color;
        font-size: $font-size-xsm;
        @include margin(0 0 14px 0);
        font-weight: $font-weight-semi-bold;
      }

      .typography-variant-body2 {
        color: $text-color;
        font-size: $font-size-md;
      }
      .remove-add-on {
        cursor: pointer;
      }
    }
  }
  .move-to-add {
    @include padding(16px 30px 20px);
    @include flex-item(row, _, center, nowrap, 30px);
    .typography-variant-body2 {
      color: $table-head-primary;
      font-size: $font-size-md;
    }

    .plus-icon {
      width: 72px;
      height: 72px;
      @include flex-item();
      border: 2px solid $avatar-border;
      @include rfs(5px, border-radius);
      background-color: $primary-bg;

      &:hover {
        box-shadow: 0px 3px 6px $btn-box-shadow-hover;
        cursor: pointer;
      }
    }
  }
}
