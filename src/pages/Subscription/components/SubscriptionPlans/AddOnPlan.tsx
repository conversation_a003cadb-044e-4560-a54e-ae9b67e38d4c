import {ISubscriptionAddon} from '@common/interfaces';
import EximTypography from '@shared/components/EximTypography';
import {AddFiles, CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {subscriptionActions} from '@subscription/store/reducer';
import {useSelector} from 'react-redux';

interface IProps {
  data?: ISubscriptionAddon;
}

function AddOnPlan({data}: IProps) {
  const {
    subscription: {subscriptionSummaryAddon},
  } = useSelector((state: RootState) => state);

  const handleRemoveAddon = (itemCode: string) => {
    const newList: Array<ISubscriptionAddon> = subscriptionSummaryAddon?.map(
      (item: ISubscriptionAddon) =>
        item.addonCode === itemCode ? {...item, purchased: false} : {...item}
    );

    dispatch(subscriptionActions.setSubscriptionAllAddons(newList));
  };

  return (
    <div className='add-on'>
      <div className='add-file-icon'>
        <AddFiles width={32} height={32} />
      </div>
      <div className='add-on-details'>
        <div>
          <EximTypography variant='body1'>Product (Add-on)</EximTypography>
          <EximTypography variant='body2' fontWeight='semi-bold'>
            {data?.name}
          </EximTypography>
        </div>
        <div
          role='presentation'
          className='remove-add-on'
          onClick={() => handleRemoveAddon(data?.addonCode as string)}>
          <CloseIcon width={15} height={15} fill='#FE4242' />
        </div>
      </div>
    </div>
  );
}

export default AddOnPlan;

AddOnPlan.defaultProps = {
  data: null,
};
