import {Path} from '@common/constants';
import {ISubscriptionAddon} from '@common/interfaces';
import {getImage} from '@pages/Dashboard/utils';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {Plus} from '@shared/icons';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router-dom';

import AddOnPlan from './AddOnPlan';
import './index.scss';

interface IProps {
  addOnPlans?: Array<ISubscriptionAddon>;
  isAddonAvailable?: boolean;
}

function SubscriptionPlans({addOnPlans, isAddonAvailable}: IProps) {
  const navigate = useNavigate();

  const {
    subscription: {
      productDetails: {
        productName,
        planDetails: {name: planName},
      },
      organizationDetails: {
        iecDetails: {iecCode},
        pan,
      },
    },
  } = useSelector((state: RootState) => state);

  const handleNavigate = () => {
    navigate(`${Path.SUBSCRIPTION}${Path.SETUP_PROCESS_SELECT_ADDON}`);
  };

  const handleGoToSelectPlan = () => {
    navigate(`${Path.SUBSCRIPTION}${Path.VIEW_PLAN}`);
  };

  return (
    <div
      className={`subscription-plans ${
        isAddonAvailable ? 'available-addon' : ''
      }`}>
      <EximPaper>
        <div className='product-details'>
          <img src={getImage(productName)} alt='plan-logo' />
          <div className='product-data'>
            <div className='product-details-data'>
              <div>
                <EximTypography variant='body1'>Product</EximTypography>
                <EximTypography variant='body2' fontWeight='semi-bold'>
                  {productName || '-'}
                </EximTypography>
              </div>
              <div>
                <EximTypography variant='body1'>Plan</EximTypography>
                <div className='plan-type'>
                  <EximTypography variant='body2'>
                    {planName || '-'}
                  </EximTypography>
                  <EximButton
                    dataTestId='select-plan'
                    onClick={handleGoToSelectPlan}
                    size='small'
                    variant='text'
                    color='information'>
                    Change
                  </EximButton>
                </div>
              </div>
              <div>
                <EximTypography variant='body1'>PAN</EximTypography>
                <EximTypography variant='body2'>{pan || '-'}</EximTypography>
              </div>
              <div>
                <EximTypography variant='body1'>IEC</EximTypography>
                <EximTypography variant='body2'>
                  {iecCode || '-'}
                </EximTypography>
              </div>
            </div>
            {/* // TODO: This will be added in future if have multiple products */}
            {/* <CloseIcon width={15} height={15} fill='#FE4242' /> */}
          </div>
        </div>
        {isAddonAvailable && (
          <div>
            {addOnPlans
              ?.filter((item: ISubscriptionAddon) => item.purchased)
              .map((item: ISubscriptionAddon) => (
                <AddOnPlan key={item.addonCode} data={item} />
              ))}
            <div className='move-to-add'>
              <div
                data-testid='add-more-plans'
                onClick={handleNavigate}
                role='presentation'
                className='plus-icon'>
                <Plus />
              </div>
              <EximTypography variant='body2' fontWeight='semi-bold'>
                Add More Add-Ons
              </EximTypography>
            </div>
          </div>
        )}
      </EximPaper>
    </div>
  );
}

export default SubscriptionPlans;

SubscriptionPlans.defaultProps = {
  addOnPlans: [],
  isAddonAvailable: false,
};
