@import '@utils/main.scss';

.plan-detail-main-container {
  width: 100%;
  @include flex-item(row, flex-start);
  .elevation-2 {
    box-shadow: $product-subscription-box-shadow;
  }
  .paper-wrapper-rounded {
    @include margin(0);
  }
  .plan-detail-header {
    @include flex-item(row, flex-start, center, nowrap, 30px);
    @include padding(16px 30px);

    .details-icon {
      border: 2px solid $avatar-border;
      @include padding(15px);
      @include rfs(5px, border-radius);

      img {
        width: 51px;
        height: 51px;
      }
    }

    .details {
      min-width: 125px;
      @include flex-item(column, _, _, nowrap, 12px);
      .title {
        font-size: $font-size-xsm;
        color: $label-color;
        font-weight: $font-weight-semi-bold;
      }
      .product-name {
        font-size: $font-size-md;
        font-weight: $font-weight-semi-bold;
        color: $text-color;
      }
    }
  }
}
