import {getImage} from '@pages/Dashboard/utils';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useSelector} from 'react-redux';

import './index.scss';

export default function PlanCard() {
  const {
    subscription: {
      organizationDetails: {
        pan,
        iecDetails: {iecCode},
      },
      productDetails: {productName},
    },
    dashboard: {dashboardActiveProduct},
  } = useSelector((state: RootState) => state);

  return (
    <div className='plan-detail-main-container'>
      <EximPaper elevation={2} variant='elevation'>
        <div className='plan-detail-header'>
          <div className='details-icon'>
            <img src={getImage(productName)} alt='icon' />
          </div>
          <div className='details'>
            <div className='title'>Product</div>
            <div className='product-name'>{dashboardActiveProduct || '-'}</div>
          </div>
          <div className='details'>
            <div className='title'>PAN</div>
            <div>{pan || '-'}</div>
          </div>
          <div className='details'>
            <div className='title'>IEC</div>
            <div>{iecCode || '-'}</div>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}
