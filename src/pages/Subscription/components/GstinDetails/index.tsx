import {
  AlertStatus,
  GSTIN_ALREADY_ADDED,
  ResponseStatus,
} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import EximButton from '@shared/components/EximButton';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ArrowLeftIcon} from '@shared/icons';
import {RootState} from '@store';
import getGstInApi from '@subscription/api';
import {
  emptyGstInDetails,
  subscriptionActions,
} from '@subscription/store/reducer';
import {validationSchemaGstInSchema} from '@subscription/utils';
import {AxiosResponse} from 'axios';
import {useFormik} from 'formik';
import {MouseEvent, memo, useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {Link} from 'react-router-dom';

import GstInForm from '../GstInForm';
import GstinInfo from '../GstinInfo';
import './index.scss';

function GstinDetails() {
  const {
    alert,
    subscription: {
      productDetails: {productName},
      organizationDetails: {
        gstinDetails: {gstin},
      },
    },
  } = useSelector((state: RootState) => state);
  const dispatch = useDispatch();
  const [disable, setDisable] = useState(false);
  const [isEditGstin, setIsEditGstin] = useState('');

  const getGstInDetailsData = async (gstInNumber: string) => {
    const {status, data}: AxiosResponse = await getGstInApi({
      gstInNumber,
      productName,
    });
    if (status.toString() === ResponseStatus.SUCCESS) {
      if (data?.showSaveLater) {
        dispatch(
          alertActions.setAlertMsg({
            message: data.message,
            alertType: AlertStatus.DANGER,
          })
        );
      }
      const {
        lgnm,
        dty,
        tradeNam,
        sts,
        gstin: gstNumber,
        ctj,
        stj,
        ctb,
        rgdt,
      } = data;
      const panId = gstNumber.substring(2, 12);

      const payload = {
        businessName: lgnm,
        taxPayerType: dty,
        tradeName: tradeNam || lgnm,
        status: sts,
        gstin: gstNumber,
        city: ctj,
        stj,
        constitutionOfBusiness: ctb,
        registrationDate: rgdt,
        panNumber: panId,
      };
      const organisationGstPayload = {
        gstin: gstNumber,
        tradeName: tradeNam || lgnm,
        legalName: tradeNam || lgnm,
      };
      dispatch(subscriptionActions.getGstInDetails(payload));
      dispatch(subscriptionActions.addGstDetails(organisationGstPayload));
      dispatch(subscriptionActions.addPanNumberAndOrgName({panId, lgnm}));
      setDisable(false);
    } else {
      dispatch(
        alertActions.setAlertMsg({
          message: `${gstInNumber} ${GSTIN_ALREADY_ADDED}`,
          alertType: AlertStatus.DANGER,
        })
      );
    }
  };

  const formik = useFormik({
    initialValues: {gstin: gstin || isEditGstin},
    validationSchema: validationSchemaGstInSchema,
    onSubmit: (values: {gstin: string}) => {
      getGstInDetailsData(values.gstin);
    },
  });

  useEffect(() => {
    if (alert) {
      setDisable(false);
    }
  }, [alert]);

  const handleEditGstIn = (e: MouseEvent) => {
    setIsEditGstin(gstin);
    dispatch(subscriptionActions.getGstInDetails(emptyGstInDetails));
    dispatch(subscriptionActions.resetOrganizationDetails());
    e.preventDefault();
  };

  return (
    <div className='subscription-middle'>
      <EximPaper variant='elevation'>
        <div className='middle-paper-wrapper'>
          <div className='gstin-business-details'>
            <Link to='/dashboard'>
              <ArrowLeftIcon width={22} height={20} />
            </Link>
            <EximTypography variant='h3' fontWeight='semi-bold' gutterBottom>
              Business Details:
            </EximTypography>
          </div>
          <EximDivider type='solid' />
          <div className='primary-body'>
            <div className='form-wrapper'>
              <div className='gstin-details'>
                <div
                  className={`${
                    gstin
                      ? 'billing-fields billing-fields-form'
                      : 'billing-fields'
                  }`}>
                  <div className='billing-details'>
                    <EximTypography variant='h4' fontWeight='semi-bold'>
                      Provide Primary GSTIN Details:
                    </EximTypography>
                  </div>

                  <form onSubmit={formik.handleSubmit}>
                    <div className='input-button-wrapper'>
                      <EximInput
                        placeholder='Enter GSTIN'
                        label='GSTIN'
                        id='gstin'
                        name='gstin'
                        isRequired
                        value={formik.values.gstin}
                        autoComplete='on'
                        onBlur={formik.handleBlur}
                        isInvalid={!!formik.errors.gstin || false}
                        errorMessage={
                          formik.errors.gstin ? formik.errors.gstin : ''
                        }
                        onChange={(e) => {
                          formik.setFieldValue('gstin', e.target.value.trim());
                        }}
                      />
                      {gstin ? (
                        <EximButton
                          onClick={handleEditGstIn}
                          color='tertiary'
                          size='small'
                          name='submit'
                          className='text-button'
                          disabled={disable}
                          variant='text'>
                          Edit Details
                        </EximButton>
                      ) : (
                        <EximButton
                          type='submit'
                          color='tertiary'
                          size='small'
                          name='submit'
                          className='get-button'
                          disabled={disable}
                          variant='contained'>
                          Get Details
                        </EximButton>
                      )}
                    </div>
                  </form>
                </div>
                {gstin ? <GstinInfo /> : null}
              </div>
              {gstin ? (
                <>
                  <EximDivider type='solid' />
                  <GstInForm />
                </>
              ) : null}
            </div>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}

export default memo(GstinDetails);
