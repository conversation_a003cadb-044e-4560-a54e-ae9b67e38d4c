@import '@utils/main.scss';
.subscription-middle {
  .paper-wrapper-rounded {
    width: 100%;
    @include margin(0);
    @include rfs(5px, border-radius);
    box-shadow: $product-subscription-box-shadow;
  }

  .middle-paper-wrapper {
    @include padding(35px 32px);
    .gstin-business-details {
      @include flex-item(row, _, center, _, _);
      column-gap: 10px;
    }
    .typography-container {
      .typography-variant-h3 {
        color: $text-color;
      }
      .gutter-bottom {
        @include margin(0);
      }
    }

    .primary-body {
      .form-wrapper {
        .gstin-details {
          @include flex-item(row, space-between);
          .billing-fields {
            width: 386px;
            @include padding(10px 0 73px);

            .billing-details {
              @include flex-item(_, _, center, _, 5px);
              @include margin-bottom(27px);

              .typography-container {
                .typography-variant-h4 {
                  color: $heading-color;
                }
              }
              span {
                color: $Link-color;
                @include font-size($font-size-xsm);
              }
            }

            .input-button-wrapper {
              @include flex-item(row, flex-start, flex-end, nowrap, 42px);
              column-gap: 29px;
              @include margin(0);
              .input-wrapper {
                @include margin-top(13px);
                @include rfs(5px, border-radius);

                .input-normal-wrapper {
                  @include margin-top(0);
                  input {
                    @include rfs(5px, border-radius);
                    @include flex-item();
                    @include font-size($font-size-sm);
                    color: $text-color;
                  }
                  .error-message {
                    @include font-size($font-size-sm);
                  }
                  label {
                    top: -35px;
                    background-color: inherit;
                  }
                }
              }

              .button-wrapper {
                min-width: 117px;
                height: 33px;
                .base-btn {
                  height: 100%;
                  font-size: $font-size-sm;
                }
                .button-text {
                  @include font-size($font-size-sm);
                  @include padding(2px 0);
                }
              }
            }
          }
          .billing-fields-form {
            @include padding(10px 0 30px);
          }

          .text-button {
            @include margin(0);
            @include padding(0);
            @include flex-item(row, flex-start, _, _, _);
            color: $primary;
            text-decoration: underline;

            &:not(:disabled):hover {
              box-shadow: none;
            }

            .btn-children {
              .button-text {
                @include font-size($font-size-sm);
                border-bottom: 1px solid $primary;
                height: 18px;
                @include margin-bottom(8px);
              }
            }
          }
        }
      }
    }
  }
}

.tip-top {
  bottom: 115%;
}

.hidden-button-next {
  display: none;
}
