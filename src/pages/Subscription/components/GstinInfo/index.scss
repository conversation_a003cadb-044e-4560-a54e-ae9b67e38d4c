@import '@utils/main.scss';

.gstin-info {
  width: 53%;
  height: 137px;
  @include padding(15px 32px 19px 20px);
  background-color: $gstin-details-background;
  @include flex-item(row, space-between, _, wrap);
  .gstin-names,
  .gstin-type-status {
    @include flex-item(column, flex-start, flex-start, nowrap, 10px);
    .typography-container {
      @include padding(0);
      @include font-size($font-size-xsm);
      font-weight: $font-weight-semi-bold;
      .typography-variant-h6 {
        @include font-size($font-size-sm);
        font-weight: $font-weight-regular;
      }
    }
  }
}
