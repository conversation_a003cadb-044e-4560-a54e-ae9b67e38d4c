import {RootState} from '@store';
import DetailsList from '@subscription/common/DetailsList';
import {useSelector} from 'react-redux';

import './index.scss';

function GstinInfo() {
  const {
    subscription: {
      gstInDetails: {businessName, taxPayerType, tradeName, panNumber, status},
    },
  } = useSelector((state: RootState) => state);
  const {names, types, panDetails} = {
    names: [
      {title: 'Legal Name', value: businessName || '-'},
      {title: 'Trade Name', value: tradeName || '-'},
    ],
    types: [
      {title: 'Tax Payer Type', value: taxPayerType || '-'},
      {title: 'Status', value: status},
    ],
    panDetails: [{title: 'PAN Number', value: panNumber || '-'}],
  };

  return (
    <div className='gstin-info' data-testid='gstin-details'>
      <div className='gstin-names'>
        {names.map(({title, value}) => (
          <div key={title}>
            <DetailsList title={title} value={value} />
          </div>
        ))}
      </div>
      <div className='gstin-type-status'>
        {types.map(({title, value}) => (
          <div key={title}>
            <DetailsList title={title} value={value} />
          </div>
        ))}
      </div>
      <div className='gstin-type-status'>
        {panDetails.map(({title, value}) => (
          <div key={title}>
            <DetailsList title={title} value={value} />
          </div>
        ))}
      </div>
    </div>
  );
}

export default GstinInfo;
