import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {IFormInitial} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import EximTypography from '@shared/components/EximTypography';
import {EyeClose, EyeOpen} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {createSaveAndSubscribeLater} from '@subscription/api';
import {subscriptionActions} from '@subscription/store/reducer';
import {
  validationSchemaIceFormDetails,
  validationSchemaIecFormDetails,
} from '@subscription/utils';
import {useFormik} from 'formik';
import {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function GstInForm() {
  const {
    subscription: {organizationDetails, productDetails},
  } = useSelector((state: RootState) => state);
  const [isVerifyLater, setIsVerifyLater] = useState(false);
  const [isIceGateVerify, setIsIceGateVerify] = useState(false);
  const navigate = useNavigate();
  const handleGstInFormSubmit = async (payload: IFormInitial) => {
    if (payload) {
      if (isVerifyLater) {
        payload = {...payload, iceGatePassword: '', iceGateUserName: ''};
      }
      setIsVerifyLater(false);
      setIsIceGateVerify(true);
      dispatch(subscriptionActions.addOrganizationDetails(payload));
      navigate(`${Path.SUBSCRIPTION}${Path.VIEW_PLAN}`);
    }
  };

  const formInitial: IFormInitial = {
    iecCode: organizationDetails.iecDetails.iecCode,
    iceGateUserName: organizationDetails.iecDetails.iceGateUserName,
    iceGatePassword: organizationDetails.iecDetails.iceGatePassword,
  };
  const formik = useFormik({
    initialValues: formInitial,
    validationSchema: isVerifyLater
      ? validationSchemaIecFormDetails
      : validationSchemaIceFormDetails,
    onSubmit: (values) => {
      handleGstInFormSubmit(values);
    },
  });
  const handleVerifyLater = () => {
    setIsIceGateVerify(false);
    setIsVerifyLater(true);
    if (formik.values.iecCode) {
      dispatch(subscriptionActions.setIceCode(formik.values.iecCode));
    }
    formik.setFieldError('iceGateUserName', '');
    formik.setFieldError('iceGatePassword', '');
  };
  const handleVerify = (formValue: {
    iceGatePassword: string;
    iceGateUserName: string;
  }) => {
    formik.validateForm();
    setIsVerifyLater(false);
    if (formValue.iceGatePassword !== '' && formValue.iceGateUserName !== '') {
      // Todo :- Need to add api here
      setIsIceGateVerify(true);
    }
  };

  const handleSaveAndSubscribeLater = async () => {
    const {productName} = productDetails;
    const data = {
      organizationDetails,
      productDetails: {
        productName,
      },
    };
    const response = await createSaveAndSubscribeLater(data);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: response?.status,
          message: response?.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
      navigate(Path.DASHBOARD);
    }
  };

  return (
    <form onSubmit={formik.handleSubmit} id='gstInForm'>
      <div className='primary-gstin-form'>
        <div className='label-field'>
          <EximTypography variant='h4' fontWeight='semi-bold'>
            1. Provide IEC Code.
            <span className='required-star'>*</span>
          </EximTypography>
          <EximInput
            id='iecCode'
            name='iecCode'
            isRequired
            placeholder='Enter IEC Code'
            value={formik.values.iecCode}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isInvalid={!!formik.errors.iecCode || false}
            errorMessage={formik.errors.iecCode ? formik.errors.iecCode : ''}
            autoComplete='on'
          />
        </div>
        <div className='label-field-verification'>
          <div className='status-button'>
            <EximTypography variant='h4' fontWeight='semi-bold'>
              2. ICE Gate Verification:
            </EximTypography>
            {isVerifyLater ? (
              <EximButton
                className='pending'
                variant='outlined'
                size='small'
                id='pending-btn'
                color='tertiary'>
                Pending
              </EximButton>
            ) : null}
            {isIceGateVerify ? (
              <EximButton
                className='success'
                variant='outlined'
                size='small'
                id='success-btn'
                color='tertiary'>
                Success
              </EximButton>
            ) : null}
          </div>
          <EximInput
            id='iceGateUserName'
            name='iceGateUserName'
            label='Username'
            placeholder='Enter Username'
            value={formik.values.iceGateUserName}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isInvalid={
              !!(
                formik.touched.iceGateUserName && formik.errors.iceGateUserName
              ) || false
            }
            errorMessage={
              formik.touched.iceGateUserName && formik.errors.iceGateUserName
                ? formik.errors.iceGateUserName
                : ''
            }
            autoComplete='off'
          />
          <EximInput
            id='iceGatePassword'
            name='iceGatePassword'
            type='password'
            label='Password'
            placeholder='Enter Password'
            passwordEyeOpenIcon={<EyeClose fill='#878787' />}
            passwordEyeCloseIcon={<EyeOpen fill='#878787' />}
            value={formik.values.iceGatePassword}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            isInvalid={
              !!(
                formik.touched.iceGatePassword && formik.errors.iceGatePassword
              ) || false
            }
            errorMessage={
              formik.touched.iceGatePassword && formik.errors.iceGatePassword
                ? formik.errors.iceGatePassword
                : ''
            }
            autoComplete='off'
          />
          <div className='api-access'>
            <EximButton
              onClick={() => handleVerify(formik.values)}
              size='small'
              disabled
              id='verify'
              color='tertiary'>
              Verify
            </EximButton>
            <EximButton
              name='verifyLater'
              color='secondary'
              id='verify-later'
              disabled={isIceGateVerify}
              onClick={handleVerifyLater}
              size='small'>
              Verify Later
            </EximButton>
          </div>
        </div>
        <div className='multi-save'>
          <EximButton
            onClick={handleSaveAndSubscribeLater}
            size='medium'
            type='button'
            id='save-subscribe-later'
            disabled={
              (!isVerifyLater && !isIceGateVerify) ||
              !!formik.errors.iecCode ||
              formik.isSubmitting
            }
            color='secondary'>
            Save & Subscribe Later
          </EximButton>
          <EximButton
            size='medium'
            type='submit'
            id='save-and-next'
            disabled={
              (!isVerifyLater && !isIceGateVerify) ||
              !!formik.errors.iecCode ||
              formik.isSubmitting
            }>
            Save & Next
          </EximButton>
        </div>
      </div>
    </form>
  );
}

export default GstInForm;
