@import '@utils/main.scss';

@mixin batch($background, $border-color, $color) {
  width: 55px;
  height: 20px;
  background-color: $background;
  box-shadow: 0px 1px 2px $alert-box-shadow;
  border: 1px solid $border-color;
  @include rfs(5px, border-radius);
  color: $color;
  font-size: $font-size-xsm;
  font-weight: $font-weight-medium;
  text-align: center;
  @include flex-item();
}

.primary-gstin-form {
  @include flex-item(column, flex-start, flex-start, wrap, 20px);
  @include margin-top(30px);
  .typography-variant-h4 {
    color: $text-color;
    @include margin(-2px 0px);
  }

  .label-field {
    @include flex-item(column, flex-start, flex-start, wrap, 15px);
    @include margin-bottom(24px);
    width: 28%;
    .required-star {
      color: $error;
    }
    @media screen and (min-width: 768px) and (max-width: 1200px) {
      width: 50%;
    }
    input {
      &::placeholder {
        color: $label-color;
        @include font-size($font-size-sm);
      }
    }
  }
  .label-field-verification {
    @include flex-item(column, flex-start, flex-start, nowrap, 45px);
    width: 24%;
    @media screen and (min-width: 768px) and (max-width: 1200px) {
      width: 45%;
    }
    .input-wrapper {
      .input-group {
        label {
          color: $text-color;
        }
      }
      .password-icon {
        top: 59%;
      }
      input {
        &::placeholder {
          color: $label-color;
          @include font-size($font-size-sm);
        }
      }
    }
    .button-wrapper {
      .base-btn {
        width: 61px;
        height: 20px;
        letter-spacing: 0;
        font-size: $font-size-sm;
        box-shadow: $alert-box-shadow;
        @include rfs(5px, border-radius);
      }
    }
    .status-button {
      @include flex-item(row, center, _, _, _);
      .pending {
        width: 61px;
        .outlined[class^='tertiary'] {
          color: $warning;
          background: $pending-background 0% 0% no-repeat padding-box;
          border: 1px solid $warning;
          @include margin-left(10px);
        }
      }
      .success {
        width: 61px;
        .outlined[class^='tertiary'] {
          color: $success-color;
          background: $success-background 0% 0% no-repeat padding-box;
          border: 1px solid $success-border;
          @include margin-left(10px);
        }
      }
    }
    .api-access {
      @include flex-item(row, flex-start, center, nowrap, 15px);
      @include margin-top(-15px);
      .button-wrapper {
        .base-btn {
          height: 34px;
        }
      }
      .contained[class^='secondary'] {
        color: $text-color;
      }
      .button-wrapper:nth-child(1) {
        width: 100px;
      }
      .button-wrapper:nth-child(2) {
        width: 120px;
      }
    }
    .error-message {
      @include font-size($font-size-sm);
    }
  }
  .multi-save {
    width: 100%;
    @include flex-item(row, flex-end, center, wrap, 15px);
    .button-wrapper {
      @include margin-top(30px);
      height: 34px;
      .base-btn {
        height: 100%;
        font-size: $font-size-sm;
        @include padding(8px 8px);
        letter-spacing: 0;
      }
    }
    .contained[class^='secondary'] {
      color: $text-color;
    }
    .button-wrapper:nth-child(1) {
      width: 167px;
    }
    .button-wrapper:nth-child(2) {
      width: 109px;
    }
  }
}
