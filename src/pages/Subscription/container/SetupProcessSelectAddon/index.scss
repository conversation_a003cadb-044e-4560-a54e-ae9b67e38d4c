@import '@utils/main.scss';

.setup-process-select-addon-page {
  width: 70%;
  margin: auto;

  .subscription-header {
    @include padding(30px 0 24px);
  }

  //Heading
  .typography-variant-h1,
  .typography-variant-h3 {
    color: $text-color;
  }

  .divider {
    hr {
      margin: 0;
    }
  }

  //Addon Card
  .add-on-heading {
    @include margin(20px 0 24px);
  }

  .add-product-btn {
    @include padding-left(16px);
    background-color: $white;

    .button-wrapper {
      .base-btn {
        padding: 0;
      }
    }
  }

  .next-step-button {
    .button-wrapper {
      width: 100px;
      float: right;
      @include margin(35px 0 70px 0);
      .base-btn {
        height: 32px;
        .btn-children {
          font-size: $font-size-sm;
        }
      }
      .unknown {
        @include margin(-16px 0 0);
      }
    }
  }
}
