import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {Path} from '@common/constants';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import AddOnCard from '@subscription/components/AddOnCard';
import SelectionDetails from '@subscription/components/SelectionDetails';
import SubscriptionPlans from '@subscription/components/SubscriptionPlans';
import {useNavigate} from 'react-router';

import './index.scss';

function SetupProcessSelectAddon() {
  const navigate = useNavigate();

  const handleNavigateToPayment = () => {
    navigate(`${Path.SUBSCRIPTION}${Path.SETUP_PROCESS_PAYMENT_DETAILS}`);
  };

  return (
    <div className='setup-process-select-addon-page'>
      <NavigationSubHeader
        hasLeftArrow
        leftArrowRoute='#'
        isNavigate
        hasTitle
        leftArrowText='Subscription Summary'
      />
      {/* Selection details */}
      <SelectionDetails />

      {/* Subscription Plans */}
      <SubscriptionPlans />

      {/* Addon cards */}
      <EximTypography classNames='add-on-heading' variant='h1'>
        Add on:
      </EximTypography>
      <AddOnCard />

      <div className='next-step-button'>
        <EximButton onClick={handleNavigateToPayment} size='small'>
          Next
        </EximButton>
      </div>
    </div>
  );
}

export default SetupProcessSelectAddon;
