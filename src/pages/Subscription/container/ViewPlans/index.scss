@import '@utils/main.scss';

.view-plans {
  width: 70%;
  min-height: 100vh;
  @include flex-item(column, _, _);
  @include margin(0 auto 20px);
  color: $text-color;
  .subscription-header {
    @include padding(30px 0 24px 0);
  }

  // Accordion
  .accordion-wrapper {
    width: 100%;
    height: 61px;
    margin: 0;
    @include margin-top(15px);
    .accordion-item {
      box-shadow: $product-subscription-box-shadow;
      .accordion-title {
        border: none;
        font-size: $font-size-lg;
        @include padding(20px 30px);
        .accordion-down-icon {
          position: static;
        }
      }
      .accordion-content {
        border: none;
        box-shadow: $product-subscription-box-shadow;
      }
    }
  }
}

.paper-wrapper-rounded {
  margin: 0;
  box-shadow: $product-subscription-box-shadow;
}
