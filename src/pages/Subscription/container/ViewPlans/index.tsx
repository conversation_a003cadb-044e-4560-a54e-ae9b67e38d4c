import NavigationSubHeader from '@common/components/NavigationSubHeader';
import EximAccordion from '@shared/components/EximAccordion';
import {SolidDownAngle} from '@shared/icons';
import {RootState} from '@store';
import PlanCard from '@subscription/components/PlanCard';
import PlanTypeCard from '@subscription/components/PlanTypeCard';
import {useSelector} from 'react-redux';

import './index.scss';

function ViewPlan() {
  const {
    subscription: {
      organizationDetails: {
        gstinDetails: {tradeName},
      },
    },
  } = useSelector((state: RootState) => state);

  return (
    <div className='view-plans'>
      <NavigationSubHeader
        hasLeftArrow
        leftArrowRoute='#'
        isNavigate
        hasTitle
        leftArrowText={`Suitable Plans for ${tradeName || '-'}`}
      />
      <PlanCard />

      <PlanTypeCard />

      <EximAccordion
        title='View Plan Details'
        content=''
        accordionIcon={<SolidDownAngle width='11px' height='7px' />}
      />
    </div>
  );
}

export default ViewPlan;
