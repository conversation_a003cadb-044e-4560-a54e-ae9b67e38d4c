import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {RootState} from '@store';
import SelectionDetails from '@subscription/components/SelectionDetails';
import SubscriptionPlans from '@subscription/components/SubscriptionPlans';
import VerifyPartner from '@subscription/components/VerifyPartner';
import {useSelector} from 'react-redux';

import './index.scss';

function SetupProcessPaymentDetails() {
  const {
    subscription: {subscriptionSummaryAddon},
  } = useSelector((state: RootState) => state);

  const isAddonAvailable: boolean =
    subscriptionSummaryAddon && subscriptionSummaryAddon.length > 0;

  return (
    <div className='setup-process-payment-details-page'>
      <NavigationSubHeader
        hasLeftArrow
        leftArrowRoute='#'
        isNavigate
        hasTitle
        leftArrowText='Subscription Summary'
      />
      {/* Selection details */}
      <SelectionDetails />

      {/* Subscription Plans */}
      <SubscriptionPlans
        addOnPlans={subscriptionSummaryAddon}
        isAddonAvailable={isAddonAvailable}
      />
      <div className='verify-partner-div'>
        <VerifyPartner />
      </div>
    </div>
  );
}

export default SetupProcessPaymentDetails;
