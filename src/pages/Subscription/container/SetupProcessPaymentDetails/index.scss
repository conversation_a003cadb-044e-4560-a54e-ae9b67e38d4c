@import '@utils/main.scss';

.setup-process-payment-details-page {
  width: 70%;
  margin: auto;
  @include margin(0 auto 32px);

  .subscription-header {
    @include padding(30px 0 24px);
  }

  //Heading
  .typography-variant-h1,
  .typography-variant-h3 {
    color: $text-color;
  }

  .divider {
    hr {
      margin: 0;
    }
  }

  //Addon Card
  .add-on-heading {
    @include margin(20px 0 24px);
  }

  .add-product-btn {
    width: 116px;
    float: right;
    @include margin(-16px 0 0);
    background-color: $primary-bg;

    .button-wrapper {
      .base-btn {
        padding: 0;
      }
    }

    .button-wrapper {
      width: 100px;
      float: right;

      .base-btn {
        height: 32px;
        @include margin(30px 0 0);
      }

      .unknown {
        @include margin(0);
      }
    }
  }

  .verify-partner-div {
    @include flex-item(_, flex-end, _, _, _);
    @include margin-top(45px);
  }
}
