import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {HelmetTitle} from '@common/constants';
import EximButton from '@shared/components/EximButton';

import BusinessDashboard from './BusinessDashboard';

export default function MyBusiness() {
  return (
    <>
      <Helmet title={HelmetTitle.BUSINESS_DASHBOARD} />
      <div className='my-business-container'>
        <NavigationSubHeader
          leftArrowRoute='#'
          hasTitle
          leftArrowText='My Business'>
          <EximButton
            color='tertiary'
            size='small'
            // TODO: We will Uncomment this line at the time of add sub organization flow.
            // disabled={paymentStatus !== 'Active Subscription'}
            onClick={() => {
              //
            }}>
            Add New Business
          </EximButton>
        </NavigationSubHeader>
        <BusinessDashboard />
      </div>
    </>
  );
}
