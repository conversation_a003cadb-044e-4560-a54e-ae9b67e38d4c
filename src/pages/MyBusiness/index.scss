@import '@utils/main.scss';

.my-business-container {
  height: calc(100vh - 200px);

  .subscription-header {
    border-bottom: 1px solid $border;
  }

  .business-dashboard-container {
    height: 100%;
    @include margin(20px);
    @include rfs(16px, border-radius);
    display: flex;
    background-color: $white;
    .sidebar {
      @include padding(20px);
      width: 25%;
      @include rfs(16px 0px 0px 16px, border-radius);
      background-color: $sidebar-color;
      .search-field {
        background-color: $white;
        @include margin-bottom(20px);
        @include rfs(10px, border-radius);
      }
    }
    .subscription-container {
      width: 75%;
      @include padding(20px);
    }
    .subscription-head-section {
      @include flex-item(_, space-between, _, _);
      @include margin(0px 0px 20px 0px);
    }
    .subscription-detail-section {
      border: 1px solid $accordion-border;
      @include flex-item(_, space-between, _, _);
      @include rfs(10px, border-radius);
      .product-list {
        width: 30%;
      }
      .subscribed-product-section {
        width: 100%;
        .subscribed-product-status {
          @include flex-item(_, space-between, _, _);
          @include padding(20px 20px 20px 20px);
          border-bottom: 1px solid $accordion-border;
          width: 100%;
          .icon-div {
            @include flex-item(_, _, center, _);
            width: 30%;
            gap: 16px;
          }
          .described-div {
            display: flex;
            flex: 1;
          }
          .description {
            font-size: $font-size-xsm;
            @include padding-right(15px);
            flex: 0.7;
          }
          .button-section {
            flex: 0.3;
            .button-wrapper {
              min-width: 160px;
            }
            .auth-pending-button {
              font-size: $font-size-xxsm;
              width: 100%;
              color: $process-color;
            }
            .auth-success-button {
              font-size: $font-size-xxsm;
              width: 100%;
              color: $success-color;
            }
            .view-dash-btn {
              background-color: $information-light;
              color: $white;
            }
          }
        }
        .subscription-details {
          @include padding(10px);
          .subscription-status {
            display: flex;
            @include margin(20px);
            .details-div {
              width: 50%;
              .sub-expiry-section {
                @include flex-item(_, _, center, _);
                .upgrade-btn {
                  @include margin(0);
                  @include padding-left(10);
                  div {
                    text-decoration: underline;
                  }
                }
              }
              .label {
                font-size: $font-size-xsm;
                color: $tertiary;
              }
              .value {
                font-size: $font-size-xsm;
              }
              .active {
                color: $success-color;
              }
              .danger {
                color: $error-color;
              }
              .info {
                color: $information;
              }
            }
          }
        }
        .not-subscribed-data {
          @include padding(250px);
          .subscribe-button button {
            width: 100%;
          }
        }
      }
    }
  }
}
