import Helmet from '@common/components/utils/Helmet';
import {
  EximProducts,
  HelmetTitle,
  Path,
  SubscriptionStatus,
} from '@common/constants';
import {searchTableData} from '@common/helpers';
import {
  IBusinessData,
  ICommonScrollableData,
  IProductData,
} from '@common/interfaces';
import {TextField} from '@mui/material';
import {dashboardActions} from '@pages/Dashboard/store/reducer';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import {moowrActions} from '@pages/Moowr/store/reduce';
import {profileActions} from '@pages/Profile/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {EyeOpen} from '@shared/icons';
import {DataExtractorIcon} from '@shared/icons/DataExtractorIcon';
import {DutyDrawbackIcon} from '@shared/icons/DutyDrawbackIcon';
import {MoowrIcon} from '@shared/icons/MoowrIcon';
import {dispatch} from '@store';
import {getBusinessDetails, getSubscriptionDetails} from '@subscription/api';
import DashboardBusinessDetails from '@subscription/common/DashboardBusinessDetails';
import {subscriptionActions} from '@subscription/store/reducer';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router';

import './index.scss';

export default function BusinessDashboard() {
  const navigate = useNavigate();

  const reformBusinessData = (data: IBusinessData[]) => {
    const sortedData = data.sort((a, b) => {
      return (b.isPrimary ? 1 : 0) - (a.isPrimary ? 1 : 0);
    });
    return sortedData.map((record) => ({
      title: record.organizationName,
      value: record.pan,
      orgId: record.orgId,
      isPrimary: record.isPrimary,
      status: '',
      disabled: false,
    }));
  };

  const reformProductData = (data: IProductData[]) => {
    return data.map((record) => ({
      title: record.productName,
      value: record.status,
      subscriptionId: record.subscriptionId,
      businessUnitSubscribed: record.businessUnitSubscribed,
      availableSubscriptionCount: record.availableSubscriptionCount,
      disabled: false,
    }));
  };

  const [productDetails, setProductDetails] = useState<ICommonScrollableData[]>(
    []
  );

  const [subscriptionData, setSubscriptionData] =
    useState<ICommonScrollableData>();
  const [businessDetails, setBusinessDetails] = useState<
    ICommonScrollableData[]
  >([]);
  const [searchBusinessDetails, setSearchBusinessDetails] = useState<
    ICommonScrollableData[]
  >([]);
  const [currentPan, setCurrentPan] = useState<string>('');
  const [selectedProduct, setSelectedProduct] = useState<string>('');
  const [primaryPanNo, setPrimaryPanNo] = useState<string>('');
  const [organizationName, setOrganizationName] = useState<string>('');
  const [isOpenAuthModal, setIsOpenAuthModal] = useState(false);
  const [isAuthenticate, setAuthentication] = useState(false);
  const [isPrimaryTin, setIsPrimaryTin] = useState(false);
  const [isOpenUpgradeModal, setIsOpenUpgradeModal] = useState(false);

  const getSubscribedData = async (
    value: string,
    product: string,
    pan: string
  ) => {
    const payload = {
      pan,
      primaryPan: primaryPanNo || pan,
      subscriptionId: value,
      product,
    };
    if (product !== '') {
      setSelectedProduct(product);
    }
    if (value === undefined) {
      payload.subscriptionId = '';
    }
    const data = await getSubscriptionDetails(payload);
    setSubscriptionData(data.data[0]);
    dispatch(
      subscriptionActions.setPartnerDetails({
        companyName: '',
        partnerCode: data?.data[0]?.partnerCode,
      })
    );
    dispatch(
      subscriptionActions.setAvailableSubscriptionCount(
        data.data[0]?.availableSubscriptionCount
      )
    );
    dispatch(
      subscriptionActions.setBusinessUnitSubscribed(
        data.data[0].businessUnitSubscribed
      )
    );
    dispatch(dashboardActions.setSelectedIECNumber(data.data[0].iecCode));
    if (data.data[0].productName === EximProducts.DATA_EXTRACTOR) {
      dispatch(dutyDrawbackActions.setIecNumber(data.data[0].iecCode));
    }

    if (data.data[0].productName === EximProducts.MOOWR) {
      dispatch(moowrActions.setIecNumber(data.data[0].iecCode));
    }

    if (primaryPanNo === '') {
      dispatch(
        subscriptionActions.setSubscriptionId(data.data[0]?.subscriptionId)
      );
      dispatch(subscriptionActions.setPaymentStatus(data.data[0]?.status));
    }
  };

  const getPrimarySubscriptionData = async (
    pan: string,
    orgName: string,
    orgId: number,
    isPrimary: boolean
  ) => {
    const payload = {
      pan,
      primaryOrg: primaryPanNo || pan,
      subscriptionId: '',
      product: '',
    };
    if (!isPrimary) {
      payload.primaryOrg = '';
    }
    dispatch(subscriptionActions.setPanNumber(pan));
    dispatch(subscriptionActions.setSelectedBusiness(orgName));
    dispatch(subscriptionActions.setSelectedOrgId(orgId));
    setCurrentPan(pan);
    if (orgName !== '') {
      setOrganizationName(orgName);
    }
    setProductDetails([]);
    const tinData = await getSubscriptionDetails(payload);
    const productData = reformProductData(tinData.data);

    setProductDetails(productData);

    const productDataStatus = productData[0];

    getSubscribedData(
      productDataStatus?.subscriptionId || '',
      productDataStatus?.title || '',
      pan
    );
  };

  const getTinList = useCallback(async () => {
    // TODO: At UI side there is no any pagination flow but we have handle this at backend side.
    const payload = {
      pageNo: 0,
      limit: 20,
    };
    const data = await getBusinessDetails(payload);
    const response = reformBusinessData(data.data.records);
    setBusinessDetails(response);
    setSearchBusinessDetails(response);
    const primaryRecord = response.find((record) => record.isPrimary === true);
    if (primaryRecord !== undefined) {
      setPrimaryPanNo(primaryRecord?.value || '');
      dispatch(subscriptionActions.setPrimaryPan(primaryRecord?.value || ''));
      setCurrentPan(primaryRecord?.value || '');
      dispatch(profileActions.setShouldCompanyProfileShow(true));
      dispatch(
        dashboardActions.setSelectedPanNumber(primaryRecord?.value || '')
      );
      dispatch(profileActions.setProfilePan(primaryRecord?.value || ''));

      dispatch(
        dashboardActions.setSelectedPanNumber(primaryRecord?.value || '')
      );

      setOrganizationName(primaryRecord?.title || '');
      dispatch(
        dashboardActions.setSelectedBusinessName(primaryRecord?.title || '')
      );
      dispatch(
        dataExtractorActions.setBusinessName(primaryRecord?.title || '')
      );
      dispatch(dutyDrawbackActions.setBusinessName(primaryRecord?.title || ''));
      dispatch(moowrActions.setBusinessName(primaryRecord?.title || ''));
      getPrimarySubscriptionData(
        primaryRecord?.value || '',
        primaryRecord?.title || '',
        primaryRecord?.orgId,
        primaryRecord?.isPrimary
      );
      setIsPrimaryTin(primaryRecord?.isPrimary);
    } else {
      const secondaryData = response.find(
        (record) => record.isPrimary === false
      );

      setCurrentPan(secondaryData?.value || '');
      dispatch(profileActions.setShouldCompanyProfileShow(true));
      dispatch(
        dashboardActions.setSelectedPanNumber(secondaryData?.value || '')
      );
      dispatch(profileActions.setProfilePan(secondaryData?.value || ''));

      dispatch(
        dashboardActions.setSelectedPanNumber(secondaryData?.value || '')
      );

      setOrganizationName(secondaryData?.title || '');
      dispatch(
        dashboardActions.setSelectedBusinessName(secondaryData?.title || '')
      );
      dispatch(
        dataExtractorActions.setBusinessName(secondaryData?.title || '')
      );
      dispatch(dutyDrawbackActions.setBusinessName(secondaryData?.title || ''));
      dispatch(moowrActions.setBusinessName(secondaryData?.title || ''));
      getPrimarySubscriptionData(
        secondaryData?.value || '',
        secondaryData?.title || '',
        secondaryData?.orgId || 0,
        secondaryData?.isPrimary || false
      );
      setIsPrimaryTin(secondaryData?.isPrimary || false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getSubscriptionStatus = (status: string) => {
    if (status === SubscriptionStatus.PAID) {
      return 'active';
    }
    if (
      status === SubscriptionStatus.EXPIRED ||
      status === SubscriptionStatus.INACTIVE
    ) {
      return 'danger';
    }
    return 'info';
  };

  const getProductIcon = useCallback((product: string) => {
    switch (product) {
      case EximProducts.DATA_EXTRACTOR:
        return <DataExtractorIcon width={60} height={60} />;
      case EximProducts.DUTY_DRAWBACK:
        return <DutyDrawbackIcon width={60} height={60} />;
      case EximProducts.MOOWR:
        return <MoowrIcon width={60} height={60} />;
      case EximProducts.EBRC:
        return <DataExtractorIcon width={60} height={60} />;
      default:
        return '';
    }
  }, []);

  const translateSubscriptionName = (status: string) => {
    if (status === SubscriptionStatus.PAID) {
      return 'Active Subscription';
    }
    if (status === SubscriptionStatus.FREE) {
      return 'FREE';
    }
    if (status === SubscriptionStatus.PENDING_PAYMENT) {
      return 'Pending Payment';
    }
    if (status === SubscriptionStatus.PENDING_ACTIVATION) {
      return 'Pending Activation';
    }
    if (status === SubscriptionStatus.EXPIRED) {
      return 'Expired';
    }
    if (status === SubscriptionStatus.NON_SUBSCRIBED) {
      return 'No Subscribed';
    }
    if (status === SubscriptionStatus.INACTIVE) {
      return 'Inactive';
    }
    if (status === SubscriptionStatus.PAYMENT_COMPLETED) {
      return 'Payment Completed';
    }
    return '';
  };

  const renderOnEximDashboard = () => {
    if (selectedProduct !== '') {
      if (selectedProduct === EximProducts.DATA_EXTRACTOR) {
        dispatch(dataExtractorActions.setPanNumber(currentPan || ''));
        dispatch(
          profileActions.setSelectedProductName(EximProducts.DATA_EXTRACTOR)
        );
        navigate(Path.DATA_EXTRACTOR);
      }
      if (selectedProduct === EximProducts.DUTY_DRAWBACK) {
        dispatch(dutyDrawbackActions.setPanNumber(currentPan));
        dispatch(dutyDrawbackActions.setIecNumber('FreePlan12'));
        dispatch(
          profileActions.setSelectedProductName(EximProducts.DUTY_DRAWBACK)
        );
        navigate(Path.DUTY_DRAWBACK);
      }
      if (selectedProduct === EximProducts.MOOWR) {
        dispatch(moowrActions.setPanNumber(currentPan));
        dispatch(moowrActions.setIecNumber('FreePlan12'));
        dispatch(profileActions.setSelectedProductName(EximProducts.MOOWR));
        navigate(Path.MOOWR);
      }
      if (selectedProduct === EximProducts.EBRC) {
        dispatch(profileActions.setSelectedProductName(EximProducts.EBRC));
        navigate(Path.EBRC);
      }
    }
  };

  const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
    const searchData = searchTableData(
      searchBusinessDetails as [],
      e.target.value
    );
    setBusinessDetails(searchData);
  };

  useEffect(() => {
    getTinList();
  }, [getTinList]);

  return (
    <>
      <Helmet title={HelmetTitle.DASHBOARD} />
      <div className='business-dashboard-container'>
        <div className='sidebar'>
          <div className='search-field'>
            <TextField
              id='searchBusiness'
              className='common-input'
              fullWidth
              onChange={handleSearch}
              label='Search'
              size='small'
              name='searchBusiness'
            />
          </div>
          <DashboardBusinessDetails
            options={businessDetails}
            onSelect={(value: string, title: string, tinId: number) => {
              getPrimarySubscriptionData(value, title, tinId, isPrimaryTin);
            }}
            productData={false}
            selectedData={currentPan}
          />
        </div>
        <div className='subscription-container'>
          <div className='subscription-head-section'>
            <EximTypography variant='h5'>{organizationName}</EximTypography>
            <EximTypography variant='h5'>PAN : {currentPan}</EximTypography>
          </div>
          <div className='subscription-detail-section'>
            <div className='product-list'>
              <DashboardBusinessDetails
                options={productDetails}
                onSelect={(value: string, orgName: string) =>
                  getSubscribedData(value, orgName, currentPan)
                }
                productData
                selectedData={subscriptionData?.productName || ''}
              />
            </div>
            <div className='subscribed-product-section'>
              {subscriptionData?.status === 'Not Available in your country' ? (
                <div
                  className='not-subscribed-data'
                  style={{textAlign: 'center'}}>
                  <EximTypography variant='h4'>UnSubscibed</EximTypography>
                  <div className='subscribe-button'>
                    <EximButton variant='text' type='reset' size='small'>
                      Subscribed Now
                    </EximButton>
                  </div>
                </div>
              ) : (
                <div>
                  <div className='subscribed-product-status'>
                    <div className='icon-div'>
                      {getProductIcon(subscriptionData?.productName || '')}
                      <EximTypography variant='h1' fontWeight='bold'>
                        {subscriptionData?.productName}
                      </EximTypography>
                    </div>
                    <div className='described-div'>
                      <div className='description'>
                        <p>
                          streamline invoicing, ensure compliance, and boost
                          efficiency for seamless financial operations.
                        </p>
                      </div>

                      <div className='button-section'>
                        <EximButton
                          size='small'
                          color='secondary'
                          className='view-dash-btn'
                          disabled={
                            subscriptionData?.subscriptionStatus !==
                              SubscriptionStatus.PAID &&
                            subscriptionData?.subscriptionStatus !==
                              SubscriptionStatus.FREE
                          }
                          onClick={() => {
                            renderOnEximDashboard();
                          }}>
                          <EyeOpen fill='#fff' />
                          View Dashboard
                        </EximButton>
                        {isAuthenticate ? (
                          <EximButton
                            className='auth-success-button'
                            variant='text'
                            disabled
                            size='small'>
                            Authenticate
                          </EximButton>
                        ) : (
                          <EximButton
                            className='auth-pending-button'
                            variant='text'
                            type='submit'
                            size='small'
                            onClick={() => setIsOpenAuthModal(true)}>
                            Authentication Pending
                          </EximButton>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className='subscription-details'>
                    <div className='subscription-status'>
                      <div className='details-div'>
                        <p className='label'>Subscription Status</p>
                        <div className='sub-expiry-section'>
                          <p
                            className={`value ${getSubscriptionStatus(
                              subscriptionData?.subscriptionStatus || ''
                            )}`}>
                            {translateSubscriptionName(
                              subscriptionData?.subscriptionStatus || ''
                            )}
                          </p>
                        </div>
                      </div>
                      <div className='details-div'>
                        <p className='label'>Subscription Expiry Date</p>
                        <div className='sub-expiry-section'>
                          <p className='value danger'>
                            {subscriptionData?.subscriptionEndDate}
                          </p>
                          {subscriptionData?.status ===
                          SubscriptionStatus.FREE ? (
                            <EximButton
                              variant='text'
                              className='value upgrade-btn'
                              onClick={() => setIsOpenUpgradeModal(true)}
                              type='submit'
                              size='small'>
                              Upgrade
                            </EximButton>
                          ) : null}
                          {subscriptionData?.subscriptionStatus ===
                          SubscriptionStatus.EXPIRED ? (
                            <EximButton
                              variant='text'
                              className='value upgrade-btn'
                              onClick={() => setIsOpenUpgradeModal(true)}
                              type='submit'
                              size='small'>
                              Renew
                            </EximButton>
                          ) : null}
                        </div>
                      </div>
                    </div>
                    <div className='subscription-status'>
                      <div className='details-div'>
                        <p className='label'>Business Unit Subscribed</p>
                        <p className='value info'>
                          {subscriptionData?.businessUnitSubscribed}
                        </p>
                      </div>
                      <div className='details-div'>
                        <p className='label'>Available Subscription Count</p>
                        <p className='value info'>
                          {subscriptionData?.availableSubscriptionCount}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
