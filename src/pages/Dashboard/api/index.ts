import {Api, ApiAction, ApiVersion} from '@common/constants';
import {get, post} from '@core/api/axios';
import {AxiosResponse} from 'axios';

const {EXIM_SUBSCRIPTION_SERVICE, SUBSCRIPTIONS, ALL, ACTIVE_GSTIN} = Api;

// INFO: Below URL is common for all the API in entire file
const COMMON_BASE_URL = `${EXIM_SUBSCRIPTION_SERVICE}${ApiVersion.V1}`;

export const getProductsData = async () => {
  const response = await post(
    `${COMMON_BASE_URL}${SUBSCRIPTIONS}${ACTIVE_GSTIN}`,
    {},
    {
      headers: {
        action: ApiAction.ACTIVE_GSTIN,
      },
    }
  );
  return response as AxiosResponse;
};

export const getSubscription = async (productName: string) => {
  const response = await get(
    `${COMMON_BASE_URL}${SUBSCRIPTIONS}?productName=${productName}`,
    {
      headers: {
        action: ApiAction.GET_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const getAllSubscription = async () => {
  const response = await get(`${COMMON_BASE_URL}${SUBSCRIPTIONS}${ALL}`, {
    headers: {
      action: ApiAction.GET_SUB,
    },
  });
  return response as AxiosResponse;
};
