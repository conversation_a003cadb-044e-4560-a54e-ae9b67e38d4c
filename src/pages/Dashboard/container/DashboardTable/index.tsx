import {EximProducts} from '@common/constants';
import DataExtractorTable from '@pages/Dashboard/components/DataExtractorTable';
import DutyDrawbackTable from '@pages/Dashboard/components/DutyDrawbackTable';
import MoowrTable from '@pages/Dashboard/components/MoowrTable';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function DashboardTable() {
  const {dashboardActiveProduct} = useSelector(
    (state: RootState) => state.dashboard
  );

  const getDashboardTable = useCallback(() => {
    switch (dashboardActiveProduct) {
      case EximProducts.DATA_EXTRACTOR:
        return <DataExtractorTable />;
      case EximProducts.DUTY_DRAWBACK:
        return <DutyDrawbackTable />;
      case EximProducts.MOOWR:
        return <MoowrTable />;
      default:
        return null;
    }
  }, [dashboardActiveProduct]);

  return (
    <div className='dashboard-table-container'>
      <EximPaper>
        <EximTypography
          variant='h3'
          fontWeight='semi-bold'
          classNames='table-title'>
          Businesses Under {dashboardActiveProduct}
        </EximTypography>

        {/* Product Table Data */}
        {getDashboardTable()}
      </EximPaper>
    </div>
  );
}

export default DashboardTable;
