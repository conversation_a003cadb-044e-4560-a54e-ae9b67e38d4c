import Helmet from '@common/components/utils/Helmet';
import {
  EximProducts,
  HelmetTitle,
  Path,
  ResponseStatus,
} from '@common/constants';
import {profileActions} from '@pages/Profile/store/reducer';
import EximButton from '@shared/components/EximButton';
import {Plus} from '@shared/icons';
import {dispatch} from '@store';
import {
  emptyGstInDetails,
  subscriptionActions,
} from '@subscription/store/reducer';
import {useEffect} from 'react';
import {Link} from 'react-router-dom';

import {getProductsData} from '../api';
import ProductCarousel from '../components/ProductCarousel';
import {dashboardActions} from '../store/reducer';
import DashboardTable from './DashboardTable';
import './index.scss';

function Dashboard() {
  useEffect(() => {
    (async () => {
      const response = await getProductsData();
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        // getting keys in array from product enum to align the product in sequence
        const productNames = Object.keys(EximProducts);
        const eximProductsArr = [];
        for (let i = 0; i < productNames.length; i += 1) {
          const currentProduct =
            EximProducts[productNames[i] as keyof typeof EximProducts];
          eximProductsArr.push({
            ...response?.data[currentProduct],
            productName: currentProduct,
          });
        }
        dispatch(dashboardActions.setEximProduct(eximProductsArr));
      }
    })();

    // Resetting the values on dashboard
    dispatch(subscriptionActions.resetAllDetails());
    dispatch(subscriptionActions.getGstInDetails(emptyGstInDetails));
    dispatch(
      subscriptionActions.setExistingPlanDetails({
        planName: '',
        paymentStatus: '',
      })
    );
    dispatch(subscriptionActions.setIsRenewOrUpgradePlan(false));
    // INFO: Resetting the value on dashboard
    dispatch(profileActions.setShouldCompanyProfileShow(false));
    dispatch(profileActions.setSelectedProductName(''));
  }, []);

  return (
    <>
      <Helmet title={HelmetTitle.DASHBOARD} />
      <div className='dashboard-wrapper'>
        <section className='business-container'>
          <div className='business-title'>
            <p data-testid='business-title'>EXIM Dashboard</p>
          </div>
          <div className='business-add-button'>
            <Link to={`${Path.SUBSCRIPTION}`}>
              <EximButton color='tertiary'>
                <Plus fill='#fff' height={12} width={12} />
                Add New Business
              </EximButton>
            </Link>
          </div>
        </section>
        <p className='product-head'>Products</p>
        <ProductCarousel />

        <DashboardTable />
      </div>
    </>
  );
}

export default Dashboard;
