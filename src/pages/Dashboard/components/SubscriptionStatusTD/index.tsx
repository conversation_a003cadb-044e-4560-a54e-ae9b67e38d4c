import {Path, SubscriptionStatus} from '@common/constants';
import {formatDate, getPaymentStatus, getStatusLink} from '@common/helpers';
import {ISubscriptionData} from '@common/interfaces';
import EximLink from '@shared/components/EximLink';
import {RootState, dispatch} from '@store';
import {subscriptionActions} from '@subscription/store/reducer';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const PLAN_DETAILS = {
  'Free Plan': {
    code: 'FREE',
    name: 'Basic Plan',
  },
  'Basic Plan': {
    code: 'DEX-BASIC-PLN',
    name: 'Basic Plan',
  },
  'Advance Plan': {
    code: 'DEX-ADV-PLN',
    name: 'Advance Plan',
  },
};

interface IStatusProps {
  subscriptionData: ISubscriptionData;
}
const {FREE, PAID, EXPIRED} = SubscriptionStatus;

function SubscriptionStatusTD({subscriptionData}: IStatusProps) {
  const navigate = useNavigate();
  const {paymentStatus, planName, endDate, iecCode, pan, businessName} =
    subscriptionData;

  const {
    dashboard: {dashboardActiveProduct},
    subscription: {
      productDetails: {planDetails},
    },
  } = useSelector((state: RootState) => state);

  const handleRedirect = () => {
    dispatch(subscriptionActions.setIceCode(iecCode));
    dispatch(
      subscriptionActions.addPanNumberAndOrgName({
        panId: pan,
        lgnm: businessName,
      })
    );
    dispatch(
      subscriptionActions.addGstDetails({
        gstin: '',
        legalName: businessName,
        tradeName: businessName,
      })
    );
    dispatch(
      subscriptionActions.setSubscriptionProduct(dashboardActiveProduct)
    );
    dispatch(
      subscriptionActions.setSubscriptionPlanData({
        ...planDetails,
        ...PLAN_DETAILS[planName as keyof typeof PLAN_DETAILS],
        dashboardActiveProduct,
      })
    );
    dispatch(
      subscriptionActions.setExistingPlanDetails({planName, paymentStatus})
    );
    // INFO: Need to call renew subscription API for renew and upgrade plan
    if (paymentStatus === EXPIRED) {
      // INFO: Redirect to renew plan
      navigate(`${Path.SUBSCRIPTION}${Path.SETUP_PROCESS_PAYMENT_DETAILS}`);
      dispatch(subscriptionActions.setIsRenewOrUpgradePlan(true));
    } else {
      // INFO: Redirect to subscribe and upgrade plan
      navigate(`${Path.SUBSCRIPTION}${Path.VIEW_PLAN}`);
      if (paymentStatus === SubscriptionStatus.FREE) {
        dispatch(subscriptionActions.setIsRenewOrUpgradePlan(true));
      }
    }
  };

  return (
    <div className='subscription-status-container'>
      {getPaymentStatus(paymentStatus) ? (
        <span
          className={`plan-status ${getPaymentStatus(
            paymentStatus
          )?.toLowerCase()}`}>
          {paymentStatus === FREE ? planName : getPaymentStatus(paymentStatus)}
        </span>
      ) : null}

      {/* TODO: No need to show the subscribe now, upgrade now and renew link */}
      {/* <span className='status-link'>
        <EximLink href='' variantColor='information' onClick={handleRedirect}>
          {owner ? getStatusLink(paymentStatus) : null}
        </EximLink>
      </span> */}

      {planName && paymentStatus === PAID ? (
        <span className='plan-name'>
          {planName}
          <span className='arrow-left' />
        </span>
      ) : null}

      {(paymentStatus === PAID || paymentStatus === FREE) && endDate ? (
        <h5 className='expiring-date'>
          {`Expiring on ${formatDate(endDate)}`}
        </h5>
      ) : null}
    </div>
  );
}

export default SubscriptionStatusTD;
