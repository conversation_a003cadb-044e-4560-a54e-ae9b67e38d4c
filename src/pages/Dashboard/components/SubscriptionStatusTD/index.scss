@import '@utils/main.scss';

.subscription-status-container {
  .expiring-date {
    color: $label-color;
    font-weight: normal;
    @include margin-top(6px);
  }

  .status-link {
    font-size: $font-size-sm;
    color: $information;
    a {
      text-decoration: underline;
      padding: 0;
      &:before {
        height: 2px;
        bottom: 0;
        top: 20px;
      }
    }
  }

  .plan-status {
    @include margin-right(12px);
    @include padding(2px 5px);
    @include rfs(5px, border-radius);
    color: $white;
    font-size: $font-size-xsm;
  }

  .subscribed {
    background-color: $warning;
    border: solid 1px $warning;
  }

  .active,
  .pending {
    background-color: $exim-success-background;
    border: solid 1px $exim-success-background;
  }

  .expired {
    background-color: $error;
    border: solid 1px $error;
  }

  .free {
    background-color: $information-light;
    border: solid 1px $information-light;
  }

  .plan-name {
    position: relative;
    bottom: 1px;
    font-size: $font-size-xsm;
    @include padding(1px 20px 1px 4px);
    font-family: sans-serif;
    color: $table-head-primary;
    background-color: $primary-border-light;
    border: 1.5px solid $primary-border;
    @include rfs(5px, border-radius);
    border-right: 0px;

    .arrow-left {
      width: 0;
      height: 0;
      right: 0px;
      top: -1px;
      position: absolute;
      border-top: 9px solid transparent;
      border-bottom: 9px solid transparent;
      border-right: 13px solid $white;
    }
  }
}
