@import '~slick-carousel/slick/slick.css';
@import '~slick-carousel/slick/slick-theme.css';
@import 'src/utils/main.scss';

.exim-products {
  .slick-track {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  button {
    background-color: transparent;
    border: none;
    outline: none;
  }
  .rotate-90deg {
    & > svg {
      transform: rotate(90deg);
    }
  }
  .rotate-275deg {
    & > svg {
      transform: rotate(275deg);
    }
  }

  .select {
    .product-card-wrapper:not(.product-card-wrapper .product-card-wrapper) {
      border-bottom: 3px solid #e87b56;
    }
  }
  .disabled-product {
    pointer-events: none;
    opacity: 0.6;
  }
}

.slick-slider {
  .slick-prev {
    left: 0;
  }
  .slick-next {
    right: 0;
  }
  .slick-prev,
  .slick-next {
    transform: none;
    padding: 0;
    height: 50%;
    top: 30px;
  }

  .slick-list {
    @include margin(0 15px 0 15px);
  }
  .slick-slide {
    @include padding(5px);
  }
  .slick-disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
.product-card-wrapper {
  @include margin-left(10px);
  cursor: pointer;
}
