import {fireEvent, render, waitFor} from '@testing-library/react';
import {act} from 'react-dom/test-utils';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';
import configureStore from 'redux-mock-store';

import EximProductCarousel from '.';
import mockEximProduct from '../../mock/mockEximProducts.json';

const mockStore = configureStore([]);
const initialState = {
  dashboard: {
    eximProducts: mockEximProduct,
  },
};
describe('EximProductCarousel', () => {
  it('Should render EximProductCarousel', () => {
    const mockedStore = mockStore(initialState);

    const {getByTestId} = render(
      <Provider store={mockedStore}>
        <BrowserRouter>
          <EximProductCarousel />
        </BrowserRouter>
      </Provider>
    );
    const productCarousel = getByTestId('exim-product-carousel');
    expect(productCarousel).toBeInTheDocument();
  });

  it('Should click on product', async () => {
    const mockedStore = mockStore(initialState);

    const {getAllByRole} = render(
      <Provider store={mockedStore}>
        <BrowserRouter>
          <EximProductCarousel />
        </BrowserRouter>
      </Provider>
    );
    const products = getAllByRole('button');

    act(() => {
      fireEvent.click(products[1]);
    });
    waitFor(() => expect(products[1].className).toBe('select'));
  });
});
