import EximProductCard from '@shared/components/EximProductCard';
import {SolidDownAngle} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {subscriptionActions} from '@subscription/store/reducer';
import {useState} from 'react';
import {useSelector} from 'react-redux';
import Slider, {CustomArrowProps} from 'react-slick';

import {dashboardActions} from '../../store/reducer';
import {disabledProducts, getImage} from '../../utils';
import './index.scss';

function NextArrow({
  currentSlide,
  slideCount,
  className,
  onClick,
  style,
}: CustomArrowProps) {
  return (
    <div
      role='button'
      tabIndex={0}
      className={className}
      onClick={onClick}
      onKeyDown={() => {
        /* */
      }}
      style={style}>
      <span className='rotate-275deg'>
        <SolidDownAngle />
      </span>
    </div>
  );
}
function PrevArrow({
  currentSlide,
  slideCount,
  className,
  onClick,
  style,
}: CustomArrowProps) {
  return (
    <div
      role='button'
      tabIndex={0}
      className={className}
      onClick={onClick}
      onKeyDown={() => {
        /* */
      }}
      style={style}>
      <span className='rotate-90deg'>
        <SolidDownAngle />
      </span>
    </div>
  );
}

function ProductCarousel() {
  const {eximProducts, dashboardActiveProduct} = useSelector(
    (state: RootState) => state.dashboard
  );
  const [product, setProduct] = useState<string>(dashboardActiveProduct);

  const handleClick = (productName: string) => {
    if (disabledProducts.includes(productName)) return;
    dispatch(dashboardActions.setDashboardActiveProduct(productName));
    dispatch(subscriptionActions.setSubscriptionProduct(productName));
    setProduct(productName);
  };
  const handleClasses = (productName: string) => {
    let classes = '';
    if (productName === product) {
      classes += 'select';
    }
    if (disabledProducts.includes(productName)) {
      classes += ' disabled-product';
    }
    return classes;
  };

  return (
    <div className='exim-products' data-testid='exim-product-carousel'>
      <Slider
        slidesToShow={4}
        slidesToScroll={1}
        adaptiveHeight
        variableWidth
        initialSlide={0}
        infinite
        prevArrow={<PrevArrow />}
        nextArrow={<NextArrow />}>
        {eximProducts.map((productItem) => (
          <button
            type='button'
            className={handleClasses(productItem.productName)}
            onClick={() => handleClick(productItem.productName)}
            key={`product${productItem.productName}`}
            data-testid='product-btn'>
            <EximProductCard
              activeCount={productItem.activeGstins}
              inactiveCount={productItem.inactiveGstins}
              totalIEC={productItem.totalGstins}
              title={productItem.productName}
              icon={getImage(productItem.productName)}
            />
          </button>
        ))}
      </Slider>
    </div>
  );
}

export default ProductCarousel;
