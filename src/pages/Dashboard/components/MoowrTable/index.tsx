import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {EximProducts, Path, SubscriptionStatus} from '@common/constants';
import {searchTableData} from '@common/helpers';
import {ISubscriptionData} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getSubscription} from '@pages/Dashboard/api';
import {dashboardActions} from '@pages/Dashboard/store/reducer';
import {
  DASHBOARD_DUTY_DRAWBACK_TABLE_HEADER,
  SHOW_ENTRIES,
} from '@pages/Dashboard/utils';
import {moowrActions} from '@pages/Moowr/store/reduce';
import {profileActions} from '@pages/Profile/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {dispatch} from '@store';
import {ChangeEvent, useEffect, useState} from 'react';
import {useNavigate} from 'react-router';

import SubscriptionStatusTD from '../SubscriptionStatusTD';
import './index.scss';

const {FREE, PAID} = SubscriptionStatus;

function MoowrTable() {
  const navigate = useNavigate();

  const [page, setPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [subscriptionData, setSubscriptionData] = useState<ISubscriptionData[]>(
    []
  );
  const [renderData, setRenderData] = useState<ISubscriptionData[]>([]);
  const [searchData, setSearchData] = useState<ISubscriptionData[]>([]);

  const handlePageChange = (pageNumber: string | number) => {
    setPage(Number(pageNumber));
    if (searchQuery.length === 0) {
      const showDataInTable = subscriptionData.slice(
        (Number(pageNumber) - 1) * Number(showEntries),
        Number(showEntries) * Number(pageNumber)
      );
      setRenderData(showDataInTable);
    } else {
      const showDataInTable = searchData.slice(
        (Number(pageNumber) - 1) * Number(showEntries),
        Number(showEntries) * Number(pageNumber)
      );
      setRenderData(showDataInTable);
    }
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
    if (showEntries === 'all' && searchQuery.length === 0)
      setRenderData(subscriptionData);
    else {
      const currentData = subscriptionData.slice(0, +showEntries);
      setRenderData(currentData);
    }
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  const handleProcess = (
    pan: string,
    businessName: string,
    iecNumber: string
  ) => {
    dispatch(moowrActions.setPanNumber(pan));
    dispatch(moowrActions.setBusinessName(businessName));
    dispatch(moowrActions.setIecNumber(iecNumber));

    dispatch(dashboardActions.setSelectedBusinessName(businessName));
    dispatch(dashboardActions.setSelectedPanNumber(pan));
    dispatch(dashboardActions.setSelectedIECNumber(iecNumber));

    dispatch(profileActions.setProfilePan(pan));
    dispatch(profileActions.setShouldCompanyProfileShow(true));
    dispatch(profileActions.setSelectedProductName(EximProducts.MOOWR));
    navigate(Path.MOOWR);
  };
  useEffect(() => {
    if (searchQuery.length > 0 && subscriptionData.length > 0) {
      const searchCurrentData = searchTableData(
        subscriptionData as [],
        searchQuery
      );
      setSearchData(searchCurrentData);
      if (showEntries === 'all') setRenderData(searchCurrentData);
      else {
        const currentData = searchCurrentData.slice(0, +showEntries);
        setRenderData(currentData);
      }
    } else if (showEntries === 'all' && searchQuery.length === 0)
      setRenderData(subscriptionData);
    else {
      const currentData = subscriptionData.slice(0, +showEntries);
      setRenderData(currentData);
    }
  }, [searchQuery, showEntries, subscriptionData, setRenderData]);

  useEffect(() => {
    (async () => {
      const {data} = await getSubscription(EximProducts.MOOWR);
      setSubscriptionData(data?.data ?? []);
    })();
  }, []);

  return (
    <div className='moowr-db-table-container'>
      <TableSearchFilter
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}
        options={SHOW_ENTRIES}
      />
      <table className='moowr-db-table'>
        <TableHeader mainHeader={DASHBOARD_DUTY_DRAWBACK_TABLE_HEADER} />

        {renderData?.length > 0 ? (
          <TableBody className='moowr-db-tbody'>
            {renderData?.map((subscription, index) => {
              const {businessName, pan, iecCode, unit, paymentStatus} =
                subscription;

              return (
                <TableRow key={`dashboardTable${index + 1}`}>
                  <TableCell>
                    <EximTypography fontWeight='semi-bold'>
                      {businessName}
                    </EximTypography>
                  </TableCell>
                  <TableCell>{pan}</TableCell>
                  <TableCell>{iecCode}</TableCell>
                  <TableCell>{unit ?? '1'}</TableCell>
                  <TableCell>
                    <SubscriptionStatusTD subscriptionData={subscription} />
                  </TableCell>
                  <TableCell>
                    <EximButton
                      size='small'
                      // TODO: Need to uncomment below code when we have MOOWR subscription
                      // disabled={
                      //   paymentStatus !== FREE && paymentStatus !== PAID
                      // }
                      onClick={() => handleProcess(pan, businessName, iecCode)}>
                      Process
                    </EximButton>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        ) : (
          <EmptyTable
            colSpan={DASHBOARD_DUTY_DRAWBACK_TABLE_HEADER.length}
            isNoSubscription
          />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchQuery}
        showEntries={showEntries}
        totalRecords={subscriptionData.length}
        searchData={searchData as []}
        renderData={renderData as []}
        handlePageChange={handlePageChange}
      />
    </div>
  );
}

export default MoowrTable;
