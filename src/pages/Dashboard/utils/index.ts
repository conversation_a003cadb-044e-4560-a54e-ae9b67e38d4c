import DATA_EXTRACTOR from '@assets/EximImages/DataExtractor.svg';
import DUTY_DRAWBACK from '@assets/EximImages/DutyDrawback.svg';
import MOOWR from '@assets/EximImages/Moowr.svg';
import {REGEXP} from '@common/constants';
import * as yup from 'yup';

const imgs = {
  'Data Extractor': DATA_EXTRACTOR,
  'Duty Drawback': DUTY_DRAWBACK,
  MOOWR,
};

export function getImage(key: string) {
  return imgs[key as keyof typeof imgs];
}

export const SHOW_ENTRIES = ['5', '10', '25', '50', 'all'];

export const disabledProducts = ['Advance License', 'IGCRD'];

export const DASHBOARD_DATA_EXTRACTOR_TABLE_HEADER = [
  {title: 'Business Name', width: '25%'},
  {title: 'PAN No.', width: '25%'},
  {title: 'Subscription Status', width: '40%'},
  {title: 'Action', width: '10%'},
];

export const DASHBOARD_DUTY_DRAWBACK_TABLE_HEADER = [
  {title: 'Business Name', width: '20%'},
  {title: 'PAN No.', width: '14%'},
  {title: 'IEC Number', width: '14%'},
  {title: 'Branch / Unit', width: '14%'},
  {title: 'Subscription Status', width: '28%'},
  {title: 'Action', width: '10%'},
];

export const addressDetailsSchema = () =>
  yup.object().shape({
    address1: yup
      .string()
      .max(150, 'Address can not be more than 150 characters.')
      .required('AddressRequired'),
    address2: yup
      .string()
      .max(150, 'Address can not be more than 150 characters.'),
    pincode: yup
      .string()
      .max(6, 'Pincode can not be more than 6 digit.')
      .matches(REGEXP.pinCode, 'Please Enter Valid Pincode')
      .required('Please enter Pincode.'),
    city: yup
      .string()
      .matches(REGEXP.city, 'Please Enter Valid City')
      .required('Please enter City.'),
    state: yup.string().required('Please select state.'),
    country: yup.string().required('Please select Country.'),
    billingAddress1: yup
      .string()
      .max(150, 'Billing Address can not be more than 150 characters.')
      .required('Please Enter Billing Address.'),
    billingAddress2: yup
      .string()
      .max(150, 'Billing Address can not be more than 150 characters.'),
    billingPincode: yup
      .string()
      .max(6, 'Please Enter Valid Billing Pincode')
      .matches(REGEXP.pinCode, 'Please Enter Valid Billing Pincode')
      .required('Please Enter Billing Pincode.'),
    billingCity: yup
      .string()
      .matches(REGEXP.city, 'Please Enter Valid Billing City')
      .required('Please Enter Billing City.'),
    billingState: yup.string().required('Please Select Billing State.'),
    billingCountry: yup.string().required('Please Select Billing Country.'),
  });

export const gstinDetailsSchema = () =>
  yup.object().shape({
    gstin: yup
      .string()
      .max(15, 'Gstin is not more than 15 Character.')
      .min(15, 'Gstin is not less than 15 Character.')
      .required('Gstin is required'),
  });

export const partnerCodeSchema = () =>
  yup.object().shape({
    partnerCode: yup
      .string()
      .matches(REGEXP.partnerCode, 'Partner Code Not Match')
      .required('Please enter the Partner Code'),
  });
