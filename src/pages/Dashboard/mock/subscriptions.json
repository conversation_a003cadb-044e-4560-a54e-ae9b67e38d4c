{"status-code": 1, "data": {"data": [{"pan": "**********", "iecCode": "code1", "paymentStatus": "UNREGISTERED", "paymentStatusToolTip": "You have not subscribed to Data Extractor. Please contact support for further assistance.", "businessName": "Pere1", "unitName": null, "planName": "-", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": false, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "UNREGISTERED", "paymentStatusToolTip": "You have not subscribed to Data Extractor. Please contact support for further assistance.", "businessName": "Pere1", "unitName": null, "planName": "-", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "FREE", "paymentStatusToolTip": "Your Free trial will expire in 11 days", "businessName": "Pere1", "unitName": null, "planName": "Free Trial", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "FREE", "paymentStatusToolTip": "Your subscription is expired Duty Drawback. Please subscribe to EximHero Plans.", "businessName": "Pere1", "unitName": null, "planName": "Free Trial", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "PAID", "paymentStatusToolTip": "Your subscription is expired Duty Drawback. Please subscribe to EximHero Plans.", "businessName": "Pere1", "unitName": null, "planName": "Basic Plan", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "EXPIRED", "paymentStatusToolTip": "Your subscription is expired Duty Drawback. Please subscribe to EximHero Plans.", "businessName": "Pere1", "unitName": null, "planName": "Basic Plan", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "PENDING_ACTIVATION", "paymentStatusToolTip": "Your subscription is expired Duty Drawback. Please subscribe to EximHero Plans.", "businessName": "Pere1", "unitName": null, "planName": "Basic Plan", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}, {"pan": "**********", "iecCode": "code1", "paymentStatus": "PENDING_PAYMENT", "paymentStatusToolTip": "Your subscription is expired Duty Drawback. Please subscribe to EximHero Plans.", "businessName": "Pere1", "unitName": null, "planName": "Advance Plan", "subscriptionId": null, "endDate": "28-10-2023", "organizationVOList": null, "owner": true, "iceGateVerified": false}]}, "status": "SUCCESS"}