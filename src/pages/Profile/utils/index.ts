import {REGEXP} from '@common/constants';
import * as yup from 'yup';

export const profileDetailsSchema = yup.object().shape({
  firstName: yup
    .string()
    .max(64, 'First name can not be more than 64 characters.')
    .matches(REGEXP.name, 'First name must contain only letters.')
    .required('Please enter first name.')
    .trim(),
  lastName: yup
    .string()
    .max(64, 'Last name can not be more than 64 characters.')
    .matches(REGEXP.name, 'Last name must contain only letters.')
    .required('Please enter last name.')
    .trim(),
  email: yup
    .string()
    .email('Please enter valid email address.')
    .required('Please enter email address.'),
  mobile: yup
    .string()
    .min(10, 'Phone No should contain minimum 10 digits.')
    .max(10, 'Max length for input is 10')
    .matches(REGEXP.mobileNumber, 'Please enter valid number.')
    .required('Please enter mobile number.'),
});

export const businessDetailsSchema = yup.object().shape({
  businessName: yup.string().required('Please enter your business name.'),
  unitName: yup.string().required('Please enter your unit name.'),
  statusOfBusiness: yup
    .string()
    .required('Please enter status of business name.'),
  typeOfDealer: yup.string().required('Please enter type of dealer.'),
  city: yup.string().required('Please enter city name.'),
  userType: yup.string().required('Please select user type.'),
});

export const gstinDetailsSchema = yup.object().shape({
  gstinNumber: yup
    .string()
    .required('Please enter GSTIN number.')
    .test('valid-gstin', 'Please enter a valid GSTIN.', (value) =>
      REGEXP.validateGstinWithFixedZ.test(value || '')
    ),
  panNumber: yup
    .string()
    .required('Please enter PAN number.')
    .test('valid-pan', 'Please enter a valid PAN.', (value) =>
      REGEXP.validatePan.test(value || '')
    ),
  iecNumber: yup
    .string()
    .min(10, 'IEC Code should not be less than 10 characters.')
    .max(10, 'IEC Code should not be more than 10 characters.')
    .matches(REGEXP.alphanumericValue, 'Must be a 10-digit alphanumeric value.')
    .required('Please enter IEC Code.')
    .test(
      'no-spaces',
      'Please enter valid IEC Code',
      (value) => !REGEXP.whiteSpace.test(value || '')
    ),
});

export const addUserFormSchema = yup.object().shape({
  firstName: yup
    .string()
    .max(64, 'First name can not be more than 64 characters.')
    .matches(REGEXP.name, 'First name must contain only letters.')
    .required('Please enter first name.')
    .trim(),
  lastName: yup
    .string()
    .max(64, 'Last name can not be more than 64 characters.')
    .matches(REGEXP.name, 'Last name must contain only letters.')
    .required('Please enter last name.')
    .trim(),
  email: yup
    .string()
    .max(64, 'Email can not be more than 64 characters.')
    .email('Please enter valid email address.')
    .required('Please enter email address.'),
  mobile: yup
    .string()
    .min(10, 'Phone No should contain minimum 10 digits.')
    .max(10, 'Phone No should contain maximum 10 digits.')
    .matches(REGEXP.mobileNumber, 'Please enter valid number.')
    .required('Please enter mobile number.'),
  designation: yup
    .string()
    .max(50, 'Designation can not be more than 50 characters.')
    .required('Please enter designation.'),
});

export const iceGateVerificationSchema = yup.object().shape({
  username: yup.string().required('Please enter username.'),
  password: yup.string().required('Please enter password.'),
});

export const addSignatorySchema = yup.object().shape({
  report_type: yup.string().required('Please select category.'),
  name: yup
    .string()
    .required('Please enter name.')
    .test('extra-spaces', 'Please enter valid name', (value) =>
      REGEXP.extraSpaces.test(value || '')
    )
    .matches(REGEXP.digits, 'Digits are not allowed.')
    .max(64, 'Name can not be more than 64 characters.'),
  designation: yup
    .string()
    .required('Please enter designation.')
    .test('extra-spaces', 'Please enter valid name', (value) =>
      REGEXP.extraSpaces.test(value || '')
    )
    .matches(REGEXP.digits, 'Digits are not allowed.')
    .max(64, 'Designation can not be more than 64 characters.'),
});

export const ALL_USERS_MANAGEMENT_TABLE_HEADER = [
  {title: 'Sr. No.', width: '6%'},
  {title: 'User Name', width: '15%'},
  {title: 'Access & Role Types', width: '15%'},
  {title: 'Designation', width: '15%'},
  {title: 'Mobile', width: '12%'},
  {title: 'Email', width: '22%'},
  {title: 'Status', width: '8%'},
  {title: 'Action', width: '12%'},
];
export const VIEW_BUSINESS_DETAILS_TABLE_HEADER = [
  {title: 'Business', width: '20%'},
  {title: 'PAN', width: '15%'},
  {title: 'IEC Code', width: '15%'},
  {title: 'Products', width: '20%'},
  {title: 'Access / Role Types', width: '15%'},
];

export const ADD_USER_TABLE_HEADER = [
  {title: 'Business', width: '25%'},
  {title: 'Subscribed Products', width: '25%'},
  {title: 'Unified Access Types', width: '25%'},
  {title: 'Distinct Access / Role', width: '25%'},
];

export const UNIFIED_ACCESS_TYPE_RADIO = [
  {id: 'allBusiness', label: 'All Business', value: 'ALL_BUSINESS'},
  {id: 'businessWise', label: 'Business Wise', value: 'BUSINESS_WISE'},
  {id: 'productWise', label: 'Product Wise', value: 'PRODUCT_WISE'},
];

export const ACCESS_ROLE_DROPDOWN = [
  {id: '1', value: 'BASIC', label: 'Basic'},
  {id: '2', value: 'ADMIN', label: 'Admin'},
];

export const CLAIM_REPORTS_TABLE_HEADER = [
  {title: 'Report Category', width: '30%'},
  {title: 'Name', width: '30%'},
  {title: 'Designation', width: '30%'},
  {title: 'Action', width: '10%'},
];

export const CLAIM_REPORTS_CATEGORY_DROPDOWN = [
  {id: '1', value: 'DBK_CLAIM_REPORT', label: 'DBK Report'},
];

export const CLAIM_REPORTS_NAME = {
  DBK_CLAIM_REPORT: 'DBK Reports',
};

export const DATA_PARSING_SETUP_TABLE_HEADER = [
  {title: 'Entity Name', width: '10%'},
  {title: 'Input Files', width: '15%'},
  {title: 'Rule', width: '30%'},
  {title: 'Prefix / Suffix', width: '15%'},
  {title: 'Termination', width: '22%'},
  {title: 'Action', width: '8%'},
];

export const DATA_PARSING_SETUP_RULE_DROPDOWN = [
  {
    id: '1',
    value: 'WITH_PREFIX',
    label: 'Parse using Prefix of the description',
  },
  {
    id: '2',
    value: 'WITH_SUFFIX',
    label: 'Parse using Suffix of the description',
  },
  {
    id: '3',
    value: 'FIRST_WORD',
    label: 'Parse using First word of the description',
  },
  {
    id: '4',
    value: 'LAST_WORD',
    label: 'Parse using Last word of the description',
  },
];

export const DATA_PARSING_DESCRIPTION_DROPDOWN = [
  {
    id: '1',
    value: 'SPACE',
    label: 'Space',
  },
  {
    id: '2',
    value: 'LENGTH',
    label: 'Length',
  },
  {
    id: '3',
    value: 'SPECIAL_CHAR',
    label: 'Special Character',
  },
];
