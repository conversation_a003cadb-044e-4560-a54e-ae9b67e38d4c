import {Api, ApiAction, ApiVersion} from '@common/constants/index';
import {
  IAddSignatoryDetails,
  IBusinessDetails,
  IFiledIdentificationStrategy,
  ISignatoryDetails,
  IUserDetails,
} from '@common/interfaces';
import {_delete, get, post, put} from '@core/api/axios';
import {AxiosResponse} from 'axios';

interface IGetUserDetails {
  passKey: string;
  userCsvList: string;
}
interface ISaveUserDetails {
  email: string;
  firstName: string;
  lastName: string;
  mobile: string;
  uuid: string;
}

interface IAddUserDetails {
  businessDetailsBeans: IBusinessDetails[];
  userDetails: IUserDetails;
}

const {
  EXIM_SUBSCRIPTION_SERVICE,
  USERS,
  USER_DETAILS,
  USERS_SINGLE,
  ORGANIZATIONS,
  COMPANY,
  MANAGEMENT,
  RESEND_INVITE,
  BUSINESS,
  PROFILE,
  EXIM,
  DBK,
  SIGNATORY_DETAILS,
  FIELD_IDENTIFICATION_STRATEGY,
  PDF_READER,
} = Api;

const COMMON_BASE_URL = `${EXIM_SUBSCRIPTION_SERVICE}${ApiVersion.V1}`;

export const getUserDetails = async (data: IGetUserDetails) => {
  const response = await get(`${COMMON_BASE_URL}${USERS}${USER_DETAILS}`, {
    headers: {
      passKey: data.passKey,
      userCsvList: data.userCsvList,
      action: ApiAction.CREATE_SUB,
    },
  });
  return response as AxiosResponse;
};

export const saveUserDetails = async (data: ISaveUserDetails) => {
  const response = await put(
    `${COMMON_BASE_URL}${USERS}${USER_DETAILS}`,
    data,
    {
      headers: {
        action: ApiAction.CREATE_SUB,
      },
    }
  );
  return response as AxiosResponse;
};

export const getCompanyProfileDetails = async (pan: string) => {
  const response = await get(
    `${COMMON_BASE_URL}${ORGANIZATIONS}${COMPANY}${PROFILE}`,
    {
      headers: {
        pan,
        action: ApiAction.MANAGE_ORG,
      },
    }
  );
  return response as AxiosResponse;
};

export const getAllUsers = async () => {
  const response = await get(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS}`,
    {
      headers: {
        action: ApiAction.GET_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const deleteUser = async (email: string) => {
  const response = await _delete(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS}`,
    {
      headers: {
        email,
        action: ApiAction.MANAGE_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBusiness = async () => {
  const response = await get(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS}${BUSINESS}`,
    {
      headers: {
        action: ApiAction.MANAGE_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const resendInviteUser = async (email: string) => {
  const response = await post(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${RESEND_INVITE}`,
    email,
    {
      headers: {
        email,
        action: ApiAction.MANAGE_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const addNewUser = async (body: IAddUserDetails) => {
  const response = await post(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS}`,
    body,
    {
      headers: {
        action: ApiAction.MANAGE_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const getSingleUser = async (email: string) => {
  const response = await get(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS_SINGLE}`,
    {
      headers: {
        email,
        action: ApiAction.GET_USER,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveEditUser = async (body: IAddUserDetails) => {
  const response = await put(
    `${COMMON_BASE_URL}${USERS}${MANAGEMENT}${USERS}`,
    body,
    {
      headers: {
        action: ApiAction.MANAGE_USER,
      },
    }
  );
  return response as AxiosResponse;
};

// INFO: Claim Reports API
const COMMON_SIGNATORY_DETAILS_URL = `${EXIM}${DBK}${SIGNATORY_DETAILS}`;
export const getSignatoryDetails = async (pan: string) => {
  const response = await get(
    `${COMMON_SIGNATORY_DETAILS_URL}?action=${ApiAction.GET_SIGNATORY_DETAILS}`,
    {
      headers: {
        pan,
      },
    }
  );
  return response as AxiosResponse;
};

export const addSignatoryDetails = async (
  pan: string,
  body: IAddSignatoryDetails | ISignatoryDetails
) => {
  const response = await post(
    `${COMMON_SIGNATORY_DETAILS_URL}?action=${ApiAction.MANAGE_SIGNATORY_DETAILS}`,
    [body],
    {
      headers: {
        pan,
      },
    }
  );
  return response as AxiosResponse;
};

export const deleteSignatoryDetails = async (
  pan: string,
  reportType: string,
  refIdList: (number | null)[]
) => {
  const response = await put(
    `${COMMON_SIGNATORY_DETAILS_URL}?deleteAll=false&action=${ApiAction.MANAGE_SIGNATORY_DETAILS}`,
    refIdList,
    {
      headers: {
        pan,
        'report-type': reportType,
      },
    }
  );
  return response as AxiosResponse;
};

export const getParsingDetails = async (
  pan: string,
  email: string,
  fileType: string
) => {
  const response = await get(
    `${PDF_READER}${FIELD_IDENTIFICATION_STRATEGY}?&action=${ApiAction.GET_FIELD_IDENTIFICATION_STRATEGY}`,
    {
      headers: {
        pan,
        email,
        'file-type': fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const updateParsingDetails = async (
  pan: string,
  email: string,
  data: IFiledIdentificationStrategy
) => {
  const response = await post(
    `${PDF_READER}${FIELD_IDENTIFICATION_STRATEGY}?&action=${ApiAction.SET_FIELD_IDENTIFICATION_STRATEGY}`,
    data,
    {
      headers: {pan, email},
    }
  );
  return response as AxiosResponse;
};

export const deleteParsingDetails = async (
  pan: string,
  email: string,
  fileType: string,
  targetField: string
) => {
  const response = await _delete(
    `${PDF_READER}${FIELD_IDENTIFICATION_STRATEGY}?&action=${ApiAction.DELETE_FIELD_IDENTIFICATION_STRATEGY}`,
    {
      headers: {
        pan,
        email,
        'file-type': fileType,
        'target-field': targetField,
      },
    }
  );
  return response as AxiosResponse;
};
