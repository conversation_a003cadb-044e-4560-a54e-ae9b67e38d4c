@import '@utils/main.scss';

.add-user-container {
  @include padding(0 32px);
  @include margin-bottom(20px);
  height: max-content;
  .user-management-sub-header {
    @include flex-item(_, flex-start, center, _, 12px);
    .breadcrumb-wrapper {
      display: none;
    }
    .typography-variant-body2 {
      color: $white;
      @include font-size($font-size-sm);
      background-color: $warning;
      @include padding(2px 5px);
      @include rfs(5px, border-radius);
    }
  }
  .paper-wrapper-rounded {
    @include padding(24px 23px);
    @include rfs(5px, border-radius);
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    height: fit-content;
  }
  .add-user-content {
    @include flex-item(_, space-between, _, _, 12px);
    // Below CSS for left part
    .add-user-form-container {
      flex: 1;
      @include flex-item(column, flex-start, flex-start);
      @include padding-right(32px);
      .form-container {
        width: 100%;
        .add-details-inp-container {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          column-gap: 48px;
          row-gap: 50px;
          @include margin-top(48px);
          @include lessThan(md) {
            grid-template-columns: repeat(1, 1fr);
          }
          .input-wrapper {
            .input-mobile-wrapper {
              .mobile-label {
                color: $text-color;
              }
            }
          }
        }
      }
      .table-container {
        @include margin(32px 0 16px);
        width: 100%;
        .add-user-table {
          width: 100%;
          border-spacing: 0;
          .add-user-tbody {
            .business-dropdown-td {
              .select-field-content {
                .select-field-content-item {
                  span {
                    font-weight: $font-weight-semi-bold;
                  }
                }
              }
            }
            .business-details-td {
              max-width: 350px;
              .business-name {
                max-width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
              & > span {
                display: block;
                @include margin-top(5px);
                color: $primary;
                font-size: $font-size-xsm;
                cursor: pointer;
              }
            }
            .product-name-checkbox-td {
              @include padding(10px 0 0 0);
              .checkbox-wrapper {
                @include padding-left(10px);
                .checkbox-container {
                  font-size: $font-size-sm;
                  .checkmarks {
                    top: 2px;
                  }
                }
                &:nth-child(n + 2) {
                  @include padding-top(10px);
                  border-top: 1px solid $primary-border;
                }
              }
            }
            .access-type-radio-btn-td {
              vertical-align: top;
              color: $text-color;
              .containers {
                font-size: $font-size-sm;
                input:checked ~ .checkmark {
                  background-color: $success;
                }
                .checkmark {
                  top: 1.5px;
                  &::after {
                    top: 6px;
                    left: 6px;
                    width: 6px;
                    height: 6px;
                  }
                }
              }
            }
            .access-role-dropdown-td {
              vertical-align: top;
              @include padding(0);
              & > div {
                width: 100%;
                @include flex-item(column, space-between);
                .select-dropdown {
                  width: 100%;
                  @include padding(6px 10px);
                  .custom-dropdown {
                    background-color: transparent;
                    .dropdown-item:hover {
                      background-color: $secondary;
                      color: $tertiary;
                    }
                  }
                  &:nth-child(n + 2) {
                    border-top: 1px solid $primary-border;
                  }
                }
              }
            }
            .add-row-btn-td {
              .button-wrapper {
                width: 95px;
                .base-btn {
                  height: 26px;
                  &:hover {
                    box-shadow: none;
                  }
                }
                .btn-children {
                  font-size: $font-size-sm;
                }
              }
            }
          }
        }
      }
      .submit-btn {
        flex: 1;
        display: grid;
        place-items: end;
        .button-wrapper {
          width: 100px;
          .base-btn {
            height: 32px;
          }
          .btn-children {
            font-size: $font-size-sm;
          }
        }
      }
    }
    // Below CSS for right part
    .add-user-text-container {
      width: 355px;
      height: max-content;
      border-radius: 5px;
      background-color: $secondary-light;
      .typography-container {
        .typography-wrapper {
          color: $tertiary-text;
        }
      }
      .header-text {
        @include flex-item(_, flex-start, center, _, 10px);
        @include padding(17px 23px);
        border-radius: 5px 5px 0 0;
        background-color: $secondary-dark;
      }
      .text-container {
        color: $tertiary-text;
        @include padding(16px 23px);
        .main-text-container {
          margin-top: 28px;
        }
      }
    }
  }
}
