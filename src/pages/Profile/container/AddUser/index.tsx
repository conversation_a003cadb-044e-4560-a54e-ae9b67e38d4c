import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableHeader from '@common/components/TableHeader';
import {
  AlertStatus,
  Path,
  ResponseStatus,
  UNIFIED_ACCESS_TYPE,
} from '@common/constants';
import {
  getBusinessOptions,
  getProductsSelection,
  selectedOptionId,
} from '@common/helpers';
import {
  DropdownOptionType,
  IBusinessDetails,
  IProductDetails,
  ISubscribedProducts,
  IUserDetails,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  addNewUser,
  getBusiness,
  getSingleUser,
  saveEditUser,
} from '@pages/Profile/api';
import UserAccessDetails from '@pages/Profile/components/UserAccessDetails';
import {
  ACCESS_ROLE_DROPDOWN,
  ADD_USER_TABLE_HEADER,
  UNIFIED_ACCESS_TYPE_RADIO,
  addUserFormSchema,
} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximMultiSelectDropdown from '@shared/components/EximMultiSelectDropdown';
import EximPaper from '@shared/components/EximPaper';
import EximRadioButton from '@shared/components/EximRadioButton';
import EximTypography from '@shared/components/EximTypography';
import {BellIcon, Plus} from '@shared/icons';
import {dispatch} from '@store';
import {useFormik} from 'formik';
import {Fragment, useCallback, useEffect, useState} from 'react';
import {useLocation, useNavigate} from 'react-router';

import './index.scss';

const initialValue: IUserDetails = {
  email: '',
  firstName: '',
  lastName: '',
  mobile: '',
  designation: '',
  type: 'BO',
};

const initialRowData = [
  {
    pan: '',
    iecCode: '',
    organizationName: '',
    productDetails: [],
  },
];

export default function AddUser() {
  const navigate = useNavigate();
  const location = useLocation();
  const {isEditUser, userEmail} = (location.state ?? {}) as {
    isEditUser: boolean;
    userEmail: string;
  };

  const [allBusiness, setAllBusiness] = useState<IBusinessDetails[]>([]);
  const [selectedBusiness, setSelectedBusiness] =
    useState<IBusinessDetails[]>(initialRowData);
  const [subscribedProduct, setSubscribedProduct] =
    useState<ISubscribedProducts>();
  const [businessOptions, setBusinessOptions] = useState<DropdownOptionType[]>(
    []
  );
  const [businessRenderOptions, setBusinessRenderOptions] = useState<
    DropdownOptionType[]
  >([]);
  const [accessType, setAccessType] = useState(
    isEditUser
      ? UNIFIED_ACCESS_TYPE.PRODUCT_WISE
      : UNIFIED_ACCESS_TYPE.ALL_BUSINESS
  );

  // INFO: below state only for product wise dropdown selection
  const [dropdownValues, setDropdownValues] = useState<{
    [key: string]: string;
  }>();

  const formik = useFormik({
    initialValues: initialValue,
    validationSchema: addUserFormSchema,
    onSubmit: async (payload: IUserDetails) => {
      const selectedData: IBusinessDetails[] = [];
      if (subscribedProduct) {
        // Transform the data to upload
        Object.keys(subscribedProduct).forEach((pan) => {
          if (subscribedProduct[pan]) {
            const availableProducts = Object.entries(subscribedProduct[pan])
              .filter(([, value]) => value)
              .map(([productName]) => productName);

            const matchingFirstData = selectedBusiness.find(
              (item) => item.pan === pan
            );
            if (matchingFirstData) {
              const newThirdDataItem: IBusinessDetails = {
                iecCode: matchingFirstData.iecCode,
                organizationName: matchingFirstData.organizationName,
                pan: matchingFirstData.pan,
                productDetails: matchingFirstData.productDetails
                  .filter((product) =>
                    availableProducts.includes(product.productName)
                  )
                  .map((product) => ({
                    availableProductRoles: [product.roleName],
                    productName: product.productName,
                    roleName: product.roleName,
                  })),
              };
              selectedData.push(newThirdDataItem);
            }
          }
        });
      }
      // INFO: filter the selected business based-on the product selection
      const selectedBusinessData = selectedData.filter(
        (item) => item.productDetails.length > 0
      );

      const body = {
        businessDetailsBeans: selectedBusinessData,
        userDetails: payload,
      };
      const response = isEditUser
        ? await saveEditUser(body)
        : await addNewUser(body);
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.data,
            alertType: AlertStatus.SUCCESS,
          })
        );
        setTimeout(
          () => navigate(`${Path.PROFILE}${Path.UNIFIED_USER_MANAGEMENT}`),
          1500
        );
      }
    },
  });

  const handleAddRow = () => {
    const newItem = {
      pan: '',
      iecCode: '',
      organizationName: '',
      productDetails: [],
    };
    setSelectedBusiness([...selectedBusiness, newItem]);
  };

  const handleAddBusiness = (options: DropdownOptionType[]) => {
    const panNumbers = options.map((opt) => opt.uniqueId);
    const foundBusiness = allBusiness.filter((item) =>
      panNumbers.includes(item.pan)
    );
    // INFO: filter the empty values
    const currentBusiness = selectedBusiness.filter(
      (item) => item.pan.length > 0
    );
    setSelectedBusiness([...currentBusiness, ...foundBusiness]);

    // INFO: Removing the options from the dropdown those has been selected
    const foundOptions = businessRenderOptions.filter(
      (item) => !panNumbers.includes(item.uniqueId)
    );
    setBusinessRenderOptions(foundOptions);
  };

  // INFO: Removing business based-on business pan because pan will be unique
  const handleRemoveBusiness = (pan: string) => {
    const foundBusiness = selectedBusiness.filter(
      (item) => !pan.includes(item.pan)
    );
    setSelectedBusiness(foundBusiness);

    // INFO: Adding option in the dropdown that is removed from the the table
    const foundOption = businessOptions.find((item) => item.uniqueId === pan);
    if (foundOption) {
      const mergeOptions = [...businessRenderOptions, foundOption];
      const sortOptionById = mergeOptions.sort((a, b) => a.id - b.id);
      setBusinessRenderOptions(sortOptionById);
    }
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setAccessType(event.target.value);
  };

  const handleSelectProduct = (pan: string, product: string) => {
    if (subscribedProduct) {
      subscribedProduct[pan][product] = !subscribedProduct[pan][product];
    }
  };

  const handleSelectRole = (
    type: string,
    pan: string,
    value: string,
    productName = ''
  ) => {
    if (type === 'ALL') {
      const updatedData = selectedBusiness.map((item) => ({
        ...item,
        productDetails: item.productDetails.map((product) => ({
          ...product,
          roleName: value,
        })),
      }));
      setSelectedBusiness(updatedData);
    } else if (type === 'BUSINESS') {
      const updatedData = selectedBusiness.map((item) => {
        if (item.pan === pan) {
          return {
            ...item,
            productDetails: item.productDetails.map((product) => ({
              ...product,
              roleName: value,
            })),
          };
        }
        return item;
      });
      setSelectedBusiness(updatedData);
    } else {
      // INFO: same id we are creating while rendering the product dropdown to identify the respective value
      const dropdownId = `${pan}${productName?.split(' ')[0].toUpperCase()}`;
      // INFO: Maintain the record of product wise selection to show the respective value in a specific dropdown
      setDropdownValues((prevStates) => ({
        ...prevStates,
        [dropdownId]: value,
      }));

      const updatedData = selectedBusiness.map((item) => {
        if (item.pan === pan) {
          return {
            ...item,
            productDetails: item.productDetails.map((product) => {
              if (product.productName === productName) {
                return {...product, roleName: value};
              }
              return product;
            }),
          };
        }
        return item;
      });
      setSelectedBusiness(updatedData);
    }
  };

  const getSelectedBusiness = async (
    allBusinessOptions: DropdownOptionType[],
    allProducts: ISubscribedProducts
  ) => {
    const {data} = await getSingleUser(userEmail);
    const businessNames = getBusinessOptions(data?.businessDetailsBeans);
    setSelectedBusiness(data?.businessDetailsBeans);
    formik.setValues(data.userDetails);

    // INFO: Extracting the product to select based-on business pan while edit
    const products: ISubscribedProducts = getProductsSelection(
      data?.businessDetailsBeans,
      true
    );
    setSubscribedProduct({...allProducts, ...products});

    // INFO: Maintain the record of product wise selection to show the respective value in a specific dropdown
    data?.businessDetailsBeans?.forEach((business: IBusinessDetails) => {
      const {pan, productDetails} = business;
      productDetails.forEach((details: IProductDetails) => {
        // INFO: same id we are creating while rendering the product dropdown to identify the respective value
        const dropdownId = `${pan}${details.productName
          ?.split(' ')[0]
          .toUpperCase()}`;
        setDropdownValues((prevStates) => ({
          ...prevStates,
          [dropdownId]: details.roleName,
        }));
      });
    });

    // INFO: If both length is equal it means all businesses already added
    if (businessOptions.length === allBusinessOptions.length) {
      setBusinessRenderOptions([]);
    } else {
      // INFO: Filtering options those are not added in the business table

      // Extract uniqueIds from the selected businessNames
      const uniqueIds = businessNames.map((item) => item.uniqueId);

      // Filter allBusinessOptions based on uniqueIds from businessNames
      const filteredBusinessNames = allBusinessOptions.filter(
        (item) => !uniqueIds.includes(item.uniqueId)
      );
      setBusinessRenderOptions(filteredBusinessNames);
    }
  };

  const getAllBusiness = useCallback(async () => {
    const {data} = await getBusiness();
    const businessNames = getBusinessOptions(data);
    setAllBusiness(data);
    setBusinessOptions(businessNames);
    setBusinessRenderOptions(businessNames);

    // INFO: Extracting the product to select and unselect based-on business pan
    const products: ISubscribedProducts = getProductsSelection(data, false);
    setSubscribedProduct(products);

    // INFO: Calling function once we need to edit the existing user
    if (isEditUser && userEmail) {
      getSelectedBusiness(businessNames, products);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    getAllBusiness();
  }, [getAllBusiness]);

  return (
    <div className='add-user-container'>
      <div className='user-management-sub-header'>
        <NavigationSubHeader
          leftArrowRoute='#'
          hasLeftArrow
          isNavigate
          hasTitle
          leftArrowText='Unified User Management'
        />
        <EximTypography variant='body2'>Beta Version</EximTypography>
        <EximTypography variant='caption'>
          Final release version is available for certain type of subscriptions
        </EximTypography>
      </div>
      <EximPaper>
        <div className='add-user-content'>
          <form
            className='add-user-form-container'
            onSubmit={formik.handleSubmit}>
            <div className='form-container'>
              <EximTypography variant='h3' fontWeight='bold'>
                Add Business user Information
              </EximTypography>
              <div className='add-details-inp-container'>
                <EximInput
                  id='firstName'
                  label='First Name'
                  dataTestid='firstName'
                  name='firstName'
                  autoComplete='on'
                  maxLength={64}
                  isRequired
                  isFocused
                  placeholder='Enter First Name e.g. John'
                  value={formik.values.firstName?.trim()}
                  onChange={formik.handleChange}
                  isInvalid={
                    ((formik.errors.firstName &&
                      formik.touched.firstName) as boolean) || false
                  }
                  onBlur={(e) =>
                    formik.values.firstName && formik.handleBlur(e)
                  }
                  errorMessage={
                    formik.errors.firstName
                      ? (formik.errors.firstName as string)
                      : ''
                  }
                />
                <EximInput
                  id='lastName'
                  label='Last Name'
                  dataTestid='lastName'
                  isRequired
                  placeholder='Enter Last Name e.g. Smith'
                  name='lastName'
                  autoComplete='on'
                  maxLength={64}
                  value={formik.values.lastName?.trim()}
                  onChange={formik.handleChange}
                  onBlur={(e) => formik.values.lastName && formik.handleBlur(e)}
                  isInvalid={
                    ((formik.errors.lastName &&
                      formik.touched.lastName) as boolean) || false
                  }
                  errorMessage={
                    formik.errors.lastName
                      ? (formik.errors.lastName as string)
                      : ''
                  }
                />
                <EximInput
                  id='email'
                  label='Email'
                  dataTestid='email'
                  isRequired
                  placeholder='Enter Email Address'
                  name='email'
                  maxLength={64}
                  autoComplete='on'
                  value={formik.values.email?.trim()}
                  onChange={formik.handleChange}
                  onBlur={(e) => formik.values.email && formik.handleBlur(e)}
                  isInvalid={
                    ((formik.errors.email &&
                      formik.touched.email) as boolean) || false
                  }
                  errorMessage={
                    formik.errors.email ? (formik.errors.email as string) : ''
                  }
                />
                <EximInput
                  id='mobile'
                  label='Mobile'
                  dataTestid='mobile'
                  type='text'
                  fieldType='mobileNumber'
                  name='mobile'
                  maxLength={10}
                  autoComplete='on'
                  isRequired
                  placeholder='Enter Phone Number'
                  value={formik.values.mobile}
                  onBlur={(e) => formik.values.mobile && formik.handleBlur(e)}
                  isInvalid={
                    ((formik.errors.mobile &&
                      formik.touched.mobile) as boolean) || false
                  }
                  onChange={formik.handleChange}
                  errorMessage={
                    formik.errors.mobile ? (formik.errors.mobile as string) : ''
                  }
                />
                <EximInput
                  id='designation'
                  label='Designation'
                  dataTestid='designation'
                  isRequired
                  placeholder='Enter Designation'
                  name='designation'
                  autoComplete='on'
                  value={formik.values.designation?.trim()}
                  onChange={formik.handleChange}
                  onBlur={(e) =>
                    formik.values.designation && formik.handleBlur(e)
                  }
                  isInvalid={
                    ((formik.errors.designation &&
                      formik.touched.designation) as boolean) || false
                  }
                  errorMessage={
                    formik.errors.designation
                      ? (formik.errors.designation as string)
                      : ''
                  }
                />
              </div>
            </div>
            <div className='table-container'>
              <table className='add-user-table'>
                <TableHeader mainHeader={ADD_USER_TABLE_HEADER} />
                <TableBody className='add-user-tbody'>
                  {selectedBusiness?.map(
                    (business: IBusinessDetails, index) => {
                      const {organizationName, pan, productDetails} = business;
                      return (
                        <Fragment key={`business${index + 1}`}>
                          {business.pan === '' ? (
                            <TableRow>
                              <TableCell className='business-dropdown-td'>
                                <EximMultiSelectDropdown
                                  placeholder='Select Business'
                                  searchPlaceholder='Search Business'
                                  options={businessRenderOptions}
                                  disabled={businessRenderOptions.length === 0}
                                  onSelect={(list: DropdownOptionType[]) =>
                                    handleAddBusiness(list)
                                  }
                                />
                              </TableCell>
                              <TableCell />
                              <TableCell />
                              <TableCell />
                            </TableRow>
                          ) : (
                            <TableRow>
                              <TableCell className='business-details-td'>
                                <EximTypography
                                  classNames='business-name'
                                  fontWeight='bold'>
                                  {organizationName}
                                </EximTypography>
                                <EximTypography variant='caption'>
                                  {`PAN : ${pan}`}
                                </EximTypography>
                                <span
                                  role='presentation'
                                  className='remove-business-btn'
                                  onClick={() => handleRemoveBusiness(pan)}>
                                  Remove
                                </span>
                              </TableCell>
                              <TableCell className='product-name-checkbox-td'>
                                {productDetails.map((product) => {
                                  const {productName} = product;
                                  const unique = productName
                                    ?.split(' ')
                                    .join('');
                                  return (
                                    <EximCheckbox
                                      key={`product${unique + Math.random()}`}
                                      id={unique}
                                      name={unique}
                                      dataTestId={unique}
                                      label={productName}
                                      color='#2CB544'
                                      size='medium'
                                      checked={
                                        subscribedProduct
                                          ? subscribedProduct[pan][productName]
                                          : false
                                      }
                                      onChange={() =>
                                        handleSelectProduct(pan, productName)
                                      }
                                    />
                                  );
                                })}
                              </TableCell>
                              {/* INFO: need to show only once that's why index should be 0 */}
                              {index === 0 ? (
                                <TableCell
                                  className='access-type-radio-btn-td'
                                  rowSpan={
                                    selectedBusiness?.filter((opt) => opt.pan)
                                      .length
                                  }>
                                  {UNIFIED_ACCESS_TYPE_RADIO.map((item) => (
                                    <EximRadioButton
                                      key={`accessType${Math.random()}`}
                                      id={item.id}
                                      label={item.label}
                                      size='medium'
                                      onChange={handleRadioChange}
                                      value={item.value}
                                      isSelected={accessType === item.value}
                                    />
                                  ))}
                                </TableCell>
                              ) : null}
                              {/* INFO: need to show only once that's why index should be 0 */}
                              {accessType ===
                                UNIFIED_ACCESS_TYPE.ALL_BUSINESS &&
                              index === 0 ? (
                                <TableCell
                                  className='access-role-dropdown-td'
                                  rowSpan={
                                    selectedBusiness?.filter((opt) => opt.pan)
                                      .length
                                  }>
                                  <div>
                                    <EximCustomDropdown
                                      placeholder='Select Role'
                                      onSelect={({value}) =>
                                        handleSelectRole('ALL', pan, value)
                                      }
                                      dataTestId='column-dropdown'
                                      optionsList={ACCESS_ROLE_DROPDOWN}
                                    />
                                  </div>
                                </TableCell>
                              ) : null}
                              {accessType ===
                              UNIFIED_ACCESS_TYPE.BUSINESS_WISE ? (
                                <TableCell className='access-role-dropdown-td'>
                                  <div>
                                    <EximCustomDropdown
                                      placeholder='Select Role'
                                      onSelect={({value}) =>
                                        handleSelectRole('BUSINESS', pan, value)
                                      }
                                      dataTestId='column-dropdown'
                                      optionsList={ACCESS_ROLE_DROPDOWN}
                                    />
                                  </div>
                                </TableCell>
                              ) : null}
                              {accessType ===
                              UNIFIED_ACCESS_TYPE.PRODUCT_WISE ? (
                                <TableCell className='access-role-dropdown-td'>
                                  <div>
                                    {productDetails.map((product) => {
                                      const {productName} = product;
                                      const dropdownId = `${pan}${productName
                                        ?.split(' ')[0]
                                        .toUpperCase()}`;
                                      return (
                                        <EximCustomDropdown
                                          id={product.productName}
                                          key={`accessRole${Math.random()}`}
                                          placeholder='Select Role'
                                          onSelect={({value}) =>
                                            handleSelectRole(
                                              'PRODUCT',
                                              pan,
                                              value,
                                              productName
                                            )
                                          }
                                          dataTestId='column-dropdown'
                                          optionsList={ACCESS_ROLE_DROPDOWN}
                                          defaultOption={
                                            dropdownValues
                                              ? selectedOptionId(
                                                  ACCESS_ROLE_DROPDOWN,
                                                  dropdownValues[dropdownId]
                                                )
                                              : null
                                          }
                                        />
                                      );
                                    })}
                                  </div>
                                </TableCell>
                              ) : null}
                            </TableRow>
                          )}
                        </Fragment>
                      );
                    }
                  )}
                  <TableRow>
                    <TableCell
                      colSpan={ADD_USER_TABLE_HEADER.length}
                      className='add-row-btn-td'>
                      <EximButton
                        size='small'
                        color='secondary'
                        onClick={handleAddRow}
                        disabled={
                          selectedBusiness.length >= 1 &&
                          selectedBusiness[selectedBusiness.length - 1].pan ===
                            ''
                        }>
                        <Plus height={10} width={10} /> Add Row
                      </EximButton>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </table>
            </div>
            <div className='submit-btn'>
              <EximButton
                type='submit'
                size='small'
                disabled={
                  selectedBusiness.length === 0 ||
                  selectedBusiness[0]?.pan === ''
                }>
                Submit
              </EximButton>
            </div>
          </form>
          <div className='add-user-text-container'>
            <div className='header-text'>
              <BellIcon />
              <EximTypography variant='h4' fontWeight='bold'>
                User Access/Role Type
              </EximTypography>
            </div>
            <div className='text-container'>
              <EximTypography>
                GSTHero provides you with Two Access types / Roles.
              </EximTypography>
              <UserAccessDetails
                step='1.'
                title='Admin Access:'
                content='When you provide Admin access / role to a User, this user
                    can manage all aspects of the GSTHero Portal. Admin can do
                    activities like Adding Business/GSTINS, Adding Users and
                    Process Returns Activities like.'
                listItem={[
                  'Map Excels',
                  'Upload and Validate Data, GSTR1 and GSTR2',
                  'Reconcile',
                  'Save to GSTN',
                  'Submit to GSTN',
                  'e-sign and File to GSTN',
                ]}
              />
              <UserAccessDetails
                step='2.'
                title='Basic Access:'
                content='When you provide Basic access / role to a user, this user can only manage limited activities on User and Business Management as well as Limited Activities in Process Return like.'
                listItem={[
                  'Map Excels',
                  'Upload and Validate Data, GSTR1 and GSTR2',
                  'Only work on Reconcile',
                ]}
              />
            </div>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}
