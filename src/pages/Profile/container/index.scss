@import '@utils/main.scss';

.profile-main-container {
  .subscription-header {
    @include padding(8px 0px);
    .subscription-header-left {
      .typography-variant-h1 {
        color: $title-color;
      }
    }
  }

  .tabs-wrapper {
    @include padding-right(30px);
    .tab-vertical {
      align-items: flex-start;
    }
    .tab-container {
      .tab {
        height: calc(100vh - 112px);
        gap: 2px;
        background: $white;
        .tab-button {
          width: 216px;
          text-align: left;
          font-size: $font-size-sm;
          color: $text-color;

          &:hover {
            background-color: $table-head-1;
            color: $white;
          }

          .button-content-topIcon {
            gap: 0;
          }
        }
        button:disabled {
          background: $white;
          color: $table-head-1;
          font-weight: $font-weight-semi-bold;
          font-size: $font-size-md;
          @include padding(24px 20px 16px);
          background: none;

          &:hover {
            background: none;
            color: $table-head-1;
            cursor: text;
          }

          // Below CSS to add the line in sideBarMenu
          .button-content-topIcon:not(:first-child)::before {
            content: '';
            width: 170px;
            border: 1px solid $secondary;
            position: relative;
            bottom: 18px;
            opacity: 0.5;
          }
        }
        .tab-primary {
          background-color: $table-head-1;
          color: $white;
        }
      }
      .tab-content-vertical {
        width: 100%;
      }
    }
  }
}
