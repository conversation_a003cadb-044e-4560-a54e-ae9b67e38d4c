import {
  EximProducts,
  INACTIVE_TAB_ID,
  Path,
  ProfilePages,
  ProfileTabs,
} from '@common/constants';
import EximTabs from '@shared/components/EximTabs';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router-dom';

import AutoLinkConfiguration from '../components/AutoLinkConfiguration';
import CompanyProfile from '../components/CompanyProfile';
import DEDataParsingSetup from '../components/DEDataParsingSetup';
import DataFetchConfiguration from '../components/DataFetchConfiguration';
import DutyDrawbackClaimReport from '../components/DutyDrawbackClaimReport';
import UnifiedUserManagement from '../components/UserManagement';
import UserProfile from '../components/UserProfile';
import helpSupport from './config';
import './index.scss';

function Profile() {
  const {
    USER_PROFILE,
    COMPANY_PROFILE,
    UNIFIED_USER_MANAGEMENT,
    MANAGE_SUBSCRIPTION,
    NOTIFICATIONS_SETTINGS,
    CLAIM_REPORTS,
    DATA_PARSING_SETUP,
    DATA_FETCH_CONFIGURATION,
    AUTO_LINK_CONFIGURATION,
  } = ProfilePages;

  /* instead of view key we can assign a new variable name as activeTab for it and it coming form url params
  if we have don't url params so here assigned the default value as COMPANY_PROFILE */
  const {view: activeTab} = useParams();
  const navigate = useNavigate();

  const {shouldCompanyProfileShow, selectedProductName} = useSelector(
    (state: RootState) => state.profile
  );

  const handleChange = (id: string | number) => {
    navigate(`${Path.PROFILE}/${id}`);
  };

  const userProfileTab = () => {
    const userProfile = [
      {
        id: USER_PROFILE,
        tabName: ProfileTabs.USER_PROFILE,
        tabContent: <UserProfile />,
      },
    ];
    if (shouldCompanyProfileShow) {
      userProfile.push({
        id: COMPANY_PROFILE,
        tabName: ProfileTabs.COMPANY_PROFILE,
        tabContent: <CompanyProfile />,
      });
    } else {
      userProfile.push({
        id: UNIFIED_USER_MANAGEMENT,
        tabName: ProfileTabs.UNIFIED_USER_MANAGEMENT,
        tabContent: <UnifiedUserManagement />,
      });
    }
    return userProfile;
  };

  // INFO: Below option only for duty drawback
  const dutyDrawbackSettings = () => {
    if (selectedProductName === EximProducts.DUTY_DRAWBACK) {
      return [
        {
          id: INACTIVE_TAB_ID,
          tabName: 'Duty Drawback Settings',
          tabContent: null,
        },
        {
          id: CLAIM_REPORTS,
          tabName: ProfileTabs.CLAIM_REPORTS,
          tabContent: <DutyDrawbackClaimReport />,
        },
      ];
    }
    return [];
  };

  // INFO: Below option only for data extractor
  const dataExtractorSettings = () => {
    if (selectedProductName === EximProducts.DATA_EXTRACTOR) {
      return [
        {
          id: INACTIVE_TAB_ID,
          tabName: 'Data Extractor Settings',
          tabContent: null,
        },
        {
          id: DATA_PARSING_SETUP,
          tabName: ProfileTabs.DATA_PARSING_SETUP,
          tabContent: <DEDataParsingSetup />,
        },
      ];
    }
    return [];
  };

  // INFO: Below option only for eBRC
  const ebrcSettings = () => {
    if (selectedProductName === EximProducts.EBRC) {
      return [
        {
          id: INACTIVE_TAB_ID,
          tabName: 'eBRC Settings',
          tabContent: null,
        },
        {
          id: AUTO_LINK_CONFIGURATION,
          tabName: ProfileTabs.AUTO_LINK_CONFIGURATION,
          tabContent: <AutoLinkConfiguration />,
        },
      ];
    }
    return [];
  };

  return (
    <div className='profile-main-container'>
      <EximTabs
        onChange={handleChange}
        activeTab={activeTab}
        inactiveTab={INACTIVE_TAB_ID}
        border={false}
        vertical
        values={[
          {
            id: INACTIVE_TAB_ID,
            tabName: 'General Settings',
            tabContent: null,
          },
          ...userProfileTab(),
          {
            id: NOTIFICATIONS_SETTINGS,
            tabName: ProfileTabs.NOTIFICATIONS_SETTINGS,
            tabContent: <h1>Notifications Settings</h1>,
          },
          {
            id: MANAGE_SUBSCRIPTION,
            tabName: ProfileTabs.MANAGE_SUBSCRIPTION,
            tabContent: <h1>Manage Subscriptions</h1>,
          },
          {
            id: DATA_FETCH_CONFIGURATION,
            tabName: ProfileTabs.DATA_FETCH_CONFIGURATION,
            tabContent: <DataFetchConfiguration />,
          },

          ...dutyDrawbackSettings(),

          ...dataExtractorSettings(),

          ...ebrcSettings(),

          ...helpSupport,
        ]}
      />
    </div>
  );
}

export default Profile;
