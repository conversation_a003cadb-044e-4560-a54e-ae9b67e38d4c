import EximTypography from '@shared/components/EximTypography';

import './index.scss';

interface IUserAccessProps {
  step?: string;
  title?: string;
  content?: string;
  listItem?: string[];
}

export default function UserAccessDetails({
  step,
  title,
  content,
  listItem,
}: IUserAccessProps) {
  return (
    <div className='main-text-container'>
      {content ? (
        <div className='text'>
          <span>
            {step ? <EximTypography>{step}</EximTypography> : null}
            {title ? (
              <EximTypography fontWeight='bold'>{title}</EximTypography>
            ) : null}
          </span>
          <EximTypography>{content}</EximTypography>
        </div>
      ) : null}
      {listItem && listItem?.length > 0 ? (
        <div className='dot-list'>
          <ul>
            {listItem?.map((item) => (
              <li key={item}>{item}</li>
            ))}
          </ul>
        </div>
      ) : null}
    </div>
  );
}

UserAccessDetails.defaultProps = {
  step: '',
  title: '',
  content: '',
  listItem: [],
};
