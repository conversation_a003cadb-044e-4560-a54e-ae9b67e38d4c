@import '@utils/main.scss';

.main-text-container {
  .typography-container {
    .typography-wrapper {
      color: #0e3668;
    }
  }
  @include flex-item(column, _, _, _, 32px);
  .text {
    span {
      @include flex-item(_, flex-start, center, _, 4px);
    }
  }
  .dot-list {
    list-style: none;
    padding: 0;
  }

  .dot-list li {
    position: relative;
    padding-left: 20px;
    line-height: 1.6;
    font-size: $font-size-sm;
  }

  .dot-list li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 26%;
    width: 8px;
    height: 8px;
    background-color: #0e3668;
    border-radius: 50%;
  }
}
