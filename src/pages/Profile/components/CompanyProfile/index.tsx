import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {
  IBusinessProfileDetails,
  ICompanyProfileData,
  IGstinDetails,
} from '@common/interfaces';
import {getCompanyProfileDetails} from '@pages/Profile/api';
import EximDivider from '@shared/components/EximDivider';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import BusinessDetails from './components/BusinessDetails';
import GstinDetails from './components/GstinDetails';
import IceGateVerification from './components/IceGateVerification';
import './index.scss';

const initialValues = {
  pan: '',
  orgName: '',
  unitName: '',
  stateOfBusiness: '',
  typeOfDealer: '',
  city: '',
  userType: '',
  iecDetails: {
    iecCode: '',
    iceGateUserName: '',
  },
  gstinNumber: '',
};

function CompanyProfile() {
  const {profilePan} = useSelector((state: RootState) => state.profile);

  const [isLoading, setIsLoading] = useState(true);
  const [companyDetails, setCompanyDetails] =
    useState<ICompanyProfileData>(initialValues);

  const getUserProfileData = useCallback(async () => {
    const {data} = await getCompanyProfileDetails(profilePan);
    setCompanyDetails(data);
    setIsLoading(false);
  }, [profilePan]);

  useEffect(() => {
    getUserProfileData();
  }, [getUserProfileData]);

  const businessDetails: IBusinessProfileDetails = {
    businessName: companyDetails?.orgName || '-',
    unitName: companyDetails?.unitName || '-',
    statusOfBusiness: companyDetails?.stateOfBusiness || '-',
    city: companyDetails?.city || '-',
    typeOfDealer: companyDetails?.typeOfDealer || '-',
    userType: companyDetails?.userType || '-',
  };

  const gstinDetails: IGstinDetails = {
    gstinNumber: companyDetails?.gstinNumber,
    panNumber: companyDetails?.pan,
    iecNumber: companyDetails?.iecDetails.iecCode,
  };

  return (
    <div className='company-profile-container'>
      <NavigationSubHeader
        leftArrowRoute='#'
        isNavigate
        hasLeftArrow
        hasTitle
        leftArrowText='Company Profile'
      />
      <EximPaper>
        {!isLoading ? (
          <>
            <BusinessDetails businessDetails={businessDetails} />
            <EximDivider type='solid' />
            <GstinDetails gstinDetails={gstinDetails} />
            <EximDivider type='solid' />
            <IceGateVerification
              iceGateUserName={companyDetails?.iecDetails?.iceGateUserName}
            />
          </>
        ) : null}
      </EximPaper>
    </div>
  );
}

export default CompanyProfile;
