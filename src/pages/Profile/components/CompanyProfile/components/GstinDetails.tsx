import {IGstinDetails} from '@common/interfaces';
import {gstinDetailsSchema} from '@pages/Profile/utils';
import EximInput from '@shared/components/EximInput';
import EximTypography from '@shared/components/EximTypography';
import {useFormik} from 'formik';
import {useState} from 'react';

interface IProps {
  gstinDetails: IGstinDetails;
}

function GstinDetails({gstinDetails}: IProps) {
  const [isEdit, setIsEdit] = useState(false);

  const formik = useFormik({
    initialValues: gstinDetails,
    validationSchema: gstinDetailsSchema,
    onSubmit: async (payload) => {
      if (isEdit) {
        // TODO: Need to integrate the API once available
      }
      setIsEdit((prev) => !prev);
    },
  });

  return (
    <div className='gstin-details-container'>
      <form onSubmit={formik.handleSubmit}>
        <div className='form-header-wrapper'>
          <EximTypography variant='h2'>GSTIN Details</EximTypography>
        </div>
        <div className='form-inputs-container'>
          <EximInput
            label='GSTIN Number'
            id='gstinNumber'
            name='gstinNumber'
            isRequired={isEdit}
            disabled={!isEdit}
            value={formik.values.gstinNumber}
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.gstinNumber
                ? (formik.errors.gstinNumber as string)
                : ''
            }
            isInvalid={
              ((formik.errors.gstinNumber &&
                formik.touched.gstinNumber) as boolean) || false
            }
          />

          <EximInput
            label='PAN Number'
            id='panNumber'
            name='panNumber'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.panNumber}
            errorMessage={
              formik.errors.panNumber ? (formik.errors.panNumber as string) : ''
            }
            isInvalid={
              ((formik.errors.panNumber &&
                formik.touched.panNumber) as boolean) || false
            }
          />

          <EximInput
            label='IEC Number'
            id='iecNumber'
            name='iecNumber'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.iecNumber}
            errorMessage={
              formik.errors.iecNumber ? (formik.errors.iecNumber as string) : ''
            }
            isInvalid={
              ((formik.errors.iecNumber &&
                formik.touched.iecNumber) as boolean) || false
            }
          />
        </div>
      </form>
    </div>
  );
}

export default GstinDetails;
