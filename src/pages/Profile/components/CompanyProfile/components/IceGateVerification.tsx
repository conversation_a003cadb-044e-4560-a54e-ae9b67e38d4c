import {iceGateVerificationSchema} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import EximTypography from '@shared/components/EximTypography';
import {SolidCheckCircle} from '@shared/icons';
import {useFormik} from 'formik';
import {useState} from 'react';

interface IIceGateProps {
  iceGateUserName: string;
}

// TODO: Need to remove static values once we have API
function IceGateVerification({iceGateUserName}: IIceGateProps) {
  const [isVerified, setIsVerified] = useState(!!iceGateUserName);
  const [passwordStar, setPasswordStar] = useState(
    iceGateUserName ? '********' : ''
  );

  const formik = useFormik({
    initialValues: {
      username: iceGateUserName,
      password: iceGateUserName ? '1234567' : '',
    },
    validationSchema: iceGateVerificationSchema,
    onSubmit: async (payload) => {
      if (isVerified) {
        /* */
      }
      setIsVerified(true);
      setPasswordStar('*'.repeat(payload.password.length));
    },
  });

  return (
    <div className='ice-gate-verification-container'>
      <form onSubmit={formik.handleSubmit}>
        <div className='form-header-wrapper'>
          <div className='verified-container'>
            <EximTypography variant='h2'>ICEGate Verification</EximTypography>
            {isVerified ? (
              <EximButton className='verify-btn' color='success' size='small'>
                Verified
                <SolidCheckCircle />
              </EximButton>
            ) : null}
          </div>
          <EximButton type='submit' size='small' disabled={isVerified}>
            {isVerified ? 'Verified' : 'Verify'}
          </EximButton>
        </div>
        <div className='form-inputs-container'>
          <EximInput
            label='Username'
            id='username'
            name='username'
            maxLength={64}
            placeholder='Enter Username'
            isRequired={!isVerified}
            disabled={isVerified}
            value={formik.values.username}
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.username ? (formik.errors.username as string) : ''
            }
            isInvalid={
              ((formik.errors.username &&
                formik.touched.username) as boolean) || false
            }
          />
          <EximInput
            label='Password'
            id='password'
            name='password'
            maxLength={64}
            placeholder='Enter Password'
            isRequired={!isVerified}
            disabled={isVerified}
            onChange={formik.handleChange}
            value={isVerified ? passwordStar : formik.values.password}
            errorMessage={
              formik.errors.password ? (formik.errors.password as string) : ''
            }
            isInvalid={
              ((formik.errors.password &&
                formik.touched.password) as boolean) || false
            }
          />
        </div>
      </form>
    </div>
  );
}

export default IceGateVerification;
