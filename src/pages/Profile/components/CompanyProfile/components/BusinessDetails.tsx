import {selectedOptionId} from '@common/helpers';
import {
  IBusinessProfileDetails,
  IRegularDropdownData,
  SetFieldValue,
} from '@common/interfaces';
import {APPLICANT_DROPDOWN} from '@pages/DutyDrawback/utils';
import {businessDetailsSchema} from '@pages/Profile/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximTypography from '@shared/components/EximTypography';
import {useFormik} from 'formik';
import {useState} from 'react';

interface IProps {
  businessDetails: IBusinessProfileDetails;
}

function BusinessDetails({businessDetails}: IProps) {
  const [isEdit, setIsEdit] = useState(false);

  const formik = useFormik({
    initialValues: businessDetails,
    validationSchema: businessDetailsSchema,
    onSubmit: async (payload) => {
      if (isEdit) {
        // TODO: Need to integrate the API once available
      }
      setIsEdit((prev) => !prev);
    },
  });

  // set dropdown selected item data
  const onSelect = (
    data: IRegularDropdownData,
    setFieldValue: SetFieldValue,
    type: string
  ) => {
    setFieldValue(type, data.value);
  };

  return (
    <div className='business-details-container'>
      <form onSubmit={formik.handleSubmit}>
        <div className='form-header-wrapper'>
          <EximTypography variant='h2'>Business Details</EximTypography>
        </div>
        <div className='form-inputs-container'>
          <EximInput
            label='Business Name'
            id='businessName'
            name='businessName'
            isRequired={isEdit}
            disabled={!isEdit}
            value={formik.values.businessName}
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.businessName
                ? (formik.errors.businessName as string)
                : ''
            }
            isInvalid={
              ((formik.errors.businessName &&
                formik.touched.businessName) as boolean) || false
            }
          />

          <EximInput
            label='Unit Name'
            id='unitName'
            name='unitName'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.unitName}
            errorMessage={
              formik.errors.unitName ? (formik.errors.unitName as string) : ''
            }
            isInvalid={
              ((formik.errors.unitName &&
                formik.touched.unitName) as boolean) || false
            }
          />

          <EximInput
            label='Status Of Business'
            id='statusOfBusiness'
            name='statusOfBusiness'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.statusOfBusiness}
            errorMessage={
              formik.errors.statusOfBusiness
                ? (formik.errors.statusOfBusiness as string)
                : ''
            }
            isInvalid={
              ((formik.errors.statusOfBusiness &&
                formik.touched.statusOfBusiness) as boolean) || false
            }
          />

          <EximInput
            label='Type Of Dealer (Regular / Composite / NRI)'
            id='typeOfDealer'
            name='typeOfDealer'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.typeOfDealer}
            errorMessage={
              formik.errors.typeOfDealer
                ? (formik.errors.typeOfDealer as string)
                : ''
            }
            isInvalid={
              ((formik.errors.typeOfDealer &&
                formik.touched.typeOfDealer) as boolean) || false
            }
          />

          <EximInput
            label='City'
            id='city'
            name='city'
            isRequired={isEdit}
            disabled={!isEdit}
            onChange={formik.handleChange}
            value={formik.values.city}
            errorMessage={
              formik.errors.city ? (formik.errors.city as string) : ''
            }
            isInvalid={
              ((formik.errors.city && formik.touched.city) as boolean) || false
            }
          />

          {!isEdit ? (
            <EximInput
              label='User Type'
              id='userType'
              name='userType'
              isRequired={isEdit}
              disabled={!isEdit}
              onChange={formik.handleChange}
              value={formik.values.userType}
              errorMessage={
                formik.errors.userType ? (formik.errors.userType as string) : ''
              }
              isInvalid={
                ((formik.errors.userType &&
                  formik.touched.userType) as boolean) || false
              }
            />
          ) : (
            <EximCustomDropdown
              label='User Type'
              optionsList={APPLICANT_DROPDOWN}
              placeholder='Please Select User Type'
              isRequired={isEdit}
              readOnly={!isEdit}
              onSelect={(e) => onSelect(e, formik.setFieldValue, 'userType')}
              defaultOption={selectedOptionId(
                APPLICANT_DROPDOWN,
                formik.values.userType
              )}
              errorMessage={
                formik.errors.userType ? (formik.errors.userType as string) : ''
              }
              isInvalid={
                ((formik.errors.userType &&
                  formik.touched.userType) as boolean) || false
              }
            />
          )}
        </div>
      </form>
    </div>
  );
}

export default BusinessDetails;
