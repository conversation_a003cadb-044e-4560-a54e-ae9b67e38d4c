@import '@utils/main.scss';

.company-profile-container {
  @include margin-bottom(20px);
  .paper-wrapper-rounded {
    @include padding(32px 80px 60px 32px);
    @include rfs(5px, border-radius);
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    height: fit-content;
  }

  .gstin-details-container {
    @include padding(8px 0 12px);
  }
  .ice-gate-verification-container {
    .form-header-wrapper {
      .verified-container {
        @include flex-item(_, _, center, _, 20px);
        .button-wrapper {
          width: 68px;
          .verify-btn.base-btn {
            height: 18px;
            font-size: $font-size-xsm;
            @include rfs(2px, border-radius);
          }
        }
      }
    }
    .form-inputs-container {
      grid-template-columns: 1fr 1fr 1fr;
    }
  }

  // INFO: Common style for Business Details, GSTIN Details and ICE Gate Verification form
  .form-header-wrapper {
    @include flex-item(row, space-between, center);

    .button-wrapper {
      width: 100px;
      .base-btn {
        height: 32px;
        font-size: $font-size-sm;
      }
    }
  }
  .form-inputs-container {
    @include margin-top(48px);
    width: 90%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(285px, 1fr));
    column-gap: 80px;
    row-gap: 60px;
    .input-wrapper {
      input {
        font-size: $font-size-sm;
      }
      .form-input.disabled {
        input {
          @include margin-left(-10px);
          font-size: $font-size-sm;
          color: $text-color;
          border: none;
          outline: none;
        }
      }
    }

    .select-dropdown {
      position: relative;
      top: -15px;
      .dropdown-label {
        font-size: $font-size-sm;
        color: $label-color;
      }
      .custom-dropdown {
        background: none;
        .dropdown-item:hover {
          background: $secondary;
          color: inherit;
        }
      }
    }
  }
}
