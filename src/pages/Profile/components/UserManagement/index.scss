@import '@utils/main.scss';

.user-management-container {
  @include margin-bottom(20px);
  .paper-wrapper-rounded {
    @include padding(32px);
    @include rfs(5px, border-radius);
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    height: fit-content;
  }
  .user-management-sub-header {
    @include flex-item(_, flex-start, center, _, 12px);
    .breadcrumb-wrapper {
      display: none;
    }
    .typography-variant-body2 {
      color: $white;
      @include font-size($font-size-sm);
      background-color: $warning;
      @include padding(2px 5px);
      @include rfs(5px, border-radius);
    }
  }

  // All User Table Style
  .all-users-table-container {
    width: 100%;
    .table-title {
      @include flex-item(_, space-between, center);
      @include margin-bottom(24px);
      .button-wrapper {
        width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 5px);
        }
      }
    }
    .table-search-container {
      .search-div {
        .middle-content-div {
          @include flex-item(_, center, center, _, 20px);
          & > span {
            font-size: $font-size-sm;
            font-weight: $font-weight-semi-bold;
            color: $label-color;
          }
        }
        // INFO: Below CSS to hide the search box from the table header according to design
        .table-input {
          display: none;
        }
      }
    }
    .all-users-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .all-users-tbody {
        width: 100%;
        .view-details {
          @include flex-item(_, flex-start, baseline, _, 10px);
          font-size: $font-size-sm;
          color: $information;
          cursor: pointer;
        }
        .status-td {
          font-size: $font-size-xsm;
          border-radius: 5px;
          @include padding(1px 4px 2px);
          text-transform: capitalize;
        }
        .status-td.active,
        .status-td.accepted {
          color: $success-color;
          background-color: $success-background;
          border: 1px solid $success-border;
        }
        .status-td.pending,
        .status-td.invited {
          color: $warning-color;
          background-color: $warning-background-color;
          border: 1px solid $warning;
        }
        .status-td.rejected {
          color: $error-color;
          background-color: $error-background-color;
          border: 1px solid $error-border-color;
        }
        .tooltip-td {
          .tooltip-wrapper {
            width: 250px;
            .tooltip-arrow {
              bottom: 24px;
              .content-tag {
                text-align: justify;
                text-transform: none;
              }
            }
            .tip {
              top: -14px;
            }
          }
        }

        // View Details Table Style
        .view-details-container {
          padding: 0;
          .view-details-table {
            width: 100%;
            border-spacing: 0;
            .thead-container {
              .table-head-tr {
                th {
                  background-color: $table-head-1;
                  @include padding(10px);
                }
              }
            }
            .view-details-tbody {
              tr td {
                vertical-align: baseline;
              }
            }
          }
        }
      }
    }
  }
}
