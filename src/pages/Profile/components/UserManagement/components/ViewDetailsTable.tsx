import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {USER_ROLE} from '@common/constants';
import {IBusinessDetails} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {VIEW_BUSINESS_DETAILS_TABLE_HEADER} from '@pages/Profile/utils';
import {memo} from 'react';

interface IViewDetailsProps {
  data: IBusinessDetails[];
}

function ViewDetailsTable({data}: IViewDetailsProps) {
  const uniquePanNumber = new Set<string>();

  // Adding pan number in Set to merge the cells for the same pan, org name and iec code
  const addPanInSet = (set: Set<string>, pan: string) => {
    set.add(pan);
    return pan;
  };
  return (
    <table className='view-details-table'>
      <TableHeader mainHeader={VIEW_BUSINESS_DETAILS_TABLE_HEADER} />
      {data.length > 0 ? (
        <TableBody className='view-details-tbody'>
          {data.flatMap((business: IBusinessDetails, index) =>
            business.productDetails.map((product) => {
              const {iecCode, organizationName, pan, productDetails} = business;
              const {productName, roleName} = product;
              return (
                <TableRow key={`viewDetailsRow${productName}${index + 1}`}>
                  {!uniquePanNumber.has(business.pan) ? (
                    <>
                      <TableCell rowSpan={productDetails.length}>
                        {organizationName}
                      </TableCell>
                      <TableCell rowSpan={productDetails.length}>
                        {addPanInSet(uniquePanNumber, pan)}
                      </TableCell>
                      <TableCell rowSpan={productDetails.length}>
                        {iecCode}
                      </TableCell>
                    </>
                  ) : null}
                  <TableCell>{productName}</TableCell>
                  <TableCell>
                    {USER_ROLE[roleName as keyof typeof USER_ROLE]}
                  </TableCell>
                </TableRow>
              );
            })
          )}
        </TableBody>
      ) : (
        <EmptyTable colSpan={VIEW_BUSINESS_DETAILS_TABLE_HEADER.length} />
      )}
    </table>
  );
}

export default memo(ViewDetailsTable);
