import DeleteConfirmationModal from '@common/components/DeleteConfirmationModal';
import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {
  DropdownOptionType,
  IAllUsersDetails,
  IBusinessDetails,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  deleteUser,
  getAllUsers,
  getBusiness,
  resendInviteUser,
} from '@pages/Profile/api';
import {ALL_USERS_MANAGEMENT_TABLE_HEADER} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximMultiSelectDropdown from '@shared/components/EximMultiSelectDropdown';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {SendMessageIcon, SolidDownAngle, SolidUpIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {
  ChangeEvent,
  Fragment,
  memo,
  useCallback,
  useEffect,
  useState,
} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import ViewDetailsTable from './ViewDetailsTable';

const {PROFILE, UNIFIED_USER_MANAGEMENT, ADD_USER} = Path;

const INVITED_STATUS = {
  invited: 'invited',
  accepted: 'pending',
  pending: 'pending',
  active: 'active',
};
const TOOLTIP_STATUS = {
  invited:
    'The user has invited but has not accept the invitation yet. Please accept the invitation.',
  accepted:
    'The user has accepted the invitation but has not completed the registration yet. Please resend the invitation.',
  pending:
    'The user has accepted the invitation but has not completed the registration yet. Please resend the invitation.',
  active:
    'The user has accepted the invitation and completed the registration.',
};

function AllUsersTable() {
  const navigate = useNavigate();
  const {loginEmail} = useSelector((state: RootState) => state.auth);

  const [viewDetailsId, setViewDetailsId] = useState('');
  const [totalRecords, setTotalRecords] = useState(0);
  const [allUsers, setAllUsers] = useState<IAllUsersDetails[]>([]);
  const [renderUsers, setRenderUsers] = useState<IAllUsersDetails[]>([]);
  const [searchUsers, setSearchUsers] = useState<IAllUsersDetails[]>([]);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState(false);
  const [userEmailToDelete, setUserEmailToDelete] = useState('');
  const [businessOptions, setBusinessOptions] = useState<DropdownOptionType[]>(
    []
  );
  const [page, setPage] = useState<number>(1);
  const [filterValues, setFilterValues] = useState<string[]>([]);
  const [showEntries, setShowEntries] = useState<string>('5');

  const handlePageChange = (pageNumber: string | number) => {
    setPage(Number(pageNumber));
    if (filterValues.length === 0) {
      const showDataInTable = allUsers.slice(
        (Number(pageNumber) - 1) * Number(showEntries),
        Number(showEntries) * Number(pageNumber)
      );
      setRenderUsers(showDataInTable);
    } else {
      const showDataInTable = searchUsers.slice(
        (Number(pageNumber) - 1) * Number(showEntries),
        Number(showEntries) * Number(pageNumber)
      );
      setRenderUsers(showDataInTable);
    }
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
    if (showEntries === 'all' && filterValues.length === 0)
      setRenderUsers(allUsers);
    else {
      const currentData = allUsers.slice(0, +showEntries);
      setRenderUsers(currentData);
    }
  };

  const handleFilter = (options: DropdownOptionType[]) => {
    const businessNames = options.map((opt) => opt.title);
    setFilterValues(businessNames);
    setPage(1);
  };

  const handleViewDetails = (id: string) => {
    if (id === viewDetailsId) {
      setViewDetailsId('');
    } else {
      setViewDetailsId(id);
    }
  };

  const getAllUserDetails = useCallback(async () => {
    const {data} = await getAllUsers();
    setAllUsers(data);
    setRenderUsers(data);
    setTotalRecords(data.length);
  }, []);

  const handleAddUser = (isEdit: boolean, email: string | null) => {
    navigate(`${PROFILE}${UNIFIED_USER_MANAGEMENT}${ADD_USER}`, {
      state: {isEditUser: isEdit, userEmail: email},
    });
  };

  // TODO: Need to add API once available
  const handleInviteUser = async (email: string) => {
    const response = await resendInviteUser(email);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
  };

  const handleDeleteUser = async () => {
    const response = await deleteUser(userEmailToDelete);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getAllUserDetails(); // Update the user list after delete a user
      setUserEmailToDelete(''); // Reset the value after delete
      setIsOpenDeleteModal(false); // Close the modal after delete
    }
  };

  const getAllBusiness = useCallback(async () => {
    const {data} = await getBusiness();
    // INFO: creating the options array of business names for filter dropdown
    const businessNames = data?.map(
      (item: IBusinessDetails, index: number) => ({
        id: index + 1,
        title: item.organizationName,
        isChecked: false,
      })
    );
    setBusinessOptions(businessNames);
  }, []);

  useEffect(() => {
    getAllBusiness();
  }, [getAllBusiness]);

  useEffect(() => {
    getAllUserDetails();
  }, [getAllUserDetails]);

  useEffect(() => {
    if (filterValues.length > 0 && allUsers.length > 0) {
      // INFO: Filter data based-on the selected values
      const filteredData = allUsers.map((user) => ({
        userDetails: user.userDetails,
        businessDetailsBeans: user.businessDetailsBeans.filter((business) =>
          filterValues.includes(business.organizationName)
        ),
      }));
      // INFO: Filter data based-on the business details data
      const finalFilterData = filteredData.filter(
        (user) => user.businessDetailsBeans.length > 0
      );
      setSearchUsers(finalFilterData);

      if (showEntries === 'all') setRenderUsers(finalFilterData);
      else {
        const currentData = finalFilterData.slice(0, +showEntries);
        setRenderUsers(currentData);
      }
    } else if (showEntries === 'all' && filterValues.length === 0)
      setRenderUsers(allUsers);
    else {
      const currentData = allUsers.slice(0, +showEntries);
      setRenderUsers(currentData);
    }
  }, [filterValues, showEntries, allUsers, setRenderUsers]);

  return (
    <div className='all-users-table-container'>
      <div className='table-title'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          All Users
        </EximTypography>
        <EximButton
          size='small'
          onClick={() => handleAddUser(false, null)}
          disabled={totalRecords === 0}>
          Add User
        </EximButton>
      </div>
      <TableSearchFilter
        handleSearchQuery={() => undefined}
        handleShowEntries={handleShowEntries}>
        <>
          <span>Filters:</span>
          <EximMultiSelectDropdown
            placeholder='All Businesses'
            searchPlaceholder='Search Businesses'
            options={businessOptions}
            onSelect={(list: DropdownOptionType[]) => {
              handleFilter(list);
            }}
          />
        </>
      </TableSearchFilter>
      <table className='all-users-table'>
        <TableHeader mainHeader={ALL_USERS_MANAGEMENT_TABLE_HEADER} />
        {renderUsers.length > 0 ? (
          <TableBody className='all-users-tbody'>
            {renderUsers.map((user: IAllUsersDetails, index) => {
              const {userDetails, businessDetailsBeans} = user;
              const {
                firstName,
                lastName,
                email,
                mobile,
                designation,
                status,
                uuid,
              } = userDetails;
              const statusVal =
                INVITED_STATUS[
                  status?.toLowerCase() as keyof typeof INVITED_STATUS
                ];
              return (
                <Fragment key={`allUsers${uuid}`}>
                  <TableRow>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{`${firstName} ${lastName}`}</TableCell>
                    <TableCell>
                      <span
                        role='presentation'
                        className='view-details'
                        onClick={() => handleViewDetails(email)}>
                        <span>View Details</span>
                        {viewDetailsId === email ? (
                          <SolidUpIcon fill='#408DFC' />
                        ) : (
                          <SolidDownAngle
                            width={12}
                            height={9}
                            fill='#408DFC'
                          />
                        )}
                      </span>
                    </TableCell>
                    <TableCell>{designation}</TableCell>
                    <TableCell>{mobile}</TableCell>
                    <TableCell>{email}</TableCell>
                    <TableCell className='tooltip-td'>
                      <span className={`status-td ${statusVal}`}>
                        <EximTooltip
                          content={
                            TOOLTIP_STATUS[
                              statusVal as keyof typeof TOOLTIP_STATUS
                            ]
                          }
                          direction='top'>
                          {statusVal}
                        </EximTooltip>
                      </span>
                    </TableCell>
                    <TableCell>
                      <TableActions
                        isEditIcon
                        isDeleteIcon
                        isDeleteIconDisabled={email === loginEmail}
                        isEditIconDisabled={
                          statusVal !== INVITED_STATUS.active ||
                          email === loginEmail
                        }
                        customIcon={<SendMessageIcon />}
                        isCustomIconDisabled={
                          statusVal === INVITED_STATUS.active ||
                          email === loginEmail
                        }
                        handleDelete={() => {
                          setUserEmailToDelete(email);
                          setIsOpenDeleteModal(true);
                        }}
                        handleEdit={() => handleAddUser(true, email)}
                        handleCustom={() => handleInviteUser(email)}
                      />
                    </TableCell>
                  </TableRow>
                  {viewDetailsId === email ? (
                    <TableRow>
                      <TableCell colSpan={8} className='view-details-container'>
                        <ViewDetailsTable data={businessDetailsBeans} />
                      </TableCell>
                    </TableRow>
                  ) : null}
                </Fragment>
              );
            })}
          </TableBody>
        ) : (
          <EmptyTable colSpan={ALL_USERS_MANAGEMENT_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={filterValues?.[0] || ''}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={searchUsers as []}
        renderData={renderUsers as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isOpenDeleteModal}
        handleConfirm={handleDeleteUser}
        onClose={() => {
          setIsOpenDeleteModal(false);
        }}
        content='Are you sure you want to delete the user?'
      />
    </div>
  );
}

export default memo(AllUsersTable);
