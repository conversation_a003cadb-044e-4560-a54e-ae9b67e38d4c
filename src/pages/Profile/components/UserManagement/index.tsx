import NavigationSubHeader from '@common/components/NavigationSubHeader';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';

import AllUsersTable from './components/AllUsersTable';
import './index.scss';

export default function UnifiedUserManagement() {
  return (
    <div className='user-management-container'>
      <div className='user-management-sub-header'>
        <NavigationSubHeader
          leftArrowRoute='#'
          isNavigate
          hasLeftArrow
          hasTitle
          leftArrowText='Unified User Management'
        />
        <EximTypography variant='body2'>Beta Version</EximTypography>
        <EximTypography variant='caption'>
          Final release version is available for certain type of subscriptions
        </EximTypography>
      </div>
      <EximPaper>
        <AllUsersTable />
      </EximPaper>
    </div>
  );
}
