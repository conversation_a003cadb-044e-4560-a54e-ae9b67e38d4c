import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {EximProducts, HelmetTitle} from '@common/constants';
import {getConfigDetails} from '@pages/EBRC/api';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximSwitchWrapper from '@shared/components/EximSwitch';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function DataFetchConfiguration() {
  const {
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const [toggleState, setToggleState] = useState({
    dutyDrawback: 'N',
    moowr: 'N',
    ebrc: 'N',
  });

  const getConfigurationDtls = useCallback(async () => {
    const response = await getConfigDetails(iecNumber);
    setToggleState({
      dutyDrawback: response?.data?.dutyDrawback,
      moowr: response?.data?.moowr,
      ebrc: response?.data?.ebrc,
    });
  }, [iecNumber]);

  const handleToggle = async (key: string) => {
    setToggleState((prevState) => ({
      ...prevState,
      [key]: prevState[key as keyof typeof prevState] === 'Y' ? 'N' : 'Y',
    }));
  };

  const handleSave = async () => {
    const payload = {
      iecCode: iecNumber,
      dutyDrawback: toggleState.dutyDrawback,
      moowr: toggleState.moowr,
      ebrc: toggleState.ebrc,
    };

    // TODO: API Integration is pending, uncomment when API is ready
    // const response = (await saveConfigDetails(payload)) as ICustomAxiosResp;
    // if (response?.status?.toString() === ResponseStatus.SUCCESS) {
    //   dispatch(
    //     alertActions.setAlertMsg({
    //       code: 200,
    //       message: response.msg,
    //       alertType: AlertStatus.SUCCESS,
    //     })
    //   );
    //   getConfigurationDtls();
    // }
  };

  useEffect(() => {
    getConfigurationDtls();
  }, [getConfigurationDtls]);

  return (
    <>
      <Helmet title={HelmetTitle.MOOWR} />
      <div className='data-fetch-configuration-container'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          isNavigate
          leftArrowRoute='#'
          leftArrowText='Data Fetch Configuration'
        />
        <EximPaper>
          <div className='sub-header-container'>
            <EximTypography variant='h5'>
              Enable fetching of extracted data
            </EximTypography>
            <div className='btn-container'>
              <EximButton size='small' onClick={handleSave}>
                Save
              </EximButton>
            </div>
          </div>
        </EximPaper>

        <div className='configuration-container'>
          <EximPaper>
            <div className='configuration-content'>
              <div className='config-settings'>
                <div>
                  <EximTypography variant='body1'>
                    {EximProducts.DUTY_DRAWBACK}
                  </EximTypography>
                  <EximTypography variant='caption'>
                    Last configured by: Vedant
                  </EximTypography>
                </div>
                <div>
                  <EximSwitchWrapper
                    id='dutyDrawback'
                    name='dutyDrawback'
                    isActive={toggleState.dutyDrawback === 'Y'}
                    onToggle={() => handleToggle('dutyDrawback')}
                  />
                </div>
              </div>
              <div className='config-settings'>
                <div>
                  <EximTypography variant='body1'>
                    {' '}
                    {EximProducts.MOOWR}
                  </EximTypography>
                  <EximTypography variant='caption'>
                    Last configured by: Vedant
                  </EximTypography>
                </div>
                <div>
                  <EximSwitchWrapper
                    id='moowr'
                    name='moowr'
                    isActive={toggleState.moowr === 'Y'}
                    onToggle={() => handleToggle('moowr')}
                  />
                </div>
              </div>
              <div className='config-settings'>
                <div>
                  <EximTypography variant='body1'>
                    {' '}
                    {EximProducts.EBRC}
                  </EximTypography>
                  <EximTypography variant='caption'>
                    Last configured by: Vedant
                  </EximTypography>
                </div>
                <div>
                  <EximSwitchWrapper
                    id='ebrc'
                    name='ebrc'
                    isActive={toggleState.ebrc === 'Y'}
                    onToggle={() => handleToggle('ebrc')}
                  />
                </div>
              </div>
            </div>
          </EximPaper>
        </div>
      </div>
    </>
  );
}

export default DataFetchConfiguration;
