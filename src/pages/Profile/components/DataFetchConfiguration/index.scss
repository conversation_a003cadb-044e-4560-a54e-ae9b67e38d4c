@import '@utils/main.scss';

.data-fetch-configuration-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .sub-header-container {
    @include flex-item(row, space-between, center);
    @include margin-top(2px);
    @include padding(10px 16px);
    .button-wrapper {
      min-width: 100px;
      .base-btn {
        height: 32px;
        font-size: $font-size-sm;
        @include padding(7px 16px);
      }
    }
  }

  .configuration-container {
    @include margin-top(20px);

    .header-title {
      border-bottom: 1px solid $primary-border;
      @include padding(20px);
      @include flex-item(row, space-between, center);
    }

    .configuration-content {
      @include flex-item(column, flex-start, flex-start);
      @include padding(10px 20px);

      .config-settings {
        width: 100%;
        @include flex-item(row, space-between, flex-end);
        @include padding(10px 0);
        border-bottom: 1px solid $primary-border;
        .typography-variant-caption {
          color: $label-color;
        }
      }
      .config-settings:last-child {
        border-bottom: none;
      }
    }
  }
}
