@import '@utils/main.scss';

.claim-report-container {
  .paper-wrapper-rounded {
    @include padding(28px 24px 28px 24px);
    @include rfs(5px, border-radius);
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include flex-item(column, _, _);
    @include margin(0);
    border: none;
  }
  .add-category {
    width: 30%;
    @include padding-bottom(28px);
    .select-dropdown {
      width: 100%;
      @include padding(6px 10px);
      .dropdown-label {
        font-size: $font-size-sm;
        color: $label-color;
      }
      .custom-dropdown {
        background-color: transparent;
        .dropdown-item:hover {
          background-color: $secondary;
          color: $tertiary;
        }
      }
      .error-message {
        font-size: $font-size-sm;
      }
    }
  }
  .add-signatory-details {
    width: 100%;
    border-top: 1px solid $primary-border;
    border-bottom: 1px solid$primary-border;
    @include padding(28px 0);
    .title-div {
      @include flex-item(_, space-between, center);
      @include padding-bottom(10px);
      .button-wrapper {
        width: 100px;
        .base-btn {
          height: 32px;
        }
        .btn-children {
          font-size: $font-size-sm;
        }
      }
    }
    .add-details-input {
      @include flex-item(_, flex-start, center, _, 20px);
      @include margin-top(24px);
      width: 60%;
    }
    .btn-container {
      @include flex-item(_, flex-end, center, _, 20px);
      @include margin-top(24px);
      .button-wrapper {
        width: 100px;
        .base-btn {
          height: 32px;
        }
        .btn-children {
          font-size: $font-size-sm;
        }
      }
    }
  }
  .claim-details-table-container {
    @include margin-top(32px);
    width: 100%;
    .claim-details-table {
      @include margin-top(24px);
      width: 100%;
      border-spacing: 0;
      .claim-details-tbody {
        .editable-td {
          @include padding(5px 10px);
          .select-dropdown {
            padding: 0;
            width: 100%;
            .dropdown-label {
              font-size: $font-size-sm;
              color: $label-color;
            }
            .custom-dropdown {
              background-color: transparent;
              .dropdown-item:hover {
                background-color: $secondary;
                color: $tertiary;
              }
            }
          }
          .tooltip-wrapper {
            .tooltip-top.secondary {
              justify-content: flex-start;
              position: absolute;
              width: 260px;
              left: 130px;
              bottom: 34px;
              .content-tag {
                width: max-content;
                @include padding(1px 4px);
              }
            }
            .tip-top.secondary {
              display: none;
            }
          }
        }
      }
    }
  }
}
