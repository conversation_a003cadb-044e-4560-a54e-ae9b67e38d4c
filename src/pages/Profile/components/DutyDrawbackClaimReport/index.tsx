import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {AlertStatus, ResponseStatus} from '@common/constants';
import {ICustomAxiosResp, ISignatoryDetails} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {addSignatoryDetails, getSignatoryDetails} from '@pages/Profile/api';
import {
  CLAIM_REPORTS_CATEGORY_DROPDOWN,
  addSignatorySchema,
} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import ClaimReportTable from './ClaimReportTable';
import './index.scss';

export default function DutyDrawbackClaimReport() {
  const {profilePan} = useSelector((state: RootState) => state.profile);

  const [isOpenInput, setIsOpenInput] = useState(false);
  const [signatoryData, setSignatoryData] = useState<ISignatoryDetails[]>([]);

  const getSignatoryData = useCallback(async () => {
    const {data} = await getSignatoryDetails(profilePan);
    setSignatoryData(data || []);
  }, [profilePan]);

  const formik = useFormik({
    initialValues: {report_type: '', name: '', designation: ''},
    validationSchema: addSignatorySchema,
    onSubmit: async (values, {resetForm}) => {
      const response = (await addSignatoryDetails(
        profilePan,
        values
      )) as ICustomAxiosResp;

      if (response.status.toString() === ResponseStatus.SUCCESS) {
        getSignatoryData(); // INFO: getting the records after adding a new record
        dispatch(
          alertActions.setAlertMsg({
            code: response.status,
            message: response.data.message || response.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
      resetForm();
    },
  });

  useEffect(() => {
    getSignatoryData();
  }, [getSignatoryData]);

  return (
    <div className='claim-report-container'>
      <NavigationSubHeader
        leftArrowRoute='#'
        isNavigate
        hasLeftArrow
        hasTitle
        leftArrowText='Signatory for Brand Rate Duty Drawback'
      />
      <EximPaper>
        <form onSubmit={formik.handleSubmit}>
          <div className='add-category'>
            <EximCustomDropdown
              id='report-type'
              label='Report Category'
              placeholder='Select Category'
              onSelect={({value}) => formik.setFieldValue('report_type', value)}
              dataTestId='report-type-dropdown'
              optionsList={CLAIM_REPORTS_CATEGORY_DROPDOWN}
              isInvalid={
                ((formik.errors.report_type &&
                  formik.touched.report_type) as boolean) || false
              }
              errorMessage={
                formik.errors.report_type
                  ? (formik.errors.report_type as string)
                  : ''
              }
            />
          </div>
          <div className='add-signatory-details'>
            <div className='title-div'>
              <EximTypography variant='h3' fontWeight='semi-bold'>
                Add Signatory for Brand Rate Duty Drawback
              </EximTypography>
              {!isOpenInput && (
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenInput(true)}>
                  Add
                </EximButton>
              )}
            </div>
            {isOpenInput && (
              <>
                <div className='add-details-input'>
                  <EximInput
                    id='name'
                    label='Name'
                    dataTestid='name'
                    placeholder='Enter name'
                    name='name'
                    maxLength={64}
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    onBlur={(e) => formik.values.name && formik.handleBlur(e)}
                    isInvalid={
                      ((formik.errors.name &&
                        formik.touched.name) as boolean) || false
                    }
                    errorMessage={
                      formik.errors.name ? (formik.errors.name as string) : ''
                    }
                  />
                  <EximInput
                    id='designation'
                    label='Designation'
                    dataTestid='designation'
                    placeholder='Enter designation'
                    name='designation'
                    maxLength={64}
                    value={formik.values.designation}
                    onChange={formik.handleChange}
                    onBlur={(e) =>
                      formik.values.designation && formik.handleBlur(e)
                    }
                    isInvalid={
                      ((formik.errors.designation &&
                        formik.touched.designation) as boolean) || false
                    }
                    errorMessage={
                      formik.errors.designation
                        ? (formik.errors.designation as string)
                        : ''
                    }
                  />
                </div>
                <div className='btn-container'>
                  <EximButton
                    size='small'
                    color='secondary'
                    onClick={() => setIsOpenInput(false)}>
                    Cancel
                  </EximButton>
                  <EximButton type='submit' size='small'>
                    Save
                  </EximButton>
                </div>
              </>
            )}
          </div>
        </form>
        <ClaimReportTable
          signatoryData={signatoryData}
          getSignatoryData={getSignatoryData}
        />
      </EximPaper>
    </div>
  );
}
