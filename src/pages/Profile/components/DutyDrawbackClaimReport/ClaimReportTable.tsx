import DeleteConfirmationModal from '@common/components/DeleteConfirmationModal';
import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableHeader from '@common/components/TableHeader';
import {AlertStatus, ResponseStatus} from '@common/constants';
import {selectedOptionId} from '@common/helpers';
import {ICustomAxiosResp, ISignatoryDetails} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {addSignatoryDetails, deleteSignatoryDetails} from '@pages/Profile/api';
import {
  CLAIM_REPORTS_CATEGORY_DROPDOWN,
  CLAIM_REPORTS_NAME,
  CLAIM_REPORTS_TABLE_HEADER,
} from '@pages/Profile/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {SquareCheckIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IClaimReportProps {
  signatoryData: ISignatoryDetails[];
  getSignatoryData: () => void;
}

export default function ClaimReportTable({
  signatoryData,
  getSignatoryData,
}: IClaimReportProps) {
  const {profilePan} = useSelector((state: RootState) => state.profile);

  const [reportType, setReportType] = useState('');
  const [name, setName] = useState('');
  const [designation, setDesignation] = useState('');
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState(false);
  const [reportTypeToDelete, setReportTypeToDelete] = useState('');
  const [recordToDelete, setRecordToDelete] = useState<number | null>(null);
  const [editableId, setEditableId] = useState<number | null>(null);

  const handleSaveDetails = async (refId: number) => {
    const payload = {
      name,
      designation,
      report_type: reportType,
      ref_id: editableId,
    };
    const response = (await addSignatoryDetails(
      profilePan,
      payload
    )) as ICustomAxiosResp;

    if (response.status.toString() === ResponseStatus.SUCCESS) {
      getSignatoryData(); // INFO: getting the records after updating the current record
      dispatch(
        alertActions.setAlertMsg({
          code: response.status,
          message: response.data.message || response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      // INFO: Reset the values
      setEditableId(null);
      setName('');
      setReportType('');
      setDesignation('');
    }
  };

  const handleDeleteRecord = async () => {
    const response = (await deleteSignatoryDetails(
      profilePan,
      reportTypeToDelete,
      [recordToDelete]
    )) as ICustomAxiosResp;

    if (response.status.toString() === ResponseStatus.SUCCESS) {
      getSignatoryData(); // INFO: getting the records after deleting a record
      dispatch(
        alertActions.setAlertMsg({
          code: response.status,
          message: response.data.message || response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      // INFO: Reset the values
      setRecordToDelete(null);
      setIsOpenDeleteModal(false); // Closing the delete modal
    }
  };

  useEffect(() => {
    const getItemToEdit = signatoryData?.find(
      (item: ISignatoryDetails) => item.ref_id === editableId
    );
    if (getItemToEdit) {
      setReportType(getItemToEdit.report_type);
      setName(getItemToEdit.name);
      setDesignation(getItemToEdit.designation);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editableId]);

  return (
    <div className='claim-details-table-container'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Claim Report Settings
      </EximTypography>
      <table className='claim-details-table'>
        <TableHeader mainHeader={CLAIM_REPORTS_TABLE_HEADER} />
        {signatoryData.length > 0 ? (
          <TableBody className='claim-details-tbody'>
            {signatoryData.map((item) =>
              item.ref_id === editableId ? (
                <TableRow key={item.ref_id}>
                  <TableCell className='editable-td'>
                    <EximCustomDropdown
                      id='edit-report-type'
                      placeholder='Select Category'
                      onSelect={({value}) => setReportType(value)}
                      dataTestId='report-type-dropdown'
                      optionsList={CLAIM_REPORTS_CATEGORY_DROPDOWN}
                      defaultOption={selectedOptionId(
                        CLAIM_REPORTS_CATEGORY_DROPDOWN,
                        item.report_type
                      )}
                    />
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      className='input-tooltip-text'
                      content={null}
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='name'
                        name='name'
                        dataTestid='name'
                        value={name}
                        onChange={(event) => setName(event.target.value)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell className='editable-td'>
                    <EximTooltip
                      className='input-tooltip-text'
                      content={null}
                      direction='top'
                      variant='secondary'>
                      <EximInput
                        id='designation'
                        name='designation'
                        dataTestid='designation'
                        value={designation}
                        onChange={(event) => setDesignation(event.target.value)}
                      />
                    </EximTooltip>
                  </TableCell>
                  <TableCell>
                    <TableActions
                      isDeleteIcon
                      customIcon={<SquareCheckIcon />}
                      handleDelete={() => {
                        setReportTypeToDelete(item.report_type);
                        setRecordToDelete(item.ref_id);
                        setIsOpenDeleteModal(true);
                      }}
                      handleCustom={() => handleSaveDetails(item.ref_id)}
                    />
                  </TableCell>
                </TableRow>
              ) : (
                <TableRow key={item.ref_id}>
                  <TableCell>
                    {
                      CLAIM_REPORTS_NAME[
                        item.report_type as keyof typeof CLAIM_REPORTS_NAME
                      ]
                    }
                  </TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.designation}</TableCell>
                  <TableCell>
                    <TableActions
                      isEditIcon
                      isDeleteIcon
                      handleDelete={() => {
                        setReportTypeToDelete(item.report_type);
                        setRecordToDelete(item.ref_id);
                        setIsOpenDeleteModal(true);
                      }}
                      handleEdit={() => setEditableId(item.ref_id)}
                    />
                  </TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        ) : (
          <EmptyTable colSpan={CLAIM_REPORTS_TABLE_HEADER.length} />
        )}
      </table>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isOpenDeleteModal}
        handleConfirm={handleDeleteRecord}
        onClose={() => {
          setIsOpenDeleteModal(false);
        }}
        content='Are you sure you want to delete this record?'
      />
    </div>
  );
}
