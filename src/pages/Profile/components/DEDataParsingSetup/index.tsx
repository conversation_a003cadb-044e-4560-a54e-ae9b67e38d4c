import BusinessHeader from '@common/components/BusinessHeader';
import DeleteConfirmationModal from '@common/components/DeleteConfirmationModal';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableActions from '@common/components/TableActions';
import TableHeader from '@common/components/TableHeader';
import {
  AlertStatus,
  DATA_EXTRACTOR_FILE_TYPE,
  REGEXP,
  ResponseStatus,
} from '@common/constants';
import {selectedOptionId} from '@common/helpers';
import {
  ICustomAxiosResp,
  IFiledIdentificationStrategy,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  deleteParsingDetails,
  getParsingDetails,
  updateParsingDetails,
} from '@pages/Profile/api';
import {
  DATA_PARSING_DESCRIPTION_DROPDOWN,
  DATA_PARSING_SETUP_RULE_DROPDOWN,
  DATA_PARSING_SETUP_TABLE_HEADER,
} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximInput from '@shared/components/EximInput';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon, SquareCheckIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

const {SB, BOE} = DATA_EXTRACTOR_FILE_TYPE;

const prefixSuffix = {
  WITH_PREFIX: 'Prefix',
  WITH_SUFFIX: 'Suffix',
};

export default function DEDataParsingSetup() {
  const {
    profile: {profilePan},
    auth: {
      userData: {email},
    },
  } = useSelector((state: RootState) => state);

  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState(false);
  const [isOpenParsingDescModal, setIsOpenParsingDescModal] = useState(false);
  const [editableRule, setEditableRule] = useState<'BOE' | 'SB' | ''>('');
  const [prefixSuffixVal, setPrefixSuffixVal] = useState<string>('');
  const [termination, setTermination] = useState<string>('SPACE');
  const [parsingOptVal, setParsingOptVal] = useState<string>('');
  const [optionErrMsg, setOptionErrMsg] = useState<string>('');
  const [prefixErrMsg, setPrefixErrMsg] = useState<string>('');
  const [recordToDelete, setRecordToDelete] = useState({
    targetField: '',
    type: '',
  });
  const [boeData, setBoeData] = useState<IFiledIdentificationStrategy[]>([]);
  const [sbData, setSbData] = useState<IFiledIdentificationStrategy[]>([]);
  const [sbRule, setSbRule] = useState('');
  const [boeRule, setBoeRule] = useState('');

  const getFieldIdentityData = useCallback(async () => {
    const boeResp = await getParsingDetails(profilePan, email, BOE);
    const sbResp = await getParsingDetails(profilePan, email, SB);
    if (boeResp.data.length > 0) {
      const data = boeResp.data[0]['active-strategy-list'];
      if (data.length > 0) setBoeData(data);
    } else {
      setBoeData([]);
    }
    if (sbResp.data.length > 0) {
      const data = sbResp.data[0]['active-strategy-list'];
      if (data.length > 0) setSbData(data);
    } else {
      setSbData([]);
    }
  }, [profilePan, email]);

  const handleResetValues = () => {
    setTermination('');
    setParsingOptVal('');
    setOptionErrMsg('');
    setPrefixSuffixVal('');
    setPrefixErrMsg('');
  };

  const handleSaveRule = async (type: 'SB' | 'BOE') => {
    const payload = {
      active: true,
      'is-active': true,
      'file-type': type === 'SB' ? SB : BOE,
      'target-field': type === 'SB' ? 'prodCode' : 'itemCode',
      'source-field': type === 'SB' ? 'prodDesc' : 'itemDesc',
      strategy: type === 'SB' ? sbRule : boeRule,
      termination,
      'prefix-suffix': prefixSuffixVal,
      length: 0,
      'special-char': '',
    };
    if (termination === 'LENGTH') {
      payload.length = Number(parsingOptVal);
    }
    if (termination === 'SPECIAL_CHAR') {
      payload['special-char'] = parsingOptVal;
    }

    const response = (await updateParsingDetails(
      profilePan,
      email,
      payload
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      handleResetValues();
      getFieldIdentityData();
      setEditableRule('');
    }
  };

  const handleDeleteRule = async () => {
    const response = await deleteParsingDetails(
      profilePan,
      email,
      recordToDelete.type,
      recordToDelete.targetField
    );
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getFieldIdentityData();
      setIsOpenDeleteModal(false);
    }
  };

  const getTerminationValue = (data: IFiledIdentificationStrategy) => {
    if (data?.termination === 'LENGTH')
      return <span>{`${data.termination} : ${data.length}`}</span>;
    if (data?.termination === 'SPECIAL_CHAR')
      return <span>{`${data.termination} : ${data['special-char']}`}</span>;
    return '-';
  };

  useEffect(() => {
    getFieldIdentityData();
  }, [getFieldIdentityData]);

  return (
    <div className='data-parsing-setup-container'>
      <NavigationSubHeader
        leftArrowRoute='#'
        isNavigate
        hasLeftArrow
        hasTitle
        leftArrowText='Data Parsing Setup'
      />

      <BusinessHeader />
      <div className='data-parsing-table-container'>
        <EximPaper>
          <table className='data-parsing-table'>
            <TableHeader mainHeader={DATA_PARSING_SETUP_TABLE_HEADER} />
            <TableBody className='data-parsing-tbody'>
              <TableRow>
                <TableCell>Item Code</TableCell>
                <TableCell>Bill of Entry</TableCell>
                <TableCell className='editable-td'>
                  <EximCustomDropdown
                    id='edit-boe-rule'
                    placeholder={editableRule === 'BOE' ? 'Select Rule' : '-'}
                    onSelect={({value}) => {
                      setBoeRule(value);
                      if (value === 'WITH_PREFIX' || value === 'WITH_SUFFIX') {
                        handleResetValues();
                        setIsOpenParsingDescModal(true);
                      }
                    }}
                    readOnly={editableRule !== 'BOE'}
                    dataTestId='boe-rule-dropdown'
                    optionsList={DATA_PARSING_SETUP_RULE_DROPDOWN}
                    defaultOption={selectedOptionId(
                      DATA_PARSING_SETUP_RULE_DROPDOWN,
                      boeData[0]?.strategy || ''
                    )}
                  />
                </TableCell>
                <TableCell>{boeData[0]?.['prefix-suffix'] || '-'}</TableCell>
                <TableCell>{getTerminationValue(boeData[0])}</TableCell>
                <TableCell>
                  <TableActions
                    isDeleteIcon
                    isEditIcon={editableRule !== 'BOE'}
                    customIcon={
                      editableRule === 'BOE' ? <SquareCheckIcon /> : null
                    }
                    handleEdit={() => setEditableRule('BOE')}
                    handleDelete={() => {
                      setRecordToDelete({targetField: 'itemCode', type: BOE});
                      setIsOpenDeleteModal(true);
                    }}
                    handleCustom={() => handleSaveRule('BOE')}
                  />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell>Product Code</TableCell>
                <TableCell>Shipping Bill</TableCell>
                <TableCell className='editable-td'>
                  <EximCustomDropdown
                    id='edit-sb-rule'
                    placeholder={editableRule === 'SB' ? 'Select Rule' : '-'}
                    onSelect={({value}) => {
                      setSbRule(value);
                      if (value === 'WITH_PREFIX' || value === 'WITH_SUFFIX') {
                        handleResetValues();
                        setIsOpenParsingDescModal(true);
                      }
                    }}
                    readOnly={editableRule !== 'SB'}
                    dataTestId='sb-rule-dropdown'
                    optionsList={DATA_PARSING_SETUP_RULE_DROPDOWN}
                    defaultOption={selectedOptionId(
                      DATA_PARSING_SETUP_RULE_DROPDOWN,
                      sbData[0]?.strategy || ''
                    )}
                  />
                </TableCell>
                <TableCell>{sbData[0]?.['prefix-suffix'] || '-'}</TableCell>
                <TableCell>{getTerminationValue(sbData[0])}</TableCell>
                <TableCell>
                  <TableActions
                    isDeleteIcon
                    isEditIcon={editableRule !== 'SB'}
                    customIcon={
                      editableRule === 'SB' ? <SquareCheckIcon /> : null
                    }
                    handleEdit={() => setEditableRule('SB')}
                    handleDelete={() => {
                      setRecordToDelete({targetField: 'prodCode', type: SB});
                      setIsOpenDeleteModal(true);
                    }}
                    handleCustom={() => handleSaveRule('SB')}
                  />
                </TableCell>
              </TableRow>
            </TableBody>
          </table>
        </EximPaper>
      </div>

      {/* Parsing Description Modal */}
      <div className='parsing-desc-container'>
        <EximModal
          isOpen={isOpenParsingDescModal}
          onClose={() => setIsOpenParsingDescModal(false)}
          onOutSideClickClose={() => setIsOpenParsingDescModal(false)}
          content={
            <div className='parsing-desc-modal'>
              <div className='prefix-suffix-cont'>
                <EximInput
                  id='prefixSuffixVal'
                  name='prefixSuffixVal'
                  maxLength={50}
                  onChange={(event) => {
                    const {value} = event.target;
                    setPrefixSuffixVal(value);
                    // TODO: Need to discuss it and apply validation
                    // if (
                    //   prefixSuffixVal.length > 50 ||
                    //   !REGEXP.alphanumeric.test(value)
                    // ) {
                    //   setPrefixErrMsg('Please enter the valid input');
                    // } else {
                    //   setPrefixErrMsg('');
                    // }
                  }}
                  value={prefixSuffixVal}
                  placeholder={
                    editableRule === 'SB'
                      ? prefixSuffix[sbRule as keyof typeof prefixSuffix]
                      : prefixSuffix[boeRule as keyof typeof prefixSuffix]
                  }
                  errorMessage={prefixSuffixVal.length > 0 ? prefixErrMsg : ''}
                  isInvalid={
                    prefixSuffixVal.length > 0 && prefixErrMsg.length > 0
                  }
                />
                {(editableRule === 'SB' && sbRule === 'WITH_PREFIX') ||
                (editableRule === 'BOE' && boeRule === 'WITH_PREFIX') ? (
                  <EximCustomDropdown
                    label='Termination'
                    id='parsing-description'
                    placeholder='Select Option'
                    onSelect={({value}) => setTermination(value)}
                    dataTestId='parsing-description-dropdown'
                    optionsList={DATA_PARSING_DESCRIPTION_DROPDOWN}
                    defaultOption={selectedOptionId(
                      DATA_PARSING_DESCRIPTION_DROPDOWN,
                      'Space'
                    )}
                  />
                ) : null}
              </div>
              {termination === 'LENGTH' || termination === 'SPECIAL_CHAR' ? (
                <div className='parsing-desc-input'>
                  <EximInput
                    label='Option'
                    id='parsingOptVal'
                    name='parsingOptVal'
                    maxLength={64}
                    onChange={(event) => {
                      const {value} = event.target;
                      setParsingOptVal(value);
                      if (
                        (termination === 'LENGTH' &&
                          REGEXP.numbers.test(value)) ||
                        (termination === 'SPECIAL_CHAR' &&
                          REGEXP.specialChar.test(value))
                      ) {
                        setOptionErrMsg('');
                      } else {
                        setOptionErrMsg('Please enter the valid input');
                      }
                    }}
                    value={parsingOptVal}
                    placeholder='Option 1'
                    errorMessage={parsingOptVal.length > 0 ? optionErrMsg : ''}
                    isInvalid={
                      parsingOptVal.length > 0 && optionErrMsg.length > 0
                    }
                  />
                </div>
              ) : null}
              <div className='parsing-desc-modal-actions'>
                <EximButton
                  color='secondary'
                  onClick={() => setIsOpenParsingDescModal(false)}>
                  Cancel
                </EximButton>
                <EximButton
                  type='submit'
                  onClick={() => setIsOpenParsingDescModal(false)}>
                  Submit
                </EximButton>
              </div>
            </div>
          }
          footer={false}
          header={
            <EximTypography
              classNames='data-parsing-modal-title'
              variant='h4'
              fontWeight='semi-bold'>
              Parse using{' '}
              {editableRule === 'SB'
                ? prefixSuffix[sbRule as keyof typeof prefixSuffix]
                : prefixSuffix[boeRule as keyof typeof prefixSuffix]}{' '}
              of the description
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isOpenDeleteModal}
        handleConfirm={handleDeleteRule}
        onClose={() => {
          setIsOpenDeleteModal(false);
        }}
        content='Are you sure you want to delete this record?'
      />
    </div>
  );
}
