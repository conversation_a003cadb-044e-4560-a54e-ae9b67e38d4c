@import '@utils/main.scss';

.data-parsing-setup-container {
  .data-parsing-table-container {
    @include margin-top(24px);
    .paper-wrapper-rounded {
      @include padding(28px 24px 28px 24px);
      @include rfs(5px, border-radius);
      box-shadow: 0px 3px 6px $box-shadow-color;
      @include flex-item(column, _, _);
      @include margin(0);
      border: none;
    }

    .data-parsing-table {
      width: 100%;
      border-spacing: 0;

      .data-parsing-tbody {
        .editable-td {
          padding: 0 10px;
          .select-dropdown {
            .custom-dropdown {
              height: 28px;
              background: none;
              .item-holder {
                .dropdown-item {
                  height: 28px;
                }
              }
            }
          }
        }
      }
    }
  }

  // Parsing Description Modal
  .parsing-desc-container {
    .modal-body {
      width: 348px;
      height: 342px;
      .modal-header {
        @include flex-item(row, space-between, flex-end);
        @include padding(30px 30px 0px 30px);
      }
      .modal-content {
        height: 274px;
        @include margin-top(20px);
        @include padding(0 30px 30px 30px);
        @include flex-item(column, space-between, flex-start);
      }
      .data-parsing-modal-title {
        color: $secondary-text;
      }
    }

    .parsing-desc-modal {
      width: 100%;
      .select-dropdown {
        width: 100%;
        .custom-dropdown {
          background: none;
        }
      }
      .prefix-suffix-cont {
        width: 100%;
        @include flex-item(column, space-between, flex-start, _, 20px);
      }
      .parsing-desc-input {
        @include margin-top(48px);
      }
      &-actions {
        position: absolute;
        bottom: 30px;
        right: 30px;
        @include flex-item(row, flex-end, flex-end, nowrap, 10px);
        .button-wrapper {
          width: 100px;
          .base-btn {
            height: 32px;
            font-size: $base-font-size;
          }
        }
      }
    }
  }
}
