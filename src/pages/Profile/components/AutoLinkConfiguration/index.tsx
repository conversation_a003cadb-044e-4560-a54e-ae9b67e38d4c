import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {AlertStatus, HelmetTitle, ResponseStatus} from '@common/constants';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {getConfigDetails, saveConfigDetails} from '@pages/EBRC/api';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximSwitchWrapper from '@shared/components/EximSwitch';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function AutoLinkConfiguration() {
  const {
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const [toggleState, setToggleState] = useState({
    linkOneIrmToManySb: 'N',
    linkOneSbToManyIrm: 'N',
    checkSubstringFromRemitterName: 'N',
  });

  const getConfigurationDtls = useCallback(async () => {
    const response = await getConfigDetails(iecNumber);
    setToggleState({
      linkOneIrmToManySb: response?.data?.linkOneIrmToManySb,
      linkOneSbToManyIrm: response?.data?.linkOneSbToManyIrm,
      checkSubstringFromRemitterName:
        response?.data?.checkSubstringFromRemitterName,
    });
  }, [iecNumber]);

  const handleToggle = async (key: string) => {
    setToggleState((prevState) => ({
      ...prevState,
      [key]: prevState[key as keyof typeof prevState] === 'Y' ? 'N' : 'Y',
    }));
  };

  const handleSave = async () => {
    const payload = {
      iecCode: iecNumber,
      linkOneIrmToManySb: toggleState.linkOneIrmToManySb,
      linkOneSbToManyIrm: toggleState.linkOneSbToManyIrm,
      checkSubstringFromRemitterName:
        toggleState.checkSubstringFromRemitterName,
    };

    const response = (await saveConfigDetails(payload)) as ICustomAxiosResp;
    if (response?.status?.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getConfigurationDtls();
    }
  };

  useEffect(() => {
    getConfigurationDtls();
  }, [getConfigurationDtls]);

  return (
    <>
      <Helmet title={HelmetTitle.MOOWR} />
      <div className='ebrc-autolink-configuration-container'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          isNavigate
          leftArrowRoute='#'
          leftArrowText='Auto Link Configuration'
        />
        <BusinessHeader />
        <EximPaper>
          <div className='sub-header-container'>
            <EximTypography variant='h5'>Configuration</EximTypography>
            <div className='btn-container'>
              <EximButton size='small' onClick={handleSave}>
                Save
              </EximButton>
            </div>
          </div>
        </EximPaper>

        <div className='configuration-container'>
          <EximPaper>
            <div className='configuration-content'>
              <div className='config-settings'>
                <EximTypography variant='body1'>
                  Link One IRM to Many Shipping Bills
                </EximTypography>
                <div>
                  <EximSwitchWrapper
                    id='linkOneIrmToManySb'
                    name='linkOneIrmToManySb'
                    isActive={toggleState.linkOneIrmToManySb === 'Y'}
                    onToggle={() => handleToggle('linkOneIrmToManySb')}
                  />
                </div>
              </div>
              <div className='config-settings'>
                <EximTypography variant='body1'>
                  Link One Shipping to Many IRMs
                </EximTypography>
                <div>
                  <EximSwitchWrapper
                    id='linkOneSbToManyIrm'
                    name='linkOneSbToManyIrm'
                    isActive={toggleState.linkOneSbToManyIrm === 'Y'}
                    onToggle={() => handleToggle('linkOneSbToManyIrm')}
                  />
                </div>
              </div>
              <div className='config-settings'>
                <EximTypography variant='body1'>
                  Check the Substring of Remitter name in Consignee Name from
                  Shipping
                </EximTypography>
                <div>
                  <EximSwitchWrapper
                    id='checkSubstringFromRemitterName'
                    name='checkSubstringFromRemitterName'
                    isActive={
                      toggleState.checkSubstringFromRemitterName === 'Y'
                    }
                    onToggle={() =>
                      handleToggle('checkSubstringFromRemitterName')
                    }
                  />
                </div>
              </div>
            </div>
          </EximPaper>
        </div>
      </div>
    </>
  );
}

export default AutoLinkConfiguration;
