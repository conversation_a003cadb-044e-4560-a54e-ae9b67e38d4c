import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {IUserProfileData} from '@common/interfaces';
import {getUserDetails} from '@pages/Profile/api';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import UserDetailsFrom from './UserDetailsFrom';
import './index.scss';

const initialValues = {
  email: '',
  firstName: '',
  lastName: '',
  mobile: '',
  uuid: '',
};

function UserProfile() {
  const {
    userData: {uuid},
  } = useSelector((state: RootState) => state.auth);

  const [isLoading, setIsLoading] = useState(true);
  const [profileData, setProfileData] =
    useState<IUserProfileData>(initialValues);

  const getUserProfileData = useCallback(async () => {
    const payload = {
      passKey: 'EX!^^#3R0',
      userCsvList: uuid,
    };
    const {data} = await getUserDetails(payload);
    setProfileData(data?.[0]);
    setIsLoading(false);
  }, [uuid]);

  useEffect(() => {
    getUserProfileData();
  }, [getUserProfileData]);

  return (
    <div className='user-profile-container'>
      <NavigationSubHeader
        leftArrowRoute='#'
        isNavigate
        hasLeftArrow
        hasTitle
        leftArrowText='User Profile'
      />
      <EximPaper>
        {!isLoading ? (
          <UserDetailsFrom
            getUserProfileData={getUserProfileData}
            userProfileData={profileData}
          />
        ) : null}
      </EximPaper>
    </div>
  );
}

export default UserProfile;
