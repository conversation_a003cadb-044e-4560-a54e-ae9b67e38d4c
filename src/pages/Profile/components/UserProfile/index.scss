@import '@utils/main.scss';

.user-profile-container {
  .paper-wrapper-rounded {
    @include padding(32px 80px 32px 32px);
    @include rfs(5px, border-radius);
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include flex-item(column, _, _, _, 20px);
    @include margin(0);
    border: none;
  }
  .profile-details-container {
    .profile-header-container {
      @include flex-item(row, space-between, flex-start);

      .profile-details {
        @include flex-item(_, flex-start, _, _, 28px);
        .user-profile {
          position: relative;
          span {
            position: absolute;
            bottom: 4px;
            right: -5px;
            cursor: pointer;
          }
        }
        .user-details {
          .typography-wrapper {
            text-transform: capitalize;
          }
          @include flex-item(column, _, flex-start, _, 5px);
          .user-created {
            font-size: $font-size-md;
            color: $label-color;
            text-align: left;
          }
          .user-email {
            font-size: $font-size-xsm;
            color: $label-color;
            text-align: left;
          }
        }
      }
      .button-wrapper {
        width: 100px;
        .base-btn {
          height: 32px;
          font-size: $font-size-sm;
        }
      }
    }

    // User profile form style
    .profile-form-inputs-container {
      @include margin-top(30px);
      width: 50%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 24px;
      row-gap: 60px;
      @include padding(24px 0 60px);
      .input-wrapper.capitalize-text {
        input {
          text-transform: capitalize;
        }
      }
      .input-wrapper {
        input {
          font-size: $font-size-md;
        }
        .form-input.disabled {
          input {
            @include margin-left(-10px);
            font-size: $font-size-md;
            color: $text-color;
            border: none;
            outline: none;
          }
        }
      }
    }
  }
}
