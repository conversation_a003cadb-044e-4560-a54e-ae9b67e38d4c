import {AlertStatus, ResponseStatus} from '@common/constants';
import {IUserProfileData} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {authActions} from '@pages/Auth/store/reducer';
import {saveUserDetails} from '@pages/Profile/api';
import {profileDetailsSchema} from '@pages/Profile/utils';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import EximTypography from '@shared/components/EximTypography';
import {EditProfileIcon, UserProfile} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useState} from 'react';
import {useSelector} from 'react-redux';

interface IProps {
  getUserProfileData: () => void;
  userProfileData: IUserProfileData;
}

function UserDetailsFrom({getUserProfileData, userProfileData}: IProps) {
  const {
    userData: {createdAt},
  } = useSelector((state: RootState) => state.auth);

  const [isEdit, setIsEdit] = useState(false);

  const formik = useFormik({
    initialValues: userProfileData,
    validationSchema: profileDetailsSchema,
    onSubmit: async (payload) => {
      if (isEdit) {
        const response = await saveUserDetails(payload);
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.data,
              alertType: AlertStatus.SUCCESS,
            })
          );
        }
        // INFO: update the redux values if get the success response
        dispatch(authActions.updateUserDetails(payload));
      }
      getUserProfileData(); // Update the data
      setIsEdit((prev) => !prev);
    },
  });

  return (
    <div className='profile-details-container'>
      <form onSubmit={formik.handleSubmit}>
        <div className='profile-header-container'>
          <div className='profile-details'>
            <div className='user-profile'>
              <UserProfile height={83} width={83} />
              <span>
                <EditProfileIcon height={26} width={26} />
              </span>
            </div>
            <div className='user-details'>
              <EximTypography variant='h2' fontWeight='semi-bold'>
                {`${userProfileData.firstName} ${userProfileData.lastName}`}
              </EximTypography>
              <span className='user-created'>{`Member Since ${createdAt}`}</span>
              <span className='user-email'>{userProfileData.email}</span>
            </div>
          </div>
          <EximButton color='primary' size='small' type='submit'>
            {isEdit ? 'Save' : 'Edit'}
          </EximButton>
        </div>
        <div className='profile-form-inputs-container'>
          <EximInput
            id='firstName'
            name='firstName'
            label='First Name'
            className='capitalize-text'
            isRequired={isEdit}
            maxLength={64}
            value={formik.values.firstName?.trim()}
            onChange={formik.handleChange}
            disabled={!isEdit}
            isInvalid={
              ((formik.errors.firstName &&
                formik.touched.firstName) as boolean) || false
            }
            errorMessage={
              formik.errors.firstName ? (formik.errors.firstName as string) : ''
            }
          />
          <EximInput
            id='lastName'
            name='lastName'
            label='Last Name'
            className='capitalize-text'
            isRequired={isEdit}
            maxLength={64}
            value={formik.values.lastName?.trim()}
            onChange={formik.handleChange}
            disabled={!isEdit}
            isInvalid={
              ((formik.errors.lastName &&
                formik.touched.lastName) as boolean) || false
            }
            errorMessage={
              formik.errors.lastName ? (formik.errors.lastName as string) : ''
            }
          />
          <EximInput
            id='email'
            name='email'
            label='Email'
            value={formik.values.email}
            disabled
          />
          <EximInput
            id='mobile'
            name='mobile'
            label='Contact'
            type='text'
            maxLength={10}
            disabled={!isEdit}
            isRequired={isEdit}
            value={formik.values.mobile}
            onChange={formik.handleChange}
            isInvalid={
              ((formik.errors.mobile && formik.touched.mobile) as boolean) ||
              false
            }
            errorMessage={
              formik.errors.mobile ? (formik.errors.mobile as string) : ''
            }
          />
        </div>
      </form>
    </div>
  );
}

export default UserDetailsFrom;
