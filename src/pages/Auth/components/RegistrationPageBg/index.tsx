import {ReactComponent as ImportExportSvg} from '@assets/EximImages/RegistrationBg.svg';
import EximLink from '@shared/components/EximLink';
import EximTypography from '@shared/components/EximTypography';
import {ReactNode} from 'react';

import './index.scss';

interface IRegistrationPageProps {
  children: ReactNode;
}

function RegistrationPageBg({children}: IRegistrationPageProps) {
  return (
    <div className='registration-bg-container'>
      <div className='registration-bg'>
        <ImportExportSvg />
      </div>
      <div className='details-container'>
        {children}
        <div className='registration-footer'>
          <EximTypography classNames='copy-right' variant='body1'>
            Copyright ©
            <strong>
              {`${new Date().getFullYear()} `}
              <EximLink
                href='https://perennialsys.com/'
                animationUnderline
                target='_blank'>
                Perennial Systems
              </EximLink>
            </strong>
            All rights reserved.
          </EximTypography>
          <div className='privacy-feedback'>
            <span>Privacy</span>
            <span>|</span>
            <span>Feedback</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RegistrationPageBg;
