@import '@utils/main.scss';

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.registration-bg-container {
  display: flex;
  // subtract the navbar height to avoid the scrollbar
  height: calc(100% - 50px);

  @include lessThan(md) {
    flex-direction: column;
  }

  .registration-bg {
    background: radial-gradient(#eaf6f6ed, #84d4d4);
    display: flex;
    justify-content: center;
    flex: 0.45;
  }

  .details-container {
    flex: 0.55;
    background-color: $white;
    color: $text-color;
    @include padding(0 75px 75px 75px);
  }

  .registration-footer {
    .copy-right {
      @include flex-item(_, center, center, _, 4px);
      @include font-size($font-size-sm);
      letter-spacing: 0.5px;
      margin: auto;
      color: $text-color;
      strong {
        a {
          @include font-size(14px);
          color: $text-color;
        }
        & a:before {
          background: $text-color;
          height: 2px;
          bottom: 5px;
        }
      }
    }
    .privacy-feedback {
      @include flex-item(_, center, center, _, 10px);
      @include margin-top(10px);
      font-size: $font-size-sm;
      opacity: 0.65;
    }
  }
}
