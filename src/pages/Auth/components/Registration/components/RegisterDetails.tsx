import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {Path} from '@common/constants';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {Link} from 'react-router-dom';

import RegistrationCompletePage from '../../RegistrationComplete';
import RegisterForm from './RegisterForm';

function RegisterDetails() {
  const {isRegistrationSuccess} = useSelector((state: RootState) => state.auth);

  return (
    <div className='registration-details'>
      {!isRegistrationSuccess ? (
        <>
          <NavigationSubHeader
            hasLeftArrow
            hasTitle
            leftArrowRoute={Path.LOGIN}
            leftArrowText='Back'
          />
          <EximTypography
            classNames='create-account-header'
            variant='h1'
            fontWeight='semi-bold'>
            Create Your Account
          </EximTypography>
          <RegisterForm />
          <div className='move-to-login'>
            <Link to={Path.LOGIN}>
              <EximTypography variant='h3'>
                Already Have An Account?
              </EximTypography>
            </Link>
          </div>
        </>
      ) : (
        <RegistrationCompletePage />
      )}
    </div>
  );
}

export default RegisterDetails;
