import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {IRegisterApiProps, registrationApi} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import {registrationSchema} from '@pages/Auth/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximInput from '@shared/components/EximInput';
import EximLink from '@shared/components/EximLink';
import EximTypography from '@shared/components/EximTypography';
import {EyeClose, EyeOpen} from '@shared/icons';
import {dispatch} from '@store';
import {useFormik} from 'formik';
import {useLocation, useNavigate} from 'react-router';

const initialValue: IRegisterApiProps = {
  email: '',
  firstName: '',
  lastName: '',
  mobile: '',
  password: '',
  type: 'BO',
  weeklyUpdates: true,
  inviteId: '',
};

export default function RegisterForm() {
  const navigate = useNavigate();
  const location = useLocation();
  const {invitedUserData} = (location.state ?? {}) as {
    invitedUserData: IRegisterApiProps;
  };

  const formik = useFormik({
    initialValues: invitedUserData || initialValue,
    validationSchema: registrationSchema,
    onSubmit: async (payload: IRegisterApiProps) => {
      delete payload?.confirmPassword;
      delete payload?.status;
      if (!payload?.inviteId) delete payload?.inviteId;
      const response = await registrationApi(payload);
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(authActions.setRegistrationEmail(payload.email));
        dispatch(
          alertActions.setAlertMsg({
            code: response.status,
            message: response.data.message,
            alertType: AlertStatus.SUCCESS,
          })
        );
        if (payload.inviteId) {
          navigate(Path.LOGIN);
        } else {
          dispatch(authActions.setIsRegistrationSuccess(true));
        }
      }
    },
  });

  return (
    <div className='registration-form'>
      <form onSubmit={formik.handleSubmit}>
        <div className='form-input-container'>
          <EximInput
            id='firstName'
            label='First Name'
            dataTestid='firstName'
            maxLength={64}
            isRequired
            placeholder='Enter First Name e.g. John'
            value={formik.values.firstName?.trim()}
            isInvalid={
              ((formik.errors.firstName &&
                formik.touched.firstName) as boolean) || false
            }
            isFocused
            onBlur={(e) => formik.values.firstName && formik.handleBlur(e)}
            name='firstName'
            autoComplete='on'
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.firstName ? (formik.errors.firstName as string) : ''
            }
          />
          <EximInput
            id='lastName'
            label='Last Name'
            dataTestid='lastName'
            maxLength={64}
            isRequired
            placeholder='Enter Last Name e.g. Smith'
            value={formik.values.lastName?.trim()}
            isInvalid={
              ((formik.errors.lastName &&
                formik.touched.lastName) as boolean) || false
            }
            onBlur={(e) => formik.values.lastName && formik.handleBlur(e)}
            name='lastName'
            autoComplete='on'
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.lastName ? (formik.errors.lastName as string) : ''
            }
          />
          <EximInput
            id='email'
            label='Email Id'
            dataTestid='email'
            maxLength={64}
            isRequired
            placeholder='Enter Email Address'
            value={formik.values.email?.trim()}
            isInvalid={
              ((formik.errors.email && formik.touched.email) as boolean) ||
              false
            }
            onBlur={(e) => formik.values.email && formik.handleBlur(e)}
            name='email'
            autoComplete='on'
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.email ? (formik.errors.email as string) : ''
            }
          />
          <EximInput
            id='mobile'
            label='Phone No'
            dataTestid='mobile'
            type='text'
            fieldType='mobileNumber'
            isRequired
            placeholder='Enter Phone Number'
            value={formik.values.mobile}
            isInvalid={
              ((formik.errors.mobile && formik.touched.mobile) as boolean) ||
              false
            }
            onBlur={(e) => formik.values.mobile && formik.handleBlur(e)}
            name='mobile'
            maxLength={10}
            autoComplete='on'
            onChange={formik.handleChange}
            errorMessage={
              formik.errors.mobile ? (formik.errors.mobile as string) : ''
            }
          />
          <EximInput
            id='password'
            label='Password'
            type='password'
            maxLength={64}
            isRequired
            dataTestid='password'
            placeholder='Enter Password'
            passwordEyeOpenIcon={<EyeClose />}
            passwordEyeCloseIcon={<EyeOpen />}
            value={formik.values.password}
            isInvalid={
              ((formik.errors.password &&
                formik.touched.password) as boolean) || false
            }
            onBlur={(e) => formik.values.password && formik.handleBlur(e)}
            name='password'
            autoComplete='on'
            onChange={(e) =>
              formik.setFieldValue('password', e.target.value.trim())
            }
            errorMessage={
              formik.errors.password ? (formik.errors.password as string) : ''
            }
          />
          <EximInput
            id='confirmPassword'
            label='Confirm Password'
            type='password'
            dataTestid='confirmPassword'
            maxLength={64}
            isRequired
            placeholder='Enter Confirm Password'
            passwordEyeOpenIcon={<EyeClose />}
            passwordEyeCloseIcon={<EyeOpen />}
            isInvalid={
              ((formik.errors.confirmPassword &&
                formik.touched.confirmPassword) as boolean) || false
            }
            onBlur={(e) =>
              formik.values.confirmPassword && formik.handleBlur(e)
            }
            name='confirmPassword'
            autoComplete='on'
            isPreventPaste
            onChange={(e) =>
              formik.setFieldValue('confirmPassword', e.target.value.trim())
            }
            errorMessage={
              formik.errors.confirmPassword
                ? (formik.errors.confirmPassword as string)
                : ''
            }
          />
        </div>
        <EximCheckbox
          id='weeklyUpdates'
          name='weeklyUpdates'
          dataTestId='weeklyUpdates'
          label='I would like to receive weekly Updates Newsletters.'
          color='#2CB544'
          size='small'
          checked={formik.values.weeklyUpdates}
          onChange={formik.handleChange}
        />
        <EximButton
          type='submit'
          dataTestId='submitBtn'
          size='small'
          disabled={formik.isSubmitting}>
          Submit
        </EximButton>
      </form>
      <EximTypography variant='body1' classNames='privacy-policy'>
        <span>
          By clicking submit above, you acknowledge you have read and agree to
          the
        </span>
        {/* TODO: Need to be Updated the below URL */}
        <span className='underline'>
          <EximLink
            href='https://qa.Eximhero.com/privacy-policy/'
            target='_blank'>
            Terms of Service
          </EximLink>
        </span>
        <span>&</span>
        <span className='underline'>
          {/* TODO: Need to be Updated the below URL */}
          <EximLink
            href='https://qa.Eximhero.com/privacy-policy/'
            target='_blank'>
            Privacy Policy
          </EximLink>
          .
        </span>
      </EximTypography>
    </div>
  );
}
