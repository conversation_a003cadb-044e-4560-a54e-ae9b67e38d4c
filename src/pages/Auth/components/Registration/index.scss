@import '@utils/main.scss';

.registration-details {
  @include lessThan(lg) {
    flex: 0.5;
    @include padding(75px 24px);
  }

  @include lessThan(md) {
    flex: 1;
  }

  .typography-variant-body1 {
    color: $label-color;
  }
  .typography-variant-h1 {
    color: $text-color;
  }

  .create-account-header {
    @include padding(10px 0 12px);
    letter-spacing: 0.5px;
  }

  .subscription-header {
    .typography-container {
      h1 {
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        letter-spacing: 0.5px;
      }
    }
    svg {
      width: 18px;
      height: 16px;
      @include margin-top(3px);
    }
  }

  // Registration form
  .registration-form {
    @include padding(0 0 32px);
    border-top: 1px solid $product-header-border;
    border-bottom: 1px solid $product-header-border;

    .form-input-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      column-gap: 24px;
      row-gap: 36px;
      @include margin(36px 0 30px);

      @include lessThan(lg) {
        column-gap: 24px;
      }

      @include lessThan(md) {
        grid-template-columns: repeat(1, 1fr);
      }

      .input-wrapper {
        margin: 12px 0;

        .mobile-label {
          color: $text-color;
          background-color: $label-background;
          width: 40px;
        }
      }

      .error-message {
        @include padding(2px 0 0);
        @include font-size(14px);
        color: $error;
      }

      .password-icon {
        display: flex;
        align-items: center;
      }

      input {
        color: $text-color;
      }
    }

    .checkbox-container {
      @include padding(0 0 8px);
      margin: 0;
      color: $label-color;
      @include font-size(14px);
      .checkbox-small {
        width: 18px;
        height: 18px;
        &::after {
          left: 5px;
          top: 2px;
          width: 4px;
          height: 8px;
          border-width: 0px 2px 2px 0;
        }
      }
      span {
        @include margin(0 0 0 24px);
      }
      .checkmarks {
        margin: 0;
      }
    }

    .button-wrapper {
      width: 175px;
      @include margin(30px 0);

      .base-btn {
        @include padding(8px 0);
        font-size: $font-size-sm;
        margin: 0;
      }
    }

    @include lessThan(md) {
      .button-wrapper {
        width: 100%;
      }
    }

    .typography-container {
      p {
        span.underline {
          text-decoration: underline;
          color: $information;
        }
        span {
          & a:before {
            background: $information;
            height: 2px;
            bottom: 5px;
          }
        }
      }
    }
    .privacy-policy {
      @include flex-item(_, _, _, _, 5px);
    }
  }

  .move-to-login {
    @include margin(30px 0 28px);

    .typography-variant-h3 {
      color: $information;
    }
    .typography-container {
      width: fit-content;
      margin: auto;

      h3 {
        text-decoration: underline;
      }
      .animation-underline {
        background-size: 200% 100%;
        background-position: -100%;
        display: inline-block;
        @include padding(5px 0);
        position: relative;
        transition: all 0.3s ease-in-out;

        &:before {
          content: '';
          background: $information;
          display: block;
          position: absolute;
          bottom: 4px;
          left: 0;
          width: 0;
          height: 2px;
          transition: all 0.3s ease-in-out;
        }

        &:hover {
          background-position: 0;
        }

        &:hover::before {
          width: 100%;
        }
      }
    }
  }
}
