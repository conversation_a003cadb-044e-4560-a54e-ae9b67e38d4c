import store from '@store';
import {act, fireEvent, render, screen, waitFor} from '@testing-library/react';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';

import RegistrationPage from '.';

// import {getInvitedUserDataApi, registerUserApi} from '../../api';

jest.mock('../../api');

const registrationMockData = {
  firstName: 'test',
  lastName: 'test',
  email: '<EMAIL>',
  mobile: '**********',
  usrPwd: '12345678',
  cnfPwd: '12345678',
  signUpAs: 'Tax Professional',
  weeklyUpdatde: '1',
  invitedBy: '',
  invite_uuid: '',
};

const invitedUserMockData = {
  firstName: 'test',
  lastName: 'test',
  email: '<EMAIL>',
  mobile: '**********',
  usrPwd: '',
  cnfPwd: '',
  signUpAs: 'BO',
  weeklyUpdatde: '0',
  invitedBy: '',
  invite_uuid: '',
};

describe('Registration page', () => {
  it('should render the registration page without error', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    expect(
      document.querySelector('.registration-bg-container')
    ).toBeInTheDocument();
    fireEvent.mouseOver(screen.getByTestId('tooltip-wrapper'));
  });

  it('should render the trusted brands on mobile screen', () => {
    window.innerWidth = 600;
    fireEvent(window, new Event('resize'));
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );
    expect(document.querySelector('.brand-logo-mobile')).toBeInTheDocument();
  });

  it('should render the registration page with validation errors', async () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    await act(async () => {
      const firstName = screen.getByTestId('firstName');
      expect(firstName).toBeInTheDocument();
      fireEvent.change(firstName, {target: {value: '12'}});

      const lastName = screen.getByTestId('lastName');
      expect(lastName).toBeInTheDocument();
      fireEvent.change(lastName, {target: {value: '123'}});

      const email = screen.getByTestId('email');
      expect(email).toBeInTheDocument();
      fireEvent.change(email, {target: {value: 'test_0077_perennialsys'}});

      const mobile = screen.getByTestId('mobile');
      expect(mobile).toBeInTheDocument();
      fireEvent.change(mobile, {target: {value: '987654321'}});

      const password = screen.getByTestId('usrPwd');
      expect(password).toBeInTheDocument();
      fireEvent.change(password, {target: {value: '1234567'}});

      const cnfPassword = screen.getByTestId('cnfPwd');
      expect(cnfPassword).toBeInTheDocument();
      fireEvent.change(cnfPassword, {target: {value: '1234567'}});

      const userType = screen.getByTestId('signUpAs');
      expect(userType).toBeInTheDocument();
      fireEvent.click(userType);

      const weeklyUpdatde = screen.getByTestId('weeklyUpdatde');
      expect(weeklyUpdatde).toBeInTheDocument();
      fireEvent.change(weeklyUpdatde, {target: {value: ''}});

      const signUpButton = screen.getByTestId('submitBtn');
      expect(signUpButton).toBeInTheDocument();
      fireEvent.click(signUpButton);
    });
    const dropdownValue = await waitFor(
      () => document.getElementsByClassName('dropdown-item')[0]
    );
    fireEvent.click(dropdownValue);
    expect(screen.getByText('Tax Professional')).toBeInTheDocument();

    expect(
      screen.queryByText('First name must contain only letters.')
    ).toBeInTheDocument();

    expect(
      screen.queryByText('Last name must contain only letters.')
    ).toBeInTheDocument();

    expect(
      screen.queryByText('Please enter valid email address.')
    ).toBeInTheDocument();

    expect(
      screen.queryByText('Mobile No. should contain minimum 10 digits.')
    ).toBeInTheDocument();

    expect(
      screen.getAllByText(
        'Password should be minimum length of 8 characters.'
      )[0]
    ).toBeInTheDocument();

    expect(
      screen.getAllByText(
        'Password should be minimum length of 8 characters.'
      )[1]
    ).toBeInTheDocument();

    expect(
      screen.queryByText('Please select account type.')
    ).toBeInTheDocument();
    expect(screen.getByText('Tax Professional')).toBeInTheDocument();
  });

  it('should render the registration page with success', async () => {
    // (registerUserApi as jest.Mock).mockReturnValue(
    //   Promise.resolve({
    //     data: registrationMockData,
    //   })
    // );

    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    expect(
      document.querySelector('.registration-bg-container')
    ).toBeInTheDocument();

    await act(async () => {
      const firstName = screen.getByTestId('firstName');
      expect(firstName).toBeInTheDocument();
      fireEvent.change(firstName, {target: {value: 'test'}});

      const lastName = screen.getByTestId('lastName');
      expect(lastName).toBeInTheDocument();
      fireEvent.change(lastName, {target: {value: 'test'}});

      const email = screen.getByTestId('email');
      expect(email).toBeInTheDocument();
      fireEvent.change(email, {target: {value: '<EMAIL>'}});

      const mobile = screen.getByTestId('mobile');
      expect(mobile).toBeInTheDocument();
      fireEvent.change(mobile, {target: {value: '**********'}});

      const password = screen.getByTestId('usrPwd');
      expect(password).toBeInTheDocument();
      fireEvent.change(password, {target: {value: '12345678'}});

      const cnfPassword = screen.getByTestId('cnfPwd');
      expect(cnfPassword).toBeInTheDocument();
      fireEvent.change(cnfPassword, {target: {value: '12345678'}});

      const userType = screen.getByTestId('signUpAs');
      expect(userType).toBeInTheDocument();
      fireEvent.click(userType);

      const weeklyUpdatde = screen.getByTestId('weeklyUpdatde');
      expect(weeklyUpdatde).toBeInTheDocument();
      fireEvent.click(weeklyUpdatde);
    });

    await act(async () => {
      const dropdownValue = await waitFor(
        () => document.getElementsByClassName('dropdown-item')[0]
      );
      fireEvent.click(dropdownValue);
      expect(screen.getByText('Tax Professional')).toBeInTheDocument();
    });

    await act(async () => {
      const signUpButton = screen.getByTestId('submitBtn');
      expect(signUpButton).toBeInTheDocument();
      fireEvent.click(signUpButton);
    });
    // expect(registerUserApi).toHaveBeenCalledTimes(1);
  });

  it('should render the registration page with success and unchecked updates', async () => {
    // (registerUserApi as jest.Mock).mockReturnValue(
    //   Promise.resolve({
    //     data: registrationMockData,
    //   })
    // );

    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    expect(
      document.querySelector('.registration-bg-container')
    ).toBeInTheDocument();

    await act(async () => {
      const firstName = screen.getByTestId('firstName');
      expect(firstName).toBeInTheDocument();
      fireEvent.change(firstName, {target: {value: 'test'}});

      const lastName = screen.getByTestId('lastName');
      expect(lastName).toBeInTheDocument();
      fireEvent.change(lastName, {target: {value: 'test'}});

      const email = screen.getByTestId('email');
      expect(email).toBeInTheDocument();
      fireEvent.change(email, {target: {value: '<EMAIL>'}});

      const mobile = screen.getByTestId('mobile');
      expect(mobile).toBeInTheDocument();
      fireEvent.change(mobile, {target: {value: '**********'}});

      const password = screen.getByTestId('usrPwd');
      expect(password).toBeInTheDocument();
      fireEvent.change(password, {target: {value: '12345678'}});

      const cnfPassword = screen.getByTestId('cnfPwd');
      expect(cnfPassword).toBeInTheDocument();
      fireEvent.change(cnfPassword, {target: {value: '12345678'}});

      const userType = screen.getByTestId('signUpAs');
      expect(userType).toBeInTheDocument();
      fireEvent.click(userType);

      const weeklyUpdatde = screen.getByTestId('weeklyUpdatde');
      expect(weeklyUpdatde).toBeInTheDocument();
      fireEvent.click(weeklyUpdatde);
      fireEvent.click(weeklyUpdatde);
    });
    await act(async () => {
      const dropdownValue = await waitFor(
        () => document.getElementsByClassName('dropdown-item')[0]
      );
      fireEvent.click(dropdownValue);
      expect(screen.getByText('Tax Professional')).toBeInTheDocument();
    });

    await act(async () => {
      const signUpButton = screen.getByTestId('submitBtn');
      expect(signUpButton).toBeInTheDocument();
      fireEvent.click(signUpButton);
    });

    // expect(registerUserApi).toHaveBeenCalledTimes(1);
  });

  it('should handle blur', async () => {
    // (registerUserApi as jest.Mock).mockReturnValue(
    //   Promise.resolve({
    //     data: registrationMockData,
    //   })
    // );

    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    expect(
      document.querySelector('.registration-bg-container')
    ).toBeInTheDocument();

    await act(async () => {
      const firstName = screen.getByTestId('firstName');
      expect(firstName).toBeInTheDocument();
      fireEvent.focusIn(firstName);
      fireEvent.focusOut(firstName);
      fireEvent.change(firstName, {target: {value: ''}});
      fireEvent.focusOut(firstName);

      const lastName = screen.getByTestId('lastName');
      expect(lastName).toBeInTheDocument();
      fireEvent.focusIn(lastName);
      fireEvent.focusOut(lastName);
      fireEvent.change(lastName, {target: {value: ''}});
      fireEvent.focusOut(lastName);

      const email = screen.getByTestId('email');
      expect(email).toBeInTheDocument();
      fireEvent.focusIn(email);
      fireEvent.focusOut(email);
      fireEvent.change(email, {target: {value: 'test'}});
      fireEvent.focusOut(email);

      const mobile = screen.getByTestId('mobile');
      expect(mobile).toBeInTheDocument();
      fireEvent.focusIn(mobile);
      fireEvent.focusOut(mobile);
      fireEvent.change(mobile, {target: {value: ''}});
      fireEvent.focusOut(mobile);

      const password = screen.getByTestId('usrPwd');
      expect(password).toBeInTheDocument();
      fireEvent.focusIn(password);
      fireEvent.focusOut(password);
      fireEvent.change(password, {target: {value: ''}});
      fireEvent.focusOut(password);

      const cnfPassword = screen.getByTestId('cnfPwd');
      expect(cnfPassword).toBeInTheDocument();
      fireEvent.focusIn(cnfPassword);
      fireEvent.focusOut(cnfPassword);
      fireEvent.change(cnfPassword, {target: {value: ''}});
      fireEvent.focusOut(cnfPassword);
    });

    expect(
      screen.queryByText('Please enter valid email address.')
    ).toBeInTheDocument();
  });
});

describe('Invited Registration page ', () => {
  beforeAll(() => {
    global.window = Object.create(window);
    const url =
      'https://qa.gsthero.com/GspModel/registration?key=cpE2t3hFImeiM40hF5bJBR79WQe/QIqUX+nUaaSHxE4=&uuid=872652a4-dc1c-4fcc-a728-534d726d6b8e&type=bo';
    Object.defineProperty(window, 'location', {
      value: {
        href: url,
      },
    });
  });

  it('should handle assignments to location.href correctly', () => {
    // (getInvitedUserDataApi as jest.Mock).mockReturnValue(
    //   Promise.resolve({
    //     data: invitedUserMockData,
    //   })
    // );
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );
    // expect(getInvitedUserDataApi).toHaveBeenCalledTimes(1);
  });

  it('should render the invited user registration page with validation errors', async () => {
    // (getInvitedUserDataApi as jest.Mock).mockReturnValue(
    //   Promise.resolve({
    //     data: invitedUserMockData,
    //   })
    // );
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationPage />
        </Provider>
      </BrowserRouter>
    );

    await act(async () => {
      const firstName = screen.getByTestId('firstName');
      expect(firstName).toBeInTheDocument();
      fireEvent.change(firstName, {target: {value: 'test'}});

      const lastName = screen.getByTestId('lastName');
      expect(lastName).toBeInTheDocument();
      fireEvent.change(lastName, {target: {value: 'test'}});

      const email = screen.getByTestId('email');
      expect(email).toBeInTheDocument();
      fireEvent.change(email, {target: {value: '<EMAIL>'}});

      const mobile = screen.getByTestId('mobile');
      expect(mobile).toBeInTheDocument();
      fireEvent.change(mobile, {target: {value: '**********'}});

      const password = screen.getByTestId('usrPwd');
      expect(password).toBeInTheDocument();
      fireEvent.change(password, {target: {value: '1234567'}});

      const cnfPassword = screen.getByTestId('cnfPwd');
      expect(cnfPassword).toBeInTheDocument();
      fireEvent.change(cnfPassword, {target: {value: '1234567'}});

      const userType = screen.getByTestId('signUpAs');
      expect(userType).toBeInTheDocument();
      fireEvent.click(userType);

      const weeklyUpdatde = screen.getByTestId('weeklyUpdatde');
      expect(weeklyUpdatde).toBeInTheDocument();
      fireEvent.change(weeklyUpdatde, {target: {value: ''}});
    });
    await act(async () => {
      const dropdownValue = await waitFor(
        () => document.getElementsByClassName('dropdown-item')[0]
      );
      fireEvent.click(dropdownValue);
      expect(screen.getByText('Tax Professional')).toBeInTheDocument();
    });

    await act(async () => {
      const signUpButton = screen.getByTestId('submitBtn');
      expect(signUpButton).toBeInTheDocument();
      fireEvent.click(signUpButton);
    });

    expect(
      screen.getAllByText(
        'Password should be minimum length of 8 characters.'
      )[0]
    ).toBeInTheDocument();

    expect(
      screen.getAllByText(
        'Password should be minimum length of 8 characters.'
      )[1]
    ).toBeInTheDocument();
  });
});
