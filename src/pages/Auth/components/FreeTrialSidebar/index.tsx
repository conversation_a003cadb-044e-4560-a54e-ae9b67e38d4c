import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {IAccountSetupAddress} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {authActions} from '@pages/Auth/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {DataExtractorIcon} from '@shared/icons/DataExtractorIcon';
import {DutyDrawbackIcon} from '@shared/icons/DutyDrawbackIcon';
import {MoowrIcon} from '@shared/icons/MoowrIcon';
import {RootState, dispatch} from '@store';
import {createFreeSubscription} from '@subscription/api';
import {FormikProps} from 'formik';
import {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IFreeTrial {
  onClose: () => void;
  onClearAll: () => void;
  gstin: string;
  addressFormik: FormikProps<IAccountSetupAddress>;
  partnerDetails: {
    partnerCode: string;
    companyName: string;
  };
}

type ProductKey = 'dataExtractor' | 'dutyDrawback' | 'moowr' | 'ebrc';

const productNameMap: Record<ProductKey, string> = {
  dataExtractor: 'Data Extractor',
  dutyDrawback: 'Duty Drawback',
  moowr: 'MOOWR',
  ebrc: 'EBRC',
};

export default function FreeTrialSidebar({
  onClose,
  onClearAll,
  gstin,
  addressFormik,
  partnerDetails,
}: IFreeTrial) {
  const navigate = useNavigate();
  const {
    subscription: {
      gstInDetails: {businessName, tradeName, panNumber},
    },
  } = useSelector((state: RootState) => state);

  const [productSelection, setProductSelection] = useState({
    dataExtractor: false,
    dutyDrawback: false,
    moowr: false,
    ebrc: false,
  });

  const handleSelectProduct = (product: string) => {
    if (product === 'dataExtractor') {
      setProductSelection((prev) => ({
        ...prev,
        dataExtractor: !prev.dataExtractor,
      }));
    }
    if (product === 'dutyDrawback') {
      setProductSelection((prev) => ({
        ...prev,
        dutyDrawback: !prev.dutyDrawback,
      }));
    }
    if (product === 'moowr') {
      setProductSelection((prev) => ({
        ...prev,
        moowr: !prev.moowr,
      }));
    }
    if (product === 'ebrc') {
      setProductSelection((prev) => ({
        ...prev,
        ebrc: !prev.ebrc,
      }));
    }
  };

  const submitForActivation = async () => {
    const selectedProducts = Object.entries(productSelection).filter(
      ([, selected]) => selected
    );
    if (selectedProducts.length === 0) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: 'Please Select Atleast One Product.',
          alertType: AlertStatus.DANGER,
        })
      );
      return;
    }

    const productDetails = (Object.keys(productSelection) as ProductKey[])
      .filter((key) => productSelection[key])
      .map((key) => ({
        productName: productNameMap[key],
        planDetails: {
          code: 'FREE',
          name: 'FREE',
        },
      }));

    const orgPayload = {
      organizationDetails: {
        orgName: businessName,
        pan: panNumber,
        gstinDetails: {
          gstin,
          tradeName,
          legalName: businessName,
        },
        registeredAddressBean: {
          addressLine1: addressFormik.values.address1,
          addressLine2: addressFormik.values.address2,
          city: addressFormik.values.city,
          state: addressFormik.values.state,
          pinCode: addressFormik.values.pincode,
          country: addressFormik.values.country,
        },
        billingAddressBean: {
          addressLine1: addressFormik.values.billingAddress1,
          addressLine2: addressFormik.values.billingAddress2,
          city: addressFormik.values.billingCity,
          state: addressFormik.values.billingState,
          pinCode: addressFormik.values.billingPincode,
          country: addressFormik.values.billingCountry,
        },
      },
      partnerDetails,
      productDetails,
    };

    const response = await createFreeSubscription(orgPayload);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: response.status || response.status,
          message: response.data,
          alertType: AlertStatus.SUCCESS,
        })
      );
      dispatch(authActions.setIsNewUser(false));
      navigate(Path.DASHBOARD);
    }
  };

  return (
    <div className='product-sidebar'>
      <div className='table-title'>
        <EximTypography fontWeight='semi-bold' variant='h3'>
          Product
        </EximTypography>
        <button
          type='button'
          className='close-icon'
          onClick={onClose}
          aria-label='Close sidebar'>
          <CloseIcon width={9.5} height={9.5} />
        </button>
      </div>
      <div className='product-div'>
        <div className='product-details'>
          <EximCheckbox
            id='dataExtractor'
            name='dataExtractor'
            color='#3E8FF8'
            size='medium'
            checked={productSelection.dataExtractor}
            onChange={() => handleSelectProduct('dataExtractor')}
          />
          <div className='product-box disabled-product'>
            <DataExtractorIcon width={50} height={50} />
          </div>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            Data Extractor
          </EximTypography>
        </div>
        <div className='product-details'>
          <EximCheckbox
            id='dutyDrawback'
            name='dutyDrawback'
            color='#3E8FF8'
            size='medium'
            checked={productSelection.dutyDrawback}
            onChange={() => handleSelectProduct('dutyDrawback')}
          />
          <div className='product-box disabled-product'>
            <DutyDrawbackIcon width={50} height={50} />
          </div>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            Duty Drawback
          </EximTypography>
        </div>
        <div className='product-details'>
          <EximCheckbox
            id='moowr'
            name='moowr'
            color='#3E8FF8'
            size='medium'
            checked={productSelection.moowr}
            onChange={() => handleSelectProduct('moowr')}
          />
          <div className='product-box disabled-product'>
            <MoowrIcon width={50} height={50} />
          </div>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            Moowr
          </EximTypography>
        </div>
        <div className='product-details'>
          <EximCheckbox
            id='ebrc'
            name='ebrc'
            color='#3E8FF8'
            size='medium'
            checked={productSelection.ebrc}
            onChange={() => handleSelectProduct('ebrc')}
          />
          <div className='product-box disabled-product'>
            <MoowrIcon width={50} height={50} />
          </div>
          <EximTypography fontWeight='semi-bold' variant='h3'>
            EBRC
          </EximTypography>
        </div>
      </div>
      <div className='sidebar-btn'>
        <EximButton
          type='reset'
          color='secondary'
          onClick={() => {
            onClearAll();
            setProductSelection({
              dataExtractor: false,
              dutyDrawback: false,
              moowr: false,
              ebrc: false,
            });
          }}
          size='small'>
          Clear All
        </EximButton>
        <EximButton size='small' onClick={submitForActivation} type='submit'>
          Submit
        </EximButton>
      </div>
    </div>
  );
}
