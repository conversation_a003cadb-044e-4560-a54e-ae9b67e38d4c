@import '@utils/main.scss';
.product-sidebar {
  right: 0;
  top: 7%;
  position: fixed;
  width: 25%;
  height: 100%;
  @include rfs(16px, border-radius);
  background-color: $white;
  .table-title {
    width: 100%;
    height: 70px;
    @include padding(23px 0 23px 20px);
    @include flex-item(_, space-between, center, _);
    @include rfs(16px 16px 0 0, border-radius);
    background: $table-title-bg;
    .close-icon {
      @include padding-right(20px);
      cursor: pointer;
      border: none;
    }
  }
  .product-div {
    @include margin-top(20px);
    .product-details {
      @include padding(0 20px 20px 20px);
      @include flex-item(_, _, center, wrap, 20px);
      .product-box {
        @include padding(14px 14px 14px 14px);
        @include flex-item(_, _, center, _, 20px);
        border: 1px solid $accordion-border;
        border-radius: 5px;
        width: 85px;
        height: 85px;
      }
    }
  }
  .sidebar-btn {
    @include padding-right(20px);
    @include flex-item(_, flex-end, _, _, 15px);
  }
}
