import {ReactComponent as LoginBg} from '@assets/EximImages/LoginBg.svg';
import {SupportContact} from '@common/constants';
import {ReactNode} from 'react';

import './index.scss';

interface ILoginPageProps {
  children: ReactNode;
}

const {mail, phone} = SupportContact;

//  INFO: This is the common parent component for login and forgot password
function LoginPageBg({children}: ILoginPageProps) {
  return (
    <>
      <div className='support-contact-container'>
        <div className='support-contact'>
          <a href={`mailto:${mail}`}>{mail}</a>
          <span>|</span>
          <a href={`tel:${phone}`}>{phone}</a>
        </div>
      </div>
      <div className='login-wrapper' data-testid='login'>
        <div className='privacy-feedback'>
          <span>Privacy</span>
          <span>|</span>
          <span>Feedback</span>
        </div>
        <div className='login-bg'>
          <LoginBg />
        </div>
        <div className='children-container'>{children}</div>
      </div>
    </>
  );
}

export default LoginPageBg;
