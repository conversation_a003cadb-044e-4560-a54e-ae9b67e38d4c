@import '@utils/main.scss';

.support-contact-container {
  width: 60%;
  background: $black;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 2;
  height: 30px;
  .support-contact {
    @include flex-item(_, flex-end, center, _, 8px);
    color: $white;
    font-size: $font-size-xsm;
    letter-spacing: 0.5px;
    font-weight: lighter;
    @include margin(8px 20px 0 0);
  }
}

.login-wrapper {
  background-color: $tertiary;
  position: relative;
  width: 100%;
  .login-bg {
    width: 100%;
    height: 100%;
    svg {
      width: 100%;
      height: 100%;
    }
  }

  .privacy-feedback {
    @include flex-item(_, flex-end, center, _, 10px);
    position: absolute;
    top: calc(2.12% - 10px);
    right: 20px;
    color: $white;
    font-size: $font-size-sm;
    letter-spacing: 0.5px;
    font-weight: lighter;
  }
  .children-container {
    overflow: hidden;
    width: 43.94%;
    height: 80%;
    position: absolute;
    right: 0;
    top: 4.23%;
    background-color: $white;
    @include rfs(15px 0 0 15px, border-radius);
    @include padding(64px 0 32px 0);
  }
}
