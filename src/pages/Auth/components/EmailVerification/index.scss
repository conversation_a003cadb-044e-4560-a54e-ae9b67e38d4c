@import '@utils/main.scss';

.email-verification-container {
  @include margin(15% auto 20px);
  @include padding(32px 32px 110px);
  text-align: center;
  border-bottom: 1px solid $product-header-border;

  .verify-email {
    width: 80%;
    margin: auto;
    .congrats-text {
      @include padding-top(12px);
      .typography-wrapper {
        color: $exim-success-background;
      }
    }
    .typography-wrapper {
      color: $text-color;
      @include padding(12px 0 0);
      span {
        color: $primary;
      }
    }

    .button-wrapper {
      width: 142px;
      @include margin(auto);
      @include margin-top(32px);
      .base-btn {
        @include padding(7px 2px);
        font-size: $font-size-sm;
      }
    }
  }

  @include lessThan(md) {
    width: 100%;
  }
}

.email-not-verified-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .not-verified {
    @include flex-item(column, center, center, _ 12px);
  }
}
