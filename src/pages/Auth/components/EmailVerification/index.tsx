import Helmet from '@common/components/utils/Helmet';
import {Path, ResponseStatus} from '@common/constants';
import {verifyEmailApi} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useEffect} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router-dom';

import RegistrationPageBg from '../RegistrationPageBg';
import './index.scss';

function EmailVerification() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');

  const {
    loader: {isAppLoading},
    auth: {isEmailVerified},
  } = useSelector((state: RootState) => state);

  useEffect(() => {
    (async () => {
      if (token) {
        const response = await verifyEmailApi(token);
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(authActions.setVerifiedEmail(response?.data?.email));
          dispatch(authActions.setIsEmailVerified(true));
          navigate(Path.EMAIL_VERIFICATION);
        }
      }
    })();
  }, [token, navigate]);

  return (
    <>
      <Helmet title='Email Verification' />
      {isEmailVerified ? (
        <RegistrationPageBg>
          <div className='email-verification-container'>
            <div className='verify-email'>
              <CheckIcon fill='#2cb445' width='145' height='145' />
              <div className='congrats-text'>
                <EximTypography variant='h2' fontWeight='semi-bold'>
                  Congratulations!
                </EximTypography>
                <EximTypography variant='h3' fontWeight='semi-bold'>
                  Your account has been verified successfully.
                </EximTypography>
              </div>
              <EximButton size='small' onClick={() => navigate(Path.LOGIN)}>
                Sign In
              </EximButton>
            </div>
          </div>
        </RegistrationPageBg>
      ) : null}
      {!isEmailVerified && !isAppLoading ? (
        <div className='email-not-verified-container'>
          <div className='not-verified'>
            <EximTypography variant='h2' fontWeight='normal'>
              Oops!!!
            </EximTypography>
            <EximTypography variant='h2' fontWeight='normal'>
              Requested link is not valid.
            </EximTypography>
          </div>
        </div>
      ) : null}
    </>
  );
}

export default EmailVerification;
