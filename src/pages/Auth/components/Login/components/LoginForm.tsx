import {ReactComponent as EximLogoDark} from '@assets/EximImages/EximLogoDark.svg';
import {Path, ResponseStatus, StorageKeys} from '@common/constants';
import StorageHelper from '@core/helpers/storage.helper';
import {loginApi} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import {loginSchema} from '@pages/Auth/utils';
import {getProductsData} from '@pages/Dashboard/api';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import {EyeClose, EyeOpen} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useSelector} from 'react-redux';
import {Link as ReactLink, useNavigate} from 'react-router-dom';

function LoginForm() {
  const navigate = useNavigate();

  const {verifiedEmail} = useSelector((state: RootState) => state.auth);

  const rememberEmail = StorageHelper.getItem(StorageKeys.USER_EMAIL);
  const rememberPassword = StorageHelper.getItem(StorageKeys.USER_PASSWORD);
  const rememberMe = StorageHelper.getItem(StorageKeys.REMEMBER_ME);

  const rememberValues = {
    email: rememberEmail,
    password: rememberPassword,
    rememberMe: true,
  };

  const initialValues = {
    email: verifiedEmail || '',
    password: '',
    rememberMe: false,
  };

  const formik = useFormik({
    initialValues: rememberMe ? rememberValues : initialValues,
    validationSchema: loginSchema,
    onSubmit: async (values) => {
      StorageHelper.setItem(StorageKeys.REMEMBER_ME, values.rememberMe);
      if (values.rememberMe) {
        StorageHelper.setItem(StorageKeys.USER_EMAIL, values.email);
        StorageHelper.setItem(StorageKeys.USER_PASSWORD, values.password);
      } else {
        StorageHelper.remove(StorageKeys.USER_EMAIL);
        StorageHelper.remove(StorageKeys.USER_PASSWORD);
      }
      dispatch(authActions.setUserEmailWhileLogin(values.email));
      const response = await loginApi(values);
      const {
        data: {
          firstName,
          lastName,
          mobile,
          email,
          sessionToken,
          userID,
          uuid,
          createdAt,
        },
      } = response;

      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(authActions.setVerifiedEmail(null)); // Reset the value after login
        dispatch(authActions.setAccessToken(sessionToken));
        dispatch(
          authActions.login({
            firstName,
            lastName,
            mobile,
            email,
            userID,
            uuid,
            createdAt,
          })
        );
        const responseData = await getProductsData();
        if (responseData.status.toString() === ResponseStatus.SUCCESS) {
          if (responseData?.data?.subscriptionValid) {
            navigate(Path.DASHBOARD);
          } else {
            navigate(Path.SETUP_ACCOUNT);
          }
        }
      }
    },
  });

  const handleForgotPassword = () => {
    // INFO: Reset the value because we need to show form every time
    dispatch(authActions.setForgotPasswordEmail(null));
    navigate(Path.FORGOT_PASSWORD);
  };

  return (
    <div className='login-form-container'>
      <div className='paper-wrapper'>
        <div className='logo'>
          <EximLogoDark />
        </div>
        <form onSubmit={formik.handleSubmit} className='form-wrapper'>
          <div className='inputs-container'>
            <div className='inner-layout'>
              <div className='inputs'>
                <EximInput
                  id='email'
                  name='email'
                  dataTestid='loginEmailInput'
                  label='Email Address (User ID)'
                  placeholder='Enter Email'
                  maxLength={64}
                  isRequired
                  isFocused
                  value={formik.values.email}
                  isInvalid={
                    ((formik.errors.email &&
                      formik.touched.email) as boolean) || false
                  }
                  onBlur={(e) => formik.values.email && formik.handleBlur(e)}
                  onChange={(e) => {
                    formik.setFieldValue('email', e.target.value.trim());
                  }}
                  errorMessage={
                    formik.errors.email ? (formik.errors.email as string) : ''
                  }
                />

                <EximInput
                  id='password'
                  name='password'
                  dataTestid='loginPasswordInput'
                  label='Password'
                  isRequired
                  placeholder='Enter Password'
                  type='password'
                  maxLength={64}
                  passwordEyeOpenIcon={<EyeClose />}
                  passwordEyeCloseIcon={<EyeOpen />}
                  value={formik.values.password}
                  isInvalid={
                    ((formik.errors.password &&
                      formik.touched.password) as boolean) || false
                  }
                  onChange={(e) => {
                    formik.setFieldValue('password', e.target.value.trim());
                  }}
                  onBlur={(e) => formik.values.password && formik.handleBlur(e)}
                  errorMessage={
                    formik.errors.password
                      ? (formik.errors.password as string)
                      : ''
                  }
                />
              </div>
            </div>

            <div className='checkbox-remember'>
              <EximCheckbox
                id='rememberMe'
                label='Remember my user ID'
                color='#2CB544'
                size='medium'
                checked={formik.values.rememberMe}
                onChange={formik.handleChange}
              />
            </div>
            <div className='links-wrapper'>
              <div className='submit-button-wrapper'>
                <EximButton
                  type='submit'
                  dataTestId='submitBtn'
                  disabled={formik.isSubmitting}>
                  Sign In
                </EximButton>
              </div>
              <div className='license-agreement'>
                <span>By clicking Sign In, you agree to our </span>
                <span>License Agreement.</span>
              </div>
              <div className='forgot-password'>
                <div
                  role='presentation'
                  className='forgot-password-text'
                  onClick={handleForgotPassword}>
                  I forgot my password
                </div>
                <div className='no-account'>{`Don't Have an account yet?`}</div>
              </div>
            </div>
          </div>
        </form>
        <ReactLink to={Path.REGISTRATION}>
          <div className='submit-button-wrapper'>
            <EximButton color='tertiary'>Register</EximButton>
          </div>
        </ReactLink>
        <EximDivider type='solid' />
        <div className='login-footer-text'>
          <p>
            Copyright © <span>2023 Perennial Systems</span>
          </p>
          <p>All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}

export default LoginForm;
