@import '@utils/main.scss';

$buttons-inputs-width: 320px;

.login-form-container {
  @include flex-item(column);

  .paper-wrapper {
    @include flex-item(column, _, flex-start);
    .logo {
      @include margin-bottom(12px);
      svg {
        width: 165px;
        height: 70px;
      }
    }
    .form-wrapper {
      .inputs-container {
        @include flex-item(column, center, center);

        .inner-layout {
          @include padding(15px 0 15px 0);
          @include margin-left(-4px);
          .inputs {
            @include flex-item(column, _, _, _, 36px);

            .input-wrapper {
              @include margin-top(16px);
              .form-input {
                input {
                  width: $buttons-inputs-width;
                  @include rfs(5px, border-radius);
                  @include font-size($font-size-sm);
                  color: $title-color;
                }
                label {
                  .label-star::after {
                    top: 0px;
                    left: 0px;
                    @include font-size($font-size-md);
                  }
                }
              }
              .password-icon {
                top: 59%;
              }
            }
          }
        }
        .checkbox-remember {
          width: 308px;
          @include margin(12px 0 24px -2px);
          .checkbox-wrapper {
            width: 100%;
            height: 20px;
            @include flex-item(_, flex-start, _, _, 10px);
            .checkbox-container {
              @include font-size($font-size-sm);
              margin: 0;
              width: 200px;
              @include padding-left(20px);
              &:hover input ~ .checkmarks {
                background-color: $checkbox-hover-background;
              }

              span:first-child {
                @include margin-left(0px);
                color: $label-color;
              }
              .form-check {
                label {
                  height: 25px;
                  @include flex-item();
                }
              }
              .checkmarks {
                @include rfs('5px', border-radius);
                border: 1px solid $primary-border;
                top: -1px;
                left: -6px;

                &::after {
                  left: 5.5px;
                  top: 3px;
                  width: 3px;
                  height: 7px;
                  border-width: 0px 2px 2px 0;
                }
              }
            }
          }
        }

        .links-wrapper {
          width: 100%;
          @include margin-top(-3px);
          @include flex-item(column, _, center);
          text-align: center;

          .license-agreement,
          .forgot-password {
            @include font-size($font-size-sm);
          }

          .license-agreement {
            color: $label-color;
            @include margin(20px 0px 22px 0px);

            span:last-child {
              color: $text-color;
              font-weight: $font-weight-medium;
              a {
                color: $information;
                text-decoration: underline;
              }

              &:hover {
                cursor: pointer;
              }
            }

            span:last-child div {
              display: inline;
            }
          }

          .forgot-password {
            width: 100%;
            @include flex-item(_, space-between, center);
            font-weight: $font-weight-medium;
            color: $text-color;
            @include margin-bottom(24px);
            .forgot-password-text {
              cursor: pointer;
            }
          }
        }
      }
    }

    .submit-button-wrapper {
      width: $buttons-inputs-width;
      height: 35px;
      @include margin-left(-2px);

      .button-wrapper {
        .contained {
          @include margin-left(0);
        }

        .base-btn[class~='medium'] {
          @include padding(7px 0px);
          line-height: 20px;
          @include font-size($font-size-sm);
        }
        .disabled:hover {
          border: 0;
        }
      }
    }

    .divider {
      width: 100%;
      hr {
        @include margin(25px 0);
      }
    }

    .login-footer-text {
      width: 100%;
      text-align: center;
      font-size: $font-size-sm;
      color: $tertiary;
      span {
        color: $primary;
      }
    }
  }
}
