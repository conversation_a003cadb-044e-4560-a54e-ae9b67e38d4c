@import '@utils/main.scss';

.reset-password-container {
  width: 410px;
  margin: auto;
  @include margin-top(12%);

  .form-container {
    width: 100%;
    .typography-container {
      .form-title {
        border-bottom: 1px solid $product-header-border;
        @include padding-bottom(12px);
        @include margin-top(12px);
      }
    }

    .reset-password-form {
      @include flex-item(column, _, _, _, 52px);
      @include padding-top(50px);
      width: 100%;

      .input-wrapper {
        width: 100%;
      }

      .button-wrapper {
        width: 100%;
        @include margin-top(-25px);
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 0);
        }
      }
    }
  }
}
