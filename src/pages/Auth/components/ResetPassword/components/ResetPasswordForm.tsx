import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {
  IResetPassword,
  resetPasswordApi,
  verifyRequestApi,
} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import {resetPassword} from '@pages/Auth/utils';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import {EyeClose, EyeOpen} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useEffect} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

function ResetPasswordForm() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');

  const {resetPasswordToken} = useSelector((state: RootState) => state.auth);

  const formik = useFormik({
    initialValues: {
      password: '',
    },
    validationSchema: resetPassword,
    onSubmit: async (payload: IResetPassword) => {
      const response = await resetPasswordApi(
        payload.password,
        resetPasswordToken
      );

      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(authActions.setResetPasswordToken('')); // Reset the value
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.data.message,
            alertType: AlertStatus.SUCCESS,
          })
        );
        setTimeout(() => navigate(Path.LOGIN), 2000);
      }
    },
  });

  useEffect(() => {
    (async () => {
      if (token) {
        const response = await verifyRequestApi(token);
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(authActions.setResetPasswordToken(token));
          navigate(Path.RESET_PASSWORD);
        }
      }
    })();
  }, [token, navigate]);

  return (
    <form onSubmit={formik.handleSubmit} className='reset-password-form'>
      <EximInput
        id='password'
        label='Password'
        type='password'
        maxLength={64}
        isRequired
        dataTestid='password'
        placeholder='Enter New Password'
        passwordEyeOpenIcon={<EyeClose />}
        passwordEyeCloseIcon={<EyeOpen />}
        value={formik.values.password}
        isInvalid={
          ((formik.errors.password && formik.touched.password) as boolean) ||
          false
        }
        onBlur={(e) => formik.values.password && formik.handleBlur(e)}
        name='password'
        autoComplete='on'
        onChange={(e) =>
          formik.setFieldValue('password', e.target.value.trim())
        }
        errorMessage={
          formik.errors.password ? (formik.errors.password as string) : ''
        }
      />
      <EximInput
        id='confirmPassword'
        label='Confirm Password'
        type='password'
        dataTestid='confirmPassword'
        maxLength={64}
        isPreventPaste
        isRequired
        placeholder='Confirm New Password'
        passwordEyeOpenIcon={<EyeClose />}
        passwordEyeCloseIcon={<EyeOpen />}
        isInvalid={
          ((formik.errors.confirmPassword &&
            formik.touched.confirmPassword) as boolean) || false
        }
        onBlur={(e) => formik.values.confirmPassword && formik.handleBlur(e)}
        name='confirmPassword'
        autoComplete='on'
        onChange={(e) =>
          formik.setFieldValue('confirmPassword', e.target.value.trim())
        }
        errorMessage={
          formik.errors.confirmPassword
            ? (formik.errors.confirmPassword as string)
            : ''
        }
      />
      <EximButton
        type='submit'
        dataTestId='submitBtn'
        disabled={formik.isSubmitting}>
        Submit
      </EximButton>
    </form>
  );
}

export default ResetPasswordForm;
