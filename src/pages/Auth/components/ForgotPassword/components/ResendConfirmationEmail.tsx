import {AlertStatus, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {forgotPasswordApi} from '@pages/Auth/api';
import EximTypography from '@shared/components/EximTypography';
import {ResendEmail} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useSelector} from 'react-redux';

function ResendConfirmationEmail() {
  const {forgotPasswordEmail} = useSelector((state: RootState) => state?.auth);

  const handleResendEmail = async () => {
    const response = await forgotPasswordApi(forgotPasswordEmail || '');

    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data.message,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
  };

  return (
    <div className='resend-email-container'>
      <ResendEmail width={135} height={143} />
      <EximTypography variant='h4'>
        {`We have sent email to `}
        <span className='email'>{forgotPasswordEmail}</span>
        {` to confirm the validity of your email address.`}
        <p>
          After receiving the email follow the link provided to complete your
          registration
        </p>
      </EximTypography>
      <EximTypography variant='h4' classNames='not-get-email'>
        {`If you didn't got any email `}
        <span
          className='resend-email'
          data-testid='resend-email'
          role='presentation'
          onClick={handleResendEmail}>
          Resend confirmation mail
        </span>
      </EximTypography>
    </div>
  );
}

export default ResendConfirmationEmail;
