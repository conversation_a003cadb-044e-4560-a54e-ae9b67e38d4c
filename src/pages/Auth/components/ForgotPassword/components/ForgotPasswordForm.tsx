import {AlertStatus, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {forgotPasswordApi} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import {forgotPassword} from '@pages/Auth/utils';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import {dispatch} from '@store';
import {useFormik} from 'formik';

function ForgotPasswordForm() {
  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validationSchema: forgotPassword,
    onSubmit: async (values) => {
      const response = await forgotPasswordApi(values.email);

      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(authActions.setForgotPasswordEmail(values.email));
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.data.message,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className='forgot-password-form'>
      <EximInput
        id='email'
        name='email'
        dataTestid='emailInput'
        label='Email Used for Registration'
        maxLength={64}
        isRequired
        placeholder='Enter Email'
        isFocused
        value={formik.values.email}
        isInvalid={
          ((formik.errors.email && formik.touched.email) as boolean) || false
        }
        onBlur={(e) => formik.values.email && formik.handleBlur(e)}
        autoComplete='on'
        onChange={(e) => {
          formik.setFieldValue('email', e.target.value.trim());
        }}
        errorMessage={
          formik.errors.email ? (formik.errors.email as string) : ''
        }
      />
      <EximButton
        type='submit'
        dataTestId='submitBtn'
        disabled={formik.isSubmitting}>
        Submit
      </EximButton>
    </form>
  );
}

export default ForgotPasswordForm;
