import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {Path} from '@common/constants';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {Link} from 'react-router-dom';

import LoginPageBg from '../LoginPageBg';
import ForgotPasswordForm from './components/ForgotPasswordForm';
import ResendConfirmationEmail from './components/ResendConfirmationEmail';
import './index.scss';

function ForgotPassword() {
  const {forgotPasswordEmail} = useSelector((state: RootState) => state.auth);

  return (
    <>
      <Helmet title='Forgot Password' />
      <LoginPageBg>
        <div
          className={`forgot-password-container ${
            forgotPasswordEmail ? 'resend-email' : 'forgot-password'
          }`}>
          <NavigationSubHeader
            hasLeftArrow
            hasTitle
            leftArrowRoute={Path.LOGIN}
            leftArrowText='Back'
          />
          <div className='form-container'>
            <EximTypography
              fontWeight='semi-bold'
              variant='h1'
              classNames='form-title'>
              {forgotPasswordEmail ? 'Email Confirmation' : 'Forgot Password'}
            </EximTypography>
            {forgotPasswordEmail ? (
              <ResendConfirmationEmail />
            ) : (
              <ForgotPasswordForm />
            )}
            <span className='already-account'>
              <Link to={Path.LOGIN}>Already have an account login?</Link>
            </span>
          </div>
        </div>
      </LoginPageBg>
    </>
  );
}

export default ForgotPassword;
