@import '@utils/main.scss';

.forgot-password-container.forgot-password {
  @include margin-top(10%);
}
.forgot-password-container.resend-email {
  @include margin-top(6%);
}
.forgot-password-container {
  width: 360px;
  margin: auto;

  .subscription-header {
    .typography-container {
      h1 {
        font-size: $font-size-lg;
        font-weight: $font-weight-bold;
        letter-spacing: 0.5px;
      }
    }
    svg {
      width: 18px;
      height: 16px;
      @include margin-top(3px);
    }
  }

  .form-container {
    width: 100%;
    .typography-container {
      .form-title {
        border-bottom: 1px solid $product-header-border;
        @include padding-bottom(12px);
        @include margin-top(12px);
      }
    }

    .forgot-password-form {
      width: 100%;
      @include padding-top(50px);

      .input-wrapper {
        width: 100%;
      }

      .button-wrapper {
        width: 100%;
        @include margin-top(26px);
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 0);
        }
      }
    }
    .already-account {
      @include flex-item(_, center);
      @include margin-top(21px);
      font-size: $font-size-sm;
      color: $text-color;
    }
  }

  // Resend Email UI
  .resend-email-container {
    width: 100%;
    @include margin-top(20px);
    @include flex-item(column, center, center, _, 16px);

    .typography-container {
      .typography-variant-h4 {
        color: $text-color;
        letter-spacing: 0.2px;
        text-align: center;

        .email {
          font-weight: $font-weight-semi-bold;
        }
        .email + p {
          width: 88%;
          margin: auto;
        }

        .resend-email {
          color: $information;
          text-decoration: underline;
          cursor: pointer;
        }
      }
      .typography-variant-h4.not-get-email {
        color: #878787;
      }
    }
  }
}
