@import '@utils/main.scss';

.add-user-verification-container {
  @include margin(18% auto 20px);
  @include padding(32px 32px 80px);
  text-align: center;
  border-bottom: 1px solid $product-header-border;

  .verify-add-user {
    width: 80%;
    margin: auto;
    .congrats-text {
      @include padding-top(12px);
      .typography-wrapper {
        color: $exim-success-background;
      }
    }
    .typography-wrapper {
      color: $text-color;
      @include padding(12px 0 0);
      span {
        color: $primary;
      }
    }
  }

  @include lessThan(md) {
    width: 100%;
  }
}

.add-user-not-verified-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  .not-verified {
    @include flex-item(column, center, center, _ 12px);
  }
}
