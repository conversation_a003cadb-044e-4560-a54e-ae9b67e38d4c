import Helmet from '@common/components/utils/Helmet';
import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {IRegisterApiProps, getInvitedUser} from '@pages/Auth/api';
import {authActions} from '@pages/Auth/store/reducer';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useEffect} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router-dom';

import RegistrationPageBg from '../RegistrationPageBg';
import './index.scss';

function AddUserVerification() {
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');

  const {
    loader: {isAppLoading},
    auth: {isEmailVerified},
  } = useSelector((state: RootState) => state);

  useEffect(() => {
    (async () => {
      if (token) {
        const response = (await getInvitedUser(token)) as ICustomAxiosResp;
        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(authActions.setIsEmailVerified(true));
          dispatch(
            alertActions.setAlertMsg({
              code: response.status,
              message: response.data.message || response.message,
              alertType: AlertStatus.SUCCESS,
            })
          );

          const {firstName, lastName, email, mobile, uuid, status} =
            response.data;

          if (status === 'LOGIN') {
            dispatch(authActions.setVerifiedEmail(email));
            setTimeout(() => navigate(Path.LOGIN), 1000);
          } else {
            const invitedUserData: IRegisterApiProps = {
              firstName,
              lastName,
              email,
              mobile,
              password: '',
              confirmPassword: '',
              weeklyUpdates: true,
              type: 'BO',
              inviteId: uuid,
            };
            setTimeout(
              () => navigate(Path.REGISTRATION, {state: {invitedUserData}}),
              1000
            );
          }
        }
      }
    })();
  }, [token, navigate]);

  return (
    <>
      <Helmet title='Email Verification' />
      {isEmailVerified ? (
        <RegistrationPageBg>
          <div className='add-user-verification-container'>
            <div className='verify-add-user'>
              <CheckIcon fill='#2cb445' width='145' height='145' />
              <div className='congrats-text'>
                <EximTypography variant='h2' fontWeight='semi-bold'>
                  Congratulations!
                </EximTypography>
                <EximTypography variant='h3' fontWeight='semi-bold'>
                  Your account has been added successfully.
                </EximTypography>
              </div>
            </div>
          </div>
        </RegistrationPageBg>
      ) : null}
      {!isEmailVerified && !isAppLoading ? (
        <div className='add-user-not-verified-container'>
          <div className='not-verified'>
            <EximTypography variant='h2' fontWeight='normal'>
              Oops!!!
            </EximTypography>
            <EximTypography variant='h2' fontWeight='normal'>
              Requested link is not valid.
            </EximTypography>
          </div>
        </div>
      ) : null}
    </>
  );
}

export default AddUserVerification;
