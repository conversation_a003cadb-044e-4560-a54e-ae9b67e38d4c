import Helmet from '@common/components/utils/Helmet';
import {Path} from '@common/constants';
import {authActions} from '@pages/Auth/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {ResendEmail} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function RegistrationCompletePage() {
  const navigate = useNavigate();
  const {registrationEmail} = useSelector((state: RootState) => state.auth);

  const handleRedirect = () => {
    navigate(Path.LOGIN);
    // Resetting the value of registration success
    dispatch(authActions.setIsRegistrationSuccess(false));
  };

  return (
    <>
      <Helmet title='Registration Complete' />
      <div className='registration-complete-container'>
        <EximTypography
          fontWeight='semi-bold'
          variant='h1'
          classNames='form-title'>
          Email Confirmation
        </EximTypography>
        <div className='registration-complete'>
          <ResendEmail width={135} height={143} />
          <div className='text-container'>
            <EximTypography variant='h3'>
              {`A verification link has been sent to your registered email id `}
              <span className='email'>{registrationEmail || ''}</span>
            </EximTypography>
            <p>
              Please click on the verification link to complete your email
              verification. This is mandatory in order to keep your EXIMHero
              experience safe.
            </p>
          </div>
          <EximButton size='small' onClick={handleRedirect}>
            Sign In
          </EximButton>
        </div>
      </div>
    </>
  );
}

export default RegistrationCompletePage;
