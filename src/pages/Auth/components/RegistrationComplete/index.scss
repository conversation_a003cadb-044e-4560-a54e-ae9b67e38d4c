@import '@utils/main.scss';

.registration-complete-container {
  width: 80%;
  @include margin(8% auto 20px);
  @include padding(32px 32px 62px);
  text-align: center;
  border-bottom: 1px solid $product-header-border;

  .typography-container {
    .form-title {
      width: 320px;
      margin: auto;
      border-bottom: 1px solid $product-header-border;
      @include padding-bottom(12px);
      @include margin-bottom(32px);
    }
  }
  .registration-complete {
    .text-container {
      @include flex-item(column, center, center, _, 16px);
      border-top: 1px solid $product-header-border;
      @include margin-top(34px);
      @include padding-top(16px);
      .typography-wrapper {
        color: $text-color;
        @include padding(12px 0 0);
        & :not(span) {
          opacity: 0.8;
        }
        span.email {
          color: $primary;
        }
      }
      & > p {
        width: 70%;
      }
    }
    .button-wrapper {
      width: 142px;
      @include margin(auto);
      @include margin-top(32px);
      .base-btn {
        @include padding(7px 2px);
        font-size: $font-size-sm;
      }
    }
  }

  @include lessThan(md) {
    width: 100%;
  }
}
