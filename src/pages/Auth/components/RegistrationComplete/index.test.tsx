import store from '@store';
import {render} from '@testing-library/react';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';

import RegistrationCompletePage from '.';

describe('Registration page', () => {
  it('should render the Registration page without error', () => {
    render(
      <BrowserRouter>
        <Provider store={store}>
          <RegistrationCompletePage />
        </Provider>
      </BrowserRouter>
    );

    expect(
      document.querySelector('.registration-complete')
    ).toBeInTheDocument();
  });
});
