import {render} from '@testing-library/react';
import {<PERSON><PERSON>erRouter} from 'react-router-dom';

import EmailActivation from '.';

describe('Email activation page', () => {
  beforeAll(() => {
    global.window = Object.create(window);
    const url =
      'https://qa.gsthero.com/GspModel/emailactivation?info=eyJzdGF0dXMiOiJlcnJvciJ9';
    Object.defineProperty(window, 'location', {
      value: {
        href: url,
      },
    });
  });
  it('should render the Opps page without error', () => {
    render(
      <BrowserRouter>
        <EmailActivation />
      </BrowserRouter>
    );

    expect(document.querySelector('.email-activation')).toBeInTheDocument();
  });
  beforeAll(() => {
    global.window = Object.create(window);
    const url =
      'https://qa.gsthero.com/GspModel/emailactivation?info=********************************************************************************************************************************************************************';
    Object.defineProperty(window, 'location', {
      value: {
        href: url,
      },
    });
  });
  it('should render the Thank you page without error', () => {
    render(
      <BrowserRouter>
        <EmailActivation />
      </BrowserRouter>
    );

    expect(document.querySelector('.email-activation')).toBeInTheDocument();
  });
});
