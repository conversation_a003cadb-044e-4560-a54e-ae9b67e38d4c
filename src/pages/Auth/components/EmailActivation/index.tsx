import Footer from '@common/components/Footer';
import Helmet from '@common/components/utils/Helmet';
import {Path} from '@common/constants';
import EximTypography from '@shared/components/EximTypography';
import {useEffect, useState} from 'react';
import {Link, useLocation} from 'react-router-dom';

import './index.scss';

function EmailActivation() {
  const location = useLocation();
  const [isOops, setIsOops] = useState<boolean>(true);

  const info: string = new URLSearchParams(location.search).get(
    'info'
  ) as string;

  // get info param and decode it using atob;
  const decodedInfo = async (key: string) => {
    try {
      const {status} = JSON.parse(window.atob(key) as string);
      if (status === 'success') return setIsOops(false);
      return setIsOops(true);
    } catch {
      return setIsOops(true);
    }
  };

  useEffect(() => {
    if (info) decodedInfo(info);
  }, [info]);

  return (
    <>
      <Helmet title='Email Activation' />
      <div className='email-activation'>
        {isOops ? (
          <>
            <EximTypography variant='h2' fontWeight='normal'>
              Oops!!!
            </EximTypography>
            <EximTypography variant='h2' fontWeight='normal'>
              Requested link is not valid.
            </EximTypography>
          </>
        ) : (
          <>
            <EximTypography variant='h2' fontWeight='normal'>
              Thank you!!!
            </EximTypography>
            <EximTypography variant='h2' fontWeight='normal'>
              Your email activation has been completed successfully.
            </EximTypography>
          </>
        )}
        <Link to={Path.LOGIN}>
          <EximTypography variant='h4' fontWeight='bold'>
            Click here to login
          </EximTypography>
        </Link>
      </div>
      <Footer showGstLogo />
    </>
  );
}

export default EmailActivation;
