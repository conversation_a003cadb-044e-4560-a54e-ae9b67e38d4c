import {REGEXP} from '@common/constants';
import * as yup from 'yup';

export const registrationSchema = yup.object().shape({
  firstName: yup
    .string()
    .max(64, 'First name can not be more than 64 characters.')
    .matches(REGEXP.name, 'First name must contain only letters.')
    .required('Please enter first name.')
    .trim(),
  lastName: yup
    .string()
    .max(64, 'Last name can not be more than 64 characters.')
    .matches(REGEXP.name, 'Last name must contain only letters.')
    .required('Please enter last name.')
    .trim(),
  email: yup
    .string()
    .max(64, 'Email can not be more than 64 characters.')
    .email('Please enter valid email address.')
    .required('Please enter email address.'),
  mobile: yup
    .string()
    .min(10, 'Phone No should contain minimum 10 digits.')
    .max(10, 'Phone No should contain maximum 10 digits.')
    .matches(REGEXP.mobileNumber, 'Please enter valid number.')
    .required('Please enter mobile number.'),
  password: yup
    .string()
    .min(8, 'Password can not be less than 8 characters.')
    .max(64, 'Password can not be more than 64 characters.')
    .matches(
      REGEXP.password,
      'Password must be contains at least 1 uppercase, 1 lowercase, 1 numeric and 1 special characters.'
    )
    .matches(REGEXP.passwordSpace, 'Spaces are not allowed in the password.')
    .required('Please enter password.'),
  confirmPassword: yup
    .string()
    .oneOf(
      [yup.ref('password'), null],
      'Password & Confirm password do not match.'
    )
    .required('Please enter confirm password.'),
  weeklyUpdates: yup.string().nullable().notRequired(),
  type: yup.string().nullable().notRequired(),
  invite_uuid: yup.string().nullable().notRequired(),
});

export const loginSchema = yup.object().shape({
  email: yup
    .string()
    .max(64, 'Email can not be more than 64 characters.')
    .email('Please enter valid email address.')
    .required('Please enter email address.'),
  password: yup
    .string()
    .min(8, 'Password can not be less than 8 characters.')
    .max(64, 'Password can not be more than 64 characters.')
    .required('Please enter password.'),
  rememberMe: yup.string().nullable().notRequired(),
});

export const forgotPassword = yup.object().shape({
  email: yup
    .string()
    .max(64, 'Email can not be more than 64 characters.')
    .email('Please enter valid email address.')
    .required('Please enter email address.'),
});
export const resetPassword = yup.object().shape({
  password: yup
    .string()
    .min(8, 'Password can not be less than 64 characters.')
    .max(64, 'Password can not be more than 64 characters.')
    .matches(
      REGEXP.password,
      'Password must be contains at least 1 uppercase, 1 lowercase, 1 numeric and 1 special characters.'
    )
    .matches(REGEXP.passwordSpace, 'Spaces are not allowed in the password.')
    .required('Please enter password.'),
  confirmPassword: yup
    .string()
    .oneOf(
      [yup.ref('password'), null],
      'Password & Confirm password do not match.'
    )
    .required('Please enter confirm password.'),
});
