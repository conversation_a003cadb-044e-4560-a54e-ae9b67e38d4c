import * as Yup from 'yup';

// INFO: Fetch IRM Details Form Schema
export const fetchIrmDtlsSchema = Yup.object().shape({
  fromDate: Yup.string().required('Please select the from date.'),
  toDate: Yup.string().required('Please select the to date.'),
});

export const UPLOAD_HISTORY_TABLE_HEADER = [
  {title: 'File Name', width: '40%', sortingKey: 'file_name'},
  {title: 'Last Upload', width: '20%', sortingKey: 'last_updated_date'},
  {title: 'Upload By', width: '20%'},
  {title: 'Status', width: '20%'},
];

export const UPLOAD_HISTORY_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'file_name', label: 'File Name'},
  {id: '2', value: 'last_updated_date', label: 'Last Upload'},
];

export const IRM_DETAILS_TABLE_HEADER = [
  {title: 'IRM No.', width: '11%'},
  {title: 'IRM Date', width: '11%', sortingKey: 'irmIssueDate'},
  {title: 'IRM Status', width: '11%'},
  {title: 'Purpose Code', width: '11%'},
  {title: 'Remittance Date', width: '11%'},
  {title: 'Remitter Name', width: '11%', sortingKey: 'remitterName'},
  {title: 'Remittance FCC Amount', width: '12%'},
  {title: 'Remittance FCC', width: '11%'},
  {title: 'IRM Available Amount in FCC', width: '11%'},
];

export const IRM_DETAILS_SEARCH_DROPDOWN = [
  {id: '1', value: 'irmIssueDate', label: 'IRM Date'},
  {id: '2', value: 'remitterName', label: 'Remitter Name'},
];

export const SB_DETAILS_TABLE_HEADER = [
  {title: 'Shipping Bill No.', width: '12%', sortingKey: 'shipping_bill_no'},
  {title: 'Shipping Bill Date', width: '12%', sortingKey: 'shipping_bill_date'},
  {title: 'Port Code', width: '10%'},
  {title: 'Invoice No.', width: '10%'},
  {title: 'Invoice Date', width: '10%'},
  {title: 'Shipping Bill Value', width: '14%'},
  {title: 'Shipping Bill Currency', width: '14%'},
  {title: 'SAC Code (Only for Services)', width: '15%'},
];

export const SB_DETAILS_SEARCH_DROPDOWN = [
  {id: '1', value: 'shipping_bill_no', label: 'Shipping Bill No.'},
  {id: '2', value: 'shipping_bill_date', label: 'Shipping Bill Date'},
];

export const IRM_DETAILS_LINKING_TABLE_HEADER = (
  isCheckbox: boolean,
  isAction: boolean
) => {
  return [
    ...(isCheckbox ? [{title: 'checkbox', width: '2%'}] : []),
    {title: 'IRM No.', width: '15%'},
    {title: 'IRM Issue Date', width: '15%', sortingKey: 'irmIssueDate'},
    {title: 'Remitter Name', width: '25%', sortingKey: 'remitterName'},
    {title: 'Remittance FCC', width: '15%'},
    {title: 'Remittance Amount (A)', width: '20%'},
    ...(isAction ? [{title: 'Action', width: '10%'}] : []),
  ];
};

export const SB_DETAILS_LINKING_TABLE_HEADER = (
  isCheckbox: boolean,
  isAction: boolean
) => {
  return [
    ...(isCheckbox ? [{title: 'checkbox', width: '2%'}] : []),
    {title: 'Shipping Bill No.', width: '15%', sortingKey: 'shipping_bill_no'},
    {
      title: 'Shipping Bill Date',
      width: '15%',
      sortingKey: 'shipping_bill_date',
    },
    {title: 'Consignee Name', width: '25%'},
    {title: 'SB Currency Code', width: '15%'},
    {title: 'Shipping Bill Value', width: '20%'},
    ...(isAction ? [{title: 'Action', width: '10%'}] : []),
  ];
};

export const VERIFY_LINKING_TABLE_HEADER = [
  {title: 'IRM No.', width: '25%'},
  {title: 'Remittance Amount (A)', width: '25%'},
  {title: 'Shipping Bill No.', width: '25%'},
  {title: 'Total Shipping Bill Value', width: '25%'},
];

export const PENDING_EBRC_TABLE_HEADER = [
  {title: 'checkbox', width: '2%'},
  {title: 'IRM No.', width: 'auto'},
  {title: 'IRM Date', width: 'auto', sortingKey: 'irmIssueDate'},
  {title: 'Purpose Code', width: 'auto'},
  {title: 'Remitter Name', width: 'auto', sortingKey: 'remitterName'},
  {title: 'Remittance FCC Amount', width: 'auto'},
  {title: 'Remittance FCC', width: 'auto'},
  {title: 'Shipping Bill No.', width: 'auto', sortingKey: 'shipping_bill_no'},
  {
    title: 'Shipping Bill Date',
    width: 'auto',
    sortingKey: 'shipping_bill_date',
  },
  {title: 'Port Code', width: 'auto'},
  {title: 'invoice No.', width: 'auto'},
  {title: 'invoice Date', width: 'auto'},
  {title: 'Shipping Bill Value', width: 'auto'},
  {title: 'Shipping Bill Currency', width: 'auto'},
  {title: 'SAC Code (Only for Services)', width: 'auto'},
];

export const PENDING_EBRC_SEARCH_DROPDOWN = [
  {id: '1', value: 'irmIssueDate', label: 'IRM Date'},
  {id: '2', value: 'remitterName', label: 'Remitter Name'},
  {id: '3', value: 'shipping_bill_no', label: 'Shipping Bill No.'},
  {id: '4', value: 'shipping_bill_date', label: 'Shipping Bill Date'},
];

export const GENERATED_EBRC_TABLE_HEADER = [
  {title: 'checkbox', width: '2%'},
  {title: 'Processing Status', width: '8%'},
  {title: 'eBRC No.', width: '8%'},
  {title: 'eBRC Date', width: '10%'},
  {title: 'IRM No.', width: '8%'},
  {title: 'IRM Date', width: '8%'},
  {title: 'Shipping Bill/ SOFTEX / Invoice Number', width: '10%'},
  {title: 'Shipping Bill/SOFTEX / Invoice Date', width: '10%'},
  {title: 'Total Realised Value', width: '10%'},
  {title: 'Realised Date', width: '10%'},
  {title: 'BRC Utilization Status', width: '10%'},
  {title: 'Action', width: '5%'},
];

export const IRM_SB_SUMMARY_CARDS = [
  {
    type: 'OPEN_ITEMS',
    title: 'Open Items',
    dataKey: 'openIRM openShippingBill',
  },
  {
    type: 'PENDING_EBRC',
    title: 'Pending eBRC',
    dataKey: 'linkedAndNotSubmitted',
  },
  {
    type: 'GENERATED_EBRC',
    title: 'Generated eBRC',
    dataKey: 'linkedAndSubmitted',
  },
];
