@import '@utils/main.scss';

.view-irm-dtls-container {
  @include margin(24px auto 32px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .irm-dtls-table-container {
    @include padding(20px);
    .irm-dtls-list-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
    }

    .table-search-container,
    .table-footer {
      @include padding(10px auto);
    }
  }
}
.irm-dtls-container {
  @include padding(0 20px);

  .btn-children-container {
    @include flex-item(_, _, center, _, 12px);
    .base-date-picker {
      width: auto;
      .react-datepicker__portal {
        left: -77px;
      }
    }
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .btn-container {
      @include flex-item(_, space-between, center, _, 20px);
      @include padding(10px 16px);

      .button-wrapper {
        width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }
}
