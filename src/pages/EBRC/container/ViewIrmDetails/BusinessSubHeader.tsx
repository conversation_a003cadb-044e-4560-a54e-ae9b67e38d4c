import {Path} from '@common/constants';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {useNavigate} from 'react-router';

function BusinessSubHeader() {
  const navigate = useNavigate();

  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='btn-container'>
          <EximTypography>IRM Details</EximTypography>
          <EximButton
            size='small'
            dataTestId='done-txn'
            onClick={() => navigate(Path.EBRC)}>
            Done
          </EximButton>
        </div>
      </EximPaper>
    </div>
  );
}

export default BusinessSubHeader;
