import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {EximHeroDate} from '@common/constants';
import {IIrmDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getIrmDetails} from '@pages/EBRC/api';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import {
  IRM_DETAILS_SEARCH_DROPDOWN,
  IRM_DETAILS_TABLE_HEADER,
} from '@pages/EBRC/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function ViewIrmDetails() {
  const {
    ebrc: {
      iecNumber,
      invoicesPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const [irmList, setIrmList] = useState<IIrmDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    dispatch(
      ebrcActions.setInvoicesPeriod({
        startPeriod: startDate,
        endPeriod: endDate,
      })
    );
  };

  const getIrmDtls = useCallback(async () => {
    const payload = {
      startDate: startPeriod.split('-').reverse().join('-'),
      endDate: endPeriod.split('-').reverse().join('-'),
      iecCode: iecNumber,
      searchKey,
      sortBy,
      sortingOrder,
      searchValue: debouncedValue,
    };
    const response = await getIrmDetails(payload, page, +showEntries);
    setIrmList(response?.data?.records);
    setTotalRecords(response?.data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    startPeriod,
    endPeriod,
    sortBy,
    sortingOrder,
    debouncedValue,
  ]);

  useEffect(() => {
    getIrmDtls();
  }, [getIrmDtls]);

  return (
    <div className='irm-dtls-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText='IRM Details'
        hasGuide
      />
      <BusinessHeader>
        <div className='btn-children-container'>
          <EximTypography>Tax Period</EximTypography>
          <EximMonthRangePicker
            id='irmDatePicker'
            minDate={EximHeroDate.MIN_MONTH}
            onSelect={handleSelectPeriod}
            defaultStartDate={startPeriod.split('-').join('/')}
            defaultEndDate={endPeriod.split('-').join('/')}
          />
        </div>
      </BusinessHeader>
      <BusinessSubHeader />
      <div className='view-irm-dtls-container'>
        <EximPaper>
          <div className='irm-dtls-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={IRM_DETAILS_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='irm-dtls-list-table'>
              <TableHeader
                mainHeader={IRM_DETAILS_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {irmList?.length > 0 ? (
                <TableBody className='irm-dtls-list-tbody'>
                  {irmList?.map((item, index: number) => (
                    <TableRow key={`item${index + 1}`}>
                      <TableCell>{item.irmNumber}</TableCell>
                      <TableCell>{item.irmIssueDate}</TableCell>
                      <TableCell>{item.irmStatus}</TableCell>
                      <TableCell>{item.purposeOfRemittance}</TableCell>
                      <TableCell>{item.remittanceDate}</TableCell>
                      <TableCell>{item.remitterName}</TableCell>
                      <TableCell>
                        {item.remittanceFCCAmount?.toFixed(2)}
                      </TableCell>
                      <TableCell>{item.remittanceFCC}</TableCell>
                      <TableCell>{item.irmAvailableAmt}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              ) : (
                <EmptyTable colSpan={IRM_DETAILS_TABLE_HEADER.length} />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={irmList as []}
              renderData={irmList as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </EximPaper>
      </div>
    </div>
  );
}

export default ViewIrmDetails;
