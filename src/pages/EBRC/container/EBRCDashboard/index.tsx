import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {HelmetTitle, Path} from '@common/constants';
import {
  getCurrentFinancialYear,
  getFinancialYearDropdowns,
  selectedOptionId,
} from '@common/helpers';
import {IEbrcSummaryData} from '@common/interfaces';
import {ebrcSummary} from '@pages/EBRC/api';
import LinkIrmAndSBSummary from '@pages/EBRC/components/LinkIrmAndSBSummary';
import SummaryCards from '@pages/EBRC/components/SummaryCards';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDivider from '@shared/components/EximDivider';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import DataUploadValidate from './DataUploadValidate';
import './index.scss';

const ebrcInitialValues = {
  totalIrmValue: 0,
  totalEbrcGenerated: 0,
  totalNotConsiderForEbrc: 0,
  totalShippingBillValue: 0,
  totalShippingBillGenerated: 0,
  totalNotConsiderForShippingBill: 0,
};

function EBRCDashboard() {
  const {
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const [financialYear, setFinancialYear] = useState(getCurrentFinancialYear());
  const [ebrcSummaryData, setEbrcSummaryData] =
    useState<IEbrcSummaryData>(ebrcInitialValues);

  const getSummary = useCallback(async () => {
    const [startDate, endDate] = financialYear.split('#');

    const payload = {
      iecCode: iecNumber,
      startDate: startDate.split('-').reverse().join('-'),
      endDate: endDate.split('-').reverse().join('-'),
    };

    const {data} = await ebrcSummary(payload);
    setEbrcSummaryData(data);
  }, [iecNumber, financialYear]);

  useEffect(() => {
    getSummary();
  }, [getSummary]);

  return (
    <>
      <Helmet title={HelmetTitle.EBRC} />
      <div className='ebrc-dashboard-wrapper'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          leftArrowRoute={Path.DASHBOARD}
          leftArrowText='EXIM E-BRC Dashboard'
        />
        <BusinessHeader />

        <div className='ebrc-summary-container'>
          <EximDivider type='solid' text='Summary' textAlign='left' />
          <div className='fy-date-cont'>
            <EximTypography>Financial Year</EximTypography>
            <EximCustomDropdown
              placeholder=''
              onSelect={({value}) => setFinancialYear(value)}
              dataTestId='column-dropdown'
              optionsList={getFinancialYearDropdowns(2022)}
              defaultOption={selectedOptionId(
                getFinancialYearDropdowns(2022),
                financialYear
              )}
            />
          </div>
        </div>
        <SummaryCards data={ebrcSummaryData} />

        <DataUploadValidate />

        <LinkIrmAndSBSummary />
      </div>
    </>
  );
}

export default EBRCDashboard;
