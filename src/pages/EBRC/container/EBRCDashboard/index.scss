@import '@utils//main.scss';

.ebrc-dashboard-wrapper {
  @include padding(0 20px);

  .business-container {
    .business-add-button {
      color: $white;
      min-width: 100px;

      .button-wrapper {
        .btn-children {
          font-size: $font-size-sm;
        }
        .base-btn {
          @include padding(10px 16px);
        }
      }

      .base-btn {
        @include padding(8px 10px);
        @include margin(0);
      }
    }
  }
  .product-head {
    font-size: $font-size-lg;
    color: $title-color;
  }
  .divider {
    hr {
      @include margin(28px 0);
    }
  }
  .ebrc-summary-container {
    @include flex-item(row, space-between, center);
    .divider {
      flex: 0.99;
    }
    .fy-date-cont {
      @include flex-item(row, space-between, center, _, 16px);
      .select-dropdown {
        width: 150px;
        .custom-dropdown {
          background: $white;
          height: 28px;
          .dropdown-item {
            @include padding(4px 8px 4px 10px);
            &:hover {
              background-color: $gstin-details-background;
              color: $text-color;
            }
          }
        }
      }
    }
  }
}

// data upload steps style
.data-upload-step-container {
  @include margin-top(20px);
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;

    .filing-step-container {
      @include padding(0 16px);
      & > div:last-child {
        border-bottom: none;
      }
    }
    .date-container {
      @include flex-item(row, flex-end, center, _, 10px);
    }
  }

  .button-wrapper {
    .base-btn.discard-claim-btn {
      background-color: $table-head-primary;
      color: $white;
      &:hover {
        background-color: $table-head-primary;
        color: $white;
      }
    }
  }

  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .filing-head {
    .children-item {
      .button-wrapper {
        .base-btn {
          min-width: 114px;
          @include padding(7px 16px);
        }
      }
    }
  }

  .filing-step {
    .details-container {
      .filing-type {
        .avatar-wrapper {
          .avatar-container {
            .small {
              width: 28px;
              height: 28px;
            }

            .avatar-text {
              background-color: $tertiary;
              border: none;
              color: $white;
              font-size: $font-size-sm;
              font-weight: normal;
            }
          }
        }
      }
    }
  }
  .error {
    color: $error;
    .error-count {
      @include margin-left(4px);
      color: $text-color;
    }
    .error-count-space {
      @include margin-left(10px);
    }
    font-size: $font-size-sm;
  }
  .success {
    color: $success;
    font-size: $font-size-sm;
  }
  .process {
    color: $warning;
    font-size: $font-size-sm;
  }
}

// Fetch IRM Details Modal
.fetch-irm-dtls-modal {
  letter-spacing: 0.5px;
  .modal-body {
    width: 710px;
    .modal-header {
      @include flex-item(row, space-between);
      @include padding(30px 30px 0px 30px);
    }
    .modal-content {
      @include margin-top(16px);
      @include padding(0 30px 30px 30px);
    }
    .fetch-modal-title {
      font-size: $font-size-xxl;
      color: $secondary-text;
    }
  }
}
