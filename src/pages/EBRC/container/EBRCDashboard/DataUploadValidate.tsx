import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {EbrcFileType, Path} from '@common/constants';
import {
  formatDateWithTime,
  getCurrentMonthAndYear,
  getLastMonthAndYear,
} from '@common/helpers';
import {IInputFilesUploadStatus} from '@common/interfaces';
import {fetchIrmStatus, getSbUploadingStatus} from '@pages/EBRC/api';
import FetchIrmDtlsModal from '@pages/EBRC/components/FetchIrmDtlsModal';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import EximAvatar from '@shared/components/EximAvatar';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon, CloseIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const irmStatusInitialVal = {
  processingStatus: '',
  txnId: '',
  last_updated_date: '',
  last_updated_by: '',
};

const sbStatusInitialVal = {
  file_type: '',
  file_name: '',
  processing_status: '',
  last_updated_date: '',
  last_updated_by: '',
  txn_id: '',
};

export default function DataUploadValidate() {
  const navigate = useNavigate();
  const [irmDetails, setIrmDetails] = useState(irmStatusInitialVal);
  const [sbDetails, setSbDetails] =
    useState<IInputFilesUploadStatus>(sbStatusInitialVal);
  const [isOpenFetchModal, setIsOpenFetchModal] = useState(false);

  const {
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const getSbDetails = useCallback(async () => {
    const payload = {
      iecCode: iecNumber,
      fileType: EbrcFileType.SHIPPING_BILL,
    };
    const response = await getSbUploadingStatus(payload);
    setSbDetails(response?.data[0]);
  }, [iecNumber]);

  const getIrmStatusDetails = useCallback(async () => {
    const payload = {
      iecCode: iecNumber,
    };
    const response = await fetchIrmStatus(payload);
    setIrmDetails(response?.data);
    dispatch(ebrcActions.setFetchIrmTxnId(response?.data?.txnId));
  }, [iecNumber]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleViewIRMDetails = () => {
    dispatch(
      ebrcActions.setInvoicesPeriod({
        startPeriod: getLastMonthAndYear(),
        endPeriod: getCurrentMonthAndYear(),
      })
    );
    navigate(`${Path.EBRC}${Path.VIEW}${Path.IRM_DETAILS}`);
  };

  const handleViewSBDetails = (txnId: string, status: string) => {
    dispatch(ebrcActions.setInvoiceTxnId(txnId));
    dispatch(ebrcActions.setInvoicesCardActive(true));
    dispatch(
      ebrcActions.setInvoicesPeriod({
        startPeriod: getLastMonthAndYear(),
        endPeriod: getCurrentMonthAndYear(),
      })
    );
    // INFO: Track the last transaction status to pass dynamic header based on the last transaction in the invoices details API
    dispatch(
      ebrcActions.setIsLastTransactionInvalid(status?.includes('Invalid'))
    );
    navigate(`${Path.EBRC}${Path.VIEW_INVOICES}${Path.SB}`);
  };

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('In Progress'))
      return <span className='process'>{value}</span>;
    if (
      value?.includes('Failed') ||
      value?.includes('Invalid') ||
      value?.includes('Discarded')
    )
      return <span className='error'>{value}</span>;
    if (value?.includes('Completed'))
      return <span className='success'>{value}</span>;
    return null;
  };

  const getShowIrmStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (
      value?.includes('READY_TO_FETCH') ||
      value?.includes('FETCH_IN_PROGRESS')
    )
      return <span className='process'>In Progress</span>;
    if (value?.includes('Failed')) return <span className='error'>Failed</span>;
    if (value?.includes('FETCHED'))
      return <span className='success'>Completed</span>;
    return null;
  };

  useEffect(() => {
    getSbDetails();
  }, [getSbDetails]);

  useEffect(() => {
    getIrmStatusDetails();
  }, [getIrmStatusDetails]);

  return (
    <div className='data-upload-step-container'>
      <EximPaper>
        <FilingHead
          filingHead='Input Files'
          onGuideClick={handleGuideClick}
          hasGuide
        />
        <div className='filing-step-container'>
          {/* IRM Details */}
          <FilingStep
            stepIcon={
              <span>
                {irmDetails?.processingStatus?.includes('FETCHED') ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='1'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            statusIcon={
              irmDetails?.processingStatus?.includes('READY_TO_FETCH') ||
              irmDetails?.processingStatus?.includes('FETCH_IN_PROGRESS') ? (
                <span onClick={getIrmStatusDetails} role='presentation'>
                  <SolidSync />
                </span>
              ) : null
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='IRM'
            btnName='Fetch'
            btnDisable={false}
            onBtnClick={() => setIsOpenFetchModal(true)}
            secondBtnName='View'
            secondBtnDisable={
              !irmDetails?.processingStatus?.includes('FETCHED')
            }
            status={
              irmDetails?.processingStatus
                ? getShowIrmStatus(irmDetails?.processingStatus)
                : null
            }
            recentUpdate={
              irmDetails?.last_updated_date
                ? `Last Fetched on ${formatDateWithTime(
                    irmDetails?.last_updated_date,
                    false
                  )}`
                : ''
            }
            updatedBy={
              (irmDetails?.last_updated_by &&
                `By ${irmDetails?.last_updated_by}`) ||
              ''
            }
            onSecondBtnClick={handleViewIRMDetails}
          />

          {/* SB Details */}
          <FilingStep
            stepIcon={
              <span>
                {sbDetails.processing_status?.includes('Completed') ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='2'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            statusIcon={
              sbDetails.processing_status?.includes('In Progress') ? (
                <span onClick={getSbDetails} role='presentation'>
                  <SolidSync />
                </span>
              ) : null
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='Shipping Bill'
            btnName='Upload'
            btnDisable={false}
            onBtnClick={() =>
              navigate(`${Path.EBRC}${Path.UPLOAD_PROCESS}${Path.SB}`)
            }
            secondBtnName='View'
            secondBtnDisable={!sbDetails.file_name}
            status={
              sbDetails.processing_status
                ? getShowStatus(sbDetails.processing_status)
                : null
            }
            recentUpdate={
              sbDetails?.file_name
                ? `Last Uploaded on ${formatDateWithTime(
                    sbDetails?.last_updated_date,
                    false
                  )}`
                : ''
            }
            updatedBy={
              (sbDetails?.last_updated_by &&
                `By ${sbDetails?.last_updated_by}`) ||
              ''
            }
            onSecondBtnClick={() =>
              handleViewSBDetails(
                sbDetails?.txn_id,
                sbDetails?.processing_status
              )
            }
          />
        </div>
      </EximPaper>

      {/* Fetch IRM Modal */}
      <div className='fetch-irm-dtls-modal'>
        <EximModal
          isOpen={isOpenFetchModal}
          onClose={() => setIsOpenFetchModal(false)}
          onOutSideClickClose={() => setIsOpenFetchModal(false)}
          content={
            <FetchIrmDtlsModal
              onClose={() => setIsOpenFetchModal(false)}
              getIrmStatusDetails={getIrmStatusDetails}
            />
          }
          footer={false}
          header={
            <EximTypography
              classNames='fetch-modal-title'
              fontWeight='semi-bold'>
              Fetch IRM Details
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>
    </div>
  );
}
