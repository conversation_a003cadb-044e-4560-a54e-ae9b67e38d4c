import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {memo} from 'react';

import './index.scss';

interface IBusinessSubHeaderProps {
  title: string;
  isFileSelected: boolean;
  handleFileUpload: () => void;
  handleDownloadExcelTemplate: () => void;
}

function BusinessSubHeader({
  title,
  isFileSelected,
  handleFileUpload,
  handleDownloadExcelTemplate,
}: IBusinessSubHeaderProps) {
  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='sub-header-wrapper'>
          <EximTypography variant='h5'>Upload {title} Data</EximTypography>
          <div className='btn-container'>
            <EximButton
              size='small'
              color='secondary'
              dataTestId='download-excel-template-btn'
              onClick={handleDownloadExcelTemplate}>
              Download Excel Templates
            </EximButton>
            <EximButton
              size='small'
              disabled={!isFileSelected}
              dataTestId='process-next-btn'
              onClick={handleFileUpload}>
              Process & Next
            </EximButton>
          </div>
        </div>
      </EximPaper>
    </div>
  );
}

export default memo(BusinessSubHeader);
