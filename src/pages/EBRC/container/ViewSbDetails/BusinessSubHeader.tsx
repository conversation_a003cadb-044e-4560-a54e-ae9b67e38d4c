import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {discardTransaction} from '@pages/EBRC/api';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState, dispatch} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IBusinessSubHeaderProps {
  fileType: string;
}

function BusinessSubHeader({fileType}: IBusinessSubHeaderProps) {
  const navigate = useNavigate();

  const {
    ebrc: {iecNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const handleDiscardTransaction = async () => {
    const payload = {
      iecCode: iecNumber,
      txnId: invoiceTxnId,
      fileType,
    };
    const response = await discardTransaction(payload);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: 'Transaction discarded successfully.',
          alertType: AlertStatus.SUCCESS,
        })
      );
      setTimeout(() => navigate(Path.EBRC), 1500);
    }
  };

  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='btn-container'>
          <EximButton
            size='small'
            color='secondary'
            dataTestId='discard-txn'
            onClick={handleDiscardTransaction}>
            Discard
          </EximButton>
          <EximButton
            size='small'
            dataTestId='done-txn'
            onClick={() => navigate(Path.EBRC)}>
            Done
          </EximButton>
        </div>
      </EximPaper>
    </div>
  );
}

export default BusinessSubHeader;
