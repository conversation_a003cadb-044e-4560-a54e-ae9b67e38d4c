import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import RecordsCard from '@common/components/RecordsCard';
import {
  AlertStatus,
  EbrcFileType,
  EximHeroDate,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {ICustomAxiosResp, IEbrcSbList} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {
  deleteInvoices,
  downloadInvoice,
  exportInvoices,
  getInvoices,
  invoicesSummary,
} from '@pages/EBRC/api';
import ErrorRecordsUpload from '@pages/EBRC/components/ErrorRecordsUpload';
import FileDownloadStripe from '@pages/EBRC/components/FileDownloadStripe';
import SBInvoiceTable from '@pages/EBRC/components/SBInvoiceTable';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ErrorIcon, SuccessIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

interface IInvoiceCount {
  valid_inv_count: number;
  invalid_inv_count: number;
}

// It is only for this page
const CARD_TYPE = {
  SUCCESS: 'success',
  ERROR: 'error',
};

function ViewSbDetails() {
  const {
    ebrc: {
      iecNumber,
      invoiceTxnId,
      invoicesCardActive,
      isLastTransactionInvalid,
      invoicesPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState(0);
  const [sbList, setSbList] = useState<IEbrcSbList[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getSbList = useCallback(async () => {
    const sDate = `${startPeriod.split('-').reverse().join('-')}`;
    const eDate = `${endPeriod.split('-').reverse().join('-')}`;
    const payload = {
      txnId: '',
      iecCode: iecNumber,
      startDate: sDate,
      endDate: eDate,
      invType: invoicesCardActive ? 'VALID' : 'INVALID',
      fileType: EbrcFileType.SHIPPING_BILL,
      searchKey,
      sortBy,
      sortingOrder,
      searchValue: debouncedValue,
    };
    // INFO: If last transaction has invalid records the we need to fetch the data using txnId
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    }
    const {data} = await getInvoices(payload, page, +showEntries);
    setSbList(data?.records);
    setTotalRecords(data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    invoiceTxnId,
    iecNumber,
    startPeriod,
    endPeriod,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
    invoicesCardActive,
  ]);

  const [fileId, setFileId] = useState('');
  const [invoiceCount, setInvoiceCount] = useState<IInvoiceCount>({
    valid_inv_count: 0,
    invalid_inv_count: 0,
  });

  const handleViewRecord = (type: string) => {
    dispatch(ebrcActions.setInvoicesCardActive(type === CARD_TYPE.SUCCESS));
  };

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    dispatch(
      ebrcActions.setInvoicesPeriod({
        startPeriod: startDate,
        endPeriod: endDate,
      })
    );
  };

  const handleExportInvoices = async () => {
    const payload = {
      txnId: '',
      iecCode: iecNumber,
      startPeriod: '',
      endPeriod: '',
      reportType: EbrcFileType.SHIPPING_BILL,
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const resp = await exportInvoices(payload);
    setFileId(resp?.data?.['file-id']);
  };

  const handleDownloadInvoices = async () => {
    const payload2 = {
      iecCode: iecNumber,
      reportType: EbrcFileType.SHIPPING_BILL,
      fileId,
    };
    const {data} = await downloadInvoice(payload2);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const getInvoicesSummary = useCallback(async () => {
    const payload = {
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      iecCode: iecNumber,
      fileType: EbrcFileType.SHIPPING_BILL,
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await invoicesSummary(payload);
    setInvoiceCount(data);
  }, [
    iecNumber,
    invoiceTxnId,
    startPeriod,
    endPeriod,
    isLastTransactionInvalid,
  ]);

  const handleDeleteAllFailedInv = async () => {
    const payload = {
      iecCode: iecNumber,
      txnId: invoiceTxnId,
      fileType: EbrcFileType.SHIPPING_BILL,
    };
    const response = (await deleteInvoices(
      payload,
      true,
      []
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
    // Updating the data after deleting the all invalid records
    getInvoicesSummary();
  };

  useEffect(() => {
    getInvoicesSummary();
  }, [getInvoicesSummary]);

  useEffect(() => {
    getSbList();
  }, [getSbList]);

  return (
    <div className='invoice-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText='Shipping Bill'
      />
      <BusinessHeader>
        <div className='btn-children-container'>
          <EximTypography>Period</EximTypography>
          <EximMonthRangePicker
            id='invoiceDatePicker'
            minDate={EximHeroDate.MIN_MONTH}
            onSelect={handleSelectPeriod}
            defaultStartDate={startPeriod.split('-').join('/')}
            defaultEndDate={endPeriod.split('-').join('/')}
            disabled={isLastTransactionInvalid}
          />
        </div>
      </BusinessHeader>
      <BusinessSubHeader fileType={EbrcFileType.SHIPPING_BILL} />
      {invoicesCardActive && fileId ? (
        <FileDownloadStripe
          status='COMPLETED'
          handleRefresh={() => undefined}
          handleDownload={handleDownloadInvoices}
        />
      ) : null}
      <div className='records-card-container'>
        <RecordsCard
          title='Valid records'
          icon={<SuccessIcon />}
          recordType='success'
          handleViewRecord={() => handleViewRecord(CARD_TYPE.SUCCESS)}
          isActive={invoicesCardActive}
          recordCount={invoiceCount?.valid_inv_count || 0}>
          <EximButton
            onClick={handleExportInvoices}
            disabled={
              !invoicesCardActive || invoiceCount?.valid_inv_count === 0
            }
            size='small'
            color='secondary'
            className='export-excel-btn'
            dataTestId='export-excel-btn'>
            Download
          </EximButton>
        </RecordsCard>
        <RecordsCard
          title='Error records'
          icon={<ErrorIcon />}
          recordType='error'
          handleViewRecord={() => handleViewRecord(CARD_TYPE.ERROR)}
          isActive={!invoicesCardActive}
          recordCount={invoiceCount?.invalid_inv_count || 0}>
          <EximButton
            onClick={handleDeleteAllFailedInv}
            disabled={
              invoicesCardActive || invoiceCount?.invalid_inv_count === 0
            }
            size='small'
            color='secondary'
            className='delete-all-error-records-btn'
            dataTestId='delete-all-error-records-btn'>
            Delete All
          </EximButton>
        </RecordsCard>
      </div>

      <div className='view-invoices-container'>
        <EximPaper>
          {!invoicesCardActive ? (
            <ErrorRecordsUpload
              getInvoices={() => {
                getSbList();
                getInvoicesSummary();
              }}
              fileType={EbrcFileType.SHIPPING_BILL}
              disabledButton={invoiceCount?.invalid_inv_count === 0}
            />
          ) : null}
          <SBInvoiceTable
            sbList={sbList}
            page={page}
            searchKey={searchKey}
            totalRecords={totalRecords}
            showEntries={showEntries}
            searchValue={searchValue}
            handlePageChange={handlePageChange}
            handleShowEntries={handleShowEntries}
            handleSearchKey={handleSearchKey}
            handleSearchQuery={handleSearchQuery}
            handleSortBy={handleSortBy}
          />
        </EximPaper>
      </div>
    </div>
  );
}

export default ViewSbDetails;
