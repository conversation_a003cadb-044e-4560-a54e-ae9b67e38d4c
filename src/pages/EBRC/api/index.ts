import {Api, ApiAction} from '@common/constants';
import {removeUndefinedKeys} from '@common/helpers';
import {ILinkIrmAndSb} from '@common/interfaces';
import {get, post, put} from '@core/api/axios';
import {AxiosResponse} from 'axios';

const {
  EBRC_SERVICE,
  EBRC,
  IRM_DETAILS,
  EBRC_DASHBOARD_SUMMARY,
  FETCH_IRM_DETAILS,
  IRM_FETCH_PROCESSING_STATUS,
  AUTO_LINK_IRM_DETAILS,
  LINKING_SUMMARY_DETAILS,
  FILE_UPLOAD_SERVICE,
  FILE_PROCESSING_DETAILS,
  FILE_UPLOAD_TEMPLATE,
  DOWNLOAD,
  INVOICES,
  UPLOAD,
  HISTORY,
  OPEN_IRM_DETAILS,
  OPEN_SB_DETAILS,
  MANUAL_LINK_IRM_SB_DETAILS,
  GST_MATCH_IRM_SB_DETAILS,
  GENERATE_EBRC,
  EBRC_REPORT,
  GENERATED_EBRC_DETAILS,
  SUMMARY,
  <PERSON><PERSON>CARD,
  EXPORT,
  AUTO_LINK_CONFIG_DTLS,
} = Api;
const {
  GET_IRM_DETAILS,
  FETCH_IRM_DETAILS: FETCH_IRM_DTLS,
  DASHBOARD_SUMMARY,
  GET_FETCH_IRM_STATUS,
  AUTOLINK_IRM_SB_DETAILS,
  GET_LINKING_SUMMARY,
  FILE_PROCESSING_DTLS,
  DOWNLOAD_FILE_TEMPLATE,
  FILE_UPLOAD,
  GET_INVOICES,
  FILE_UPLOAD_HISTORY,
  GET_OPEN_IRM_DETAILS,
  GET_OPEN_SB_DETAILS,
  MANUAL_LINK_IRM_SB_DETAILS: MANUAL_LINKING,
  GET_MATCH_IRM_SB,
  GENERATE_EBRC: GENERATE_EBRC_DATA,
  EBRC_REPORT: GET_EBRC_REPORT,
  GET_INVOICES_SUMMARY,
  DELETE_INVOICES,
  DISCARD_INVALID_FILE_TXN,
  EXPORT_REPORT,
  DOWNLOAD_REPORT,
  GET_GENERATED_EBRC_DTLS,
  GET_AUTO_LINK_CONFIGURATION_DTL,
  UPDATE_AUTO_LINK_CONFIGURATION_DTL,
} = ApiAction;

export interface IApiData {
  iec?: string;
  'iec-code'?: string;
  iecCode?: string;
  fileType?: string;
  reportType?: string;
  fileId?: string;
  txnId?: string;
  invType?: string;
  errorFileId?: string;
  startDate?: string;
  endDate?: string;
  searchKey?: string;
  searchValue?: string;
  sortBy?: string;
  sortingOrder?: 1 | -1;
}

export const ebrcSummary = async (data: IApiData) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${EBRC_DASHBOARD_SUMMARY}?action=${DASHBOARD_SUMMARY}`,
    {
      headers: {
        'irm-start-period': data.startDate,
        'irm-end-period': data.endDate,
        'iec-code': data.iecCode,
      },
    }
  );
  return response as AxiosResponse;
};

export const fetchIrmDtls = async (data: IApiData) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${FETCH_IRM_DETAILS}?action=${FETCH_IRM_DTLS}`,
    {
      headers: {
        'irm-start-period': data.startDate,
        'irm-end-period': data.endDate,
        'iec-code': data.iecCode,
      },
    }
  );
  return response as AxiosResponse;
};

export const fetchIrmStatus = async (data: IApiData) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${IRM_FETCH_PROCESSING_STATUS}?action=${GET_FETCH_IRM_STATUS}`,
    {
      headers: {
        'iec-code': data.iecCode,
      },
    }
  );
  return response as AxiosResponse;
};

export const getIrmDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${IRM_DETAILS}?page-no=${page}&limit=${limit}&action=${GET_IRM_DETAILS}`,
    {
      headers: {
        'start-period': data.startDate,
        'end-period': data.endDate,
        'iec-code': data.iecCode,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getSbUploadingStatus = async (data: IApiData) => {
  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${FILE_PROCESSING_DETAILS}?action=${FILE_PROCESSING_DTLS}`,
    {
      headers: {
        iec: data.iecCode,
        'file-type': data.fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const downloadExcelTemplate = async (data: IApiData) => {
  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${FILE_UPLOAD_TEMPLATE}${DOWNLOAD}?file-type=${data.fileType}&action=${DOWNLOAD_FILE_TEMPLATE}`,
    {
      headers: {
        iec: data.iecCode,
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const uploadFileData = async (data: IApiData, file: File) => {
  const attachedFiles = new FormData();
  attachedFiles.append(`attached_files`, file);

  const headers = {
    iec: data.iecCode || '',
    'file-type': data.fileType || '',
    'error-file-id': data.errorFileId || '',
    'txn-id': data.txnId || '',
    'Content-Type': 'multipart/form-data',
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await post(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${UPLOAD}?action=${FILE_UPLOAD}`,
    attachedFiles,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getUploadHistory = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${UPLOAD}${HISTORY}?page=${page}&limit=${limit}&action=${FILE_UPLOAD_HISTORY}`,
    {
      headers: {
        iec: data.iecCode || '',
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getInvoices = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    iec: data.iecCode || '',
    'txn-id': data.txnId,
    'inv-type': data.invType,
    'file-type': data.fileType,
    'start-period': data.startDate,
    'end-period': data.endDate,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}?page-no=${page}&limit=${limit}&action=${GET_INVOICES}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const invoicesSummary = async (data: IApiData) => {
  const headers = {
    iec: data.iecCode || '',
    'txn-id': data.txnId,
    'start-period': data.startDate,
    'end-period': data.endDate,
    'file-type': data.fileType,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${SUMMARY}?action=${GET_INVOICES_SUMMARY}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const discardTransaction = async (data: IApiData) => {
  const headers = {
    iec: data.iecCode || '',
    'txn-id': data.txnId,
    'start-period': data.startDate,
    'end-period': data.endDate,
    'file-type': data.fileType,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await put(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${DISCARD}?action=${DISCARD_INVALID_FILE_TXN}`,
    {},
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const exportInvoices = async (data: IApiData) => {
  const response = await put(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${EXPORT}?action=${EXPORT_REPORT}`,
    {},
    {
      headers: {
        iec: data.iecCode || '',
        'txn-id': data.txnId || '',
        'report-type': data.reportType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const downloadInvoice = async (data: IApiData) => {
  const response = await get(
    `${FILE_UPLOAD_SERVICE}${INVOICES}${DOWNLOAD}?action=${DOWNLOAD_REPORT}`,
    {
      headers: {
        iec: data.iecCode || '',
        'report-type': data.reportType || '',
        'file-id': data?.fileId || '',
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const deleteInvoices = async (
  data: IApiData,
  deleteAll: boolean,
  ids: string[]
) => {
  const response = await put(
    `${FILE_UPLOAD_SERVICE}${INVOICES}?delete-all=${deleteAll}&txn-id=${data.txnId}&action=${DELETE_INVOICES}`,
    ids,
    {
      headers: {
        iec: data.iecCode || '',
        'file-type': data.fileType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const autoLinkIrmDetails = async (data: IApiData) => {
  const response = await post(
    `${EBRC_SERVICE}${EBRC}${AUTO_LINK_IRM_DETAILS}?action=${AUTOLINK_IRM_SB_DETAILS}`,
    {},
    {
      headers: {
        'irm-start-period': data.startDate || '',
        'irm-end-period': data.endDate || '',
        'iec-code': data.iecCode || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const autoLinkSummary = async (data: IApiData) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${LINKING_SUMMARY_DETAILS}?action=${GET_LINKING_SUMMARY}`,
    {
      headers: {
        'irm-start-period': data.startDate || '',
        'irm-end-period': data.endDate || '',
        'iec-code': data.iecCode || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getOpenIrmList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    'iec-code': data.iecCode || '',
    'txn-id': data.txnId || '',
    'irm-start-period': data.startDate,
    'irm-end-period': data.endDate,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EBRC_SERVICE}${EBRC}${OPEN_IRM_DETAILS}?page-no=${page}&limit=${limit}&action=${GET_OPEN_IRM_DETAILS}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getOpenSbList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    'iec-code': data.iecCode || '',
    'txn-id': data.txnId || '',
    'irm-start-period': data.startDate,
    'irm-end-period': data.endDate,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EBRC_SERVICE}${EBRC}${OPEN_SB_DETAILS}?page-no=${page}&limit=${limit}&action=${GET_OPEN_SB_DETAILS}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const manualLinkingIrmAndSb = async (data: ILinkIrmAndSb) => {
  const response = await post(
    `${EBRC_SERVICE}${EBRC}${MANUAL_LINK_IRM_SB_DETAILS}?action=${MANUAL_LINKING}`,
    data,
    {
      headers: {},
    }
  );
  return response as AxiosResponse;
};

// INFO: Pending EBRC Table API
export const getMatchIrmSbDtls = async (
  data: IApiData,
  page: number,
  limit: number,
  isSubmitToEbrc: boolean
) => {
  const headers = {
    isSubmitToEbrc,
    'iec-code': data.iecCode || '',
    'txn-id': data.txnId || '',
    'irm-start-period': data.startDate,
    'irm-end-period': data.endDate,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EBRC_SERVICE}${EBRC}${GST_MATCH_IRM_SB_DETAILS}?page-no=${page}&limit=${limit}&action=${GET_MATCH_IRM_SB}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const generateEbrc = async (data: IApiData, ids: string[]) => {
  const headers = {
    'iec-code': data.iecCode || '',
    'txn-id': data.txnId || '',
    'start-period': data.startDate,
    'end-period': data.endDate,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await post(
    `${EBRC_SERVICE}${EBRC}${GENERATE_EBRC}?action=${GENERATE_EBRC_DATA}`,
    {ids},
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const generatedEbrcDtls = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    'iec-code': data.iecCode || '',
    'txn-id': data.txnId || '',
    'irm-start-period': data.startDate,
    'irm-end-period': data.endDate,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EBRC_SERVICE}${EBRC}${GENERATED_EBRC_DETAILS}?page-no=${page}&limit=${limit}&action=${GET_GENERATED_EBRC_DTLS}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const ebrcReport = async (iecCode: string, ebrcNo: string[]) => {
  const response = await post(
    `${EBRC_SERVICE}${EBRC}${EBRC_REPORT}?action=${GET_EBRC_REPORT}`,
    ebrcNo,
    {
      headers: {
        'iec-code': iecCode,
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

// INFO: EBRC Configuration Settings API
export const getConfigDetails = async (iecCode: string) => {
  const response = await get(
    `${EBRC_SERVICE}${EBRC}${AUTO_LINK_CONFIG_DTLS}?iecCode=${iecCode}&action=${GET_AUTO_LINK_CONFIGURATION_DTL}`
  );
  return response as AxiosResponse;
};

export const saveConfigDetails = async (data: {
  iecCode: string;
  linkOneIrmToManySb: string;
  linkOneSbToManyIrm: string;
  checkSubstringFromRemitterName: string;
}) => {
  const response = await put(
    `${EBRC_SERVICE}${EBRC}${AUTO_LINK_CONFIG_DTLS}?action=${UPDATE_AUTO_LINK_CONFIGURATION_DTL}`,
    data
  );
  return response as AxiosResponse;
};
