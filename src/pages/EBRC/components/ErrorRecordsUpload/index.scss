@import '@utils/main.scss';

.error-record-upload-container {
  background: $invoice-upload-background;
  @include padding(25px 48px 25px 30px);
  .process-steps-container {
    @include flex-item(_, space-between, _);
    @include margin-top(25px);

    .process-step {
      @include flex-item(column, _, center, _, 16px);

      .base-btn {
        @include margin(0);
        font-size: $font-size-sm;
        @include padding(6px 22px);
      }
      & > span {
        @include flex-item(_, center, center, _, 16px);
      }
      .file-input-container {
        .file-button {
          background: $primary;
          color: $white;
          @include padding(7px 22px);
        }
      }
      span {
        .typography-container {
          max-width: 90px;
          p {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .step-text {
      display: flex;
      letter-spacing: 0.4px;
    }
  }
  .upload-note {
    display: flex;
  }
}
