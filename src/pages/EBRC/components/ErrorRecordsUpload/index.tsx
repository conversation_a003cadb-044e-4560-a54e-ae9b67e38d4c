import UploadFiles from '@common/components/UploadFiles';
import {
  AlertStatus,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {checkFilesExtension, downloadFile} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {downloadInvoice, exportInvoices, uploadFileData} from '@pages/EBRC/api';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import GSTButton from '@shared/components/EximButton';
import GSTTypography from '@shared/components/EximTypography';
import {ArrowRight} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {ChangeEvent, memo, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IErrorUploadProps {
  fileType: string;
  disabledButton: boolean;
  getInvoices: () => void;
}

function ErrorRecordsUpload({
  fileType,
  disabledButton,
  getInvoices,
}: IErrorUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File>();
  const errorFileType = `${fileType}_ERROR_REGISTER`;
  // const errorFileType = fileType.split('_').join('_ERROR_');

  const {
    ebrc: {
      iecNumber,
      invoiceTxnId,
      invoiceErrorFileId,
      isLastTransactionInvalid,
      invoicesPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [fileId, setFileId] = useState('');

  const handleExportInvoices = async () => {
    const payload = {
      txnId: '',
      iecCode: iecNumber,
      startPeriod: '',
      endPeriod: '',
      reportType: errorFileType,
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const resp = await exportInvoices(payload);
    setFileId(resp?.data?.['file-id']);
    dispatch(ebrcActions.setInvoiceErrorFileId(resp?.data?.['file-id']));
  };

  const handleDownloadInvoices = async () => {
    const payload2 = {
      iecCode: iecNumber,
      reportType: errorFileType,
      fileId,
    };
    const {data} = await downloadInvoice(payload2);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);

      if (checkFilesExtension(selectedFilesArray, [SupportedFileTypes.EXCEL])) {
        setSelectedFile(selectedFilesArray[0]);
        const payload = {
          iecCode: iecNumber,
          txnId: invoiceTxnId,
          errorFileId: invoiceErrorFileId,
          fileType,
        };
        const response = (await uploadFileData(
          payload,
          selectedFilesArray[0]
        )) as ICustomAxiosResp;

        if (response.status.toString() === ResponseStatus.SUCCESS) {
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          getInvoices();
        }
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message: 'Please select the excel files only!',
            alertType: 'danger',
          })
        );
      }
    }
  };

  return (
    <div className='error-record-upload-container'>
      <div className='upload-note'>
        <GSTTypography fontWeight='bold' variant='h4'>
          Note:{' '}
        </GSTTypography>
        <GSTTypography variant='h4'>
          - Follow the below steps and Download the Excel containing error
          records with valid comments, rectify the error data and Reupload the
          same Excel File.
        </GSTTypography>
      </div>
      <div className='process-steps-container'>
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 1 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Download Error Records Excel
            </GSTTypography>
          </div>
          {fileId ? (
            <GSTButton
              size='small'
              color='secondary'
              disabled={disabledButton}
              onClick={handleDownloadInvoices}>
              Download
            </GSTButton>
          ) : (
            <GSTButton
              size='small'
              color='secondary'
              disabled={disabledButton}
              onClick={handleExportInvoices}>
              Export
            </GSTButton>
          )}
        </div>
        <ArrowRight />
        <div className='step-text'>
          <GSTTypography fontWeight='bold' variant='h4'>
            Step 2 - &nbsp;
          </GSTTypography>
          <GSTTypography fontWeight='semi-bold' variant='h4'>
            Rectify the Error records
          </GSTTypography>
        </div>
        <ArrowRight />
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 3 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Upload Validation Excel
            </GSTTypography>
          </div>

          <span>
            <UploadFiles
              title='Upload'
              accept='.xlsx'
              disabled={disabledButton}
              onChange={handleFileUpload}
              icon={null}
            />
            <GSTTypography>
              {selectedFile?.name ?? `No Chosen File`}
            </GSTTypography>
          </span>
        </div>
      </div>
    </div>
  );
}

export default memo(ErrorRecordsUpload);
