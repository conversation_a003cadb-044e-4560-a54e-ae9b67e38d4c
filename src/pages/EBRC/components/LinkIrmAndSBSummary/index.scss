@import '@utils/main.scss';

.link-irm-and-sb-summary-container {
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;
    background-color: $white;
    box-shadow: 0px 3px 6px $box-shadow-color;
  }
  .summary-header {
    @include padding(20px);
    border-bottom: 1px solid $primary-border;
    @include flex-item(_, space-between, center);
    .summary-title {
      @include flex-item(_, flex-start, center);
      .filing-head {
        height: auto;
        border-bottom: none;
        .typography-container > h4 {
          font-weight: normal;
          font-size: $font-size-sm;
        }
      }

      .avatar-wrapper {
        .avatar-container {
          .small {
            width: 28px;
            height: 28px;
          }

          .avatar-text {
            background-color: $tertiary;
            border: none;
            color: $white;
            font-size: $font-size-sm;
            font-weight: normal;
          }
        }
      }
    }
    .btn-container {
      @include flex-item(_, flex-start, center, _, 16px);
      .autolink-section {
        @include flex-item(_, flex-start, center, _, 20px);
        .autolink-status {
          @include flex-item(_, flex-start, center, _, 8px);
          .refresh-icon {
            @include margin-top(5px);
            cursor: pointer;
          }
          .error {
            color: $error;
          }
          .success {
            color: $success;
          }
          .process {
            color: $warning;
          }
        }
      }
      .button-wrapper {
        min-width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
      .irm-date-picker {
        @include flex-item(_, flex-start, center, _, 16px);
      }
    }
  }
  .summary-details {
    @include padding(20px);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    .card-container {
      position: relative;
      cursor: pointer;
      @include padding(20px);
      @include flex-item(column, center, center, _, 5px);
      @include rfs(5px, border-radius);
      border: 1px solid $card-label-color;
      .open-items {
        color: $table-head-primary;
        span.pipe {
          @include margin(0 5px);
        }
      }
    }
    .card-container.active {
      border-bottom: 5px solid $table-head-primary;
      .triangle {
        position: absolute;
        left: 48%;
        top: 100%;
        border-left: 11px solid transparent;
        border-right: 11px solid transparent;
        border-top: 12px solid $table-head-primary;
      }
    }
  }
}
