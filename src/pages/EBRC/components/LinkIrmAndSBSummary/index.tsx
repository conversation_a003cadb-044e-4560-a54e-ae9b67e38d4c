import FilingHead from '@common/components/FilingHead';
import {
  AlertStatus,
  EbrcSummaryCards,
  EximHeroDate,
  ResponseStatus,
} from '@common/constants';
import {getAlertMessage} from '@common/helpers';
import {alertActions} from '@core/api/store/alertReducer';
import {autoLinkIrmDetails, autoLinkSummary} from '@pages/EBRC/api';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import {IRM_SB_SUMMARY_CARDS} from '@pages/EBRC/utils';
import EximAvatar from '@shared/components/EximAvatar';
import EximButton from '@shared/components/EximButton';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import GeneratedEbrcTable from '../GeneratedEbrcTable';
import OpenItems from '../OpenItems';
import PendingEbrcTable from '../PendingEbrcTable';
import './index.scss';

const initialSummary = {
  openShippingBill: 0,
  linkedAndNotSubmitted: 0,
  linkedAndSubmitted: 0,
  openIRM: 0,
};

export default function LinkIrmAndSBSummary() {
  const {
    ebrc: {
      iecNumber,
      activeLinkSummaryCard: activeCard = EbrcSummaryCards.OPEN_ITEMS,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [autoLinkDtls, setAutoLinkDtls] = useState(initialSummary);
  const [autoLinkStatus, setAutoLinkStatus] = useState({
    on: '10-06-2025',
    status: 'COMPLETED',
  });

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    dispatch(
      ebrcActions.setIrmSbPeriod({startPeriod: startDate, endPeriod: endDate})
    );
  };

  const getAutoLinkSummary = useCallback(async () => {
    const payload = {
      iecCode: iecNumber,
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
    };
    const response = await autoLinkSummary(payload);
    if (response.status?.toString() === ResponseStatus.SUCCESS) {
      setAutoLinkDtls(response?.data);
      dispatch(ebrcActions.setAutoLinkTxnId(response?.data?.txnId));
    } else {
      getAlertMessage(AlertStatus.DANGER, response.data);
    }
  }, [iecNumber, startPeriod, endPeriod]);

  const handleAutoLink = async () => {
    const payload = {
      iecCode: iecNumber,
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
    };
    const response = await autoLinkIrmDetails(payload);
    if (response?.status?.toString() === ResponseStatus.SUCCESS) {
      getAutoLinkSummary();
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data.message || 'Auto linking has been initiated',
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
  };

  const getShowReconStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('IN_PROGRESS'))
      return <span className='process'>Autolink In Progress</span>;
    if (value?.includes('FAILED'))
      return <span className='error'>Autolink Failed</span>;
    if (value?.includes('COMPLETED'))
      return <span className='success'>Autolink Completed</span>;
    return null;
  };

  useEffect(() => {
    getAutoLinkSummary();
  }, [getAutoLinkSummary]);

  return (
    <div className='link-irm-and-sb-summary-container'>
      <EximPaper>
        <div className='summary-header'>
          <div className='summary-title'>
            <EximAvatar
              rounded
              firstName='3'
              lastName=''
              alt='number'
              size='small'
            />
            <FilingHead
              filingHead='Summary of Linked IRM and Shipping Bill'
              onGuideClick={handleGuideClick}
              hasGuide
            />
          </div>
          <div className='btn-container'>
            <div className='autolink-section'>
              {/* TODO: Need to uncomment when API is available */}
              {/* <EximTypography variant='h5'>
                <strong>Last Autolink on :</strong> {autoLinkStatus.on}
              </EximTypography>
              <div>
                <div className='autolink-status'>
                  <EximTypography variant='h5'>
                    {getShowReconStatus(autoLinkStatus.status)}
                  </EximTypography>
                  <span
                    className='refresh-icon'
                    role='presentation'
                    onClick={() => undefined}>
                    {autoLinkStatus.status === 'IN_PROGRESS' ? (
                      <SolidSync />
                    ) : null}
                  </span>
                </div>
              </div> */}
              <EximButton
                size='small'
                color='primary'
                disabled={autoLinkStatus.status === 'IN_PROGRESS'}
                onClick={handleAutoLink}>
                Autolink
              </EximButton>
            </div>
            <div className='irm-date-picker'>
              <EximTypography>IRM Period</EximTypography>
              <EximMonthRangePicker
                id='invoiceDatePicker'
                minDate={EximHeroDate.MIN_MONTH}
                onSelect={handleSelectPeriod}
                defaultStartDate={startPeriod.split('-').join('/')}
                defaultEndDate={endPeriod.split('-').join('/')}
              />
            </div>
          </div>
        </div>
        <div className='summary-details'>
          {IRM_SB_SUMMARY_CARDS.map((card) => {
            const activeClass = activeCard === card.type ? 'active' : '';
            let openIrm;
            let openSb;
            if (card.type === EbrcSummaryCards.OPEN_ITEMS) {
              openIrm = card.dataKey?.split(' ')[0];
              openSb = card.dataKey?.split(' ')[1];
            }
            return (
              <div
                key={card.dataKey}
                className={`card-container ${activeClass}`}
                role='presentation'
                onClick={() =>
                  dispatch(ebrcActions.setActiveLinkSummaryCard(card.type))
                }>
                <EximTypography variant='h5'>{card.title}</EximTypography>
                {card.type === EbrcSummaryCards.OPEN_ITEMS ? (
                  <EximTypography
                    variant='h3'
                    fontWeight='semi-bold'
                    classNames='open-items'>
                    IRM :{' '}
                    {autoLinkDtls?.[openIrm as keyof typeof initialSummary]}
                    <span className='pipe'>|</span>
                    SB : {autoLinkDtls?.[openSb as keyof typeof initialSummary]}
                  </EximTypography>
                ) : (
                  <EximTypography variant='h3' fontWeight='semi-bold'>
                    {
                      autoLinkDtls?.[
                        card.dataKey as keyof typeof initialSummary
                      ]
                    }
                  </EximTypography>
                )}
                {activeClass && <div className='triangle' />}
              </div>
            );
          })}
        </div>
        {activeCard === EbrcSummaryCards.OPEN_ITEMS ? <OpenItems /> : null}
        {activeCard === EbrcSummaryCards.PENDING_EBRC ? (
          <PendingEbrcTable />
        ) : null}
        {activeCard === EbrcSummaryCards.GENERATED_EBRC ? (
          <GeneratedEbrcTable />
        ) : null}
      </EximPaper>
    </div>
  );
}
