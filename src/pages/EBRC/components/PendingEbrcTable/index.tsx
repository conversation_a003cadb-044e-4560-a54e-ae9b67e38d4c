import ConfirmationModal from '@common/components/ConfirmationModal';
import EmptyTable from '@common/components/EmptyTable';
import FilingHead from '@common/components/FilingHead';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {AlertStatus, ResponseStatus} from '@common/constants';
import {getAlertMessage} from '@common/helpers';
import {ICustomAxiosResp, IPendingEbrc} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {generateEbrc, getMatchIrmSbDtls} from '@pages/EBRC/api';
import {
  PENDING_EBRC_SEARCH_DROPDOWN,
  PENDING_EBRC_TABLE_HEADER,
} from '@pages/EBRC/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

export default function PendingEbrcTable() {
  const {
    ebrc: {
      iecNumber,
      autoLinkTxnId,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState(false);
  const [isOpenDelinkModal, setIsOpenDelinkModal] = useState(false);
  const [isSelectAll, setIsSelectAll] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pendingEbrcList, setPendingEbrcList] = useState<IPendingEbrc[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getMatchIrmSbData = useCallback(async () => {
    const payload = {
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
      iecCode: iecNumber,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getMatchIrmSbDtls(payload, page, +showEntries, false);
    const newData = data?.map((item: IPendingEbrc) => ({
      ...item,
      selected: false,
    }));
    setPendingEbrcList(newData);
    setTotalRecords(data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, iecNumber, showEntries, debouncedValue, sortBy, sortingOrder]);

  // TODO: Handle below functionalities
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleConfirmSubmit = async () => {
    const selectedIds = pendingEbrcList
      ?.filter((item) => item.selected)
      ?.map((el) => el.id);
    if (selectedIds.length > 0) {
      const payload = {
        iecCode: iecNumber,
        txnId: autoLinkTxnId,
        startDate: startPeriod?.split('-').reverse().join('-'),
        endDate: endPeriod?.split('-').reverse().join('-'),
      };
      const response = (await generateEbrc(
        payload,
        selectedIds
      )) as ICustomAxiosResp;
      if (response?.status?.toString() === ResponseStatus.SUCCESS) {
        const msgObj = JSON.parse(response?.msg);
        getMatchIrmSbData();
        getAlertMessage(
          AlertStatus.SUCCESS,
          `IRM Submitted Successfully, Please Note The Acknowledgement ID : ${msgObj?.dgftAckId}`
        );
        setIsOpenConfirmModal(false);
      }
    }
  };

  const handleConfirmDelink = async () => {
    const selectedIds = pendingEbrcList
      ?.filter((item) => item.selected)
      ?.map((el) => el.id);
    if (selectedIds.length > 0) {
      const payload = {
        iecCode: iecNumber,
        txnId: autoLinkTxnId,
        startDate: startPeriod?.split('-').reverse().join('-'),
        endDate: endPeriod?.split('-').reverse().join('-'),
      };
      setIsOpenDelinkModal(false);
    }
  };

  const handleSingleSelect = (id: string) => {
    const updatedList = pendingEbrcList.map((item: IPendingEbrc) =>
      item.id === id ? {...item, selected: !item.selected} : item
    );
    setPendingEbrcList(updatedList);

    const selectedItems = updatedList.filter((item) => item.selected).length;
    const isAllSelected = selectedItems === pendingEbrcList.length;
    setIsSelectAll(isAllSelected);
  };

  const handleSelectAll = () => {
    const updatedList = pendingEbrcList.map((item: IPendingEbrc) => ({
      ...item,
      selected: !isSelectAll,
    }));
    setPendingEbrcList(updatedList);
    setIsSelectAll(!isSelectAll);
  };

  useEffect(() => {
    getMatchIrmSbData();
  }, [getMatchIrmSbData]);

  return (
    <div className='pending-ebrc-container'>
      <div className='summary-header'>
        <div className='summary-title'>
          <FilingHead
            filingHead='Pending eBRC'
            onGuideClick={handleGuideClick}
            hasGuide
          />
        </div>
        <div className='btn-container'>
          <EximButton
            size='small'
            color='secondary'
            disabled={!pendingEbrcList?.find((el) => el.selected)}
            onClick={() => setIsOpenDelinkModal(true)}>
            Delink
          </EximButton>
          <EximButton
            size='small'
            color='primary'
            disabled={!pendingEbrcList?.find((el) => el.selected)}
            onClick={() => setIsOpenConfirmModal(true)}>
            Generate eBRC
          </EximButton>
        </div>
      </div>
      <div className='pending-ebrc-table-container'>
        <TableSearchFilter
          isInputDisabled={!searchKey}
          handleShowEntries={handleShowEntries}
          handleSearchQuery={handleSearchQuery}>
          <EximCustomDropdown
            placeholder='Search By Column'
            onSelect={({value}) => handleSearchKey(value)}
            dataTestId='column-dropdown'
            optionsList={PENDING_EBRC_SEARCH_DROPDOWN}
          />
        </TableSearchFilter>
        <div className='scrollable-table'>
          <table className='pending-ebrc-table'>
            <TableHeader
              mainHeader={PENDING_EBRC_TABLE_HEADER}
              handleSortBy={handleSortBy}
              onChange={handleSelectAll}
              checked={isSelectAll}
            />
            {pendingEbrcList?.length > 0 ? (
              <TableBody className='pending-ebrc-tbody'>
                {pendingEbrcList?.map((item: IPendingEbrc) => (
                  <TableRow key={item.id}>
                    <TableCell className='checkbox-td'>
                      <EximCheckbox
                        id={item.id}
                        color='#2CB544'
                        size='medium'
                        checked={item.selected}
                        onChange={() => handleSingleSelect(item.id)}
                      />
                    </TableCell>
                    <TableCell>{item.irmNumber}</TableCell>
                    <TableCell>{item.irmIssueDate}</TableCell>
                    <TableCell>{item.purposeOfRemittance}</TableCell>
                    <TableCell>{item.remitterName}</TableCell>
                    <TableCell>
                      {item.remittanceFCCAmount?.toFixed(2)}
                    </TableCell>
                    <TableCell>{item.remittanceFCC}</TableCell>
                    <TableCell>{item.shippingBillNo}</TableCell>
                    <TableCell>{item.shippingBillDate}</TableCell>
                    <TableCell>{item.portCode}</TableCell>
                    <TableCell>{item.billNo}</TableCell>
                    <TableCell>-</TableCell>
                    <TableCell>{item.sbFcVal?.toFixed(2)}</TableCell>
                    <TableCell>{item.sbFcCd}</TableCell>
                    <TableCell>{item.sacCode}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={PENDING_EBRC_TABLE_HEADER.length} />
            )}
          </table>
        </div>
        <TableFooter
          page={page}
          searchQuery={searchValue}
          showEntries={showEntries}
          totalRecords={totalRecords}
          searchData={pendingEbrcList as []}
          renderData={pendingEbrcList as []}
          handlePageChange={handlePageChange}
          hasBackendPagination
        />
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isOpenDelinkModal}
        onClose={() => {
          setIsOpenDelinkModal(false);
        }}
        title='Delink'
        primaryBtnText='Delink'
        handleConfirm={handleConfirmDelink}
        alignContent='left'
        content='Are you sure you want to Delink the following IRM and Shipping Bill?'
      />

      {/* Generate eBRC Confirmation Modal */}
      <ConfirmationModal
        isOpen={isOpenConfirmModal}
        onClose={() => {
          setIsOpenConfirmModal(false);
        }}
        title='Submit'
        handleConfirm={handleConfirmSubmit}
        content='Are you sure you want to submit the invoice?'
      />
    </div>
  );
}
