@import '@utils//main.scss';

.pending-ebrc-container {
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;
  }
  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .summary-header {
    @include padding(20px);
    border-bottom: 1px solid $primary-border;
    @include flex-item(_, space-between, center);
    .summary-title {
      @include flex-item(_, flex-start, center);
      .filing-head {
        height: auto;
        border-bottom: none;
        .typography-container > h4 {
          font-weight: normal;
          font-size: $font-size-sm;
        }
      }
    }
    .btn-container {
      @include flex-item(_, flex-start, center, _, 16px);
      .button-wrapper {
        min-width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }

  .table-search-container {
    @include margin-top(20px);
  }
  .pending-ebrc-table-container {
    @include padding(20px 16px);
    .scrollable-table {
      max-width: 100%;
      min-width: 100%;
      overflow-x: auto;
      @include rfs(16px 16px 0 0, border-radius);
      @include scrollbar(6px, 6px);

      th,
      td {
        white-space: nowrap; /* Prevent text wrapping */
      }

      thead th:first-child,
      thead th:nth-child(2),
      thead th:last-child,
      tbody td:first-child,
      tbody td:nth-child(2),
      tbody td:last-child {
        position: sticky;
      }
      tbody td:first-child,
      tbody td:nth-child(2),
      tbody td:last-child {
        background-color: $white;
      }

      thead th:last-child {
        border-left: 1px solid;
      }
      thead th:nth-last-child(2) {
        border-right: none;
      }

      thead th:first-child,
      tbody td:first-child {
        left: 0;
        z-index: 1;
      }

      thead th:nth-child(2),
      tbody td:nth-child(2) {
        z-index: 2;
        left: 55px;
      }
      thead th:last-child,
      tbody td:last-child {
        right: 0;
      }
      tbody td:nth-child(2) {
        border-right: none;
        box-shadow: 2px 0 5px -2px rgba(0, 0, 0, 0.3); /* Right shadow */
      }
      tbody td:last-child {
        box-shadow: -2px 0 5px -2px rgba(0, 0, 0, 0.3); /* Left shadow */
      }
    }
    .pending-ebrc-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .pending-ebrc-tbody {
        .status-td {
          color: $error;
        }
      }
    }
  }
}
