@import '@utils//main.scss';

.open-items-container {
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;
  }
  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .summary-header {
    @include padding(20px);
    border-bottom: 1px solid $primary-border;
    @include flex-item(_, space-between, center);
    .summary-title {
      @include flex-item(_, flex-start, center);
      .filing-head {
        height: auto;
        border-bottom: none;
        .typography-container > h4 {
          font-weight: normal;
          font-size: $font-size-sm;
        }
      }

      .avatar-wrapper {
        .avatar-container {
          .small {
            width: 28px;
            height: 28px;
          }

          .avatar-text {
            background-color: $tertiary;
            border: none;
            color: $white;
            font-size: $font-size-sm;
            font-weight: normal;
          }
        }
      }
    }
    .selection-type {
      @include flex-item(row, flex-start, flex-end, nowrap, 5px);
      color: $text-color;
      .containers {
        font-size: $base-font-size;
        input:checked ~ .checkmark {
          background-color: $success;
        }
        .checkmark {
          top: 0.4px;
          &::after {
            top: 5px;
            left: 5px;
            width: 6px;
            height: 6px;
          }
        }
      }
    }
  }

  .open-items-table-container {
    @include padding(20px 16px);
    .open-items-table {
      @include margin-top(20px);
      width: 100%;
      border-spacing: 0;
      .open-items-tbody {
        .link-td {
          color: $primary;
          text-decoration: underline;
          cursor: pointer;
        }
      }
    }

    .table-search-container {
      @include margin-top(20px);
    }
  }
}

// .manual-linking-sidebar-modal {
//   .modal-body {
//     width: 50%;
//     height: 100vh;
//     left: 75%;
//     .modal-header {
//       @include flex-item(row, space-between);
//       @include padding(30px 30px 0px 30px);
//     }
//     .modal-content {
//       @include margin-top(16px);
//       @include padding(0 30px 30px 30px);
//     }
//   }
// }
