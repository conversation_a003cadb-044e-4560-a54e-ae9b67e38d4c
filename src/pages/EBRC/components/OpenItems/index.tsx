import EximPaper from '@shared/components/EximPaper';
import EximRadioButton from '@shared/components/EximRadioButton';
import {useState} from 'react';

import ManualLinkingSidebar from '../ManualLinkingSidebar';
import IrmDetails from './IrmDetails';
import SbDetails from './SbDetails';
import './index.scss';

export default function OpenItems() {
  const [activeTab, setActiveTab] = useState<'IRM' | 'SB'>('IRM');
  const [isOpenSidebar, setIsOpenSidebar] = useState(false);

  return (
    <div className='open-items-container'>
      <EximPaper>
        <div className='summary-header'>
          <div className='selection-type'>
            <EximRadioButton
              id='irmDetails'
              label='IRM Details'
              value='IRM'
              onChange={(e) => setActiveTab(e.target.value as 'IRM')}
              isSelected={activeTab === 'IRM'}
            />
            <EximRadioButton
              id='sbDetails'
              label='SB Details'
              onChange={(e) => setActiveTab(e.target.value as 'SB')}
              value='SB'
              isSelected={activeTab === 'SB'}
            />
          </div>
        </div>
        <div className='open-items-table-container'>
          {activeTab === 'IRM' ? (
            <IrmDetails setIsOpenSidebar={setIsOpenSidebar} />
          ) : (
            <SbDetails setIsOpenSidebar={setIsOpenSidebar} />
          )}
        </div>
      </EximPaper>

      {isOpenSidebar && (
        <ManualLinkingSidebar
          type={activeTab}
          setIsOpenSidebar={setIsOpenSidebar}
        />
      )}
    </div>
  );
}
