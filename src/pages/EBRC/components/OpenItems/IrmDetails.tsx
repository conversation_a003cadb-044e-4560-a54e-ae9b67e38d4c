import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {IOpenIrmDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOpenIrmList} from '@pages/EBRC/api';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import {
  IRM_DETAILS_LINKING_TABLE_HEADER,
  IRM_DETAILS_SEARCH_DROPDOWN,
} from '@pages/EBRC/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

interface IProps {
  setIsOpenSidebar: (val: boolean) => void;
}

export default function IrmDetails({setIsOpenSidebar}: IProps) {
  const {
    ebrc: {
      iecNumber,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const [irmList, setIrmList] = useState<IOpenIrmDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const getIrmDtls = useCallback(async () => {
    const payload = {
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
      iecCode: iecNumber,
      searchKey,
      sortBy,
      sortingOrder,
      searchValue: debouncedValue,
    };
    const response = await getOpenIrmList(payload, page, +showEntries);
    setIrmList(response?.data?.records);
    setTotalRecords(response?.data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    iecNumber,
    showEntries,
    startPeriod,
    endPeriod,
    sortBy,
    sortingOrder,
    debouncedValue,
  ]);

  const handleLinkSb = (item: IOpenIrmDetails) => {
    dispatch(ebrcActions.setSelectedLinkItem(item));
    setIsOpenSidebar(true);
  };

  useEffect(() => {
    getIrmDtls();
  }, [getIrmDtls]);

  return (
    <>
      <div className='table-title'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          IRM Details
        </EximTypography>
      </div>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={IRM_DETAILS_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='open-items-table'>
        <TableHeader
          mainHeader={IRM_DETAILS_LINKING_TABLE_HEADER(false, true)}
          handleSortBy={handleSortBy}
        />
        {irmList?.length > 0 ? (
          <TableBody className='open-items-tbody'>
            {irmList?.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.irmNumber}</TableCell>
                <TableCell>{item.irmIssueDate}</TableCell>
                <TableCell>{item.remitterName}</TableCell>
                <TableCell>{item.remittanceFCC}</TableCell>
                <TableCell>{item.remittanceFCCAmount?.toFixed(2)}</TableCell>
                <TableCell className='link-td'>
                  <span role='presentation' onClick={() => handleLinkSb(item)}>
                    Link Shipping Bill
                  </span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable
            colSpan={IRM_DETAILS_LINKING_TABLE_HEADER(false, true).length}
          />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={irmList as []}
        renderData={irmList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </>
  );
}
