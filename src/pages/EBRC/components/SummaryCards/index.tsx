import {IEbrcSummaryData} from '@common/interfaces';
import EximSummaryCard from '@shared/components/EximSummaryCard';
import {memo} from 'react';

import './index.scss';

interface IEbrcSummary {
  title: string;
  value: number;
}

interface IContentTypesProps {
  contentData: IEbrcSummary[];
}

function Content({contentData}: IContentTypesProps) {
  return (
    <div className='card-content-container'>
      {contentData?.map(({title, value}, index) => (
        <div key={`content${index + 1}`} className='card-content'>
          <p>{title}</p>
          <p>{value?.toFixed(2)}</p>
        </div>
      ))}
    </div>
  );
}

const irmValues = (
  totalIrmValue: number,
  totalEbrcGenerated: number,
  totalNotConsiderForEbrc: number
) => {
  return [
    {
      title: 'Total IRM Value',
      value: totalIrmValue,
    },
    {
      title: 'Total Value Considered for EBRC',
      value: totalEbrcGenerated,
    },
    {
      title: 'Total Value not Considered for EBRC',
      value: totalNotConsiderForEbrc,
    },
  ];
};

const sbValues = (
  totalShippingBillValue: number,
  totalShippingBillGenerated: number,
  totalNotConsiderForShippingBill: number
) => {
  return [
    {
      title: 'Total Shipping Bill Value',
      value: totalShippingBillValue,
    },
    {
      title: 'Total Value Considered for EBRC',
      value: totalShippingBillGenerated,
    },
    {
      title: 'Total Value not Considered for EBRC',
      value: totalNotConsiderForShippingBill,
    },
  ];
};

function SummaryCards({data}: {data: IEbrcSummaryData}) {
  return (
    <div className='summary-cards-container'>
      <EximSummaryCard
        header='IRM Details'
        content={
          <Content
            contentData={irmValues(
              data.totalIrmValue,
              data?.totalEbrcGenerated,
              data?.totalNotConsiderForEbrc
            )}
          />
        }
        variant='success'
      />
      <EximSummaryCard
        header='Shipping Bill Details'
        content={
          <Content
            contentData={sbValues(
              data?.totalShippingBillValue,
              data?.totalShippingBillGenerated,
              data?.totalNotConsiderForShippingBill
            )}
          />
        }
        variant='information'
      />
    </div>
  );
}

export default memo(SummaryCards);
