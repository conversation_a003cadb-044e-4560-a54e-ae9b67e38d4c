@import '@utils//main.scss';

.summary-cards-container {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  @include margin-bottom(40px);

  .summary-card-wrapper {
    width: 100%;
    .card-header {
      font-size: $font-size-lg;
      font-weight: normal;
    }
  }

  .card-content-container {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    @include padding(25px 16px 32px 16px);

    .card-content {
      @include flex-item(column, center, flex-start, _, 12px);
      font-size: $font-size-sm;
      p:first-child {
        color: $label-color;
      }
    }
  }
}
