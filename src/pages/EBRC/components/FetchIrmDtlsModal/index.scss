@import '@utils//main.scss';

.fetch-irm-modal-container {
  width: 100%;
  .date-input-container {
    @include flex-item(_, space-between, center, _, 16px);
    .input-cont {
      width: 48%;
      .base-date-picker {
        width: 100%;
      }
      .label {
        @include margin-bottom(5px);
        font-size: $font-size-sm;
        color: $label-color;
      }
    }
  }
  .btn-container {
    @include flex-item(_, flex-end, center, _, 16px);
    @include margin-top(40px);
    .button-wrapper {
      min-width: 100px;
      .base-btn {
        height: 32px;
        font-size: $font-size-sm;
        @include padding(7px 16px);
      }
    }
  }
}
