import {AlertStatus, EximHeroDate, ResponseStatus} from '@common/constants';
import {getCurrentDate} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {fetchIrmDtls} from '@pages/EBRC/api';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import {fetchIrmDtlsSchema} from '@pages/EBRC/utils';
import EximButton from '@shared/components/EximButton';
import EximDatePicker from '@shared/components/EximDatePicker';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {useSelector} from 'react-redux';

import './index.scss';

interface IFetchDtlsIrmModal {
  onClose: () => void;
  getIrmStatusDetails: () => void;
}

export default function FetchIrmDtlsModal({
  onClose,
  getIrmStatusDetails,
}: IFetchDtlsIrmModal) {
  const {
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const formik = useFormik({
    initialValues: {fromDate: getCurrentDate(), toDate: getCurrentDate()},
    validationSchema: fetchIrmDtlsSchema,
    onSubmit: async (values) => {
      const payload = {
        startDate: values.fromDate?.split('/').reverse().join('-'),
        endDate: values.toDate?.split('/').reverse().join('-'),
        iecCode: iecNumber,
      };
      const response = (await fetchIrmDtls(payload)) as ICustomAxiosResp;
      if (response?.status?.toString() === ResponseStatus.SUCCESS) {
        dispatch(ebrcActions.setFetchIrmTxnId(response?.data?.['txn-id']));
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
        getIrmStatusDetails(); // Getting the IRM status details after fetching
        onClose(); // closing the modal after fetching
      }
    },
  });
  return (
    <form onSubmit={formik.handleSubmit} className='fetch-irm-modal-container'>
      <div className='date-input-container'>
        <div className='input-cont'>
          <p className='label'>IRM From Date</p>
          <EximDatePicker
            id='fromDate'
            minDate={EximHeroDate.MIN_DATE}
            calendarType='dateCalendar'
            isInvalid={!!formik.errors?.fromDate}
            onChange={(value) => {
              formik.setFieldValue('fromDate', value);
              formik.setFieldValue('toDate', '');
            }}
            defaultValue={formik?.values?.fromDate}
          />
        </div>
        <div className='input-cont'>
          <p className='label'>IRM To Date</p>
          <EximDatePicker
            id='toDate'
            minDate={formik?.values?.fromDate}
            calendarType='dateCalendar'
            isInvalid={!!formik.errors?.toDate}
            errorMessage={formik.errors.toDate}
            onChange={(value) => {
              formik.setFieldValue('toDate', value);
            }}
            defaultValue={formik?.values?.toDate}
          />
        </div>
      </div>
      <span className='btn-container'>
        <EximButton size='small' color='secondary' onClick={onClose}>
          Cancel
        </EximButton>
        <EximButton size='small' type='submit'>
          Search
        </EximButton>
      </span>
    </form>
  );
}
