@import '@utils//main.scss';

.generated-ebrc-container {
  .paper-wrapper-rounded {
    @include margin(0px auto 24px);
    border: none;
  }
  .outlined-paper {
    box-shadow: 0px 3px 6px $box-shadow-color;
  }

  .summary-header {
    @include padding(20px);
    border-bottom: 1px solid $primary-border;
    @include flex-item(_, space-between, center);
    .summary-title {
      @include flex-item(_, flex-start, center);
      .filing-head {
        height: auto;
        border-bottom: none;
        .typography-container > h4 {
          font-weight: normal;
          font-size: $font-size-sm;
        }
      }

      .avatar-wrapper {
        .avatar-container {
          .small {
            width: 28px;
            height: 28px;
          }

          .avatar-text {
            background-color: $tertiary;
            border: none;
            color: $white;
            font-size: $font-size-sm;
            font-weight: normal;
          }
        }
      }
    }
    .btn-container {
      @include flex-item(_, flex-start, center, _, 16px);
      .button-wrapper {
        min-width: 100px;
        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }

  .table-search-container {
    @include margin-top(20px);
  }
  .generated-ebrc-table-container {
    @include padding(20px 16px);
    .generated-ebrc-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .generated-ebrc-tbody {
        .status-td {
          color: $error;
        }
      }
    }
  }
}
