import EmptyTable from '@common/components/EmptyTable';
import FilingHead from '@common/components/FilingHead';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {SupportedFileTypes} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {IGeneratedEbrc} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {ebrcReport, generatedEbrcDtls} from '@pages/EBRC/api';
import {
  GENERATED_EBRC_TABLE_HEADER,
  PENDING_EBRC_SEARCH_DROPDOWN,
} from '@pages/EBRC/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

export default function GeneratedEbrcTable() {
  const {
    ebrc: {
      iecNumber,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [isSelectAll, setIsSelectAll] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [generatedEbrcList, setGeneratedEbrcList] = useState<IGeneratedEbrc[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getGeneratedEbrcData = useCallback(async () => {
    const payload = {
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
      iecCode: iecNumber,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await generatedEbrcDtls(payload, page, +showEntries);
    setGeneratedEbrcList(data?.records || []);
    setTotalRecords(data?.total_records || 0);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, iecNumber, showEntries, debouncedValue, sortBy, sortingOrder]);

  // TODO: Handle below functionalities
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleDownload = async () => {
    const selectedItem = generatedEbrcList
      ?.filter((el) => el.selected)
      ?.map((el) => el.eBRCNumber);
    const response = await ebrcReport(iecNumber, selectedItem);

    downloadFile(
      response?.data['file-data'],
      response?.data['file-name'],
      SupportedFileTypes.PDF
    );
  };

  const handleSingleSelect = (id: string) => {
    const updatedList = generatedEbrcList.map((item: IGeneratedEbrc) =>
      item.id === id ? {...item, selected: !item.selected} : item
    );
    setGeneratedEbrcList(updatedList);

    const selectedItems = updatedList.filter((item) => item.selected).length;
    const isAllSelected = selectedItems === generatedEbrcList.length;
    setIsSelectAll(isAllSelected);
  };

  const handleSelectAll = () => {
    const updatedList = generatedEbrcList.map((item: IGeneratedEbrc) => ({
      ...item,
      selected: !isSelectAll,
    }));
    setGeneratedEbrcList(updatedList);
    setIsSelectAll(!isSelectAll);
  };

  useEffect(() => {
    getGeneratedEbrcData();
  }, [getGeneratedEbrcData]);

  return (
    <div className='generated-ebrc-container'>
      <EximPaper>
        <div className='summary-header'>
          <div className='summary-title'>
            <FilingHead
              filingHead='Generated eBRC'
              onGuideClick={handleGuideClick}
              hasGuide
            />
          </div>
          <div className='btn-container'>
            <EximButton
              size='small'
              color='primary'
              disabled={!generatedEbrcList?.find((el) => el.selected)}
              onClick={handleDownload}>
              Download PDF
            </EximButton>
          </div>
        </div>
        <div className='generated-ebrc-table-container'>
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleShowEntries={handleShowEntries}
            handleSearchQuery={handleSearchQuery}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={PENDING_EBRC_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <table className='generated-ebrc-table'>
            <TableHeader
              mainHeader={GENERATED_EBRC_TABLE_HEADER}
              handleSortBy={handleSortBy}
              onChange={handleSelectAll}
              checked={isSelectAll}
            />
            {generatedEbrcList?.length > 0 ? (
              <TableBody className='generated-ebrc-tbody'>
                {generatedEbrcList?.map((item: IGeneratedEbrc) => (
                  <TableRow key={item.id}>
                    <TableCell className='checkbox-td'>
                      <EximCheckbox
                        id={item.id}
                        color='#2CB544'
                        size='medium'
                        checked={item.selected}
                        onChange={() => handleSingleSelect(item.id)}
                      />
                    </TableCell>
                    <TableCell>{item.requestProcessingStatus}</TableCell>
                    <TableCell>{item.eBRCNumber}</TableCell>
                    <TableCell>{item.eBRCDate}</TableCell>
                    <TableCell>{item.irmNo || '-'}</TableCell>
                    <TableCell>{item.irmDate || '-'}</TableCell>
                    <TableCell>{item.sbCumInvoiceNumber}</TableCell>
                    <TableCell>{item.sbCumInvoiceDate}</TableCell>
                    <TableCell>{item.realisedValueFCC?.toFixed(2)}</TableCell>
                    <TableCell>{item.realizationDt}</TableCell>
                    <TableCell>{item.brcUtilStatus}</TableCell>
                    <TableCell>
                      <TableActions isDownloadIcon />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={GENERATED_EBRC_TABLE_HEADER.length} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={generatedEbrcList as []}
            renderData={generatedEbrcList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}
