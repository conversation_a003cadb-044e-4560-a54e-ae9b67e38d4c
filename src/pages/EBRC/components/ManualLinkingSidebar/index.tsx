import TableHeader from '@common/components/TableHeader';
import {AlertStatus, ResponseStatus} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {manualLinkingIrmAndSb} from '@pages/EBRC/api';
import {
  IRM_DETAILS_LINKING_TABLE_HEADER,
  SB_DETAILS_LINKING_TABLE_HEADER,
} from '@pages/EBRC/utils';
import EximButton from '@shared/components/EximButton';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import IrmDetails from './IrmDetails';
import SbDetails from './SbDetails';
import './index.scss';

interface IProps {
  setIsOpenSidebar: (val: boolean) => void;
  type: 'SB' | 'IRM';
}

export default function ManualLinkingSidebar({setIsOpenSidebar, type}: IProps) {
  const {
    ebrc: {
      iecNumber,
      autoLinkTxnId,
      selectedLinkItem,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const handleLink = async () => {
    setIsOpenSidebar(false);
    const payload = {
      iecCode: iecNumber,
      txnId: autoLinkTxnId,
      startPeriod: startPeriod?.split('-').reverse().join('-'),
      endPeriod: endPeriod?.split('-').reverse().join('-'),
      irmIdList:
        type === 'IRM' ? ([selectedLinkItem?.id] as string[]) : selectedIds,
      sbIdList:
        type === 'SB' ? ([selectedLinkItem?.id] as string[]) : selectedIds,
    };

    const response = await manualLinkingIrmAndSb(payload);
    if (response.status?.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.data?.message,
          alertType: AlertStatus.SUCCESS,
        })
      );
    }
  };

  return (
    <div className='manual-link-bg-container'>
      <div className='manual-link-sidebar-container'>
        <div className='sidebar-header'>
          <EximTypography variant='h4' fontWeight='bold'>
            {type === 'IRM' ? 'IRM Details' : 'Shopping Bill Details'}
          </EximTypography>
          <span role='presentation' onClick={() => setIsOpenSidebar(false)}>
            <CloseIcon width={14} height={14} />
          </span>
        </div>
        <table className='selected-item-table common-table'>
          {type === 'IRM' ? (
            <TableHeader
              mainHeader={IRM_DETAILS_LINKING_TABLE_HEADER(false, false)}
            />
          ) : (
            <TableHeader
              mainHeader={SB_DETAILS_LINKING_TABLE_HEADER(false, false)}
            />
          )}
          <TableBody>
            {type === 'IRM' &&
            selectedLinkItem &&
            'irmNumber' in selectedLinkItem ? (
              <TableRow>
                <TableCell>{selectedLinkItem.irmNumber}</TableCell>
                <TableCell>{selectedLinkItem.irmIssueDate}</TableCell>
                <TableCell>{selectedLinkItem.remitterName}</TableCell>
                <TableCell>{selectedLinkItem.remittanceFCC}</TableCell>
                <TableCell>
                  {selectedLinkItem.remittanceFCCAmount?.toFixed(2)}
                </TableCell>
              </TableRow>
            ) : (
              selectedLinkItem &&
              'shippingBillNo' in selectedLinkItem && (
                <TableRow>
                  <TableCell>{selectedLinkItem.shippingBillNo}</TableCell>
                  <TableCell>{selectedLinkItem.shippingBillDate}</TableCell>
                  <TableCell>{selectedLinkItem.consigneeName}</TableCell>
                  <TableCell>{selectedLinkItem.sbFcCd}</TableCell>
                  <TableCell>{selectedLinkItem.sbFcVal?.toFixed(2)}</TableCell>
                </TableRow>
              )
            )}
          </TableBody>
        </table>

        <div className='sidebar-header'>
          <EximTypography variant='h4' fontWeight='bold'>
            {type === 'SB' ? 'IRM Details' : 'Shipping Bill Details'}
          </EximTypography>
          <EximButton
            size='small'
            color='primary'
            disabled={selectedIds.length === 0}
            onClick={handleLink}>
            Link
          </EximButton>
        </div>
        {type === 'SB' ? (
          <IrmDetails setSelectedIds={setSelectedIds} />
        ) : (
          <SbDetails setSelectedIds={setSelectedIds} />
        )}
      </div>
    </div>
  );
}
