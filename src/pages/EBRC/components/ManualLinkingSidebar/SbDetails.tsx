import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {IOpenSbDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOpenSbList} from '@pages/EBRC/api';
import {
  SB_DETAILS_LINKING_TABLE_HEADER,
  SB_DETAILS_SEARCH_DROPDOWN,
} from '@pages/EBRC/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

interface IProps {
  setSelectedIds: (ids: string[]) => void;
}

export default function SbDetails({setSelectedIds}: IProps) {
  const {
    ebrc: {
      iecNumber,
      irmSbPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const {
    page,
    showEntries,
    searchKey,
    searchValue,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const [sbList, setSbList] = useState<IOpenSbDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isSelectAll, setIsSelectAll] = useState(false);

  const getIrmDtls = useCallback(async () => {
    const payload = {
      startDate: startPeriod?.split('-').reverse().join('-'),
      endDate: endPeriod?.split('-').reverse().join('-'),
      iecCode: iecNumber,
      searchKey,
      sortBy,
      sortingOrder,
      searchValue: debouncedValue,
    };
    const response = await getOpenSbList(payload, page, +showEntries);
    setSbList(response?.data?.records);
    setTotalRecords(response?.data?.total_records);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    showEntries,
    iecNumber,
    startPeriod,
    endPeriod,
    sortBy,
    sortingOrder,
    debouncedValue,
  ]);

  const handleSingleSelect = (id: string) => {
    const updatedList = sbList.map((item: IOpenSbDetails) =>
      item.id === id ? {...item, selected: !item.selected} : item
    );
    setSbList(updatedList);

    const selectedItems = updatedList.filter((item) => item.selected).length;
    const isAllSelected = selectedItems === sbList.length;
    setIsSelectAll(isAllSelected);

    const selectedIds = updatedList
      ?.filter((el) => el.selected)
      .map((el) => el.id);
    setSelectedIds(selectedIds);
  };

  const handleSelectAll = () => {
    const updatedList = sbList.map((item: IOpenSbDetails) => ({
      ...item,
      selected: !isSelectAll,
    }));
    setSbList(updatedList);
    setIsSelectAll(!isSelectAll);

    const selectedIds = updatedList
      ?.filter((el) => el.selected)
      .map((el) => el.id);
    setSelectedIds(selectedIds);
  };

  useEffect(() => {
    getIrmDtls();
  }, [getIrmDtls]);

  return (
    <>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={SB_DETAILS_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='common-table'>
        <TableHeader
          mainHeader={SB_DETAILS_LINKING_TABLE_HEADER(true, false)}
          handleSortBy={handleSortBy}
          onChange={handleSelectAll}
          checked={isSelectAll}
        />
        {sbList?.length > 0 ? (
          <TableBody>
            {sbList?.map((item) => (
              <TableRow key={item.id}>
                <TableCell className='checkbox-td'>
                  <EximCheckbox
                    id={item.id}
                    color='#2CB544'
                    size='medium'
                    checked={item.selected}
                    onChange={() => handleSingleSelect(item.id)}
                  />
                </TableCell>
                <TableCell>{item.shippingBillNo}</TableCell>
                <TableCell>{item.shippingBillDate}</TableCell>
                <TableCell>{item.consigneeName}</TableCell>
                <TableCell>{item.sbFcCd}</TableCell>
                <TableCell>{item.sbFcVal?.toFixed(2)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable
            colSpan={SB_DETAILS_LINKING_TABLE_HEADER(true, false).length}
          />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={sbList as []}
        renderData={sbList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </>
  );
}
