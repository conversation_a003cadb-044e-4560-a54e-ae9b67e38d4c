@import '@utils//main.scss';

.manual-link-bg-container {
  width: 50%;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 101;
  height: 100%;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    background-color: $black;
  }

  .manual-link-sidebar-container {
    width: 50%;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 102;
    height: 100%;
    max-height: 100%;
    overflow-x: scroll;
    @include padding(20px);
    background-color: $white;
    box-shadow: 0px 3px 6px $box-shadow-color;

    .sidebar-header {
      @include flex-item(_, space-between, center);
      & > span {
        cursor: pointer;
      }
      .button-wrapper {
        min-width: 100px;
        .base-btn {
          height: 32px;
          font-size: $font-size-sm;
          @include padding(7px 16px);
        }
      }
    }

    .selected-item-table {
      @include margin(28px 0 36px 0);
    }
    .table-search-container {
      @include margin(24px 0 16px 0);
      .search-div {
        justify-content: flex-end;
      }
    }
  }
}
