import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {IEbrcSbList} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  SB_DETAILS_SEARCH_DROPDOWN,
  SB_DETAILS_TABLE_HEADER,
} from '@pages/EBRC/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {memo} from 'react';

import './index.scss';

interface ISBTable {
  sbList: IEbrcSbList[];
  totalRecords: number;
  page: number;
  searchKey: string;
  searchValue: string;
  showEntries: string;
  handlePageChange: (pageNumber: number | string) => void;
  handleShowEntries: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  handleSearchKey: (value: string) => void;
  handleSearchQuery: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function SBInvoiceTable({
  sbList,
  totalRecords,
  page,
  searchKey,
  searchValue,
  showEntries,
  handlePageChange,
  handleShowEntries,
  handleSearchKey,
  handleSearchQuery,
  handleSortBy,
}: ISBTable) {
  return (
    <div className='sb-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={SB_DETAILS_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='sb-list-table'>
        <TableHeader
          mainHeader={SB_DETAILS_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {sbList?.length > 0 ? (
          <TableBody className='sb-list-tbody'>
            {sbList?.map((item: IEbrcSbList, index: number) => (
              <TableRow key={`${item.invoiceKey}${index + 1}`}>
                <TableCell>{item?.shippingBillNo || '-'}</TableCell>
                <TableCell>{item?.shippingBillDate || '-'}</TableCell>
                <TableCell>{item?.portCode || '-'}</TableCell>
                <TableCell>{item?.billNo || '-'}</TableCell>
                <TableCell>-</TableCell>
                <TableCell>{item?.sbFcVal || '-'}</TableCell>
                <TableCell>{item?.sbFcCd || '-'}</TableCell>
                <TableCell>{item?.sacCode}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={SB_DETAILS_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={sbList as []}
        renderData={sbList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default memo(SBInvoiceTable);
