import {EbrcSummaryCards} from '@common/constants';
import {getCurrentMonthAndYear, getLastMonthAndYear} from '@common/helpers';
import {IOpenIrmDetails, IOpenSbDetails} from '@common/interfaces';
import {createSlice} from '@reduxjs/toolkit';

interface IInitialState {
  panNumber: string;
  iecNumber: string;
  businessName: string;
  fetchIrmTxnId: string;
  invoiceTxnId: string;
  autoLinkTxnId: string;
  invoiceErrorFileId: string;
  invoicesCardActive: boolean;
  isLastTransactionInvalid: boolean;
  invoicesPeriod: {
    startPeriod: string;
    endPeriod: string;
  };
  irmSbPeriod: {
    startPeriod: string;
    endPeriod: string;
  };
  activeLinkSummaryCard: string;
  selectedLinkItem: IOpenIrmDetails | IOpenSbDetails | null;
}

const initialState: IInitialState = {
  panNumber: '',
  iecNumber: 'IEC0001',
  businessName: '',
  fetchIrmTxnId: '',
  invoiceTxnId: '',
  autoLinkTxnId: '',
  invoiceErrorFileId: '',
  invoicesCardActive: true,
  isLastTransactionInvalid: false,
  invoicesPeriod: {
    startPeriod: '',
    endPeriod: '',
  },
  irmSbPeriod: {
    startPeriod: getLastMonthAndYear(),
    endPeriod: getCurrentMonthAndYear(),
  },
  activeLinkSummaryCard: EbrcSummaryCards.OPEN_ITEMS,
  selectedLinkItem: null,
};

const ebrc = createSlice({
  name: 'ebrc',
  initialState,
  reducers: {
    setPanNumber(state, actions) {
      state.panNumber = actions.payload;
    },
    setIecNumber(state, actions) {
      state.iecNumber = actions.payload;
    },
    setBusinessName(state, actions) {
      state.businessName = actions.payload;
    },
    setFetchIrmTxnId(state, actions) {
      state.fetchIrmTxnId = actions.payload;
    },
    setInvoiceTxnId(state, actions) {
      state.invoiceTxnId = actions.payload;
    },
    setAutoLinkTxnId(state, actions) {
      state.autoLinkTxnId = actions.payload;
    },
    setInvoiceErrorFileId(state, actions) {
      state.invoiceErrorFileId = actions.payload;
    },
    setInvoicesCardActive(state, actions) {
      state.invoicesCardActive = actions.payload;
    },
    setInvoicesPeriod(state, actions) {
      state.invoicesPeriod = actions.payload;
    },
    setIsLastTransactionInvalid(state, actions) {
      state.isLastTransactionInvalid = actions.payload;
    },
    setIrmSbPeriod(state, actions) {
      state.irmSbPeriod = actions.payload;
    },
    setActiveLinkSummaryCard(state, actions) {
      state.activeLinkSummaryCard = actions.payload;
    },
    setSelectedLinkItem(state, actions) {
      state.selectedLinkItem = actions.payload;
    },
  },
});

const ebrcReducer = ebrc.reducer;

export const ebrcActions = ebrc.actions;
export default ebrcReducer;
