import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import Helmet from '@common/components/utils/Helmet';
import {HelmetTitle, Path} from '@common/constants';
import SummaryCards from '@pages/DutyDrawback/components/SummaryCards';
import EximDivider from '@shared/components/EximDivider';

import ClaimHistory from './ClaimHistory';
import DataUploadValidate from './DataUploadValidate';
import EximSchemes from './EximSchemes';
import './index.scss';

function DutyDrawbackDashboard() {
  return (
    <>
      <Helmet title={HelmetTitle.DUTY_DRAWBACK} />
      <div className='dd_dashboard-wrapper'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          hasGuide
          leftArrowRoute={Path.DASHBOARD}
          leftArrowText='EXIM Duty Drawback Dashboard'
        />
        <BusinessHeader />

        <EximDivider type='solid' text='Summary' textAlign='left' />
        <SummaryCards />

        <EximDivider
          type='solid'
          text='Data Upload & Validate'
          textAlign='left'
        />
        <DataUploadValidate />

        <EximDivider type='solid' text='Exim Schemes' textAlign='left' />
        <EximSchemes />

        <EximDivider type='solid' text='Claim History' textAlign='left' />
        <ClaimHistory />
      </div>
    </>
  );
}

export default DutyDrawbackDashboard;
