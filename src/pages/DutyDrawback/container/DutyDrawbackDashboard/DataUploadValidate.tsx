import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {
  DDRouteType,
  DDUploadProcessTitle,
  DUTY_DRAWBACK_FILE_TYPE,
  DUTY_DRAWBACK_ROUTE_TYPE,
  Path,
  UploadProcessTitle,
} from '@common/constants';
import {
  formatDataToRender,
  formatDateWithTime,
  getCurrentMonthAndYear,
  getLastMonthAndYear,
} from '@common/helpers';
import {IInputFilesProcessDetails} from '@common/interfaces';
import {getProcessedDetails} from '@pages/DutyDrawback/api';
import EximAvatar from '@shared/components/EximAvatar';
import EximPaper from '@shared/components/EximPaper';
import {CheckIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import {dutyDrawbackActions} from '../../store/reducer';
import './index.scss';

export default function DataUploadValidate() {
  const navigate = useNavigate();
  const [processingStatus, setProcessingStatus] = useState<
    IInputFilesProcessDetails[]
  >([]);
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const getProcessingDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      fileType: DUTY_DRAWBACK_FILE_TYPE.ALL,
      email,
    };
    const {data} = await getProcessedDetails(payload);
    const formatData = formatDataToRender(data);
    setProcessingStatus(formatData);
  }, [email, panNumber]);

  useEffect(() => {
    getProcessingDetails();
  }, [getProcessingDetails]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleUpload = async (type: DDRouteType) => {
    navigate(
      `${Path.DUTY_DRAWBACK}${Path.UPLOAD_PROCESS}/${DUTY_DRAWBACK_ROUTE_TYPE[
        type
      ].toLowerCase()}`
    );
  };

  const handleViewDetails = (
    type: DDRouteType,
    txnId: string,
    status: string
  ) => {
    dispatch(dutyDrawbackActions.setInvoiceTxnId(txnId));
    dispatch(dutyDrawbackActions.setInvoicesCardActive(true));
    dispatch(
      dutyDrawbackActions.setInvoicesPeriod({
        startPeriod: getLastMonthAndYear(),
        endPeriod: getCurrentMonthAndYear(),
      })
    );
    // INFO: Track the last transaction status to pass dynamic header based on the last transaction in the invoices details API
    dispatch(
      dutyDrawbackActions.setIsLastTransactionInvalid(
        status.includes('Invalid')
      )
    );
    navigate(
      `${Path.DUTY_DRAWBACK}${Path.VIEW_INVOICES}/${DUTY_DRAWBACK_ROUTE_TYPE[
        type
      ].toLowerCase()}`
    );
  };

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('In Progress'))
      return <span className='process'>{value}</span>;
    if (
      value?.includes('Failed') ||
      value?.includes('Invalid') ||
      value?.includes('Discarded')
    )
      return <span className='error'>{value}</span>;
    if (value?.includes('Completed'))
      return <span className='success'>{value}</span>;
    return null;
  };

  return (
    <div className='data-upload-step-container'>
      <EximPaper>
        <FilingHead
          filingHead='Input Files'
          onGuideClick={handleGuideClick}
          hasGuide
        />
        <div className='filing-step-container'>
          {processingStatus.map((item, index) => (
            <FilingStep
              key={`uploadAndView${index + 1}`}
              stepIcon={
                <span>
                  {item['processing-status']?.includes('Completed') ? (
                    <CheckIcon fill='#2CB445' />
                  ) : (
                    <EximAvatar
                      rounded
                      firstName={`${index + 1}`}
                      lastName=''
                      alt='number'
                      size='small'
                    />
                  )}
                </span>
              }
              statusIcon={
                item['processing-status']?.includes('In Progress') ? (
                  <span onClick={getProcessingDetails} role='presentation'>
                    <SolidSync />
                  </span>
                ) : null
              }
              stepEndIcon={
                <span className='info-icons'>
                  <InfoCircular fill='#4379B5' width={13} height={13} />
                </span>
              }
              filingName={
                UploadProcessTitle[item['file-type'] as DDUploadProcessTitle]
              }
              btnName='Upload'
              btnDisable={false}
              onBtnClick={() => handleUpload(item['file-type'] as DDRouteType)}
              secondBtnName='View'
              secondBtnDisable={!item['file-name']}
              status={
                item['processing-status']
                  ? getShowStatus(item['processing-status'])
                  : null
              }
              recentUpdate={
                item?.['file-name']
                  ? `Last Uploaded on ${formatDateWithTime(
                      item?.['last-updated-date'],
                      false
                    )}`
                  : ''
              }
              updatedBy={
                (item?.['last-updated-by'] &&
                  `By ${item?.['last-updated-by']}`) ||
                ''
              }
              onSecondBtnClick={() =>
                handleViewDetails(
                  item['file-type'] as DDRouteType,
                  item['txn-id'],
                  item['processing-status']
                )
              }
            />
          ))}
        </div>
      </EximPaper>
    </div>
  );
}
