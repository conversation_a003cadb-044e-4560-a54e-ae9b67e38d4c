import FilingHead from '@common/components/FilingHead';
import FilingStep from '@common/components/FilingStep';
import {EximSchemesType, Path, ResponseStatus} from '@common/constants';
import {formatDateWithTime} from '@common/helpers';
import {ISchemeStatus} from '@common/interfaces';
import {
  dbkClaimProcessingStatus,
  discardDbkClaim,
} from '@pages/DutyDrawback/api';
import ClaimModalContent from '@pages/DutyDrawback/components/ClaimModal';
import {
  IDbkClaimDetails,
  dutyDrawbackActions,
  initialClaimDetails,
} from '@pages/DutyDrawback/store/reducer';
import EximAvatar from '@shared/components/EximAvatar';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CheckIcon, CloseIcon, InfoCircular, SolidSync} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

const {
  SB_PROD_SELECT,
  BOE_ITEM_SELECT,
  BOM_REVIEW_TYPE,
  DBK_CAL,
  DBK_CLAIM_HISTORY,
} = EximSchemesType;

const {
  DUTY_DRAWBACK,
  DBK_CLAIM,
  SB_SUMMARY,
  BOM_REVIEW,
  BOE_SUMMARY,
  CALCULATION,
} = Path;

export default function EximSchemes() {
  const navigate = useNavigate();
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      getClaimHistory,
      panNumber,
      dbkClaim: {
        isClaimExist,
        claimTxnId,
        sbSelectTxnId,
        claimCalculationStatus,
      },
    },
  } = useSelector((state: RootState) => state);

  const [dbkClaimDetails, setDbkClaimDetails] =
    useState<IDbkClaimDetails>(initialClaimDetails);
  const [productSelectSB, setProductSelectSB] = useState<ISchemeStatus>();
  const [itemSelectBOE, setItemSelectBOE] = useState<ISchemeStatus>();
  const [bomReview, setBomReview] = useState<ISchemeStatus>();
  const [dbkCalculation, setDbkCalculation] = useState<ISchemeStatus>();
  const [newClaimIsOpen, setNewClaimIsOpen] = useState<boolean>(false);
  const [isDiscardClaimModal, setIsDiscardClaimModal] =
    useState<boolean>(false);

  const isDbkCalcInProgress =
    dbkCalculation?.['txn-status'] === 'DBK Calculation In Progress';

  const getDbkClaimProcessStatus = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
    };
    const {data} = await dbkClaimProcessingStatus(payload);
    const isTxnDetails = data?.['txn-details']?.length > 0;
    const productSelect = data?.['txn-details']?.find(
      (el: ISchemeStatus) => el['sub-txn-type'] === DBK_CLAIM_HISTORY
    );

    const dbkClaim = {
      claimTitle: data?.['claim-name'] as string,
      startPeriod: data?.['start-period'] as string,
      endPeriod: data?.['end-period'] as string,
      applicantType: data?.['applicant-type'] as string,
      claimTxnId: productSelect?.['claim-txn-id'] as string,
      isClaimExist: true,
      sbSelectTxnId,
      claimCalculationStatus,
    };
    setDbkClaimDetails(dbkClaim);
    // dispatch the event to set the claim is exist or not
    if (
      isTxnDetails &&
      !productSelect?.['txn-status']?.includes('Freezed') &&
      !productSelect?.['txn-status']?.includes('Discarded')
    ) {
      // INFO: Adding the existing claim details
      dispatch(dutyDrawbackActions.setDbkClaim(dbkClaim));
    } else {
      dispatch(dutyDrawbackActions.setDbkClaim(initialClaimDetails));
    }

    setProductSelectSB(
      data?.['txn-details']?.find(
        (el: ISchemeStatus) => el['sub-txn-type'] === SB_PROD_SELECT
      )
    );
    setItemSelectBOE(
      data?.['txn-details']?.find(
        (el: ISchemeStatus) => el['sub-txn-type'] === BOE_ITEM_SELECT
      )
    );
    setBomReview(
      data?.['txn-details']?.find(
        (el: ISchemeStatus) => el['sub-txn-type'] === BOM_REVIEW_TYPE
      )
    );
    setDbkCalculation(
      data?.['txn-details']?.find(
        (el: ISchemeStatus) => el['sub-txn-type'] === DBK_CAL
      )
    );
  }, [panNumber, email, sbSelectTxnId, claimCalculationStatus]);

  // TODO: Handle below functionalities as per filing steps
  const handleGuideClick = () => {
    /** empty function */
  };

  const handleViewDetails = (type: string) => {
    if (dbkCalculation && type === CALCULATION) {
      dispatch(
        dutyDrawbackActions.setClaimCalculationStatus(
          dbkCalculation['txn-status']
        )
      );
    }
    if (productSelectSB?.['txn-status']?.includes('Freezed')) {
      // INFO: Adding the existing claim details
      dispatch(dutyDrawbackActions.setDbkClaim(dbkClaimDetails));
    }
    navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${type}`);
  };

  const handleDiscardClaim = async () => {
    const payload = {
      pan: panNumber,
      claimTxnId,
      email,
    };
    const data = await discardDbkClaim(payload);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(dutyDrawbackActions.setDbkClaim(initialClaimDetails));
      getDbkClaimProcessStatus();
      getClaimHistory(); // Updated the claim history data
    }
    setIsDiscardClaimModal(false);
  };

  const getShowStatus = (value: string | undefined) => {
    if (value === undefined) return null;
    if (value?.includes('Recompute'))
      return <span className='process'>Recomputing</span>;
    if (value?.includes('Generation In Progress'))
      return <span className='process'>File Generation In Progress</span>;
    if (value?.includes('In Progress'))
      return <span className='process'>In Progress</span>;
    if (value?.includes('Discard Failed'))
      return <span className='error'>Discard Failed</span>;
    if (value?.includes('Failed')) return <span className='error'>Failed</span>;
    if (value?.includes('Discarded'))
      return <span className='error'>Discarded</span>;
    if (value?.includes('Freezed'))
      return <span className='error'>Freezed</span>;
    if (value?.includes('Qualification'))
      return <span className='error'>Qualification Error</span>;
    if (value?.includes('Completed'))
      return <span className='success'>Completed</span>;
    if (value?.includes('Freeze In Process'))
      return <span className='process'>Freezing Process</span>;

    return null;
  };

  const btnDisableStatus = (value: string, type = 'OTHER') => {
    if (type === DBK_CAL) {
      const disabledStatusArray = [
        'Discarded',
        'DBK Calculation In Progress',
        'DBK Claim Freeze In Process',
      ];
      if (disabledStatusArray?.includes(value) || value === 'In Progress') {
        return true;
      }
      return false;
    }
    const disabledStatusArray = [
      'Discarded',
      'DBK Claim Freezed',
      'DBK Claim Freeze In Process',
      'DBK Claim Generation In Progress',
    ];
    if (disabledStatusArray?.includes(value)) return true;
    return false;
  };

  useEffect(() => {
    getDbkClaimProcessStatus();
  }, [getDbkClaimProcessStatus]);

  // INFO: Updating the claim history data while refreshing the claim status
  useEffect(() => {
    const refreshClaimHistoryData = [
      'Discarded',
      'DBK Calculation In Progress',
      'DBK Calculation Completed',
      'DBK Claim Freeze In Process',
      'DBK Claim Freezed',
    ];
    const shouldClaimHistoryDataUpdate = refreshClaimHistoryData?.includes(
      dbkCalculation?.['txn-status'] || ''
    );
    if (shouldClaimHistoryDataUpdate) {
      getClaimHistory(); // Updated the claim history data
    }
  }, [dbkCalculation, getClaimHistory]);

  return (
    <div className='data-upload-step-container'>
      <EximPaper>
        <FilingHead
          filingHead='Duty Drawback (DBK)'
          onGuideClick={handleGuideClick}
          hasGuide>
          {!isClaimExist ? (
            <EximButton onClick={() => setNewClaimIsOpen(true)}>
              New Claim
            </EximButton>
          ) : (
            <EximButton
              onClick={() => setIsDiscardClaimModal(true)}
              color='tertiary'
              className='discard-claim-btn'
              disabled={
                dbkCalculation?.['txn-status']?.includes('Freeze In Process') ||
                dbkCalculation?.['txn-status'] === 'In Progress' ||
                isDbkCalcInProgress
              }>
              Discard Claim
            </EximButton>
          )}
        </FilingHead>
        <div className='filing-step-container'>
          <FilingStep
            stepIcon={
              <span>
                {(productSelectSB &&
                  productSelectSB?.['txn-status']?.includes('Completed')) ||
                productSelectSB?.['txn-status']?.includes(
                  'Freeze In Process'
                ) ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='1'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='Shipping Bill (SB) & Product Selection'
            btnName='View Details'
            btnDisable={
              !productSelectSB ||
              btnDisableStatus(productSelectSB['txn-status']) ||
              isDbkCalcInProgress
            }
            onBtnClick={() => handleViewDetails(SB_SUMMARY)}
            status={
              productSelectSB
                ? getShowStatus(productSelectSB['txn-status'])
                : null
            }
            statusIcon={null}
            recentUpdate={
              productSelectSB?.['last-updated-date']
                ? `Last Processed on ${formatDateWithTime(
                    productSelectSB?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              productSelectSB
                ? `By ${productSelectSB?.['last-updated-by'] ?? '-'}`
                : ''
            }
          />
          <FilingStep
            stepIcon={
              <span>
                {(bomReview &&
                  bomReview?.['txn-status']?.includes('Completed')) ||
                bomReview?.['txn-status']?.includes('Freeze In Process') ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='2'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='Bill of Materials (BOM) Review'
            btnName='View Details'
            btnDisable={
              !bomReview ||
              btnDisableStatus(bomReview['txn-status']) ||
              isDbkCalcInProgress
            }
            onBtnClick={() => handleViewDetails(BOM_REVIEW)}
            status={bomReview ? getShowStatus(bomReview['txn-status']) : null}
            statusIcon={null}
            recentUpdate={
              bomReview?.['last-updated-date']
                ? `Last Processed on ${formatDateWithTime(
                    bomReview?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              bomReview ? `By ${bomReview?.['last-updated-by'] ?? '-'}` : ''
            }
          />

          <FilingStep
            stepIcon={
              <span>
                {(itemSelectBOE &&
                  itemSelectBOE?.['txn-status']?.includes('Completed')) ||
                itemSelectBOE?.['txn-status']?.includes('Freeze In Process') ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='3'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='Bill of Entry (BOE) Review'
            btnName='View Details'
            btnDisable={
              !itemSelectBOE ||
              btnDisableStatus(itemSelectBOE['txn-status']) ||
              isDbkCalcInProgress
            }
            onBtnClick={() => handleViewDetails(BOE_SUMMARY)}
            status={
              itemSelectBOE ? getShowStatus(itemSelectBOE['txn-status']) : null
            }
            statusIcon={null}
            recentUpdate={
              itemSelectBOE?.['last-updated-date']
                ? `Last Processed on ${formatDateWithTime(
                    itemSelectBOE?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              itemSelectBOE
                ? `By ${itemSelectBOE?.['last-updated-by'] ?? '-'}`
                : ''
            }
          />

          <FilingStep
            stepIcon={
              <span>
                {(dbkCalculation &&
                  dbkCalculation?.['txn-status']?.includes('Completed')) ||
                dbkCalculation?.['txn-status']?.includes(
                  'Freeze In Process'
                ) ? (
                  <CheckIcon fill='#2CB445' />
                ) : (
                  <EximAvatar
                    rounded
                    firstName='4'
                    lastName=''
                    alt='number'
                    size='small'
                  />
                )}
              </span>
            }
            statusIcon={
              (dbkCalculation &&
                dbkCalculation?.['txn-status']?.includes('In Progress')) ||
              dbkCalculation?.['txn-status']?.includes('Freeze In Process') ? (
                <span
                  onClick={getDbkClaimProcessStatus}
                  role='presentation'
                  className='status-icons'>
                  <SolidSync />
                </span>
              ) : null
            }
            stepEndIcon={
              <span className='info-icons'>
                <InfoCircular fill='#4379B5' width={13} height={13} />
              </span>
            }
            filingName='DBK Calculation'
            btnName='View Details'
            btnDisable={
              !dbkCalculation ||
              btnDisableStatus(dbkCalculation['txn-status'], DBK_CAL)
            }
            onBtnClick={() => handleViewDetails(CALCULATION)}
            status={
              dbkCalculation
                ? getShowStatus(dbkCalculation['txn-status'])
                : null
            }
            recentUpdate={
              dbkCalculation?.['last-updated-date']
                ? `Last Processed on ${formatDateWithTime(
                    dbkCalculation?.['last-updated-date'],
                    false
                  )}`
                : ''
            }
            updatedBy={
              dbkCalculation
                ? `By ${dbkCalculation?.['last-updated-by'] ?? '-'}`
                : ''
            }
          />
        </div>
      </EximPaper>

      {/* Create DBK Claim Modal */}
      <div className='dbk-claim-modal'>
        <EximModal
          isOpen={newClaimIsOpen}
          onClose={() => setNewClaimIsOpen(false)}
          onOutSideClickClose={() => setNewClaimIsOpen(false)}
          content={
            <ClaimModalContent onClose={() => setNewClaimIsOpen(false)} />
          }
          footer={false}
          header={
            <EximTypography
              classNames='claim-modal-title'
              fontWeight='semi-bold'>
              Create New Claim
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* Discard DBK Claim Modal */}
      <div className='discard-claim-modal'>
        <EximModal
          isOpen={isDiscardClaimModal}
          onClose={() => setIsDiscardClaimModal(false)}
          onOutSideClickClose={() => setIsDiscardClaimModal(false)}
          content={
            <div className='discard-modal-container'>
              <EximTypography variant='h4'>
                Are you sure, you want to Discard Ongoing Claim?
              </EximTypography>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsDiscardClaimModal(false)}>
                  No
                </EximButton>
                <EximButton size='small' onClick={handleDiscardClaim}>
                  Yes
                </EximButton>
              </span>
            </div>
          }
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Discard Current Claim
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>
    </div>
  );
}
