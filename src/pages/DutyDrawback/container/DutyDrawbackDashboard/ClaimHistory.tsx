import <PERSON>lip<PERSON><PERSON><PERSON><PERSON> from '@common/components/EllipsisChecker';
import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path} from '@common/constants';
import {formatDateWithTime, getClaimPeriod} from '@common/helpers';
import {IClaimHistoryData} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getClaimHistoryData} from '@pages/DutyDrawback/api';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import {
  CLAIM_HISTORY_TABLE_HEADER,
  CLAIM_HISTORY_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function ClaimHistory() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [claimNameToShow, setClaimTextToShow] = useState('');
  const [isOpenTextModal, setIsOpenTextModal] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [claimHistoryData, setClaimHistoryData] = useState<IClaimHistoryData[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleViewClaimDetails = (
    id: string,
    startPeriod: string,
    endPeriod: string
  ) => {
    navigate(`${Path.DUTY_DRAWBACK}${Path.CLAIM_DETAILS}/${id}`, {
      state: {startPeriod, endPeriod},
    });
  };

  const getClaimHistory = useCallback(async () => {
    const headers = {
      pan: panNumber,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getClaimHistoryData(headers, page, +showEntries);
    setClaimHistoryData(data?.records);
    setTotalRecords(data?.['total-records']);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    page,
    panNumber,
    email,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getClaimHistory();
    dispatch(dutyDrawbackActions.setGetClaimHistoryFunc(getClaimHistory));
  }, [getClaimHistory]);

  return (
    <div className='claim-history-container'>
      <EximPaper>
        <div className='claim-history-table-container'>
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleShowEntries={handleShowEntries}
            handleSearchQuery={handleSearchQuery}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={CLAIM_HISTORY_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <table className='claim-history-table'>
            <TableHeader
              mainHeader={CLAIM_HISTORY_TABLE_HEADER}
              handleSortBy={handleSortBy}
            />
            {claimHistoryData?.length > 0 ? (
              <TableBody className='claim-history-tbody'>
                {claimHistoryData?.map((item: IClaimHistoryData) => (
                  <TableRow key={item['claim-txn-id']}>
                    <TableCell>
                      <EllipsisChecker
                        text={item['claim-name']}
                        handleViewMore={() => {
                          setIsOpenTextModal(true);
                          setClaimTextToShow(item['claim-name']);
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      {formatDateWithTime(item['claim-date'], false)}
                    </TableCell>
                    <TableCell>
                      {`${getClaimPeriod(item['start-period'])} -
                        ${getClaimPeriod(item['end-period'])}`}
                    </TableCell>
                    <TableCell> {item['total-sb-considered']} </TableCell>
                    <TableCell> {item['total-prod-qty-considered']} </TableCell>
                    <TableCell>
                      {item['total-boe-items-qty-considered']}
                    </TableCell>
                    <TableCell>
                      {item['total-boe-items-qty-considered']}
                    </TableCell>
                    <TableCell> {item['total-duty-amt']} </TableCell>
                    <TableCell>{item?.status}</TableCell>
                    <TableCell>
                      <TableActions
                        isViewIcon
                        viewToolTipText={
                          item?.status?.includes('Freezed')
                            ? 'View Details'
                            : ''
                        }
                        isViewIconDisabled={!item?.status?.includes('Freezed')}
                        handleView={() =>
                          handleViewClaimDetails(
                            item['claim-txn-id'],
                            item['start-period'],
                            item['end-period']
                          )
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={10} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={claimHistoryData as []}
            renderData={claimHistoryData as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>

      {/* View Claim Text Modal */}
      <div className='view-claim-text-modal'>
        <EximModal
          isOpen={isOpenTextModal}
          onClose={() => setIsOpenTextModal(false)}
          onOutSideClickClose={() => setIsOpenTextModal(false)}
          content={<p>{claimNameToShow}</p>}
          footer={false}
          header={<EximTypography fontWeight='bold'>Claim Name</EximTypography>}
          closeIcon={<CloseIcon width={15} height={15} />}
        />
      </div>
    </div>
  );
}
export default ClaimHistory;
