import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import RecordsCard from '@common/components/RecordsCard';
import {
  AlertStatus,
  DDFileType,
  DDUploadProcessNavTitle,
  DUTY_DRAWBACK_FILE_TYPE,
  DUTY_DRAWBACK_ROUTE_TYPE,
  EximHeroDate,
  ResponseStatus,
  SupportedFileTypes,
  UploadProcessNavTitle,
} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {
  deleteInvoices,
  downloadFileData,
  invoicesSummary,
} from '@pages/DutyDrawback/api';
import BOMTable from '@pages/DutyDrawback/components/BOMTable';
import DBKRateTable from '@pages/DutyDrawback/components/DBKRateTable';
import ErrorRecordsUpload from '@pages/DutyDrawback/components/ErrorRecordsUpload';
import ExportDomesticSaleTable from '@pages/DutyDrawback/components/ExportDomesticSaleTable';
import ImportPurchaseTable from '@pages/DutyDrawback/components/ImportPurchaseTable';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ErrorIcon, SuccessIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useParams} from 'react-router-dom';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

interface IInvoiceCount {
  'valid-inv-count': number;
  'invalid-inv-count': number;
}

// It is only for this page
const CARD_TYPE = {
  SUCCESS: 'success',
  ERROR: 'error',
};

function ViewInvoices() {
  const {fileType} = useParams();
  const fileTypeCaps = fileType?.toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      invoiceTxnId,
      invoicesCardActive,
      isLastTransactionInvalid,
      invoicesPeriod: {startPeriod, endPeriod},
    },
  } = useSelector((state: RootState) => state);

  const [invoiceCount, setInvoiceCount] = useState<IInvoiceCount>();
  const [isDeleteAllClicked, setIsDeleteAllClicked] = useState(false);
  // Below two state to store the refId of error records for deleting
  const [exportRefIdList, setExportRefIdList] = useState<string[]>([]);
  const [importRefIdList, setImportRefIdList] = useState<string[]>([]);
  const [bomRefIdList, setBomRefIdList] = useState<string[]>([]);

  const isBOMorDBK =
    fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE ||
    fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE;

  const handleViewRecord = (type: string) => {
    dispatch(
      dutyDrawbackActions.setInvoicesCardActive(type === CARD_TYPE.SUCCESS)
    );
    // Resetting the values on change the records type
    setImportRefIdList([]);
    setExportRefIdList([]);
  };

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    dispatch(
      dutyDrawbackActions.setInvoicesPeriod({
        startPeriod: startDate,
        endPeriod: endDate,
      })
    );
  };

  const handleDownloadInvoices = async () => {
    const payload = {
      email,
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      pan: panNumber,
      reportType: DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType],
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else if (fileTypeCaps !== DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE) {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await downloadFileData(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const getInvoicesSummary = useCallback(async () => {
    // INFO: No need to call summary API for custom dbk
    if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE) {
      return;
    }
    const payload = {
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      pan: panNumber,
      email,
      fileType: DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType],
    };
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else if (fileTypeCaps !== DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE) {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await invoicesSummary(payload);
    setInvoiceCount(data);
  }, [
    panNumber,
    email,
    fileTypeCaps,
    invoiceTxnId,
    startPeriod,
    endPeriod,
    isLastTransactionInvalid,
  ]);

  const handleDeleteAllFailedFiles = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      fileType: DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType],
    };
    if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.EXPORT_FILE) {
      const response = (await deleteInvoices(
        payload,
        exportRefIdList.length === 0,
        exportRefIdList
      )) as ICustomAxiosResp;
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        setExportRefIdList([]); // Empty the array after deleting the data
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
    }
    if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.IMPORT_FILE) {
      const response = (await deleteInvoices(
        payload,
        importRefIdList.length === 0,
        importRefIdList
      )) as ICustomAxiosResp;
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        setImportRefIdList([]); // Empty the array after deleting the data
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
    }
    if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE) {
      const response = (await deleteInvoices(
        payload,
        false,
        bomRefIdList
      )) as ICustomAxiosResp;
      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: response?.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
      }
    }
    // Updating the data after deleting the all invalid records
    setIsDeleteAllClicked((prev) => !prev);
    getInvoicesSummary();
  };

  useEffect(() => {
    getInvoicesSummary();
  }, [getInvoicesSummary]);

  const disableDeleteAllBtn = () => {
    if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.IMPORT_FILE) {
      if (
        (importRefIdList.length === 0 &&
          invoiceCount?.['valid-inv-count'] !== 0) ||
        invoiceCount?.['invalid-inv-count'] === 0
      )
        return true;
    } else if (fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.EXPORT_FILE) {
      if (
        (exportRefIdList.length === 0 &&
          invoiceCount?.['valid-inv-count'] !== 0) ||
        invoiceCount?.['invalid-inv-count'] === 0
      )
        return true;
    }
    return false;
  };

  return (
    <div className='invoice-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText={`${
          UploadProcessNavTitle[fileTypeCaps as DDUploadProcessNavTitle]
        } ${
          fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE
            ? 'Data'
            : ''
        }`}
      />
      <BusinessHeader>
        <div className='btn-children-container'>
          {!isBOMorDBK ? (
            <>
              <EximTypography>Period</EximTypography>
              <EximMonthRangePicker
                id='invoiceDatePicker'
                minDate={EximHeroDate.MIN_MONTH}
                onSelect={handleSelectPeriod}
                defaultStartDate={startPeriod.split('-').join('/')}
                defaultEndDate={endPeriod.split('-').join('/')}
                disabled={isLastTransactionInvalid}
              />
            </>
          ) : (
            <BusinessSubHeader
              fileType={DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType]}
            />
          )}
        </div>
      </BusinessHeader>
      {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.EXPORT_FILE ||
      fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.IMPORT_FILE ? (
        <BusinessSubHeader
          fileType={DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType]}
        />
      ) : null}
      {fileTypeCaps !== DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE && (
        <div className='records-card-container'>
          <RecordsCard
            title='Valid records'
            icon={<SuccessIcon />}
            recordType='success'
            handleViewRecord={() => handleViewRecord(CARD_TYPE.SUCCESS)}
            isActive={invoicesCardActive}
            recordCount={invoiceCount?.['valid-inv-count'] || 0}>
            {fileTypeCaps !== DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE && (
              <EximButton
                onClick={handleDownloadInvoices}
                disabled={
                  !invoicesCardActive || invoiceCount?.['valid-inv-count'] === 0
                }
                size='small'
                color='secondary'
                className='export-excel-btn'
                dataTestId='export-excel-btn'>
                Download
              </EximButton>
            )}
          </RecordsCard>
          <RecordsCard
            title='Error records'
            icon={<ErrorIcon />}
            recordType='error'
            handleViewRecord={() => handleViewRecord(CARD_TYPE.ERROR)}
            isActive={!invoicesCardActive}
            recordCount={invoiceCount?.['invalid-inv-count'] || 0}>
            <EximButton
              onClick={handleDeleteAllFailedFiles}
              disabled={invoicesCardActive || disableDeleteAllBtn()}
              size='small'
              color='secondary'
              className='delete-all-error-records-btn'
              dataTestId='delete-all-error-records-btn'>
              Delete All
            </EximButton>
          </RecordsCard>
        </div>
      )}
      <div className='view-invoices-container'>
        <EximPaper>
          {!invoicesCardActive &&
          fileTypeCaps !== DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE ? (
            <ErrorRecordsUpload
              fileType={DUTY_DRAWBACK_FILE_TYPE[fileTypeCaps as DDFileType]}
              disabledButton={invoiceCount?.['invalid-inv-count'] === 0}
            />
          ) : null}
          {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.EXPORT_FILE ? (
            <ExportDomesticSaleTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setExportRefIdList={setExportRefIdList}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.IMPORT_FILE ? (
            <ImportPurchaseTable
              isDeleteAllClicked={isDeleteAllClicked}
              getInvoicesSummary={getInvoicesSummary}
              setImportRefIdList={setImportRefIdList}
              isValidRecord={invoicesCardActive}
              startPeriod={startPeriod}
              endPeriod={endPeriod}
            />
          ) : null}
          {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.BOM_FILE ? (
            <BOMTable
              isDeleteAllClicked={isDeleteAllClicked}
              setBomRefIdList={setBomRefIdList}
              isValidRecord={invoicesCardActive}
              getInvoicesSummary={getInvoicesSummary}
            />
          ) : null}
          {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.CUSTOM_DBK_RATE_FILE ? (
            <DBKRateTable />
          ) : null}
        </EximPaper>
      </div>
    </div>
  );
}

export default ViewInvoices;
