import {Path} from '@common/constants';
import {discardTransaction} from '@pages/DutyDrawback/api';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IBusinessSubHeaderProps {
  fileType: string;
}

function BusinessSubHeader({fileType}: IBusinessSubHeaderProps) {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const handleDiscardTransaction = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      fileType,
    };
    await discardTransaction(payload);
    navigate(Path.DUTY_DRAWBACK);
  };

  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='btn-container'>
          <EximButton
            size='small'
            color='secondary'
            dataTestId='discard-txn'
            onClick={handleDiscardTransaction}>
            Discard
          </EximButton>
          <EximButton
            size='small'
            dataTestId='done-txn'
            onClick={() => navigate(Path.DUTY_DRAWBACK)}>
            Done
          </EximButton>
        </div>
      </EximPaper>
    </div>
  );
}

export default BusinessSubHeader;
