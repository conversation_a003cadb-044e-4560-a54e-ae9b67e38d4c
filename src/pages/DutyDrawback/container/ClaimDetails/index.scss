@import '@utils/main.scss';

.claim-details-container {
  @include padding(0 20px);
  .paper-wrapper-rounded {
    @include margin(0);
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }

  .subscription-header-left {
    color: $text-color;
  }

  .breadcrumb-container {
    @include margin(0);
    @include padding(0);
  }

  // Business Header Date Picker Style
  .business-container {
    .business-header-actions {
      @include flex-item(_, space-between, center, _, 12px);
      .base-date-picker {
        width: auto;
      }
    }
  }

  // Business Sub-Header Style
  .sub-header-container {
    @include margin-top(2px);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
    .sub-header-wrapper {
      @include flex-item(_, space-between, center);
      @include padding(10px 16px);
      .claim-details {
        @include flex-item(_, center, center, _, 12px);
        span {
          @include flex-item(_, center, center, _, 4px);
        }
      }

      .btn-container {
        @include flex-item(_, flex-end, center, _, 20px);
        display: flex;
        align-items: center;

        .base-btn {
          font-size: $font-size-sm;
          @include padding(7px 20px);
        }
      }
    }
  }

  // Claim Details Tables Style
  .claim-details-table-container {
    @include margin(15px 0 32px 0);
    .paper-wrapper-rounded {
      box-shadow: 0px 3px 6px $box-shadow-color;
      border: none;
      @include margin(0);
    }
  }

  // DBK Edit Claim Details & Download Reports Modal Style
  .download-reports-modal,
  .dbk-claim-details-modal {
    letter-spacing: 0.5px;
    .modal-body {
      width: 371px;
      .modal-header {
        @include flex-item(row, space-between);
        @include padding(22px 22px 0px 22px);
      }
      .modal-content {
        @include padding(0 22px 22px 22px);
      }
      .reports-modal-title,
      .claim-modal-title {
        font-size: $font-size-xxl;
        color: $secondary-text;
      }
    }
  }
  .download-reports-modal {
    .modal-body {
      width: 611px;
      @include padding(2px 10px);
    }
  }
  .dbk-claim-details-modal {
    .edit-claim-input {
      width: 100%;
      @include margin-top(12px);
      textarea {
        width: 100%;
        outline: none;
        border: 1px solid $input-border;
        border-radius: 5px;
        @include padding(7px 10px);
        resize: none;
      }
      .btn-container {
        @include flex-item(_, flex-end, center, _, 16px);
        @include margin-top(20px);
        .button-wrapper {
          min-width: 100px;
          .base-btn {
            height: 32px;
            font-size: $font-size-sm;
            @include padding(7px 16px);
          }
        }
      }
    }
  }
}
