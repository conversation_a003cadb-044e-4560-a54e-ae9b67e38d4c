import {AlertStatus, ResponseStatus} from '@common/constants';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {
  getDbkFinalSummary,
  updateDbkClaimStatus,
} from '@pages/DutyDrawback/api';
import DownloadFilesModalContent from '@pages/DutyDrawback/components/DownloadFilesModalContent';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IBusinessHeaderProps {
  claimId: string;
  isAnyNotQualified: boolean;
}

function BusinessSubHeader({claimId, isAnyNotQualified}: IBusinessHeaderProps) {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [claimTitle, setClaimTitle] = useState('');
  const [claimStatus, setClaimStatus] = useState('');
  const [newClaimStatus, setNewClaimStatus] = useState('');
  const [applicantType, setApplicantType] = useState('');
  const [isOpenEditClaim, setIsOpenEditClaim] = useState<boolean>(false);
  const [isOpenDownloadReports, setIsOpenDownloadReports] =
    useState<boolean>(false);

  // INFO: Calling API to update and show the claim status on Business Header
  const getDbkClaimDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId: claimId,
    };
    const {data} = await getDbkFinalSummary(payload, 1, 5);
    setClaimTitle(data?.['claim-name']);
    setClaimStatus(data?.status);
    setNewClaimStatus(data?.status);
    setApplicantType(data?.['applicant-type']);
  }, [email, panNumber, claimId]);

  const handleSaveClaimStatus = async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId: claimId,
    };
    const response = (await updateDbkClaimStatus(
      payload,
      newClaimStatus
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setIsOpenEditClaim(false);
      dispatch(
        alertActions.setAlertMsg({
          code: response.statusCode,
          message: response.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      // INFO: Calling function to update the status on UI
      getDbkClaimDetails();
    }
  };

  useEffect(() => {
    getDbkClaimDetails();
  }, [getDbkClaimDetails]);

  return (
    <div className='sub-header-container'>
      <EximPaper>
        <div className='sub-header-wrapper'>
          <div className='claim-details'>
            <EximTypography variant='h5' fontWeight='bold'>
              {claimTitle}
            </EximTypography>
            <span>|</span>
            <span>
              <EximTypography variant='h5' fontWeight='bold'>
                Applicant Type
              </EximTypography>
              <EximTypography variant='h5'> - {applicantType}</EximTypography>
            </span>
            <span>|</span>
            <span>
              <EximTypography variant='h5' fontWeight='bold'>
                Claim Status:
              </EximTypography>
              <EximTypography variant='h5'>{claimStatus}</EximTypography>
            </span>
          </div>
          <div className='btn-container'>
            <EximButton
              size='small'
              color='secondary'
              dataTestId='edit-claim'
              onClick={() => setIsOpenEditClaim(true)}>
              Edit Claim Status
            </EximButton>
            <EximButton
              size='small'
              color='secondary'
              dataTestId='download-report'
              onClick={() => setIsOpenDownloadReports(true)}>
              Download Report
            </EximButton>
          </div>
        </div>
      </EximPaper>

      {/* Edit Claim Details Modal */}
      <div className='dbk-claim-details-modal'>
        <EximModal
          isOpen={isOpenEditClaim}
          onClose={() => setIsOpenEditClaim(false)}
          onOutSideClickClose={() => setIsOpenEditClaim(false)}
          content={
            <div className='edit-claim-input'>
              <textarea
                name='claim-status'
                placeholder='Enter Status'
                value={newClaimStatus}
                onChange={(event) => setNewClaimStatus(event.target.value)}
                maxLength={64}
                rows={3}
              />
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => {
                    setIsOpenEditClaim(false);
                    setNewClaimStatus(claimStatus);
                  }}>
                  No
                </EximButton>
                <EximButton size='small' onClick={handleSaveClaimStatus}>
                  Save
                </EximButton>
              </span>
            </div>
          }
          footer={false}
          header={
            <EximTypography
              classNames='claim-modal-title'
              fontWeight='semi-bold'>
              Edit Claim Status
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>
      {/* Download Reports Modal */}
      <div className='download-reports-modal'>
        <EximModal
          isOpen={isOpenDownloadReports}
          onClose={() => setIsOpenDownloadReports(false)}
          onOutSideClickClose={() => setIsOpenDownloadReports(false)}
          content={
            <DownloadFilesModalContent
              claimId={claimId}
              isQualificationError={isAnyNotQualified}
            />
          }
          footer={false}
          header={
            <EximTypography
              classNames='reports-modal-title'
              fontWeight='semi-bold'>
              Download Report
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>
    </div>
  );
}

export default BusinessSubHeader;
