import BusinessHeader from '@common/components/BusinessHeader';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {historyBreadcrumbs} from '@common/components/NavigationSubHeader/config';
import Helmet from '@common/components/utils/Helmet';
import {EximHeroDate, HelmetTitle} from '@common/constants';
import ClaimDetailsDocTable from '@pages/DutyDrawback/components/ClaimDetailsDocTable';
import ClaimDetailsTable from '@pages/DutyDrawback/components/ClaimDetailsTable';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximTypography from '@shared/components/EximTypography';
import {useState} from 'react';
import {useLocation, useParams} from 'react-router';

import BusinessSubHeader from './BusinessSubHeader';
import './index.scss';

function ClaimDetails() {
  const {claimId} = useParams();
  const location = useLocation();
  const {startPeriod, endPeriod} = (location.state ?? {}) as {
    startPeriod: string;
    endPeriod: string;
  };

  const [isAnyNotQualified, setIsAnyNotQualified] = useState(false);

  // TODO: Need to implemented
  const handleGuideClick = () => {
    /* */
  };

  return (
    <>
      <Helmet title={HelmetTitle.CLAIM_DETAILS} />
      <div className='claim-details-container'>
        <NavigationSubHeader
          hasLeftArrow
          hasTitle
          leftArrowRoute='#'
          leftArrowText={HelmetTitle.CLAIM_DETAILS}
          breadCrumbData={historyBreadcrumbs}
          handleGuideClick={handleGuideClick}
          isNavigate
          hasGuide
        />
        <BusinessHeader>
          <>
            <EximTypography>Export Period</EximTypography>
            <EximMonthRangePicker
              id='claimDetailsDatePicker'
              minDate={EximHeroDate.MIN_MONTH}
              onSelect={() => undefined}
              defaultStartDate={startPeriod?.split('-').join('/')}
              defaultEndDate={endPeriod?.split('-').join('/')}
              disabled
            />
          </>
        </BusinessHeader>
        <BusinessSubHeader
          claimId={claimId || ''}
          isAnyNotQualified={isAnyNotQualified}
        />
        <div className='claim-details-table-container'>
          <ClaimDetailsTable
            claimId={claimId || ''}
            setIsAnyNotQualified={setIsAnyNotQualified}
          />
          <ClaimDetailsDocTable claimId={claimId || ''} />
        </div>
      </div>
    </>
  );
}
export default ClaimDetails;
