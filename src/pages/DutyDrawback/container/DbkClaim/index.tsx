import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {dbkClaimBreadcrumbs} from '@common/components/NavigationSubHeader/config';
import {DbkClaimSteps, HelmetTitle, Path} from '@common/constants';
import BOMReview from '@pages/DutyDrawback/components/BOMReview';
import DBKCalculation from '@pages/DutyDrawback/components/DBKCalculation';
import DbkClaimHeader from '@pages/DutyDrawback/components/DbkClaimHeader';
import ShippingBillsDetails from '@pages/DutyDrawback/components/ShippingBillsDetails';
import BoeAndItemSelectionSummary from '@pages/DutyDrawback/container/BoeAndItemSelectionSummary';
import SbAndLocalSelectionSummary from '@pages/DutyDrawback/container/SbAndLocalSelectionSummary';
import {useCallback, useEffect} from 'react';
import {useNavigate, useParams} from 'react-router';

import './index.scss';

function DbkClaim() {
  const {claimStep} = useParams(); // Extracting the claim step
  const navigate = useNavigate();

  const getClaimStep = useCallback(() => {
    switch (claimStep) {
      case DbkClaimSteps.STEP_1_1:
        return <ShippingBillsDetails />;
      // TODO: Below step is not needed for now
      // case DbkClaimSteps.STEP_1_2:
      //   return <ProductSelectionDetails />;
      case DbkClaimSteps.STEP_1_3:
        return <SbAndLocalSelectionSummary />;
      case DbkClaimSteps.STEP_2:
        return <BOMReview />;
      // TODO: Below step is not needed for now
      // case DbkClaimSteps.STEP_3_1:
      // return <DbkClaimBOE />;
      // case DbkClaimSteps.STEP_3_2:
      // return <ItemSelectionBOE />;
      case DbkClaimSteps.STEP_3:
        return <BoeAndItemSelectionSummary />;
      case DbkClaimSteps.STEP_4:
        return <DBKCalculation />;
      default:
        return null;
    }
  }, [claimStep]);

  useEffect(() => {
    if (
      claimStep &&
      !Object.values(DbkClaimSteps)?.includes(
        claimStep as unknown as DbkClaimSteps
      )
    ) {
      navigate(`${Path.PAGE_NOT_FOUND}`);
    }
  }, [claimStep, navigate]);

  return (
    <div className='dbk-claim-wrapper'>
      {/* Navbar */}
      <NavigationSubHeader
        hasLeftArrow
        hasTitle
        hasGuide
        isNavigate
        leftArrowRoute={Path.DUTY_DRAWBACK}
        leftArrowText={HelmetTitle.DUTY_DRAWBACK}
        breadCrumbData={dbkClaimBreadcrumbs}
      />

      {/* Date period section */}
      <DbkClaimHeader isEditBtn={claimStep === DbkClaimSteps.STEP_1_1} />

      {/* Render the Step based on the param */}
      {getClaimStep()}
    </div>
  );
}

export default DbkClaim;
