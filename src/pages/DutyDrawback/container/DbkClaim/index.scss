@import '@utils/main.scss';

.dbk-claim-wrapper {
  @include padding(0 20px);

  .breadcrumb-container {
    width: unset;
    @include margin(0);
    @include padding(0);

    .breadcrumb-content {
      .breadcrumb-separator {
        color: $text-color;
      }
    }
  }

  .dbk-claim-header-wrapper {
    .paper-wrapper-rounded {
      @include margin(0);
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  .dbk-claim-modal {
    letter-spacing: 0.5px;
    .modal-body {
      width: 348px;
      .modal-header {
        @include flex-item(row, space-between);
        @include padding(30px 30px 0px 30px);
      }
      .modal-content {
        padding-top: 0px;
        @include padding(0 30px 30px 30px);
      }
      .claim-modal-title {
        font-size: $font-size-xxl;
        color: $secondary-text;
      }
    }
  }
}
