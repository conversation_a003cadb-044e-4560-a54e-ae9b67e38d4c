import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount, formatDate} from '@common/helpers';
import {IDbkBoeDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getBoeList} from '@pages/DutyDrawback/api';
import {
  DBK_CLAIM_BOE_TABLE_HEADER,
  DBK_CLAIM_BOE_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';

function SelectedBoeItemsList() {
  const tableHeads = useMemo(() => DBK_CLAIM_BOE_TABLE_HEADER(true), []);
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber: pan,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [boeListData, setBoeListData] = useState<IDbkBoeDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const headers = {
        claimTxnId,
        pan,
        email,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const response = await getBoeList(headers, page, +showEntries);
      setBoeListData(response.data['boe-list']);
      setTotalRecords(response.data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    claimTxnId,
    email,
    pan,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='boe-summary-table-wrapper'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Bill of Entries List
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={DBK_CLAIM_BOE_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='dbk-boe-list-table'>
        <TableHeader mainHeader={tableHeads} handleSortBy={handleSortBy} />
        {boeListData?.length > 0 ? (
          <TableBody>
            {boeListData?.map((item, index) => (
              <TableRow key={`BOE${index + 1}`}>
                <TableCell>{item['boe-no']}</TableCell>
                <TableCell>{formatDate(item['boe-date'])}</TableCell>
                <TableCell>{formatAmount(item['item-count'])}</TableCell>
                <TableCell>
                  {formatAmount(item['total-item-qty-available'])}
                </TableCell>
                <TableCell>
                  {formatAmount(item['total-accessable-val'])}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={tableHeads.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={boeListData as []}
        renderData={boeListData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedBoeItemsList;
