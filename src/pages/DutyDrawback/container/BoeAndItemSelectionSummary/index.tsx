import Stripe from '@common/components/Stripe';
import {
  DBK_CLAIM_REPORT_TYPE,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {downloadFileData, saveBoeItemSummary} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState, dispatch} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import OverallSelectionSummary from './OverallSelectionSummary';
import SelectedBoeItemsList from './SelectedBoeItemList';
import SelectedItems from './SelectedItems';
import './index.scss';

function BoeAndItemSelectionSummary() {
  const navigate = useNavigate();
  const {DBK_CLAIM, DUTY_DRAWBACK, CALCULATION} = Path;

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber: pan,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const handleNextBtn = async () => {
    const headers = {
      pan,
      email,
      claimTxnId,
    };
    const data = await saveBoeItemSummary(headers);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(dutyDrawbackActions.setClaimCalculationStatus(''));
      navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${CALCULATION}`);
    }
  };

  const handleDownload = async () => {
    const headers = {
      pan,
      email,
      txnId: claimTxnId,
      reportType: DBK_CLAIM_REPORT_TYPE.BOE_SELECTION_REPORT,
    };
    const {data} = await downloadFileData(headers);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  return (
    <div className='boe-summary'>
      <DbkClaimSubHeader step='3' subTitle='Bill of Entries - Summary'>
        <div className='buttons'>
          <EximButton onClick={handleDownload} color='secondary'>
            Download
          </EximButton>
          <EximButton onClick={handleNextBtn}>Save & Next</EximButton>
        </div>
      </DbkClaimSubHeader>
      <Stripe
        content='This page displays the Summary details for Bill of Entries'
        variant='info'
      />
      <OverallSelectionSummary />
      <EximPaper>
        <SelectedBoeItemsList />
        <SelectedItems />
      </EximPaper>
    </div>
  );
}

export default BoeAndItemSelectionSummary;
