import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount} from '@common/helpers';
import {IBoeItemSelection, IDbkClaimItemList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getBoeItemList} from '@pages/DutyDrawback/api';
import {
  ITEM_SELECTION_BOE_SUMMERY_TABLE_HEADER,
  ITEM_SELECTION_BOE_SUMMERY_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

function SelectedItems() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber: pan,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [boeItemList, setBoeItemList] = useState<IDbkClaimItemList[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const payload = {
        pan,
        email,
        claimTxnId,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const {data} = await getBoeItemList(payload, page, +showEntries);
      setBoeItemList(data['boe-item-list']);
      setTotalRecords(data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    pan,
    email,
    claimTxnId,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='dbk-claim-boe-summary-container'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Items List
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={ITEM_SELECTION_BOE_SUMMERY_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='boe-item-table'>
        <TableHeader
          mainHeader={ITEM_SELECTION_BOE_SUMMERY_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {boeItemList?.length > 0 ? (
          <TableBody>
            {boeItemList?.map((item: IBoeItemSelection, index: number) => (
              <TableRow key={`itemSelected${index + 1}`}>
                <TableCell>{item['boe-no']}</TableCell>
                <TableCell>{item['item-code']}</TableCell>
                <TableCell>{item['item-desc']}</TableCell>
                <TableCell>{formatAmount(item['total-value'])}</TableCell>
                <TableCell>
                  {formatAmount(item['total-purchased-qty'])}
                </TableCell>
                {/* <TableCell>
                  {formatAmount(item['total-available-qty'])}
                </TableCell>
                <TableCell>{formatAmount(item['qty-considered'])}</TableCell> */}
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable
            colSpan={ITEM_SELECTION_BOE_SUMMERY_TABLE_HEADER.length}
          />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={boeItemList as []}
        renderData={boeItemList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedItems;
