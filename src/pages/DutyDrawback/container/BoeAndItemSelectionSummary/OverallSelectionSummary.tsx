import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IDbkClaimOverallSummary} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOverAllSummary} from '@pages/DutyDrawback/api';
import {BOE_OVERALL_SELECTION_SUMMARY_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function OverallSelectionSummary() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber: pan,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [overallSummary, setOverallSummary] = useState<
    IDbkClaimOverallSummary[]
  >([]);

  useEffect(() => {
    (async () => {
      const headers = {
        claimTxnId,
        pan,
        email,
      };
      const response = await getOverAllSummary(headers);
      setOverallSummary([
        {
          particulars: 'Total Available',
          billOfEntries: response.data['total-boe-available'].toString(),
          item: response.data['total-boe-items-available'].toString(),
        },
      ]);
    })();
  }, [claimTxnId, email, pan]);

  return (
    <EximPaper>
      <div className='boe-ps-table-wrapper'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          Overall Selection Summary
        </EximTypography>
        <table className='boe-product-selection'>
          <TableHeader
            mainHeader={BOE_OVERALL_SELECTION_SUMMARY_TABLE_HEADER}
          />
          {overallSummary?.length > 0 ? (
            <TableBody>
              {overallSummary?.map((item) => (
                <TableRow key={`${item.particulars}`}>
                  <TableCell>{item.particulars}</TableCell>
                  <TableCell>
                    {formatAmount(item.billOfEntries || '')}
                  </TableCell>
                  <TableCell>{formatAmount(item.item || '')}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable
              colSpan={BOE_OVERALL_SELECTION_SUMMARY_TABLE_HEADER.length}
            />
          )}
        </table>
      </div>
    </EximPaper>
  );
}

export default OverallSelectionSummary;
