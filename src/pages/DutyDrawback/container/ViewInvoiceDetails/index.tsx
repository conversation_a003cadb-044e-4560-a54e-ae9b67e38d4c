import NavigationSubHeader from '@common/components/NavigationSubHeader';
import {DUTY_DRAWBACK_ROUTE_TYPE} from '@common/constants';
import ExportDomesticViewDetails from '@pages/DutyDrawback/components/ExportDomesticViewDetails';
import ImportDomesticViewDetails from '@pages/DutyDrawback/components/ImportDomesticViewDetails';
import {useLocation, useParams} from 'react-router-dom';

import './index.scss';

function ViewInvoiceDetails() {
  const {fileType} = useParams();
  const fileTypeCaps = fileType?.toUpperCase();
  const location = useLocation();
  const {isViewValidRecord, isEditable} = (location.state ?? {}) as {
    isViewValidRecord: boolean;
    isEditable: boolean;
  };

  return (
    <div className='view-details-container'>
      <NavigationSubHeader
        hasTitle
        leftArrowRoute='#'
        hasLeftArrow
        isNavigate
        leftArrowText='View Details'
      />

      <div className='view-details-component'>
        {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.EXPORT_FILE ? (
          <ExportDomesticViewDetails
            isEditable={isEditable}
            isViewValidRecord={isViewValidRecord}
          />
        ) : null}
        {fileTypeCaps === DUTY_DRAWBACK_ROUTE_TYPE.IMPORT_FILE ? (
          <ImportDomesticViewDetails
            isEditable={isEditable}
            isViewValidRecord={isViewValidRecord}
          />
        ) : null}
      </div>
    </div>
  );
}

export default ViewInvoiceDetails;
