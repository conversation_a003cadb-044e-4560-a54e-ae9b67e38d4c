import Stripe from '@common/components/Stripe';
import {
  DBK_CLAIM_REPORT_TYPE,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {
  downloadFileData,
  saveSbSelectionSummary,
} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import OverallSelectionSummary from './OverallSelectionSummary';
import SelectedProductsList from './SelectedProductList';
import SelectedShippingBills from './SelectedShippingBills';
import './index.scss';

function SbAndLocalSelectionSummary() {
  const navigate = useNavigate();
  const {DBK_CLAIM, DUTY_DRAWBACK, BOM_REVIEW, SB} = Path;

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const handleNextBtn = async () => {
    const headers = {
      pan: panNumber,
      email,
      claimTxnId,
    };
    const data = await saveSbSelectionSummary(headers);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${BOM_REVIEW}`);
    }
  };

  const handleEditBtn = () => {
    navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${SB}`);
  };

  const handleDownload = async () => {
    const headers = {
      pan: panNumber,
      email,
      txnId: claimTxnId,
      reportType: DBK_CLAIM_REPORT_TYPE.SB_SELECTION_REPORT,
    };
    const {data} = await downloadFileData(headers);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  return (
    <div className='sb-ps-summary'>
      <DbkClaimSubHeader
        step='1.2'
        subTitle='Shipping Bill (SB) & Local Sales Selection - Summary'>
        <div className='buttons'>
          <EximButton
            onClick={handleEditBtn}
            color='secondary'
            className='edit-btn'>
            Edit
          </EximButton>
          <EximButton onClick={handleDownload} color='secondary'>
            Download
          </EximButton>
          <EximButton onClick={handleNextBtn}>Save & Next</EximButton>
        </div>
      </DbkClaimSubHeader>
      <Stripe
        content={`This page displays the Summary details for the Shipping Bills, it's Products and Local Sales`}
        variant='info'
      />
      <OverallSelectionSummary />
      <EximPaper>
        <SelectedShippingBills />
        <SelectedProductsList />
        {/* TODO: Below table not needed for now */}
        {/* <SelectedLocalSales /> */}
      </EximPaper>
    </div>
  );
}

export default SbAndLocalSelectionSummary;
