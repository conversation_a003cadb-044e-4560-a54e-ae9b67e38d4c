import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount} from '@common/helpers';
import {IDbkClaimProductList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getDbkClaimProductList} from '@pages/DutyDrawback/api';
import {
  PRODUCT_SELECTION_TABLE_HEADER,
  PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function SelectedProductsList() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState(0);
  const [sbProductList, setSbProductList] = useState<IDbkClaimProductList[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const headers = {
        claimTxnId,
        pan: panNumber,
        email,
        selected: true,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const response = await getDbkClaimProductList(
        headers,
        page,
        +showEntries
      );
      setSbProductList(response.data['sb-prod-list']);
      setTotalRecords(response.data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    claimTxnId,
    email,
    panNumber,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='sb-ps-table-wrapper'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Products List
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='sb-product-selection'>
        <TableHeader
          mainHeader={PRODUCT_SELECTION_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {sbProductList?.length > 0 ? (
          <TableBody>
            {sbProductList?.map((item, index) => (
              <TableRow key={`productSelection${index + 1}`}>
                <TableCell>{item['sb-prod-code']}</TableCell>
                <TableCell>{item['sb-inv-no'] || '-'}</TableCell>
                <TableCell>{item['sb-prod-desc']}</TableCell>
                <TableCell>{formatAmount(item['fob-val'])}</TableCell>
                <TableCell>
                  {formatAmount(item['total-qty-exported'])}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={PRODUCT_SELECTION_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={sbProductList as []}
        renderData={sbProductList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedProductsList;
