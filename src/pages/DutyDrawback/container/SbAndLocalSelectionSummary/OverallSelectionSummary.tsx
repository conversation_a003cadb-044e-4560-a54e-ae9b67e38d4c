import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IDbkClaimOverallSummary} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getOverAllSummary} from '@pages/DutyDrawback/api';
import {SB_OVERALL_SELECTION_SUMMARY} from '@pages/DutyDrawback/utils';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

function OverallSelectionSummary() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [overallSummary, setOverallSummary] = useState<
    IDbkClaimOverallSummary[]
  >([]);

  useEffect(() => {
    (async () => {
      const headers = {
        claimTxnId,
        pan: panNumber,
        email,
      };
      const response = await getOverAllSummary(headers);
      setOverallSummary([
        {
          particulars: 'Total Available',
          shippingBills: response.data['total-sb-available'].toString(),
          product: response.data['total-prod-available'].toString(),
        },
        {
          particulars: 'Considered for DBK',
          shippingBills: response.data['total-sb-considered'].toString(),
          product: response.data['total-prod-considered'].toString(),
        },
      ]);
    })();
  }, [claimTxnId, email, panNumber]);
  return (
    <EximPaper>
      <div className='sb-ps-table-wrapper'>
        <EximTypography variant='h3' fontWeight='semi-bold'>
          Overall Selection Summary
        </EximTypography>
        <table className='sb-product-selection'>
          <TableHeader mainHeader={SB_OVERALL_SELECTION_SUMMARY} />
          {overallSummary?.length > 0 ? (
            <TableBody>
              {overallSummary?.map((item) => (
                <TableRow key={`${item.particulars}`}>
                  <TableCell>{item.particulars}</TableCell>
                  <TableCell>
                    {formatAmount(
                      item.shippingBills || item.billOfEntries || ''
                    )}
                  </TableCell>
                  <TableCell>
                    {formatAmount(item.product || item.item || '')}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable colSpan={SB_OVERALL_SELECTION_SUMMARY.length} />
          )}
        </table>
      </div>
    </EximPaper>
  );
}

export default OverallSelectionSummary;
