@import '@utils/main.scss';

.sb-ps-summary {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    @include margin(0);
    border: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    .table-search-container {
      @include margin-top(20px);
    }
  }
  .sb-ps-table-wrapper {
    @include padding(20px);
    @include margin-top(20px);
    color: $text-color;
    &:last-child {
      @include margin-bottom(20px);
    }
  }

  .dbk-sub-header {
    .action-buttons-wrapper {
      .buttons {
        display: flex;
        gap: 20px;
        .button-wrapper {
          width: 131px;
          .contained[class^='secondary'] {
            color: $tertiary;
          }
        }
        .edit-btn {
          width: 86px;
        }
      }
    }
  }

  .sb-product-selection,
  .local-sales-selection {
    @include margin-top(16px);
    width: 100%;
    border-spacing: 0;
  }
}
