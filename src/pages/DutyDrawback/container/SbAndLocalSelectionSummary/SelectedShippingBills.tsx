import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {applicantTypes} from '@common/constants';
import {ISbListTableDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {getDbkClaimSbList} from '@pages/DutyDrawback/api';
import ShippingBillsTable from '@pages/DutyDrawback/components/ShippingBillsTable';
import {SB_DETAILS_TABLE_SEARCH_DROPDOWN} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {RootState} from '@store';
import {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';

function SelectedShippingBills() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId, applicantType},
    },
  } = useSelector((state: RootState) => state);

  const tableSearchDropdown = useMemo(
    () =>
      SB_DETAILS_TABLE_SEARCH_DROPDOWN(
        applicantType === applicantTypes.EXPORTER
      ),
    [applicantType]
  );

  const [sbList, setSbList] = useState<ISbListTableDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  useEffect(() => {
    (async () => {
      const headers = {
        claimTxnId,
        pan: panNumber,
        email,
        selected: true,
        searchKey,
        searchValue: debouncedValue,
        sortBy,
        sortingOrder,
      };
      const response = await getDbkClaimSbList(headers, page, +showEntries);
      setSbList(response.data['sb-list']);
      setTotalRecords(response.data['total-records']);
    })();
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    claimTxnId,
    email,
    panNumber,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  return (
    <div className='sb-ps-table-wrapper'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Selected Shipping Bills
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={tableSearchDropdown}
        />
      </TableSearchFilter>
      <ShippingBillsTable data={sbList} handleSortBy={handleSortBy} />
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={sbList as []}
        renderData={sbList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedShippingBills;
