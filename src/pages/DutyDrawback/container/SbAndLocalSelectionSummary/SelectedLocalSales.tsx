import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount} from '@common/helpers';
import {IDbkClaimProductList} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {LOCAL_SALES_SELECTED_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximTypography from '@shared/components/EximTypography';
import {ChangeEvent, useState} from 'react';

function SelectedLocalSales() {
  const [page, setPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [localSalesList, setLocalSalesList] = useState<IDbkClaimProductList[]>(
    []
  );
  const [totalRecords, setTotalRecords] = useState(0);

  const handlePageChange = (pageNumber: string | number) => {
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  return (
    <div className='sb-ps-table-wrapper'>
      <EximTypography variant='h3' fontWeight='semi-bold'>
        Selected Local Sales
      </EximTypography>
      <TableSearchFilter
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}
      />
      <table className='local-sales-selection'>
        <TableHeader mainHeader={LOCAL_SALES_SELECTED_TABLE_HEADER} />
        {localSalesList?.length > 0 ? (
          <TableBody>
            {localSalesList?.map((item, index) => (
              <TableRow key={`localSalesSelection${index + 1}`}>
                <TableCell>{item['sb-prod-code']}</TableCell>
                <TableCell>{item['sb-prod-desc']}</TableCell>
                <TableCell>{formatAmount(item['fob-val'])}</TableCell>
                <TableCell>
                  {formatAmount(item['total-qty-exported'])}
                </TableCell>
                <TableCell>
                  {formatAmount(item['total-available-qty'])}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={LOCAL_SALES_SELECTED_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchQuery}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={[]}
        renderData={localSalesList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default SelectedLocalSales;
