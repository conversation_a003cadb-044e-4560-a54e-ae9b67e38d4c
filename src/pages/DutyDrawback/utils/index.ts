export const UPLOAD_DATA_TABLE_HEADER = [
  {title: 'File Name', width: '35%'},
  {title: 'Last Upload', width: '20%'},
  {title: 'Upload By', width: '20%'},
  {title: 'Browse Excel File', width: '15%'},
];

export const UPLOAD_HISTORY_TABLE_HEADER = [
  {title: 'File Name', width: '40%', sortingKey: 'file-name'},
  {title: 'Last Upload', width: '20%', sortingKey: 'last-updated-date'},
  {title: 'Upload By', width: '20%'},
  {title: 'Status', width: '20%'},
];

export const UPLOAD_HISTORY_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'file-name', label: 'File Name'},
  {id: '2', value: 'last-updated-date', label: 'Last Upload'},
];

export const EXPORT_DOMESTIC_SALES_TABLE_HEADER = (isCheckbox: boolean) => {
  const tableHeader = [
    {title: 'Invoice No.', sortingKey: 'sb-inv-no'},
    {title: 'Invoice Date', sortingKey: 'sb-inv-date'},
    {title: 'Shipping Bill No.', sortingKey: 'sb-no'},
    {title: 'Shipping Bill Date', sortingKey: 'sb-date'},
    {title: 'FOB Value as per Invoice Rs.', sortingKey: 'total-fob-value'},
    {title: 'Invoice Value'},
    {title: 'Action', width: '10%'},
  ];
  if (isCheckbox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};

export const EXPORT_DOMESTIC_SALES_SEARCH_DROPDOWN = [
  {id: '1', value: 'sb-inv-no', label: 'Invoice No'},
  {id: '2', value: 'sb-inv-date', label: 'Invoice Date'},
  {id: '3', value: 'sb-no', label: 'Shipping Bill No.'},
  {id: '4', value: 'sb-date', label: 'Shipping Bill Date'},
  {id: '5', value: 'total-fob-value', label: 'FOB Value as per Invoice Rs.'},
];

export const IMPORT_DOMESTIC_PURCHASE_TABLE_HEADER = (isCheckbox: boolean) => {
  const tableHeader = [
    {title: 'Bill of Entry No.', sortingKey: 'boe-no'},
    {title: 'Bill of Entry Date', sortingKey: 'boe-date'},
    {title: 'No of Items'},
    {title: 'Total Duty'},
    {title: 'Name of Customs House'},
    {
      title: 'Assessable Value as per BOE(Rs.)',
      sortingKey: 'total-accessible-val',
    },
    {title: 'Action', width: '10%'},
  ];
  if (isCheckbox) {
    tableHeader.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeader;
};

export const IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'boe-no', label: 'Bill of Entry No.'},
  {id: '2', value: 'boe-date', label: 'Bill of Entry Date'},
  {
    id: '3',
    value: 'total-accessible-val',
    label: 'Assessable Value as per BOE(Rs.)',
  },
];

export const IMPORT_VIEW_DETAILS_TABLE_HEADER = [
  {title: 'Sr. No', width: '5%'},
  {title: 'Item Code', sortingKey: 'item-code', width: '10%'},
  {
    title: 'Description and Technical Characteristics',
    width: '15%',
    sortingKey: 'item-desc',
  },
  {title: 'HSN', width: '8%'},
  {title: 'Quantity Imported', width: '12%', sortingKey: 'total-purchased-qty'},
  {title: 'Unit of Measurement', width: '12%'},
  {title: 'Assessable Value as per BOE(Rs.)', width: '15%'},
  {title: 'BCD rate', width: '8%'},
  {title: 'Customs Cess rate', width: '11%'},
];

export const VIEW_IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'item-code', label: 'Item Code'},
  {id: '2', value: 'item-desc', label: 'Description'},
  {id: '3', value: 'total-purchased-qty', label: 'Quantity Imported'},
  // {id: '4', value: 'total-available-qty', label: 'Quantity Imported'},
];

export const BOM_LIST_TABLE_HEADER = [
  {title: 'Product code', width: '20%', sortingKey: 'prod-code'},
  {
    title: 'Description & Technical Characteristics',
    width: '40%',
  },
  {title: 'Total Items', width: '10%'},
  {title: 'BOM Version', width: '14%', sortingKey: 'bom-version'},
  {title: 'Action', width: '10%'},
];

export const BOM_LIST_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod-code', label: 'Product Code'},
  {id: '2', value: 'bom-version', label: 'BOM Version'},
];

export const DBK_RATE_LIST_TABLE_HEADER = [
  {title: 'Product code', width: '20%', sortingKey: 'prod-code'},
  {title: 'Product Description', width: 'auto'},
  {title: 'DBK Rate', width: '15%'},
];

export const DBK_RATE_LIST_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod-code', label: 'Product Code'},
];

export const SB_DETAILS_TABLE_HEADER = (
  isCheckbox: boolean,
  isExporter: boolean
) => {
  const tableHeads = [
    {
      title: 'SB / Inv No.',
      width: '15%',
      sortingKey: isExporter ? 'sb-no' : 'sb-inv-no',
    },
    {
      title: 'SB / Inv Date',
      width: '15%',
      sortingKey: isExporter ? 'sb-date' : 'sb-inv-date',
    },
    {title: 'LEO Date', width: '15%', sortingKey: 'leo-date'},
    {title: 'Total Products', width: '30%'},
    {title: 'FOB / Total value', width: '30%', sortingKey: 'total-fob-value'},
  ];
  if (isCheckbox) {
    tableHeads.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeads;
};

export const SB_DETAILS_TABLE_SEARCH_DROPDOWN = (isExporter: boolean) => {
  return [
    {id: '1', value: isExporter ? 'sb-no' : 'sb-inv-no', label: 'SB / Inv No.'},
    {
      id: '2',
      value: isExporter ? 'sb-date' : 'sb-inv-date',
      label: 'SB / Inv Date',
    },
    {id: '3', value: 'leo-date', label: 'LEO Date'},
    {id: '4', value: 'total-fob-value', label: 'FOB / Total value'},
  ];
};

export const BOM_REVIEW_TABLE_HEADER = [
  {title: 'Product Code', width: 'auto', sortingKey: 'prod-code'},
  {title: 'Description & Technical Characteristics', width: 'auto'},
  {title: 'Total Items', width: 'auto'},
  {title: 'BOM Version', width: 'auto', sortingKey: 'bom-version'},
  {title: 'Action', width: '10%'},
];

export const BOM_REVIEW_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'prod-code', label: 'Product Code'},
  {id: '2', value: 'bom-version', label: 'BOM Version'},
];

export const MISSING_BOM_REVIEW_TABLE_HEADER = [
  {title: 'Sr No.', width: 'auto'},
  {title: 'Product Code', width: 'auto'},
  {title: 'Product Details', width: 'auto'},
];

export const BOM_REVIEW_VIEW_DETAILS_TABLE_HEADER = [
  {title: 'Item Code', width: 'auto', sortingKey: 'item-code'},
  {title: 'Item Name / Description', width: 'auto'},
  {
    title: 'Procurement Type',
    width: 'auto',
    sortingKey: 'procurement-type',
  },
  {title: 'Item Quantity', width: 'auto', sortingKey: 'item-qty'},
  {title: 'UQC', width: '10%'},
];

export const BOM_REVIEW_VIEW_DETAILS_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'item-code', label: 'Item Code'},
  {id: '2', value: 'item-qty', label: 'Item Quantity'},
  {id: '3', value: 'procurement-type', label: 'Procurement Type'},
];

export const BOM_VERSION_DROPDOWN_OPTIONS = [
  {id: '1', value: '1', label: 'S (V1)'},
  {id: '2', value: '2', label: 'SE (V2)'},
  {id: '3', value: '3', label: 'SE R-Line (V3)'},
];

export const DBK_CLAIM_BOE_TABLE_HEADER = (isSummaryList: boolean) => {
  const tableHeads = [
    {title: 'BOE / Inv No.', width: 'auto', sortingKey: 'boe-no'},
    {title: 'BOE / Inv Date', width: 'auto', sortingKey: 'boe-date'},
    {title: 'Unique Items', width: 'auto'},
    {title: 'Total Item Quantity', width: 'auto'},
    {
      title: 'Total Assessable Value',
      width: 'auto',
      sortingKey: 'total-accessible-val',
    },
  ];
  if (!isSummaryList) {
    tableHeads.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeads;
};

export const DBK_CLAIM_BOE_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'boe-no', label: 'BOE / Inv No.'},
  {id: '2', value: 'boe-date', label: 'BOE / Inv Date'},
  {id: '3', value: 'total-accessible-val', label: 'Total Assessable Value'},
];

export const CHECKBOX_PRODUCT_SELECTION_TABLE_HEADER = [
  {title: 'checkbox', width: '2%'},
  {title: 'Product Code', width: '10%'},
  {title: 'Product Name / Description', width: '30%'},
  {title: 'FOB Value', width: '13%'},
  {title: 'Total Quantity Sold', width: '12%'},
  {title: 'Available Quantity for Consumption', width: '16%'},
  {title: 'Quantity Considered for DBK', width: '11%'},
];

export const LOCAL_SALES_SELECTED_TABLE_HEADER = [
  {title: 'Product Code', width: '10%'},
  {title: 'Invoice no', width: '10%'},
  {title: 'Product Name / Description', width: '30%'},
  {title: 'Total Quantity Sold', width: '12%'},
  {title: 'Available Quantity for Consumption', width: '16%'},
  {title: 'Quantity Considered for DBK', width: '11%'},
];

export const LOCAL_SALES_SELECTION_TABLE_HEADER = [
  {title: 'checkbox', width: '2%'},
  {title: 'Invoice No', width: '15%'},
  {title: 'Invoice Date', width: '15%'},
  {title: 'Total Products', width: '20%'},
  {title: 'FOB / Total Value', width: '20%'},
];

export const ITEM_SELECTION_BOE_TABLE_HEADER = (isSummaryList: boolean) => {
  const tableHeads = [
    {title: 'Item code', width: 'auto'},
    {title: 'Item Name / Description', width: 'auto'},
    {title: 'Total Value', width: 'auto'},
    {title: 'Total Purchase', width: 'auto'},
    {title: 'Available Quantity for Consumption', width: 'auto'},
    {title: 'Considered for DBK', width: '10%'},
  ];
  if (!isSummaryList) {
    tableHeads.unshift({title: 'checkbox', width: '2%'});
  }
  return tableHeads;
};

export const ITEM_SELECTION_BOE_SUMMERY_TABLE_HEADER = [
  {title: 'Bill Of entry no.', width: '12%'},
  {title: 'Item code', width: '12%', sortingKey: 'item-code'},
  {
    title: 'Item Name / Description',
    width: '40%',
    sortingKey: 'item-desc',
  },
  {title: 'Total Value', width: '12%'},
  {
    title: 'Total Purchase',
    width: '12%',
    sortingKey: 'total-purchased-qty',
  },
  // TODO: Not needed for now
  // {
  //   title: 'Available Quantity for Consumption',
  //   width: '16%',
  //   sortingKey: 'total-available-qty',
  // },
  // {title: 'Considered for DBK', width: '12%'},
];

export const ITEM_SELECTION_BOE_SUMMERY_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'item-code', label: 'Item Code'},
  {id: '2', value: 'item-desc', label: 'Item Name / Description'},
  {id: '3', value: 'total-purchased-qty', label: 'Total Purchase'},
];

export const PRODUCT_SELECTION_TABLE_HEADER = [
  {title: 'Product Code', width: '10%', sortingKey: 'sb-prod-code'},
  {title: 'Shipping bill no', width: '10%'},
  {
    title: 'Product Name / Description',
    width: '40%',
    sortingKey: 'sb-prod-desc',
  },
  {title: 'FOB Value', width: '13%', sortingKey: 'fob-val'},
  {
    title: 'Qty. As per Shipping Bill / Invoice',
    width: '12%',
    sortingKey: 'total-qty-exported',
  },
];

export const PRODUCT_SELECTION_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'sb-prod-code', label: 'Product Code'},
  {id: '2', value: 'sb-prod-desc', label: 'Product Name / Description'},
  {id: '3', value: 'fob-val', label: 'FOB Value'},
  {
    id: '4',
    value: 'total-qty-exported',
    label: 'Qty. As per Shipping Bill / Invoice',
  },
];

export const SB_OVERALL_SELECTION_SUMMARY = [
  {title: 'Particulars', width: '25%'},
  {title: 'Shipping Bills', width: '25%'},
  // TODO: Local Sales No need for now
  // {title: 'Local Sales', width: '25%'},
  {title: 'Product', width: '25%'},
];

export const BOE_OVERALL_SELECTION_SUMMARY_TABLE_HEADER = [
  {title: 'Particulars', width: '25%'},
  {title: 'BOE', width: '25%'},
  {title: 'Item', width: '25%'},
];

export const EXPORT_DOMESTIC_SALES_DETAILS_TABLE_HEADER = [
  {title: 'Sr. no.', width: '5%'},
  {title: 'Product code', width: 'auto', sortingKey: 'sb-prod-code'},
  {title: 'Product Description', sortingKey: 'sb-prod-desc'},
  {
    title: 'Qty. As per shipping bill / invoice',
    sortingKey: 'total-qty-exported',
  },
  {title: 'Qty. Consumed in the Claim'},
  {title: 'UQC'},
  {title: 'Stock Available (Nos.)'},
  {title: 'FOB Value as per Product Rs.', sortingKey: 'fob-val'},
];

export const VIEW_EXPORT_SALES_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'sb-prod-code', label: 'Product Code'},
  {id: '2', value: 'sb-prod-desc', label: 'Product Description'},
  {
    id: '3',
    value: 'total-qty-exported',
    label: 'Qty. As per shipping bill / invoice',
  },
  // {id: '4', value: 'total-available-qty', label: 'Product Description'},
  {id: '4', value: 'fob-val', label: 'FOB Value as per Product Rs.'},
];

export const DBK_CALCULATION_TABLE_HERDER = [
  {title: 'Particulars', width: '15%'},
  {title: 'SB', width: '13%'},
  {title: 'Product', width: '13%'},
  {title: 'BOE', width: '13%'},
  {title: 'Item', width: '13%'},
];

export const DOWNLOAD_REPORTS_MODAL_TABLE_HEADER = [
  {title: 'Reports', width: '70%'},
  {title: 'Action', width: '30%'},
];

export const CLAIM_DETAILS_SUPPORTING_DOC_TABLE_HEADER = [
  {title: 'File Name', width: '35%', sortingKey: 'fileName'},
  {title: 'Category', width: '15%', sortingKey: 'category'},
  {title: 'Last Upload', width: '14%'},
  {title: 'Upload By', width: '14%'},
  {title: 'Status', width: '12%'},
  {title: 'Action', width: '10%'},
];

export const CLAIM_DETAILS_SUPPORTING_DOC_SEARCH_DROPDOWN = [
  {id: '1', value: 'fileName', label: 'File Name'},
  {id: '2', value: 'category', label: 'Category'},
];

export const UPLOAD_DOCUMENTS_MODAL_TABLE_HEADER = [
  {title: 'checkbox', width: '2%'},
  {title: 'File Name', width: '64%'},
  {title: 'Category', width: '34%'},
];

export const FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER = [
  {title: 'SB No.', width: '7%', sortingKey: 'dbk-sb-no'},
  {title: 'SB Date', width: '7%', sortingKey: 'dbk-sb-date'},
  {title: 'Product code', width: '7%', sortingKey: 'prod-code'},
  {title: 'Product Name / Description', width: '15%'},
  {title: 'Total Product Quantity Considered for DBK', width: '10%'},
  {title: 'Total Input Quantity Shortfall', width: '10%'},
  {title: 'FOB Value', width: '7%', sortingKey: 'total-fob-val'},
  {
    title: 'System DBK Rate',
    width: '8%',
    sortingKey: 'system-dbk-rate',
  },
  {title: 'System DBK Amount', width: '8%'},
  {title: 'Govt. Rate', width: '8%', sortingKey: 'gov-dbk-rate'},
  {title: 'Govt. DBK Amount', width: '7%'},
  {
    title: 'Qualifying Status',
    width: '10%',
    sortingKey: 'qualifying-status',
  },
];
export const FINAL_SUMMARY_CLAIM_DETAILS_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'dbk-sb-no', label: 'SB No.'},
  {id: '2', value: 'dbk-sb-date', label: 'SB Date'},
  {id: '3', value: 'prod-code', label: 'Product Code'},
  {id: '4', value: 'total-fob-val', label: 'FOB Value'},
  {id: '5', value: 'system-dbk-rate', label: 'System DBK Rate'},
  {id: '6', value: 'gov-dbk-rate', label: 'Govt. Rate'},
  {id: '7', value: 'qualifying-status', label: 'Qualifying Status'},
];

export const APPLICANT_DROPDOWN = [
  {id: '1', value: 'Intermediary', label: 'Intermediary'},
  {id: '2', value: 'Exporter', label: 'Exporter'},
];

export const CLAIM_HISTORY_TABLE_HEADER = [
  {title: 'Claim Name', width: '9%', sortingKey: 'claim-name'},
  {title: 'Claim Date', width: '9%', sortingKey: 'claim-date'},
  {title: 'Claim Period', width: '11%'},
  {title: 'Total SB Considered for DBK', width: '11%'},
  {title: 'Total Product Quantity Considered for DBK', width: '12%'},
  {title: 'Total BOE Considered for DBK', width: '11%'},
  {title: 'Total Input Quantity Considered for DBK', width: '12%'},
  {title: 'Total DBK Claim Amount (INR)', width: '10%'},
  {title: 'Status', width: '11%', sortingKey: 'claim-status'},
  {title: 'Action', width: '5%'},
];

export const CLAIM_HISTORY_TABLE_SEARCH_DROPDOWN = [
  {id: '1', value: 'claim-name', label: 'Claim Name'},
  {id: '2', value: 'claim-date', label: 'Claim Date'},
  {id: '3', value: 'claim-status', label: 'Claim Status'},
];

export const CLAIM_REPORTS_FILE_ARRAY = (isQualificationError: boolean) => {
  return [
    {
      id: '1',
      title: 'DBK Claim Report',
      type: 'DBK_CLAIM_REPORT',
      disabled: false,
    },
    {
      id: '2',
      title: 'Utilization Report',
      type: 'PROCESS1_OUTPUT_FILE',
      disabled: false,
    },
    {
      id: '3',
      title: 'Utilization Summary',
      type: 'PROCESS2_OUTPUT_FILE',
      disabled: false,
    },
    {
      id: '4',
      title: 'Consolidated DBK Claim Report',
      type: 'PROCESS3_OUTPUT_FILE',
      disabled: false,
    },
    {
      id: '5',
      title: 'Revised Purchase Statement',
      type: 'BOE_SELECTION_REPORT',
      disabled: false,
    },
    {
      id: '6',
      title: 'Revised Sales Statement',
      type: 'SB_SELECTION_REPORT',
      disabled: false,
    },
    {
      id: '7',
      title: 'Unqualified Under 4th/5th Rule Report',
      type: 'PROD_DISQUALIFICATION_DETAILS_REPORT',
      disabled: !isQualificationError,
    },
  ];
};

export const RECOMPUTE_ROLLBACK_TABLE_HEADER = [
  {title: 'Reason', width: '65%'},
  {title: 'Qty', width: '16%'},
  {title: 'Rollback', width: '19%'},
];
