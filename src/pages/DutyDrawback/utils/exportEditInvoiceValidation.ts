import {REGEXP, applicantTypes} from '@common/constants';
import {IExportViewAllDetails, IExportViewInvoices} from '@common/interfaces';

export const hasInvalidValues = (data: IExportViewAllDetails): boolean => {
  return Object.entries(data).some(([_, value]) => {
    if (typeof value === 'object' && value !== null && 'isValid' in value) {
      return value.isValid === false;
    }
    return false;
  });
};

export const hasInvalidProductInArray = (
  products: IExportViewInvoices[]
): boolean => {
  return products.some((product) => {
    return Object.values(product).some((prop) => {
      if (typeof prop === 'object' && prop !== null && 'isValid' in prop) {
        return prop.isValid === false;
      }
      return false;
    });
  });
};

// INFO: Export validations
const validateIecCode = (value: string) => {
  if (value.length > 10) return 'Can not be more than 10 characters.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  return '';
};

const validateFobValue = (value: string) => {
  const numberPattern = /^\d{1,15}(?:\.\d{1,2})?$/;
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!numberPattern.test(value)) return 'Please enter a valid number.';
  return '';
};

const validateInvValue = (value: string) => {
  const numberPattern = /^\d{1,15}(?:\.\d{1,2})?$/;
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!numberPattern.test(value)) return 'Please enter a valid number.';
  return '';
};

export const validateSbInvNo = (value: string) => {
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};

export const validateShippingBillNo = (value: string) => {
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};

export const validatePortOfExport = (value: string) => {
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};
export const validateProductCode = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};
export const validateProductDesc = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.strings.test(value))
    return 'Please enter alphabetic characters only.';
  if (value.length > 200) return `Can not be more than 200 characters.`;
  return '';
};
export const validateExportedQty = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (value.length > 17) return `Can not be more than 15 digits.`;
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!REGEXP.numbersWithDecimal.test(value))
    return 'Please enter a valid number.';
  return '';
};
const validateQtyConsidered = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (value.length > 20) return 'Can not be more than 20 digits.';
  if (!REGEXP.numbers.test(value)) return 'Please enter a valid number.';
  return '';
};
export const validateSbUQC = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.strings.test(value))
    return 'Please enter alphabetic characters only.';
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};
export const validateAvailableQty = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (value.length > 17) return `Can not be more than 15 digits.`;
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!REGEXP.numbersWithDecimal.test(value))
    return 'Please enter a valid number.';
  return '';
};

export const exportInvoiceValidation = (
  key: string,
  value: string | number
) => {
  switch (key) {
    case 'iec-code':
      return validateIecCode(value.toString());
    case 'total-fob-value':
      return validateFobValue(value.toString());
    case 'inv-val':
      return validateInvValue(value.toString());
    case 'sb-inv-no':
      return validateSbInvNo(value.toString());
    case 'shipping-bill-No':
      return validateShippingBillNo(value.toString());
    case 'port-of-export':
      return validatePortOfExport(value.toString());
    case 'sb-prod-code':
      return validateProductCode(value.toString());
    case 'sb-prod-desc':
      return validateProductDesc(value.toString());
    case 'total-qty-exported':
      return validateExportedQty(value.toString());
    case 'qty-considered':
      return validateQtyConsidered(value.toString());
    case 'sb-uqc':
      return validateSbUQC(value.toString());
    case 'total-available-qty':
      return validateAvailableQty(value.toString());
    case 'fob-val':
      return validateFobValue(value.toString());
    default:
      return '';
  }
};

// INFO: Validate the conditional mandatory fields for export invoice
export const validateExportConditionalMandatoryFields = (
  data: IExportViewAllDetails,
  value: string | number,
  updatedValues: IExportViewAllDetails
) => {
  const iecCode = data?.['iec-code']?.value;
  const leoDate = data?.['leo-date']?.value;
  const sbInvNo = data?.['sb-inv-no']?.value;
  const sbInvDate = data?.['sb-inv-date']?.value;
  const shippingBillNo = data?.['shipping-bill-No']?.value;
  const shippingBillDate = data?.['shipping-bill-date']?.value;

  if (value === applicantTypes.EXPORTER) {
    if (iecCode?.toString() === '') {
      updatedValues['iec-code'] = {
        value: iecCode,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
    if (leoDate?.toString() === '') {
      updatedValues['leo-date'] = {
        value: leoDate,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
    if (sbInvNo?.toString() === '') {
      updatedValues['sb-inv-no'] = {
        value: sbInvNo,
        isValid: true,
        errorMessage: '',
      };
    }
    if (sbInvDate?.toString() === '') {
      updatedValues['sb-inv-date'] = {
        value: sbInvDate,
        isValid: true,
        errorMessage: '',
      };
    }
    if (shippingBillDate?.toString() === '') {
      updatedValues['shipping-bill-date'] = {
        value: shippingBillDate,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
    if (shippingBillNo?.toString() === '') {
      updatedValues['shipping-bill-No'] = {
        value: shippingBillNo,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
  } else {
    if (iecCode?.toString() === '') {
      updatedValues['iec-code'] = {
        value: iecCode,
        isValid: true,
        errorMessage: '',
      };
    }
    if (leoDate?.toString() === '') {
      updatedValues['leo-date'] = {
        value: leoDate,
        isValid: true,
        errorMessage: '',
      };
    }
    if (sbInvNo?.toString() === '') {
      updatedValues['sb-inv-no'] = {
        value: sbInvNo,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
    if (sbInvDate?.toString() === '') {
      updatedValues['sb-inv-date'] = {
        value: sbInvDate,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    }
    if (shippingBillDate?.toString() === '') {
      updatedValues['shipping-bill-date'] = {
        value: shippingBillDate,
        isValid: true,
        errorMessage: '',
      };
    }
    if (shippingBillNo?.toString() === '') {
      updatedValues['shipping-bill-No'] = {
        value: shippingBillNo,
        isValid: true,
        errorMessage: '',
      };
    }
  }
};
