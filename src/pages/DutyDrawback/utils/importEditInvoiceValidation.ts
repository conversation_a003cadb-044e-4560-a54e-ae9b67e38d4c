import {REGEXP} from '@common/constants';
import {IExportViewAllDetails, IExportViewInvoices} from '@common/interfaces';

export const hasInvalidValues = (data: IExportViewAllDetails): boolean => {
  return Object.entries(data).some(([_, value]) => {
    if (typeof value === 'object' && value !== null && 'isValid' in value) {
      return value.isValid === false;
    }
    return false;
  });
};

export const hasInvalidProductInArray = (
  products: IExportViewInvoices[]
): boolean => {
  return products.some((product) => {
    return Object.values(product).some((prop) => {
      if (typeof prop === 'object' && prop !== null && 'isValid' in prop) {
        return prop.isValid === false;
      }
      return false;
    });
  });
};

// INFO: Import validations
const validateItemCode = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (value.length > 20) return 'Can not be more than 20 characters.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  return '';
};

const validateItemDesc = (value: string) => {
  if (value.length > 200) return 'Can not be more than 200 characters.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  return '';
};

const validateHSN = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (value.length > 8) return 'Can not be more than 8 digits.';
  if (!REGEXP.numbers.test(value)) return 'Please enter a valid number.';
  return '';
};

const validateImportedQty = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (value.length > 15) return 'Can not be more than 15 digits.';
  if (!REGEXP.numbers.test(value)) return 'Please enter a valid number.';
  return '';
};

export const validateUqc = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.strings.test(value))
    return 'Please enter alphabetic characters only.';
  if (value.length > 15) return `Can not be more than 15 characters.`;
  return '';
};

export const validateAccessibleVal = (value: string) => {
  const numberPattern = /^\d{1,15}(?:\.\d{1,2})?$/;
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!numberPattern.test(value)) return 'Please enter a valid number.';
  return '';
};

export const validateBcdRate = (value: string) => {
  const numberPattern = /^\d{1,3}(?:\.\d{1,2})?$/;
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!numberPattern.test(value)) return 'Please enter a valid number.';
  if (Number(value) === 0) return `Value should be more than zero.`;
  if (Number(value) > 100) return `Value should not be more than 100.`;
  return '';
};

export const validateCustomCessRate = (value: string) => {
  const numberPattern = /^\d{1,3}(?:\.\d{1,2})?$/;
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!numberPattern.test(value)) return 'Please enter a valid number.';
  if (Number(value) === 0) return `Value should be more than zero.`;
  if (Number(value) > 100) return `Value should not be more than 100.`;
  return '';
};

// INFO: below validation for invoice level
export const validateSupplierName = (value: string) => {
  if (value.length > 150) return `Can not be more than 150 characters.`;
  return '';
};
export const validateCustomHouseName = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  if (value.length > 7) return `Can not be more than 7 characters.`;
  return '';
};
export const validateBoeNo = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};
export const validateFinalAssessment = (value: string) => {
  if (value.length > 0 && !REGEXP.strings.test(value))
    return 'Please enter alphabetic characters only.';
  if (value.length > 5) return `Can not be more than 5 characters.`;
  return '';
};
export const validateImportedCountry = (value: string) => {
  if (value.length > 0 && !REGEXP.strings.test(value))
    return 'Please enter alphabetic characters only.';
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};

export const validatePurchaseInvNo = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (!REGEXP.alphanumeric.test(value))
    return 'Special character are not allowed.';
  if (value.length > 20) return `Can not be more than 20 characters.`;
  return '';
};

export const validateTotalAvailableQty = (value: string) => {
  if (value.length === 0) return 'This field is required.';
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  if (!REGEXP.numbers.test(value)) return 'Please enter a valid number.';
  if (value.length > 15) return `Can not be more than 15 digits.`;
  return '';
};

export const validateForeignMaterialsSupName = (value: string) => {
  if (value.length > 300) return `Can not be more than 15 characters.`;
  if (Number(value) === 0)
    return 'Please enter a valid number without leading zeros.';
  return '';
};

export const importInvoiceValidation = (
  key: string,
  value: string | number
) => {
  switch (key) {
    case 'item-code':
      return validateItemCode(value.toString());
    case 'item-desc':
      return validateItemDesc(value.toString());
    case 'hsn':
      return validateHSN(value.toString());
    case 'imported-qty':
      return validateImportedQty(value.toString());
    case 'uqc':
      return validateUqc(value.toString());
    case 'total-accessible-val':
      return validateAccessibleVal(value.toString());
    case 'bcd-rate':
      return validateBcdRate(value.toString());
    case 'custom-cess-rate':
      return validateCustomCessRate(value.toString());
    case 'supplier-name':
      return validateSupplierName(value.toString());
    case 'custom-house-name':
      return validateCustomHouseName(value.toString());
    case 'boe-no':
      return validateBoeNo(value.toString());
    case 'is-final-assessment':
      return validateFinalAssessment(value.toString());
    case 'imported-country':
      return validateImportedCountry(value.toString());
    case 'purchase-inv-no':
      return validatePurchaseInvNo(value.toString());
    case 'total-available-qty':
      return validateTotalAvailableQty(value.toString());
    case 'foreign-materials-sup-name':
      return validateForeignMaterialsSupName(value.toString());
    default:
      return '';
  }
};
