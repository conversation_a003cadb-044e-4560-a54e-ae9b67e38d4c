import {Api, ApiAction, ApiVersion} from '@common/constants/index';
import {removeUndefinedKeys} from '@common/helpers';
import {
  IBoeAllDataList,
  IExportViewAllDetails,
  IImportViewAllDetails,
  ISaveBoeItemList,
  ISaveSbList,
  ISaveSbProductList,
} from '@common/interfaces';
import {get, post, put} from '@core/api/axios';
import {AxiosResponse} from 'axios';

const {
  EXIM,
  DOWNLOAD_TEMPLATE,
  FILE_PROCESSING_DETAILS,
  INVOICES,
  EDIT,
  UPLOAD_FILES,
  HISTORY,
  FILE_UPLOAD,
  DBK_CLAIM,
  SB_LIST,
  EXIM_DBK_PROCESS_STATUS,
  SUMMARY,
  EXPORT_REPORT,
  SB_DETAILS,
  BOM_LIST,
  BOM_SAVE,
  BOM_DETAILS,
  BOE_LIST,
  BOE_SAVE,
  ITEM_LIST,
  ITEM_SAVE,
  SB_SAVE,
  PRODUCT_LIST,
  PRODUCT_SAVE,
  SAVE_SB_PROD,
  BOE_DETAILS,
  DISCARD,
  BOE_ITEM_SAVE,
  DBK_CALCULATION,
  CALCULATION_SUMMARY,
  PROCESS_DBK,
  DBK_CLAIM_DETAILS,
  DBK_CLAIM_STATUS,
  SUPPORTED_DOC,
  DETAILS,
  UPLOAD,
  DOWNLOAD,
  DBK_SUMMARY,
  FREEZ_CLAIM,
  AUTO_RECOMPUTE,
} = Api;
const {
  EXIM_DOWNLOAD_EXCEL_TEMPLATE,
  EXIM_FILE_UPLOAD,
  EXIM_FILE_UPLOAD_HISTORY,
  FILE_PROCESSING_DTLS,
  EXIM_GET_SB_LIST,
  EXIM_DBK_PROCESS_STATUS: DBK_PROCESS_STATUS,
  EXIM_GET_INVOICES,
  EXIM_GET_INVOICES_SUMMARY,
  EXPORT_REPORT: EXIM_EXPORT_REPORT,
  EXIM_GET_SB_DETAILS,
  EXIM_GET_BOM_LIST,
  EXIM_SAVE_BOM_REVIEW,
  EXIM_GET_BOM_DETAILS,
  EXIM_GET_BOE_LIST,
  EXIM_SAVE_BOE_SELECTION,
  EXIM_GET_ITEM_LIST,
  EXIM_SAVE_BOE_ITEM_SELECTION,
  EXIM_SAVE_SB_SELECTION,
  EXIM_GET_SB_PRODUCT_LIST,
  EXIM_SAVE_SB_PRODUCT_SELECTION,
  EXIM_GET_DBK_CLAIM_SMRY,
  EXIM_SAVE_SB_PROD_SMRY,
  EXIM_GET_BOE_DETAILS,
  EXIM_EDIT_INVOICES,
  EXIM_DBK_CLAIM_HISTORY,
  DISCARD_DBK_CLAIM,
  EXIM_DELETE_INVOICES,
  DISCARD_TXN,
  EXIM_SAVE_BOE_ITEM_SMRY,
  EXIM_GET_DBK_CAL_SMRY,
  EXIM_PROCESS_DBK_CLAIM,
  EXIM_DBK_CLAIM_DETAILS,
  EXIM_UPDATE_DBK_CLAIM_STATUS,
  EXIM_UPLOAD_DBK_CLAIM_SUPP_DOCS,
  EXIM_DOWNLOAD_DBK_CLAIM_SUPP_DOCS,
  EXIM_GET_DBK_SMRY,
  EXIM_FREEZ_CLAIM,
  EXIM_AUTO_RECOMPUTE,
} = ApiAction;

interface IFileCategory {
  'file-key': string;
  'display-name': string;
  category: string;
}

export interface IApiData {
  pan: string;
  email: string;
  fileType?: string;
  txnId?: string;
  failedFiles?: boolean;
  fileId?: string;
  startPeriod?: string;
  endPeriod?: string;
  invType?: string;
  sbRefID?: string;
  reviewTxnId?: string;
  claimTxnId?: string;
  prodCode?: string;
  bomVersion?: number;
  reportType?: string;
  applicantType?: string;
  claimName?: string;
  errorFileId?: string;
  intermediateSave?: boolean;
  suppDocCategory?: string;
  suppDocFileId?: string;
  selected?: boolean;
  searchKey?: string;
  searchValue?: string;
  sortBy?: string;
  sortingOrder?: 1 | -1;
  considerDisqualifiedProds?: boolean;
  selectAll?: boolean;
}

export const downloadExcelTemplate = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${DOWNLOAD_TEMPLATE}?action=${EXIM_DOWNLOAD_EXCEL_TEMPLATE}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const getProcessedDetails = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${FILE_PROCESSING_DETAILS}?action=${FILE_PROCESSING_DTLS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
      },
    }
  );
  return response as AxiosResponse;
};

export const discardTransaction = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${INVOICES}${DISCARD}?action=${DISCARD_TXN}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'txn-id': data.txnId || '',
        'file-type': data.fileType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const uploadFileData = async (data: IApiData, file: File) => {
  const attachedFiles = new FormData();
  attachedFiles.append(`attachedFiles`, file);

  const headers = {
    pan: data.pan,
    email: data.email,
    'file-type': data.fileType || '',
    'error-file-id': data.errorFileId,
    'txn-id': data.txnId,
    'uploaded-file-name': file.name,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await post(
    `${EXIM}${INVOICES}${UPLOAD_FILES}?action=${EXIM_FILE_UPLOAD}`,
    attachedFiles,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getUploadHistory = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${INVOICES}${FILE_UPLOAD}${HISTORY}?page-no=${page}&limit=${limit}&action=${EXIM_FILE_UPLOAD_HISTORY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const dbkClaimProcessingStatus = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${EXIM_DBK_PROCESS_STATUS}?action=${DBK_PROCESS_STATUS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
      },
    }
  );
  return response as AxiosResponse;
};

export const invoicesSummary = async (data: IApiData) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'file-type': data.fileType,
    'start-period': data.startPeriod,
    'end-period': data.endPeriod,
    'txn-id': data.txnId,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EXIM}${INVOICES}${SUMMARY}?action=${EXIM_GET_INVOICES_SUMMARY}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const getInvoices = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'txn-id': data.txnId,
    'inv-type': data.invType,
    'file-type': data.fileType,
    'start-period': data.startPeriod,
    'end-period': data.endPeriod,
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EXIM}${INVOICES}?page-no=${page}&limit=${limit}&action=${EXIM_GET_INVOICES}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const downloadFileData = async (data: IApiData) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'file-type': data.fileType,
    'report-type': data.reportType,
    'start-period': data.startPeriod,
    'end-period': data.endPeriod,
    'txn-id': data.txnId,
    'should-return-file': false,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);
  const response = await get(
    `${EXIM}${INVOICES}${EXPORT_REPORT}?action=${EXIM_EXPORT_REPORT}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const deleteInvoices = async (
  data: IApiData,
  isDeleteAll: boolean,
  refIdList: string[]
) => {
  const response = await put(
    `${EXIM}${INVOICES}?deleteAllInvoices=${isDeleteAll}&txn-id=${data.txnId}&action=${EXIM_DELETE_INVOICES}`,
    refIdList,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getSbDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${INVOICES}${SB_DETAILS}?page-no=${page}&limit=${limit}&action=${EXIM_GET_SB_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'sb-ref-id': data.sbRefID,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBoeViewDetails = async (
  data: IApiData,
  page: number,
  limit: number,
  refIds: string[]
) => {
  const response = await put(
    `${EXIM}${INVOICES}${BOE_DETAILS}?page-no=${page}&limit=${limit}&action=${EXIM_GET_BOE_DETAILS}`,
    refIds,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'search-key': data.searchKey || '',
        'search-value': data.searchValue || '',
        'sort-by': data.sortBy || '',
        'sorting-order': data.sortingOrder || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveViewInvoiceDetails = async (
  data: IApiData,
  body: IImportViewAllDetails | IExportViewAllDetails
) => {
  const response = await put(
    `${EXIM}${INVOICES}${EDIT}?txn-id=${data.txnId}&action=${EXIM_EDIT_INVOICES}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'file-type': data.fileType || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getDbkClaimSbList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${SB_LIST}?page-no=${page}&limit=${limit}&action=${EXIM_GET_SB_LIST}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        selected: data.selected as boolean,
        'claim-txn-id': data.claimTxnId || '',
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBomReviews = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${BOM_LIST}?page-no=${page}&limit=${limit}&action=${EXIM_GET_BOM_LIST}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveBomReview = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${BOM_SAVE}?action=${EXIM_SAVE_BOM_REVIEW}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getBomDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${INVOICES}${BOM_DETAILS}?page-no=${page}&limit=${limit}&action=${EXIM_GET_BOM_DETAILS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'prod-code': data.prodCode,
        'bom-version': data.bomVersion,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBoeList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${BOE_LIST}?page-no=${page}&limit=${limit}&action=${EXIM_GET_BOE_LIST}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveBoeDetails = async (data: IApiData, body: IBoeAllDataList) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${BOE_SAVE}?action=${EXIM_SAVE_BOE_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'intermediate-save': data.intermediateSave as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

export const getBoeItemList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${ITEM_LIST}${ApiVersion.V2}?page-no=${page}&limit=${limit}&action=${EXIM_GET_ITEM_LIST}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveBoeItemList = async (
  data: IApiData,
  body: IBoeAllDataList
) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${ITEM_SAVE}?action=${EXIM_SAVE_BOE_ITEM_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'intermediate-save': data.intermediateSave as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveSbList = async (data: IApiData, body: ISaveSbList) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${SB_SAVE}${ApiVersion.V2}?action=${EXIM_SAVE_SB_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.pan,
        'intermediate-save': data.intermediateSave as boolean,
        'select-all': data.selectAll as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

export const getDbkClaimProductList = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${PRODUCT_LIST}?page-no=${page}&limit=${limit}&action=${EXIM_GET_SB_PRODUCT_LIST}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        selected: data.selected as boolean,
        'claim-txn-id': data.claimTxnId,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveSbProductList = async (
  data: IApiData,
  body: ISaveSbProductList
) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${PRODUCT_SAVE}?action=${EXIM_SAVE_SB_PRODUCT_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'intermediate-save': data.intermediateSave as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

export const getOverAllSummary = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${DBK_SUMMARY}?action=${EXIM_GET_DBK_CLAIM_SMRY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const saveSbSelectionSummary = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${SAVE_SB_PROD}${ApiVersion.V2}?action=${EXIM_SAVE_SB_PROD_SMRY}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId as string,
      },
    }
  );
  return response as AxiosResponse;
};

export const getClaimHistoryData = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${HISTORY}?page-no=${page}&limit=${limit}&action=${EXIM_DBK_CLAIM_HISTORY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'search-key': data.searchKey,
        'search-value': data.searchValue,
        'sort-by': data.sortBy,
        'sorting-order': data.sortingOrder,
      },
    }
  );
  return response as AxiosResponse;
};

export const postClaim = async (data: IApiData) => {
  const response = await post(
    `${EXIM}${DBK_CLAIM}?action=${EXIM_GET_SB_LIST}`,
    null,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-name': data.claimName as string,
        'start-period': data.startPeriod as string,
        'end-period': data.endPeriod as string,
        'applicant-type': data.applicantType as string,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveBoeProducts = async (
  data: IApiData,
  body: ISaveBoeItemList
) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${PRODUCT_SAVE}?action=${EXIM_SAVE_SB_PRODUCT_SELECTION}`,
    body,
    {
      headers: {
        pan: data.pan,
        email: data.email,
      },
    }
  );
  return response as AxiosResponse;
};

export const discardDbkClaim = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${DISCARD}?action=${DISCARD_DBK_CLAIM}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const saveBoeItemSummary = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${BOE_ITEM_SAVE}${ApiVersion.V2}?action=${EXIM_SAVE_BOE_ITEM_SMRY}`,
    {},
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const getSummaryCalculation = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${DBK_CALCULATION}${CALCULATION_SUMMARY}?action=${EXIM_GET_DBK_CAL_SMRY}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const dbkClaimDetails = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'claim-txn-id': data.claimTxnId || '',
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EXIM}${DBK_CLAIM}${DBK_CLAIM_DETAILS}?page-no=${page}&limit=${limit}&action=${EXIM_DBK_CLAIM_DETAILS}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};
export const getSupportedDoc = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'claim-txn-id': data.claimTxnId || '',
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };
  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EXIM}${DBK_CLAIM}${SUPPORTED_DOC}${DETAILS}?page-no=${page}&limit=${limit}&action=${EXIM_DBK_CLAIM_DETAILS}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const saveSummaryCalculation = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${DBK_CALCULATION}${PROCESS_DBK}?action=${EXIM_PROCESS_DBK_CLAIM}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const updateDbkClaimStatus = async (
  data: IApiData,
  claimStatus: string
) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${DBK_CLAIM_STATUS}?claim-status=${claimStatus}&action=${EXIM_UPDATE_DBK_CLAIM_STATUS}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};

export const uploadSupportedDoc = async (
  data: IApiData,
  files: File[],
  fileCategories: IFileCategory[]
) => {
  const attachedFiles = new FormData();
  files.forEach((file: File) => {
    attachedFiles.append(`attachedFiles`, file);
  });
  attachedFiles.append('filesDetails', JSON.stringify(fileCategories));

  const response = await post(
    `${EXIM}${DBK_CLAIM}${SUPPORTED_DOC}${UPLOAD}?action=${EXIM_UPLOAD_DBK_CLAIM_SUPP_DOCS}`,
    attachedFiles,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response as AxiosResponse;
};

export const downloadSupportedDoc = async (data: IApiData) => {
  const response = await get(
    `${EXIM}${DBK_CLAIM}${SUPPORTED_DOC}${DOWNLOAD}?action=${EXIM_DOWNLOAD_DBK_CLAIM_SUPP_DOCS}`,
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'sup-doc-file-id': data.suppDocFileId || '',
        'claim-txn-id': data.claimTxnId || '',
        'should-return-file': false,
      },
    }
  );
  return response as AxiosResponse;
};

export const getDbkFinalSummary = async (
  data: IApiData,
  page: number,
  limit: number
) => {
  const headers = {
    pan: data.pan,
    email: data.email,
    'claim-txn-id': data.claimTxnId || '',
    'search-key': data.searchKey,
    'search-value': data.searchValue,
    'sort-by': data.sortBy,
    'sorting-order': data.sortingOrder,
  };

  // Deleting the key because no need to send undefined value in some API to get the correct response
  const updatedHeaders = removeUndefinedKeys(headers);

  const response = await get(
    `${EXIM}${DBK_CLAIM}${DBK_CALCULATION}${DBK_SUMMARY}?page-no=${page}&limit=${limit}&action=${EXIM_GET_DBK_SMRY}`,
    {
      headers: updatedHeaders,
    }
  );
  return response as AxiosResponse;
};

export const freezeDbkClaim = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${DBK_CALCULATION}${FREEZ_CLAIM}?action=${EXIM_FREEZ_CLAIM}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
        'consider-disqualified-prods':
          data.considerDisqualifiedProds as boolean,
      },
    }
  );
  return response as AxiosResponse;
};

// INFO: Below API is not in use for now
export const recomputeDbkClaim = async (data: IApiData) => {
  const response = await put(
    `${EXIM}${DBK_CLAIM}${DBK_CALCULATION}${AUTO_RECOMPUTE}?action=${EXIM_AUTO_RECOMPUTE}`,
    {}, // Passing empty object because if we won't pass the {} then it consider the header as body
    {
      headers: {
        pan: data.pan,
        email: data.email,
        'claim-txn-id': data.claimTxnId || '',
      },
    }
  );
  return response as AxiosResponse;
};
