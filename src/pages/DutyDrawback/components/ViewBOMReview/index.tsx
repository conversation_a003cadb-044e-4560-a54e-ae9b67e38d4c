import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import NavigationSubHeader from '@common/components/NavigationSubHeader';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path} from '@common/constants';
import {IBomDetails, IRegularDropdownData} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import Card from '@modules/src/shared-components/Card/Card';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getBomDetails} from '@pages/DutyDrawback/api';
import DbkClaimHeader from '@pages/DutyDrawback/components/DbkClaimHeader';
import {
  BOM_REVIEW_VIEW_DETAILS_TABLE_HEADER,
  BOM_REVIEW_VIEW_DETAILS_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router';

import {
  BomVersionCardContent,
  ViewDetailsCardContent,
} from '../ViewDetailsCardContent';
import './index.scss';

export type BomFieldName =
  | 'item-code'
  | 'item-desc'
  | 'procurement-type'
  | 'item-qty'
  | 'uqc';

function ViewDetailsReview() {
  const {pathname, state: pathState} = useLocation();
  const {prodCode, bomVersion} = (pathState ?? {}) as {
    prodCode: string;
    bomVersion: string;
  };

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [currentVersion, setCurrentVersion] = useState(bomVersion || '');
  const [prodDescription, setProdDescription] = useState('');
  const [totalItems, setTotalItems] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [bomDetails, setBomDetails] = useState<IBomDetails[]>([]);
  const [bomVersionList, setBomVersionList] = useState<IRegularDropdownData[]>(
    []
  );

  const cardData = [
    {title: 'Product Code', value: prodCode || '-'},
    {title: 'Product Name / Description', value: prodDescription || '-'},
    {title: 'BOM Version', value: 'DROPDOWN'},
    {title: 'Total Item Count', value: totalItems?.toString() || '-'},
  ];

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getBomDetailsData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      bomVersion: Number(currentVersion),
      prodCode,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    // INFO: Handle the base case to avoid the unnecessary API call
    if (!bomVersion) return;
    const {data} = await getBomDetails(payload, page, +showEntries);
    setTotalItems(data['total-items']);
    setTotalRecords(data['total-records']);
    setProdDescription(data['prod-desc']?.value);
    setBomDetails(data['item-list']);
    // Creating the option list according to the version array
    const bomVersions = data['bom-version-list']
      .map((version: number) => {
        return {id: `${version}`, value: `${version}`, label: `V-${version}`};
      })
      .reverse();
    setBomVersionList(bomVersions);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    panNumber,
    email,
    prodCode,
    currentVersion,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getBomDetailsData();
  }, [getBomDetailsData]);

  return (
    <div className='view-bom-review-container'>
      <NavigationSubHeader
        hasLeftArrow
        hasTitle
        hasGuide
        isNavigate
        leftArrowRoute='#'
        leftArrowText='View Details'
      />
      {/* Date period section */}
      {pathname.includes(Path.DBK_CLAIM) ? (
        <DbkClaimHeader />
      ) : (
        <BusinessHeader />
      )}

      <div className='view-details-card-container'>
        {cardData?.map(({title, value}) => (
          <Card
            key={title}
            content={
              value === 'DROPDOWN' ? (
                <BomVersionCardContent
                  title={title}
                  dropDownValues={bomVersionList}
                  defaultSelectedOptionId={Number(currentVersion)}
                  handleSelect={(val: string) => setCurrentVersion(val)}
                />
              ) : (
                <ViewDetailsCardContent
                  title={title}
                  data={value?.toString() || ''}
                />
              )
            }
          />
        ))}
      </div>

      <EximPaper>
        <div className='view-bom-review-table-container'>
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={BOM_REVIEW_VIEW_DETAILS_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <table className='view-bom-review-table'>
            <TableHeader
              mainHeader={BOM_REVIEW_VIEW_DETAILS_TABLE_HEADER}
              handleSortBy={handleSortBy}
            />
            {bomDetails?.length > 0 ? (
              <TableBody className='view-bom-review-tbody'>
                {bomDetails?.map((item: IBomDetails, index: number) => (
                  <TableRow key={`viewDetails${index + 1}`}>
                    {[
                      'item-code',
                      'item-desc',
                      'procurement-type',
                      'item-qty',
                      'uqc',
                    ].map((fieldName) => (
                      <TableCell
                        key={fieldName}
                        className={`${
                          !item[fieldName as BomFieldName]?.isValid
                            ? 'error-text-td'
                            : ''
                        }`}>
                        {item[fieldName as BomFieldName].value || '-'}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable
                colSpan={BOM_REVIEW_VIEW_DETAILS_TABLE_HEADER.length}
              />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={bomDetails as []}
            renderData={bomDetails as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ViewDetailsReview;
