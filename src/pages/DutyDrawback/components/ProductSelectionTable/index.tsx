import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {formatAmount} from '@common/helpers';
import {IDbkClaimProductList} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {CHECKBOX_PRODUCT_SELECTION_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximInput from '@shared/components/EximInput';
import {ChangeEvent} from 'react';

import './index.scss';

interface IProductSelectionTable {
  data: IDbkClaimProductList[];
  isSelectedAll?: boolean;
  selectItem?: (id: string, code: string) => void;
  onSelectAll?: () => void;
  isOptionsCheck?: boolean;
  handleChangeItemValue?: (
    event: ChangeEvent<HTMLInputElement>,
    index: string,
    code: string
  ) => void;
  isEditableQty?: boolean;
}

function ProductSelectionTable(props: IProductSelectionTable) {
  const {
    data,
    selectItem,
    isSelectedAll,
    onSelectAll,
    isOptionsCheck,
    handleChangeItemValue,
    isEditableQty,
  } = props;

  return (
    <table className='sb-product-selection'>
      <TableHeader
        checked={isSelectedAll}
        onChange={onSelectAll}
        mainHeader={CHECKBOX_PRODUCT_SELECTION_TABLE_HEADER}
      />
      {data?.length > 0 ? (
        <TableBody>
          {data?.map((item, index) => (
            <TableRow key={`productSelection${index + 1}`}>
              {isOptionsCheck && selectItem && (
                <TableCell className='checkbox-td'>
                  <EximCheckbox
                    id={`${item['sb-ref-id']}`}
                    color='#2CB544'
                    size='medium'
                    checked={item['is-selected']}
                    onChange={() =>
                      selectItem(item['sb-ref-id'], item['sb-prod-code'])
                    }
                  />
                </TableCell>
              )}
              <TableCell>{item['sb-prod-code']}</TableCell>
              <TableCell>{item['sb-prod-desc']}</TableCell>
              <TableCell>{formatAmount(item['fob-val'])}</TableCell>
              <TableCell>{formatAmount(item['total-qty-exported'])}</TableCell>
              <TableCell>{formatAmount(item['total-available-qty'])}</TableCell>
              {isEditableQty && handleChangeItemValue ? (
                <TableCell>
                  <EximInput
                    id='qty-considered'
                    name='qty-considered'
                    dataTestid='qty-considered'
                    value={item['qty-considered'].toString()}
                    onChange={(event: ChangeEvent<HTMLInputElement>) =>
                      handleChangeItemValue(
                        event,
                        item['sb-ref-id'],
                        item['sb-prod-code']
                      )
                    }
                  />
                </TableCell>
              ) : (
                <TableCell>{item['qty-considered']}</TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      ) : (
        <EmptyTable colSpan={CHECKBOX_PRODUCT_SELECTION_TABLE_HEADER.length} />
      )}
    </table>
  );
}

ProductSelectionTable.defaultProps = {
  selectItem: () => '',
  isOptionsCheck: false,
  isSelectedAll: false,
  onSelectAll: () => '',
  handleChangeItemValue: () => '',
  isEditableQty: false,
};

export default ProductSelectionTable;
