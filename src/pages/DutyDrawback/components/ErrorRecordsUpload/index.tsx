import UploadFiles from '@common/components/UploadFiles';
import {SupportedFileTypes} from '@common/constants';
import {checkFilesExtension, downloadFile} from '@common/helpers';
import {alertActions} from '@core/api/store/alertReducer';
import {downloadFileData, uploadFileData} from '@pages/DutyDrawback/api';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import GSTButton from '@shared/components/EximButton';
import GSTTypography from '@shared/components/EximTypography';
import {ArrowRight} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {ChangeEvent, memo, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IErrorUploadProps {
  fileType: string;
  disabledButton: boolean;
}

function ErrorRecordsUpload({fileType, disabledButton}: IErrorUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File>();
  const errorFileType = fileType.split('_').join('_ERROR_');

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceErrorFileId, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const handleDownloadInvoices = async () => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      reportType: errorFileType,
    };
    const {data} = await downloadFileData(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
    dispatch(dutyDrawbackActions.setInvoiceErrorFileId(data?.['file-id']));
  };

  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);

      if (checkFilesExtension(selectedFilesArray, [SupportedFileTypes.EXCEL])) {
        setSelectedFile(selectedFilesArray[0]);
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          errorFileId: invoiceErrorFileId,
          fileType,
        };
        await uploadFileData(payload, selectedFilesArray[0]);
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message: 'Please select the excel files only!',
            alertType: 'danger',
          })
        );
      }
    }
  };

  return (
    <div className='error-record-upload-container'>
      <div className='upload-note'>
        <GSTTypography fontWeight='bold' variant='h4'>
          Note:{' '}
        </GSTTypography>
        <GSTTypography variant='h4'>
          - Follow the below steps and Download the Excel containing error
          records with valid comments, rectify the error data and Reupload the
          same Excel File.
        </GSTTypography>
      </div>
      <div className='process-steps-container'>
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 1 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Download Error Records Excel
            </GSTTypography>
          </div>
          <GSTButton
            size='small'
            color='secondary'
            disabled={disabledButton}
            onClick={handleDownloadInvoices}>
            Download
          </GSTButton>
        </div>
        <ArrowRight />
        <div className='step-text'>
          <GSTTypography fontWeight='bold' variant='h4'>
            Step 2 - &nbsp;
          </GSTTypography>
          <GSTTypography fontWeight='semi-bold' variant='h4'>
            Rectify the Error records
          </GSTTypography>
        </div>
        <ArrowRight />
        <div className='process-step'>
          <div className='step-text'>
            <GSTTypography fontWeight='bold' variant='h4'>
              Step 3 - &nbsp;
            </GSTTypography>
            <GSTTypography fontWeight='semi-bold' variant='h4'>
              Upload Validation Excel
            </GSTTypography>
          </div>

          <span>
            <UploadFiles
              title='Upload'
              accept='.xlsx'
              disabled={disabledButton}
              onChange={handleFileUpload}
              icon={null}
            />
            <GSTTypography>
              {selectedFile?.name ?? `No Chosen File`}
            </GSTTypography>
          </span>
        </div>
      </div>
    </div>
  );
}

export default memo(ErrorRecordsUpload);
