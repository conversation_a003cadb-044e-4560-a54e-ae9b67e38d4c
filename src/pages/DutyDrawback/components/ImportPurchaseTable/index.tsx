import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  DUTY_DRAWBACK_FILE_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {formatDate} from '@common/helpers';
import {ICustomAxiosResp, IImportInvoices} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {deleteInvoices, getInvoices} from '@pages/DutyDrawback/api';
import {
  IMPORT_DOMESTIC_PURCHASE_TABLE_HEADER,
  IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IInvoicesTable {
  isValidRecord: boolean;
  startPeriod: string;
  endPeriod: string;
  setImportRefIdList: (value: string[]) => void;
  getInvoicesSummary: () => void;
  isDeleteAllClicked: boolean;
}
function ImportPurchaseTable({
  isValidRecord,
  startPeriod,
  endPeriod,
  setImportRefIdList,
  getInvoicesSummary,
  isDeleteAllClicked,
}: IInvoicesTable) {
  const navigate = useNavigate();
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceTxnId, isLastTransactionInvalid},
  } = useSelector((state: RootState) => state);

  // Adding the Checkbox column based on the valid and invalid record in table header
  const tableHeader = useMemo(
    () => IMPORT_DOMESTIC_PURCHASE_TABLE_HEADER(!isValidRecord),
    [isValidRecord]
  );

  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [invoicesData, setInvoicesData] = useState<IImportInvoices[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleSingleSelect = (id: string) => {
    const updatedInvoices = invoicesData.map((item) =>
      item['boe-ref-id-list'][0] === id
        ? {...item, selected: !item.selected}
        : item
    );
    const totalSelected = updatedInvoices.filter(
      (item) => item.selected
    ).length;
    setIsSelectedAll(totalSelected === updatedInvoices.length);
    setInvoicesData(updatedInvoices);
    // Storing selected id to delete the invoices
    const selectedIds = updatedInvoices
      .filter((obj) => obj.selected === true)
      .flatMap((obj) => obj['boe-ref-id-list']);
    setImportRefIdList(selectedIds);
  };

  const handleSelectAll = () => {
    const allSelected = invoicesData.every((item) => item.selected);
    const updatedInvoices = invoicesData.map((value: IImportInvoices) => ({
      ...value,
      selected: !allSelected,
    }));
    setInvoicesData(updatedInvoices);
    setIsSelectedAll(!allSelected);
    // Storing selected id to delete the invoices
    const selectedIds = updatedInvoices
      .filter((obj) => obj.selected === true)
      .flatMap((obj) => obj['boe-ref-id-list']);
    setImportRefIdList(selectedIds);
  };

  const getInvoicesData = useCallback(async () => {
    const payload = {
      txnId: '',
      startPeriod: '',
      endPeriod: '',
      pan: panNumber,
      email,
      invType: isValidRecord ? 'VALID' : 'INVALID',
      fileType: DUTY_DRAWBACK_FILE_TYPE.BOE,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    // INFO: If last transaction has invalid records the we need to fetch the data using txnId else period
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    } else {
      payload.startPeriod = startPeriod;
      payload.endPeriod = endPeriod;
    }
    const {data} = await getInvoices(payload, page, +showEntries);
    // Adding the selected key to check and uncheck based on the value
    setInvoicesData(
      data?.records?.map((value: IImportInvoices) => ({
        ...value,
        selected: false,
      }))
    );
    setTotalRecords(data?.['total-records']);
    setIsSelectedAll(false); // Reset the value on API call
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    panNumber,
    invoiceTxnId,
    isLastTransactionInvalid,
    startPeriod,
    endPeriod,
    email,
    isValidRecord,
    showEntries,
    page,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleViewInvoice = (refIdArray: string[]) => {
    navigate(`${Path.DUTY_DRAWBACK}${Path.VIEW_DETAILS}/boe/${refIdArray[0]}`, {
      state: {
        refIds: refIdArray,
        isViewValidRecord: isValidRecord,
        isEditable: false,
      },
    });
  };

  const handleEditInvoice = (refIdArray: string[]) => {
    navigate(`${Path.DUTY_DRAWBACK}${Path.VIEW_DETAILS}/boe/${refIdArray[0]}`, {
      state: {
        refIds: refIdArray,
        isViewValidRecord: isValidRecord,
        isEditable: true,
      },
    });
  };

  const handleDeleteInvoice = async (refIdArray: string[]) => {
    const payload = {
      pan: panNumber,
      email,
      txnId: invoiceTxnId,
      fileType: DUTY_DRAWBACK_FILE_TYPE.BOE,
    };

    const response = (await deleteInvoices(
      payload,
      false,
      refIdArray
    )) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getInvoicesSummary(); // Updating the records count after delete record
      getInvoicesData(); // Updating the data after delete record
    }
  };

  useEffect(() => {
    getInvoicesData();
  }, [getInvoicesData, isDeleteAllClicked]);

  return (
    <div className='import-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='invoices-table'>
        <TableHeader
          mainHeader={tableHeader}
          checked={isSelectedAll}
          onChange={handleSelectAll}
          handleSortBy={handleSortBy}
        />
        {invoicesData?.length > 0 ? (
          <TableBody className='invoices-tbody'>
            {invoicesData?.map((item: IImportInvoices, index: number) => (
              <TableRow key={`${item?.['boe-ref-id-list'][0]}${index + 1}`}>
                {!isValidRecord ? (
                  <TableCell className='checkbox-td'>
                    <EximCheckbox
                      id={`${item['boe-no']}`}
                      name='invoiceRecord'
                      color='#2CB544'
                      size='medium'
                      checked={item.selected}
                      onChange={() =>
                        handleSingleSelect(item?.['boe-ref-id-list'][0])
                      }
                    />
                  </TableCell>
                ) : null}
                <TableCell>{item['boe-no']?.value}</TableCell>
                <TableCell>
                  {formatDate(item['boe-date']?.value?.toString())}
                </TableCell>
                <TableCell>{item?.['no-of-items']}</TableCell>
                <TableCell>{item?.['total-duty']}</TableCell>
                <TableCell>{item?.['custom-house-name']?.value}</TableCell>
                <TableCell>{item['total-accessible-val']?.value}</TableCell>
                <TableCell>
                  {isValidRecord ? (
                    <TableActions
                      isViewIcon
                      viewToolTipText='View Invoice'
                      handleView={() =>
                        handleViewInvoice(item?.['boe-ref-id-list'])
                      }
                    />
                  ) : (
                    <TableActions
                      isViewIcon
                      isEditIcon
                      isDeleteIcon
                      viewToolTipText='View Invoice'
                      editToolTipText='Edit Invoice'
                      deleteToolTipText='Delete Invoice'
                      handleView={() =>
                        handleViewInvoice(item?.['boe-ref-id-list'])
                      }
                      handleEdit={() =>
                        handleEditInvoice(item?.['boe-ref-id-list'])
                      }
                      handleDelete={() =>
                        handleDeleteInvoice(item?.['boe-ref-id-list'])
                      }
                    />
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={tableHeader.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={invoicesData as []}
        renderData={invoicesData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default ImportPurchaseTable;
