import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {applicantTypes} from '@common/constants';
import {formatAmount, formatDate} from '@common/helpers';
import {ISbListTableDetails} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {SB_DETAILS_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximCheckbox from '@shared/components/EximCheckbox';
import {RootState} from '@store';
import {useMemo} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IDbkClaimTable {
  data: ISbListTableDetails[];
  isSelectedAll?: boolean;
  selectItem?: (id: string) => void;
  onSelectAll?: () => void;
  isOptionsCheck?: boolean;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
}

function ShippingBillsTable(props: IDbkClaimTable) {
  const {
    data,
    selectItem,
    isSelectedAll,
    onSelectAll,
    isOptionsCheck,
    handleSortBy,
  } = props;

  const {
    dbkClaim: {applicantType},
  } = useSelector((state: RootState) => state.dutyDrawback);

  const tableHeads = useMemo(
    () =>
      SB_DETAILS_TABLE_HEADER(
        isOptionsCheck || false,
        applicantType === applicantTypes.EXPORTER
      ),
    [isOptionsCheck, applicantType]
  );
  return (
    <table className='dbk-sb-list-table'>
      <TableHeader
        checked={isSelectedAll}
        onChange={onSelectAll}
        mainHeader={tableHeads}
        handleSortBy={handleSortBy}
      />
      {data?.length > 0 ? (
        <TableBody>
          {data?.map((item, index) => (
            <TableRow key={`shippingBill${index + 1}`}>
              {isOptionsCheck && selectItem && (
                <TableCell className='checkbox-td'>
                  <EximCheckbox
                    id={`${item['sb-ref-id']}`}
                    color='#2CB544'
                    size='medium'
                    checked={item['is-selected']}
                    onChange={() => selectItem(item['sb-ref-id'])}
                  />
                </TableCell>
              )}
              <TableCell>{item['sb-inv-no']}</TableCell>
              <TableCell>{formatDate(item['sb-inv-date'] || '')}</TableCell>
              <TableCell>{formatDate(item['leo-date'] || '')}</TableCell>
              <TableCell>{formatAmount(item['total-prods'] || '')}</TableCell>
              <TableCell>
                {formatAmount(item['total-fob-value'] || '')}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      ) : (
        <EmptyTable colSpan={tableHeads.length} />
      )}
    </table>
  );
}

ShippingBillsTable.defaultProps = {
  isOptionsCheck: false,
  isSelectedAll: false,
  onSelectAll: () => '',
  selectItem: (id: string) => '',
};
export default ShippingBillsTable;
