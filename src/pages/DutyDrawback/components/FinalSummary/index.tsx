import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount, formatDate} from '@common/helpers';
import {IFinalSummaryClaimDetails} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER,
  FINAL_SUMMARY_CLAIM_DETAILS_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';
import {ChangeEvent} from 'react';

import './index.scss';

interface IFinalSummary {
  handleSearchQuery: (event: ChangeEvent<HTMLInputElement>) => void;
  handleShowEntries: (event: ChangeEvent<HTMLSelectElement>) => void;
  handleSortBy: (sortBy: string, order: 'asc' | 'desc') => void;
  handlePageChange: (pageNumber: string | number) => void;
  handleSearchKey: (value: string) => void;
  finalSummaryTableData: IFinalSummaryClaimDetails[];
  page: number;
  searchQuery: string;
  showEntries: string;
  totalRecords: number;
  searchKey: string;
}

function FinalSummary({
  handleSearchQuery,
  handleShowEntries,
  finalSummaryTableData,
  page,
  searchQuery,
  showEntries,
  totalRecords,
  handlePageChange,
  handleSearchKey,
  handleSortBy,
  searchKey,
}: IFinalSummary) {
  const showValue = (value: string) => {
    if (value === 'QUALIFIED') {
      return 'Qualified';
    }
    return 'Not-Qualified';
  };

  const getReason = (value: string) => {
    if (!value || value === 'N/A') return '';
    if (value === 'AIR_RATE') {
      return ' - 4th/5th Rule';
    }
    return ' - Shortfall Quantity';
  };

  return (
    <div className='final-summary-table-wrapper'>
      <EximTypography
        variant='h3'
        fontWeight='semi-bold'
        classNames='table-title'>
        Final Summary
      </EximTypography>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='final-summary-selection'>
        <TableHeader
          mainHeader={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {finalSummaryTableData?.length > 0 ? (
          <TableBody className='final-summary-table-body'>
            {finalSummaryTableData?.map((item) => (
              <TableRow key={`${item.prodCode}`}>
                <TableCell>{item.sbNo ?? '-'}</TableCell>
                <TableCell>{formatDate(item.sbDate) ?? '-'}</TableCell>
                <TableCell>{item.prodCode ?? '-'}</TableCell>
                <TableCell>{item.prodDesc ?? '-'}</TableCell>
                <TableCell>{item.totalQtyConsidered ?? '-'}</TableCell>
                <TableCell>{item.totalItemQtyShortfall ?? '-'}</TableCell>
                <TableCell>
                  {formatAmount(item.totalFobVal as number) ?? '-'}
                </TableCell>
                <TableCell>{`${item.systemDbkRate}%`}</TableCell>
                <TableCell>{item.systemDbkAmt ?? '-'}</TableCell>
                <TableCell>{`${item.govDbkRate}%`}</TableCell>
                <TableCell>
                  {formatAmount(item.govDbkAmt as number) ?? '-'}
                </TableCell>
                <TableCell
                  className={
                    item.qualifyingStatus === 'QUALIFIED'
                      ? 'cell-success'
                      : 'cell-error'
                  }>
                  {`${showValue(item.qualifyingStatus as string)} ${getReason(
                    item.disqualificationReason as string
                  )}`}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable
            colSpan={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER.length}
          />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchQuery}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={finalSummaryTableData as []}
        renderData={finalSummaryTableData as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default FinalSummary;
