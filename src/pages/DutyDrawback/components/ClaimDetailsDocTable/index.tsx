import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {SupportedFileTypes} from '@common/constants';
import {UtcToIstFormat, downloadFile} from '@common/helpers';
import {ISupportedDoc} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {downloadSupportedDoc, getSupportedDoc} from '@pages/DutyDrawback/api';
import {
  CLAIM_DETAILS_SUPPORTING_DOC_SEARCH_DROPDOWN,
  CLAIM_DETAILS_SUPPORTING_DOC_TABLE_HEADER,
} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {memo, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import UploadDocModalContent from '../UploadDocModalContent';
import './index.scss';

interface IClaimDetailsDocProps {
  claimId: string;
}

function ClaimDetailsDocTable({claimId}: IClaimDetailsDocProps) {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [isOpenUpload, setIsOpenUpload] = useState<boolean>(false);
  const [supportedDocData, setSupportedDocData] = useState<ISupportedDoc[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleDownloadDocument = async (fileId: string) => {
    const payload = {
      pan: panNumber,
      email,
      suppDocFileId: fileId,
      claimTxnId: claimId,
    };
    const {data} = await downloadSupportedDoc(payload);
    downloadFile(data['file-data'], data['file-name'], SupportedFileTypes.ANY);
  };

  const getSupportedDocDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId: claimId,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getSupportedDoc(payload, page, +showEntries);
    setTotalRecords(data?.['supporting-doc-dtls']?.['total-records'] || 0);
    setSupportedDocData(
      data?.['supporting-doc-dtls']?.['supporting-doc-list'] || []
    );
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    email,
    panNumber,
    claimId,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getSupportedDocDetails();
  }, [getSupportedDocDetails]);

  return (
    <EximPaper>
      <div className='claim-details-doc-table-wrapper'>
        <div className='table-title'>
          <EximTypography variant='h3' fontWeight='semi-bold'>
            Supporting Documents
          </EximTypography>
          <EximButton size='small' onClick={() => setIsOpenUpload(true)}>
            Upload
          </EximButton>
        </div>

        <TableSearchFilter
          isInputDisabled={!searchKey}
          handleSearchQuery={handleSearchQuery}
          handleShowEntries={handleShowEntries}>
          <EximCustomDropdown
            placeholder='Search By Column'
            onSelect={({value}) => handleSearchKey(value)}
            dataTestId='column-dropdown'
            optionsList={CLAIM_DETAILS_SUPPORTING_DOC_SEARCH_DROPDOWN}
          />
        </TableSearchFilter>
        <table className='claim-details-doc-table'>
          <TableHeader
            mainHeader={CLAIM_DETAILS_SUPPORTING_DOC_TABLE_HEADER}
            handleSortBy={handleSortBy}
          />
          {supportedDocData.length > 0 ? (
            <TableBody className='claim-details-doc-tbody'>
              {supportedDocData.map((item) => (
                <TableRow key={item.fileUploadTxnId}>
                  <TableCell>{item.fileName}</TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell>{UtcToIstFormat(item.lastUploadedDate)}</TableCell>
                  <TableCell>{item?.lastUploadedBy}</TableCell>
                  <TableCell>{item?.status}</TableCell>
                  <TableCell>
                    <TableActions
                      isDownloadIcon
                      downloadToolTipText='Download File'
                      handleDownload={() =>
                        handleDownloadDocument(item.fileUploadTxnId)
                      }
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable
              colSpan={CLAIM_DETAILS_SUPPORTING_DOC_TABLE_HEADER.length}
            />
          )}
        </table>
        <TableFooter
          page={page}
          searchQuery={searchValue}
          showEntries={showEntries}
          totalRecords={totalRecords}
          searchData={supportedDocData as []}
          renderData={supportedDocData as []}
          handlePageChange={handlePageChange}
          hasBackendPagination
        />
      </div>

      {/* Upload Documents Modal */}
      <div className='upload-doc-modal'>
        <EximModal
          isOpen={isOpenUpload}
          onClose={() => setIsOpenUpload(false)}
          onOutSideClickClose={() => setIsOpenUpload(false)}
          content={
            <UploadDocModalContent
              claimId={claimId}
              onClose={() => setIsOpenUpload(false)}
              getSupportedDoc={getSupportedDocDetails}
            />
          }
          footer={false}
          header={
            <EximTypography
              classNames='upload-doc-modal-title'
              fontWeight='semi-bold'>
              Upload multiple files
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>
    </EximPaper>
  );
}

export default memo(ClaimDetailsDocTable);
