import TableHeader from '@common/components/TableHeader';
import {SupportedFileTypes} from '@common/constants';
import {downloadFile} from '@common/helpers';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {downloadFileData} from '@pages/DutyDrawback/api';
import {
  CLAIM_REPORTS_FILE_ARRAY,
  DOWNLOAD_REPORTS_MODAL_TABLE_HEADER,
} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import {RootState} from '@store';
import {useMemo} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IDownloadFileProps {
  claimId: string;
  isQualificationError: boolean;
}

export default function DownloadFilesModalContent({
  claimId,
  isQualificationError,
}: IDownloadFileProps) {
  const REPORTS_FILE_ARRAY = useMemo(
    () => CLAIM_REPORTS_FILE_ARRAY(isQualificationError),
    [isQualificationError]
  );

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const handleDownloadReport = async (type: string) => {
    const payload = {
      pan: panNumber,
      email,
      txnId: claimId,
      reportType: type,
    };
    const {data} = await downloadFileData(payload);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  return (
    <div className='download-file-table-container'>
      <table className='download-file-table'>
        <TableHeader mainHeader={DOWNLOAD_REPORTS_MODAL_TABLE_HEADER} />
        <TableBody className='download-file-tbody'>
          {REPORTS_FILE_ARRAY.map((file) => (
            <TableRow key={file.id}>
              <TableCell>{file.title}</TableCell>
              <TableCell className='download-btn'>
                <EximButton
                  size='small'
                  disabled={file.disabled}
                  color={file.id === '1' ? 'primary' : 'tertiary'}
                  onClick={() => handleDownloadReport(file.type)}>
                  Download
                </EximButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </table>
    </div>
  );
}
