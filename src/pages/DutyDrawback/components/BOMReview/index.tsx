import EmptyTable from '@common/components/EmptyTable';
import Stripe from '@common/components/Stripe';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  DBK_CLAIM_REPORT_TYPE,
  Path,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {downloadFile, formatAmount} from '@common/helpers';
import {IBomReviews, IListWithoutBom} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  downloadFileData,
  getBomReviews,
  saveBomReview,
} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import {
  BOM_REVIEW_TABLE_HEADER,
  BOM_REVIEW_TABLE_SEARCH_DROPDOWN,
  MISSING_BOM_REVIEW_TABLE_HEADER,
} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function BOMReview() {
  const navigate = useNavigate();
  const {
    DUTY_DRAWBACK,
    DBK_CLAIM,
    BOE_SUMMARY,
    BOM_REVIEW,
    UPLOAD_PROCESS,
    VIEW_DETAILS,
  } = Path;

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [bomReviewsData, setBomReviewsData] = useState<IBomReviews[]>([]);
  const [missingBomList, setMissingBomList] = useState<IListWithoutBom[]>([]);
  const [isOpenModal, setIsOpenModal] = useState(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleDownload = async () => {
    const headers = {
      pan: panNumber,
      email,
      txnId: claimTxnId,
      reportType: DBK_CLAIM_REPORT_TYPE.BOM_SELECTION_REPORT,
    };
    const {data} = await downloadFileData(headers);
    downloadFile(
      data['file-data'],
      data['file-name'],
      SupportedFileTypes.EXCEL
    );
  };

  const handleNextBtn = async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId,
    };
    const data = await saveBomReview(payload);
    if (data.status.toString() === ResponseStatus.SUCCESS) {
      navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${BOE_SUMMARY}`);
    }
  };

  const handleViewReview = (prodCode: string, bomVersion: string) => {
    const version = bomVersion.split('-')[1];
    navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${BOM_REVIEW}${VIEW_DETAILS}`, {
      state: {prodCode, bomVersion: version},
    });
  };

  const getReviewsData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getBomReviews(payload, page, +showEntries);
    setTotalRecords(data?.['total-records']);
    setBomReviewsData(data['bom-list']);
    if (data?.['prod-list-without-bom']) {
      setMissingBomList(data?.['prod-list-without-bom']);
    } else {
      setMissingBomList([]);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    email,
    claimTxnId,
    panNumber,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getReviewsData();
  }, [getReviewsData]);

  return (
    <div className='bom-review-container'>
      <DbkClaimSubHeader step='2' subTitle='Bill of Materials (BOM) - Review'>
        <>
          <EximButton size='small' color='secondary' onClick={handleDownload}>
            Download
          </EximButton>
          <EximButton
            size='small'
            onClick={handleNextBtn}
            disabled={missingBomList.length > 0}>
            Save & Next
          </EximButton>
        </>
      </DbkClaimSubHeader>
      <Stripe
        content='This Page displays the list of BOMs for the selected products'
        variant='info'
      />
      {missingBomList.length > 0 ? (
        <Stripe
          content={
            <span>
              <strong>Validation Error!</strong> : BOM for one or more selected
              products is unavailable. Please upload the valid BOM to Proceed.
            </span>
          }
          variant='primary'
          isBtn
          btnText='View Details'
          onBtnClick={() => setIsOpenModal(true)}
        />
      ) : null}
      <EximPaper>
        <div className='bom-review-table-container'>
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => handleSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={BOM_REVIEW_TABLE_SEARCH_DROPDOWN}
            />
          </TableSearchFilter>
          <table className='bom-review-table'>
            <TableHeader
              mainHeader={BOM_REVIEW_TABLE_HEADER}
              handleSortBy={handleSortBy}
            />
            {bomReviewsData?.length > 0 ? (
              <TableBody className='bom-review-tbody'>
                {bomReviewsData?.map((item: IBomReviews, index) => (
                  <TableRow key={`${item['ref-id']}${index + 1}`}>
                    <TableCell>{item['prod-code']}</TableCell>
                    <TableCell>{item['prod-desc']}</TableCell>
                    <TableCell>{formatAmount(item['total-items'])}</TableCell>
                    <TableCell>{item['bom-version']}</TableCell>
                    <TableCell>
                      <TableActions
                        isViewIcon
                        viewToolTipText='View Details'
                        handleView={() =>
                          handleViewReview(
                            item['prod-code'],
                            item['bom-version']
                          )
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={BOM_REVIEW_TABLE_HEADER.length} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={bomReviewsData as []}
            renderData={bomReviewsData as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>

      {/* Missing BOM files Modal */}
      <div className='missing-bom-modal'>
        <EximModal
          isOpen={isOpenModal}
          onClose={() => setIsOpenModal(false)}
          onOutSideClickClose={() => setIsOpenModal(false)}
          content={
            <div className='missing-bom-container'>
              <table>
                <TableHeader mainHeader={MISSING_BOM_REVIEW_TABLE_HEADER} />
                <TableBody className='missing-bom-table-body'>
                  {missingBomList.map((item, index) => (
                    <TableRow key={`missingBom${index + 1}`}>
                      <TableCell>{index + 1}</TableCell>
                      <TableCell>{item['prod-code']}</TableCell>
                      <TableCell>{item['prod-desc']}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </table>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsOpenModal(false)}>
                  No
                </EximButton>
                <EximButton
                  size='small'
                  onClick={() =>
                    navigate(`${DUTY_DRAWBACK}${UPLOAD_PROCESS}/bom`)
                  }>
                  Upload
                </EximButton>
              </span>
            </div>
          }
          footer={false}
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Missing BOM Files
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
        />
      </div>
    </div>
  );
}

export default BOMReview;
