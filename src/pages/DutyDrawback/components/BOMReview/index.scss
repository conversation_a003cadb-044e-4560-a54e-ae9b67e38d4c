@import '@utils/main.scss';

.bom-review-container {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
    @include margin(0);
  }
  .bom-review-table-container {
    @include padding(20px);
    @include margin(15px auto 32px);
    .bom-review-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
    }

    .table-search-container,
    .table-footer {
      @include padding(10px auto);
    }
  }

  .children-container {
    @include flex-item(_, center, center, _, 16px);
  }

  .file-input-container {
    width: 100px;
    .file-button {
      background-color: $primary;
      color: $white;
      height: 32px;
    }
  }

  // Missing BOM Files Modal Style
  .missing-bom-modal {
    letter-spacing: 0.2px;
    .modal-title h2 {
      color: $secondary-text;
    }
    .modal-body {
      width: 500px;
      max-height: 80%;
      overflow-y: auto;
      .modal-header {
        align-items: flex-start;
        @include padding(32px 40px 22px 40px);
      }
      .modal-content {
        @include padding(0px 40px 32px 40px);
        .missing-bom-container {
          width: 100%;
          table {
            width: 100%;
            border-spacing: 0;
          }
          .btn-container {
            @include flex-item(_, flex-end, center, _, 16px);
            @include margin-top(20px);
            .button-wrapper {
              min-width: 100px;
              .base-btn {
                height: 32px;
                font-size: $font-size-sm;
                @include padding(7px 16px);
              }
            }
          }
        }
      }
    }
  }
}
