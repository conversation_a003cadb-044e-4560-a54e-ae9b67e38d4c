import EmptyTable from '@common/components/EmptyTable';
import TableActions from '@common/components/TableActions';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  DDFileType,
  DUTY_DRAWBACK_FILE_TYPE,
  Path,
  ResponseStatus,
} from '@common/constants';
import {getWordFromUrl} from '@common/helpers';
import {IBOMListTable, ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {deleteInvoices, getInvoices} from '@pages/DutyDrawback/api';
import {
  BOM_LIST_TABLE_HEADER,
  BOM_LIST_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState, dispatch} from '@store';
import {memo, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

import './index.scss';

const {DUTY_DRAWBACK, VIEW_INVOICES, VIEW_DETAILS} = Path;

interface IBomTable {
  isValidRecord: boolean;
  getInvoicesSummary: () => void;
  setBomRefIdList: (value: string[]) => void;
  isDeleteAllClicked: boolean;
}

function BOMTable({
  isValidRecord,
  getInvoicesSummary,
  setBomRefIdList,
  isDeleteAllClicked,
}: IBomTable) {
  const navigate = useNavigate();
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1).toUpperCase();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {invoiceTxnId, panNumber, isLastTransactionInvalid},
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState(0);
  const [BOMList, setBOMList] = useState<IBOMListTable[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getBOMList = useCallback(async () => {
    const payload = {
      txnId: '',
      pan: panNumber,
      email,
      startPeriod: '',
      endPeriod: '',
      invType: isValidRecord ? 'VALID' : 'INVALID',
      fileType: DUTY_DRAWBACK_FILE_TYPE[fileType?.toUpperCase() as DDFileType],
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    // INFO: If last transaction has invalid records the we need to fetch the data using txnId
    if (isLastTransactionInvalid) {
      payload.txnId = invoiceTxnId;
    }
    const {data} = await getInvoices(payload, page, +showEntries);
    setBOMList(data?.records);
    setTotalRecords(data?.['total-records']);
    // INFO: Adding the invalid records refId to delete the all invalid records
    if (!isValidRecord) {
      const selectedIds = data?.records?.map(
        (obj: IBOMListTable) => obj['bom-ref-id']
      );
      setBomRefIdList(selectedIds);
    } else {
      setBomRefIdList([]);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    invoiceTxnId,
    panNumber,
    email,
    isValidRecord,
    fileType,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  const handleViewLog = (prodCode: string, bomVersion: string) => {
    const version = bomVersion.split('-')[1];
    navigate(
      `${DUTY_DRAWBACK}${VIEW_INVOICES}/${fileType.toLowerCase()}${VIEW_DETAILS}`,
      {state: {prodCode, bomVersion: version}}
    );
  };

  const handleDeleteLog = async (id: string) => {
    const payload = {
      pan: panNumber,
      fileType: DUTY_DRAWBACK_FILE_TYPE.BOM,
      email,
      txnId: invoiceTxnId,
    };
    const response = (await deleteInvoices(payload, false, [
      id,
    ])) as ICustomAxiosResp;
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          code: 200,
          message: response?.msg,
          alertType: AlertStatus.SUCCESS,
        })
      );
      getBOMList(); // Updating the data after delete record
      getInvoicesSummary(); // Updating the records count after delete record
    }
  };

  useEffect(() => {
    getBOMList();
  }, [getBOMList, isDeleteAllClicked]);

  return (
    <div className='bom-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={BOM_LIST_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='bom-list-table'>
        <TableHeader
          mainHeader={BOM_LIST_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {BOMList?.length > 0 ? (
          <TableBody className='bom-list-tbody'>
            {BOMList?.map((item: IBOMListTable, index: number) => (
              <TableRow key={`${item['bom-ref-id']}${index + 1}`}>
                <TableCell>{item?.['prod-code']?.value}</TableCell>
                <TableCell>{item?.['prod-desc']?.value}</TableCell>
                <TableCell>{item?.['total-items']}</TableCell>
                <TableCell>{item?.['bom-version']}</TableCell>
                <TableCell>
                  <TableActions
                    isViewIcon
                    isDeleteIcon={!isValidRecord}
                    viewToolTipText='View Invoice'
                    deleteToolTipText='Delete Invoice'
                    handleView={() =>
                      handleViewLog(
                        item['prod-code'].value.toString(),
                        item['bom-version']
                      )
                    }
                    handleDelete={() => handleDeleteLog(item['bom-ref-id'])}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={BOM_LIST_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={BOMList as []}
        renderData={BOMList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default memo(BOMTable);
