@import '@utils/main.scss';

.sb-view-details {
  letter-spacing: 0.4px;
  @include margin-top(20px);
  .product-details-container {
    @include padding(21px 27px);
    .product-details-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(425px, 1fr));
      gap: 50px;
      row-gap: 24px;
      @include margin-top(18px);
      .product-details {
        @include flex-item(column, _, _);
        height: 60px;
        .product-details-title {
          font-weight: $font-weight-bold;
          font-size: $font-size-sm;
          @include margin-bottom(5px);
        }
        .product-details-value {
          color: $text-mute;
          font-size: $font-size-sm;
        }
        .input-container {
          max-width: 250px;
          min-width: 250px;
          label {
            color: $text-color;
            font-weight: $font-weight-bold;
          }
          .form-input.disabled {
            input {
              @include margin-left(-10px);
              @include margin-top(-16px);
              font-size: $font-size-sm;
              color: $label-color;
              border: none;
              outline: none;
            }
          }
        }
        .base-date-picker {
          input {
            width: 212px;
            background: none;
          }
        }

        // Dropdown Style
        .select-dropdown {
          width: 250px;
          .dropdown-label {
            font-size: $font-size-sm;
            font-weight: $font-weight-semi-bold;
            letter-spacing: 0.4px;
            @include margin-bottom(7px);
          }
          .custom-dropdown {
            background: none;
          }
          .error-message {
            display: none;
          }
        }
        // INFO: ToolTip text customization
        .display-inline {
          width: max-content;
        }
        .date-picker-tooltip {
          .tooltip-wrapper {
            .tooltip-top.secondary {
              bottom: 36px;
            }
          }
        }
        .input-tooltip-text {
          @include margin-top(26px);
        }
        .tooltip-wrapper {
          .tooltip-top.secondary {
            justify-content: flex-start;
            width: 275px;
            bottom: 34px;
            left: 138px;
            .content-tag {
              width: max-content;
              @include padding(1px 4px);
            }
          }
          .tip-top.secondary {
            display: none;
          }
        }
      }
    }
  }

  .divider hr {
    @include margin(0);
  }

  .view-details-table-container {
    @include padding(21px 27px);
    .sb-view-details-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .editable-td {
        .tooltip-wrapper {
          .tooltip-top.secondary {
            justify-content: flex-start;
            position: absolute;
            width: 260px;
            left: 130px;
            bottom: 34px;
            .content-tag {
              width: max-content;
              @include padding(1px 4px);
            }
          }
          .tip-top.secondary {
            display: none;
          }
        }
      }
      .form-input.disabled {
        input {
          @include margin-left(-10px);
          font-size: $font-size-sm;
          color: $text-color;
          border: none;
          outline: none;
        }
      }
    }
  }
}
