import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  DUTY_DRAWBACK_FILE_TYPE,
  EximHeroDate,
  ResponseStatus,
  applicantTypes,
} from '@common/constants';
import {formatDate, selectedOptionId} from '@common/helpers';
import {
  ICustomAxiosResp,
  IExportViewAllDetails,
  IExportViewInvoices,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getSbDetails, saveViewInvoiceDetails} from '@pages/DutyDrawback/api';
import {
  APPLICANT_DROPDOWN,
  EXPORT_DOMESTIC_SALES_DETAILS_TABLE_HEADER,
  VIEW_EXPORT_SALES_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import {
  exportInvoiceValidation,
  hasInvalidProductInArray,
  hasInvalidValues,
  validateExportConditionalMandatoryFields,
} from '@pages/DutyDrawback/utils/exportEditInvoiceValidation';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate, useParams} from 'react-router';

import './index.scss';

interface IExportViewDetailsProps {
  isViewValidRecord: boolean;
  isEditable: boolean;
}

function ExportDomesticViewDetails({
  isViewValidRecord,
  isEditable,
}: IExportViewDetailsProps) {
  const navigate = useNavigate();
  const {id} = useParams();
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const initialExportInvoices: IExportViewAllDetails = {
    'sb-prod-list': [],
    selected: false,
    'sb-inv-no': {value: '', isValid: true, errorMessage: ''},
    'sb-inv-date': {value: '', isValid: true, errorMessage: ''},
    'inv-val': {value: 0, isValid: true, errorMessage: ''},
    'shipping-bill-No': {value: '', isValid: true, errorMessage: ''},
    'shipping-bill-date': {value: '', isValid: true, errorMessage: ''},
    'total-fob-value': {value: 0, isValid: true, errorMessage: ''},
    'leo-date': {value: '', isValid: true, errorMessage: ''},
    'applicant-type': {value: '', isValid: true, errorMessage: ''},
    'iec-code': {value: '', isValid: true, errorMessage: ''},
    'port-of-export': {value: '', isValid: true, errorMessage: ''},
    'sb-ref-id': '',
  };

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(isEditable);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [viewDetailsAllData, setViewDetailsAllData] =
    useState<IExportViewAllDetails>(initialExportInvoices);
  const [viewDetailsData, setViewDetailsData] = useState<IExportViewInvoices[]>(
    []
  );

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleChangeItemValue = (
    event: ChangeEvent<HTMLInputElement>,
    index: number,
    key: keyof IExportViewInvoices
  ) => {
    setViewDetailsData((prevItem: IExportViewInvoices[]) => {
      const updatedItem = [...prevItem];
      const message = exportInvoiceValidation(key, event.target.value);
      // Update the specific object at the given index and key
      (updatedItem[index][key] as {value: string}).value = event.target.value;
      (updatedItem[index][key] as {isValid: boolean}).isValid = message === '';
      (updatedItem[index][key] as {errorMessage: string}).errorMessage =
        message;

      return updatedItem;
    });
  };

  const formik = useFormik({
    initialValues: viewDetailsAllData,
    onSubmit: async (values: IExportViewAllDetails) => {
      setIsEdit(true);
      if (
        isEdit &&
        (hasInvalidValues(values) ||
          hasInvalidProductInArray(values['sb-prod-list']))
      ) {
        dispatch(
          alertActions.setAlertMsg({
            code: 200,
            message: 'Records are not valid',
            alertType: AlertStatus.DANGER,
          })
        );
      } else if (isEdit) {
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          fileType: DUTY_DRAWBACK_FILE_TYPE.SB,
        };
        const body = {
          ...values,
          'sb-prod-list': viewDetailsData,
        };
        const response = (await saveViewInvoiceDetails(
          payload,
          body
        )) as ICustomAxiosResp;
        if (response?.status?.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(false);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(-1);
        }
        if (response?.status?.toString() === ResponseStatus.ERROR) {
          setViewDetailsAllData(response?.data);
          setViewDetailsData(response?.data?.['sb-prod-list']);
          setTotalRecords(response?.data?.['total-records']);
          formik.setValues(response?.data);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: 'Records are not valid',
              alertType: AlertStatus.DANGER,
            })
          );
        }
      }
    },
  });

  // set dropdown selected item data
  const onSelect = (key: keyof IExportViewAllDetails, value: string) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updatedValues: any = {...formik.values};
    // Update the specific key with the new value
    updatedValues[key] = {
      value: value?.trim(),
      isValid: true,
      errorMessage: '',
    };

    // INFO: validate the conditional mandatory fields
    validateExportConditionalMandatoryFields(
      viewDetailsAllData,
      value,
      updatedValues
    );

    // Update the formik values
    formik.setValues(updatedValues);
    setViewDetailsAllData(updatedValues);
  };

  // Access onChange values in the respective object
  const handleChange = (key: keyof IExportViewAllDetails, value: string) => {
    let date = null;
    if (key.includes('date')) {
      date = value?.replaceAll('/', '-');
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updatedValues: any = {...formik.values};

    const exporterMandatoryFields = [
      'iec-code',
      'shipping-bill-No',
      'shipping-bill-date',
      'leo-date',
    ];
    const intermediaryMandatoryFields = ['sb-inv-no', 'sb-inv-date'];

    // Update the specific key with the new value
    const isExporter =
      viewDetailsAllData?.['applicant-type']?.value === applicantTypes.EXPORTER;
    const isIntermediary =
      viewDetailsAllData?.['applicant-type']?.value ===
      applicantTypes.INTERMEDIARY;
    if (
      exporterMandatoryFields.includes(key) &&
      value.length === 0 &&
      isExporter
    ) {
      updatedValues[key] = {
        value,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    } else if (
      intermediaryMandatoryFields.includes(key) &&
      value.length === 0 &&
      isIntermediary
    ) {
      updatedValues[key] = {
        value,
        isValid: false,
        errorMessage: 'This field is required.',
      };
    } else {
      const message = exportInvoiceValidation(key, value);
      updatedValues[key] = {
        value: !date ? value : date,
        isValid: message === '',
        errorMessage: message,
      };
    }

    // Update the formik values
    formik.setValues(updatedValues);
    setViewDetailsAllData(updatedValues);
  };

  const getSbViewDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      sbRefID: id,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getSbDetails(payload, page, +showEntries);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setViewDetailsAllData(response?.data);
      setViewDetailsData(response?.data?.['sb-prod-list'] || []);
      setTotalRecords(response?.data?.['total-records'] || 0);
      formik.setValues(response?.data);
      setIsLoading(false);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    id,
    panNumber,
    page,
    showEntries,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getSbViewDetails();
  }, [getSbViewDetails]);

  const getDateValue = (
    formikVal: string,
    key: keyof IExportViewAllDetails
  ) => {
    const apiResVal = (
      viewDetailsAllData[key] as {value: string}
    )?.value?.toString();
    if (formikVal && formikVal.length > 4) {
      return formikVal?.replaceAll('-', '/');
    }
    if (apiResVal) {
      return apiResVal?.replaceAll('-', '/');
    }
    return undefined;
  };

  return isLoading ? null : (
    <form onSubmit={formik.handleSubmit}>
      <BusinessHeader>
        <div className='btn-container'>
          {/* INFO: If we are viewing valid record than no need to show edit button */}
          {!isViewValidRecord ? (
            <EximButton size='small' type='submit'>
              {`${isEdit ? 'Save' : 'Edit'}`}
            </EximButton>
          ) : null}
        </div>
      </BusinessHeader>
      <EximPaper>
        <div className='sb-view-details'>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Product Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit ? formik.values?.['iec-code']?.errorMessage : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='IEC Code'
                    id='iec-code'
                    name='iec-code'
                    dataTestid='iec-code'
                    maxLength={10}
                    value={formik.values?.['iec-code']?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['iec-code']?.isValid}
                    onChange={(event) =>
                      handleChange('iec-code', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit ? formik.values?.['sb-inv-no']?.errorMessage : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Invoice No'
                    id='sb-inv-no'
                    name='sb-inv-no'
                    dataTestid='sb-inv-no'
                    maxLength={20}
                    value={formik.values?.['sb-inv-no']?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['sb-inv-no']?.isValid}
                    onChange={(event) =>
                      handleChange('sb-inv-no', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['shipping-bill-No']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Shipping Bill No'
                    id='shipping-bill-No'
                    name='shipping-bill-No'
                    dataTestid='shipping-bill-No'
                    maxLength={20}
                    value={formik.values?.[
                      'shipping-bill-No'
                    ]?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['shipping-bill-No']?.isValid}
                    onChange={(event) =>
                      handleChange('shipping-bill-No', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>

              <div className='product-details'>
                {isEdit ? (
                  <EximTooltip
                    content={formik.values?.['applicant-type']?.errorMessage}
                    direction='top'
                    variant='secondary'>
                    <EximCustomDropdown
                      label='Applicant type'
                      optionsList={APPLICANT_DROPDOWN}
                      placeholder='Please Select Applicant'
                      dataTestId='applicant-type'
                      isInvalid={!formik.values?.['applicant-type']?.isValid}
                      onSelect={({value}) => onSelect('applicant-type', value)}
                      readOnly={!isEdit}
                      defaultOption={selectedOptionId(
                        APPLICANT_DROPDOWN,
                        formik.values?.['applicant-type']?.value?.toString() ||
                          ''
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <>
                    <div className='product-details-title'>Applicant type</div>
                    <div className='product-details-value'>
                      {formik.values?.['applicant-type']?.value}
                    </div>
                  </>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Invoice Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={formik.values?.['sb-inv-date']?.errorMessage}
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='sb-inv-date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!formik.values?.['sb-inv-date']?.isValid}
                      onChange={(value) => handleChange('sb-inv-date', value)}
                      defaultValue={getDateValue(
                        formik.values?.['sb-inv-date']?.value?.toString(),
                        'sb-inv-date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.['sb-inv-date']?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Shipping Bill Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={
                      formik.values?.['shipping-bill-date']?.errorMessage
                    }
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='shipping-bill-date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={
                        !formik.values?.['shipping-bill-date']?.isValid
                      }
                      onChange={(value) =>
                        handleChange('shipping-bill-date', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.[
                          'shipping-bill-date'
                        ]?.value?.toString(),
                        'shipping-bill-date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.[
                        'shipping-bill-date'
                      ]?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <div className='product-details-title'>LEO date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={formik.values?.['leo-date']?.errorMessage}
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='leo-date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!formik.values?.['leo-date']?.isValid}
                      onChange={(value) => handleChange('leo-date', value)}
                      defaultValue={getDateValue(
                        formik.values?.['leo-date']?.value?.toString(),
                        'leo-date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.['leo-date']?.value?.toString() || ''
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit ? formik.values?.['inv-val']?.errorMessage : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Invoice Value (excluding Tax)'
                    id='inv-val'
                    name='inv-val'
                    dataTestid='inv-val'
                    maxLength={18}
                    value={formik.values?.['inv-val']?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['inv-val']?.isValid}
                    onChange={(event) =>
                      handleChange('inv-val', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['port-of-export']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Port of Export'
                    id='port-of-export'
                    name='port-of-export'
                    dataTestid='port-of-export'
                    maxLength={20}
                    value={formik.values?.['port-of-export']?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['port-of-export']?.isValid}
                    onChange={(event) =>
                      handleChange('port-of-export', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['total-fob-value']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='FOB Value as per Invoice Rs.'
                    id='total-fob-value'
                    name='total-fob-value'
                    dataTestid='total-fob-value'
                    maxLength={18}
                    value={formik.values?.[
                      'total-fob-value'
                    ]?.value?.toString()}
                    disabled={!isEdit}
                    isInvalid={!formik.values?.['total-fob-value']?.isValid}
                    onChange={(event) =>
                      handleChange('total-fob-value', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
            </div>
          </div>
          <EximDivider type='dashed' />
          <div className='view-details-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleShowEntries={handleShowEntries}
              handleSearchQuery={handleSearchQuery}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={VIEW_EXPORT_SALES_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='sb-view-details-table'>
              <TableHeader
                mainHeader={EXPORT_DOMESTIC_SALES_DETAILS_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {viewDetailsData.length > 0 ? (
                <TableBody className='sb-view-list-table-body'>
                  {viewDetailsData?.map(
                    (item: IExportViewInvoices, index: number) => {
                      return (
                        <TableRow key={`exportViewInvoice${index + 1}`}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['sb-prod-code']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='sb-prod-code'
                                name='sb-prod-code'
                                dataTestid='sb-prod-code'
                                maxLength={20}
                                disabled={!isEdit}
                                value={item['sb-prod-code']?.value?.toString()}
                                isInvalid={!item['sb-prod-code']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'sb-prod-code'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['sb-prod-desc']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='sb-prod-desc'
                                name='sb-prod-desc'
                                dataTestid='sb-prod-desc'
                                maxLength={200}
                                disabled={!isEdit}
                                value={item['sb-prod-desc']?.value?.toString()}
                                isInvalid={!item['sb-prod-desc']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'sb-prod-desc'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['total-qty-exported']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='total-qty-exported'
                                name='total-qty-exported'
                                dataTestid='total-qty-exported'
                                maxLength={17}
                                disabled={!isEdit}
                                value={item[
                                  'total-qty-exported'
                                ]?.value?.toString()}
                                isInvalid={!item['total-qty-exported']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'total-qty-exported'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['qty-considered']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='qty-considered'
                                name='qty-considered'
                                dataTestid='qty-considered'
                                maxLength={17}
                                disabled={!isEdit}
                                value={item[
                                  'qty-considered'
                                ]?.value?.toString()}
                                isInvalid={!item['qty-considered']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'qty-considered'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit ? item['sb-uqc']?.errorMessage : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='sb-uqc'
                                name='sb-uqc'
                                dataTestid='sb-uqc'
                                maxLength={15}
                                disabled={!isEdit}
                                value={item['sb-uqc']?.value?.toString()}
                                isInvalid={!item['sb-uqc']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(event, index, 'sb-uqc')
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['total-available-qty']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='total-available-qty'
                                name='total-available-qty'
                                dataTestid='total-available-qty'
                                maxLength={17}
                                disabled={!isEdit}
                                value={item[
                                  'total-available-qty'
                                ]?.value?.toString()}
                                isInvalid={
                                  !item['total-available-qty']?.isValid
                                }
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'total-available-qty'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit ? item['fob-val']?.errorMessage : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='fob-val'
                                name='fob-val'
                                dataTestid='fob-val'
                                maxLength={18}
                                disabled={!isEdit}
                                value={item['fob-val']?.value?.toString()}
                                isInvalid={!item['fob-val']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(event, index, 'fob-val')
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                        </TableRow>
                      );
                    }
                  )}
                </TableBody>
              ) : (
                <EmptyTable
                  colSpan={EXPORT_DOMESTIC_SALES_DETAILS_TABLE_HEADER.length}
                />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={viewDetailsData as []}
              renderData={viewDetailsData as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </div>
      </EximPaper>
    </form>
  );
}

export default ExportDomesticViewDetails;
