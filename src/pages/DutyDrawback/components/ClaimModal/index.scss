@import '@utils/main.scss';

.claim-modal {
  width: 100%;

  &-part {
    @include flex-item(column, flex-start, flex-start, nowrap, 10px);
    @include margin(20px 0);

    &-radios {
      @include flex-item(row, flex-end, flex-end, nowrap, 5px);
      color: $text-color;
      .containers {
        font-size: $base-font-size;
        input:checked ~ .checkmark {
          background-color: $success;
        }
        .checkmark {
          top: 0.4px;
          &::after {
            top: 5px;
            left: 5.5px;
            width: 6px;
            height: 6px;
          }
        }
      }
    }

    .custom-base-date-picker {
      width: 100%;
    }
  }

  &-actions {
    @include flex-item(row, flex-end, flex-end, nowrap, 10px);
    &-cancel,
    &-submit {
      width: 100px;
    }

    .base-btn {
      height: 32px;
      font-size: $base-font-size;
    }
  }
}
