import {
  APPLICANT_TYPE,
  EximHeroDate,
  Path,
  REGEXP,
  ResponseStatus,
  applicantTypes,
} from '@common/constants';
import {getValue} from '@common/helpers';
import {postClaim} from '@pages/DutyDrawback/api';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import EximButton from '@shared/components/EximButton';
import EximInput from '@shared/components/EximInput';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximRadioButton from '@shared/components/EximRadioButton';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {ChangeEvent, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

interface IClaimModalContent {
  onClose: () => void;
}

function ClaimModalContent(props: IClaimModalContent) {
  const {onClose} = props;

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      getSbListFunc,
      panNumber,
      dbkClaim: {
        applicantType: type,
        claimTitle: title,
        startPeriod: start,
        endPeriod: end,
        isClaimExist,
        claimTxnId: claimId,
      },
    },
  } = useSelector((state: RootState) => state);

  const navigate = useNavigate();
  const [startPeriod, setStartPeriod] = useState<string>(start || '');
  const [endPeriod, setEndPeriod] = useState<string>(end || '');
  const [claimTitle, setClaimTitle] = useState<string>(title || '');
  const [errorMsg, setErrorMsg] = useState<string>('');
  const [applicantType, setApplicantType] = useState<string>(
    type || applicantTypes.EXPORTER
  );

  const handleChangeClaimTitle = (event: ChangeEvent<HTMLInputElement>) => {
    if (!REGEXP.alphanumeric.test(event.target.value)) {
      setErrorMsg('Special characters are not allowed.');
    } else {
      setErrorMsg('');
    }
    setClaimTitle(event.target.value);
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setApplicantType(event.target.value);
  };

  const handleSelectPeriod = (startDate: string, endDate: string) => {
    setStartPeriod(startDate);
    setEndPeriod(endDate);
  };

  const handleSubmit = async () => {
    const headers = {
      pan: panNumber,
      email,
      claimName: claimTitle,
      startPeriod,
      endPeriod,
      applicantType: getValue(applicantType, APPLICANT_TYPE)?.key || '',
      claimTxnId: isClaimExist ? claimId : '',
    };
    const res = await postClaim(headers);
    if (res.status.toString() === ResponseStatus.SUCCESS) {
      const dbkClaim = {
        claimTitle,
        startPeriod,
        endPeriod,
        applicantType: getValue(applicantType, APPLICANT_TYPE)?.value || '',
        claimTxnId: res.data['claim-txn-id'] || '',
        sbSelectTxnId: res.data['sb-product-select-txn-id'] || '',
        isClaimExist: true,
      };
      dispatch(dutyDrawbackActions.setDbkClaim(dbkClaim));
      if (!isClaimExist) {
        navigate(`${Path.DUTY_DRAWBACK}${Path.DBK_CLAIM}${Path.SB}`);
      } else {
        // INFO: Updating the sb list after edit the claim period
        getSbListFunc();
      }
      onClose(); // Closing the Modal
    }
  };

  return (
    <div className='claim-modal'>
      <div className='claim-modal-part'>
        <EximTypography fontWeight='semi-bold'>Claim Title</EximTypography>
        <EximInput
          id='claimTitle'
          maxLength={64}
          onChange={handleChangeClaimTitle}
          value={claimTitle}
          placeholder='Enter Claim Title'
          isInvalid={errorMsg !== ''}
          errorMessage={errorMsg}
        />
      </div>
      <div className='claim-modal-part'>
        <EximTypography fontWeight='semi-bold'>Period</EximTypography>
        <EximMonthRangePicker
          id='claim-date-picker'
          minDate={EximHeroDate.MIN_MONTH}
          onSelect={handleSelectPeriod}
          defaultEndDate={isClaimExist ? endPeriod.split('-').join('/') : ''}
          defaultStartDate={
            isClaimExist ? startPeriod.split('-').join('/') : ''
          }
        />
      </div>
      <div className='claim-modal-part'>
        <EximTypography fontWeight='semi-bold'>Applicant Type</EximTypography>
        <div className='claim-modal-part-radios'>
          <EximRadioButton
            id='Exporter'
            label='Exporter'
            value='Exporter'
            onChange={handleRadioChange}
            isSelected={applicantType === applicantTypes.EXPORTER}
          />
          <EximRadioButton
            id='Intermediary'
            label='Intermediary'
            onChange={handleRadioChange}
            value='Intermediary'
            isSelected={applicantType === applicantTypes.INTERMEDIARY}
            disabled
          />
        </div>
      </div>
      <div className='claim-modal-actions'>
        <div className='claim-modal-actions-cancel'>
          <EximButton color='secondary' onClick={() => onClose()}>
            Cancel
          </EximButton>
        </div>
        <div className='claim-modal-actions-submit'>
          <EximButton
            type='submit'
            onClick={handleSubmit}
            disabled={errorMsg.length > 0 || claimTitle === ''}>
            {isClaimExist ? 'Apply' : 'Create'}
          </EximButton>
        </div>
      </div>
    </div>
  );
}

export default ClaimModalContent;
