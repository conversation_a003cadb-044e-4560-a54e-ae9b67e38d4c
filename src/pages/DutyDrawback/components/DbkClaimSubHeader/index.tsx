import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {ReactNode} from 'react';

import './index.scss';

interface IDbkClaimSubHeader {
  step: string;
  children: ReactNode;
  subTitle: string;
  isSecondSub?: boolean;
  secondSubTitle?: string;
}

function DbkClaimSubHeader(props: IDbkClaimSubHeader) {
  const {step, children, subTitle, secondSubTitle, isSecondSub} = props;

  return (
    <div className='dbk-sub-header-container'>
      <EximPaper>
        <div className='dbk-sub-header'>
          <div className='title-part'>
            <EximTypography fontWeight='semi-bold'>{`Step ${step} of 4`}</EximTypography>
            <EximTypography> | </EximTypography>
            <EximTypography fontWeight='semi-bold'>{subTitle}</EximTypography>
            {isSecondSub && (
              <>
                <EximTypography> | </EximTypography>
                <EximTypography fontWeight='semi-bold'>
                  {secondSubTitle}
                </EximTypography>
              </>
            )}
          </div>
          <div className='action-buttons-wrapper'>{children}</div>
        </div>
      </EximPaper>
    </div>
  );
}

DbkClaimSubHeader.defaultProps = {
  secondSubTitle: '',
  isSecondSub: false,
};

export default DbkClaimSubHeader;
