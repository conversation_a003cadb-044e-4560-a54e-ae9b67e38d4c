@import '@utils/main.scss';

.dbk-sub-header-container {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
  }
}
.dbk-sub-header {
  height: 53px;
  @include flex-item(row, space-between, normal);
  .title-part {
    @include flex-item(row, normal, center, nowrap, 10px);
    @include margin(0px 18px);
    letter-spacing: 0.5px;
    .typography-container {
      color: $text-color;
    }
  }

  .action-buttons-wrapper {
    @include flex-item(row, normal, center, nowrap, 16px);
    @include margin-right(15px);
    .button-wrapper {
      width: 145px;
      .base-btn {
        @include padding(7px 0);
        font-size: $base-font-size;
      }
    }
  }
}
