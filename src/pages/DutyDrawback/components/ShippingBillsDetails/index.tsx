import AllFileSelectionStripe from '@common/components/AllFileSelectionStripe';
import Stripe from '@common/components/Stripe';
import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path, ResponseStatus, applicantTypes} from '@common/constants';
import {ISaveSbList, ISbListTableDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import {getDbkClaimSbList, saveSbList} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import ShippingBillsTable from '@pages/DutyDrawback/components/ShippingBillsTable';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import {SB_DETAILS_TABLE_SEARCH_DROPDOWN} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {RootState, dispatch} from '@store';
import {ChangeEvent, useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function ShippingBillsDetails() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId, applicantType},
    },
  } = useSelector((state: RootState) => state);

  const tableSearchDropdown = useMemo(
    () =>
      SB_DETAILS_TABLE_SEARCH_DROPDOWN(
        applicantType === applicantTypes.EXPORTER
      ),
    [applicantType]
  );

  const [page, setPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [sortBy, setSortBy] = useState<string>('');
  const [sortingOrder, setSortingOrder] = useState<1 | -1>(-1);
  const [allData, setAllData] = useState<ISaveSbList>();
  const [sbList, setSbList] = useState<ISbListTableDetails[]>([]);
  const [isSelectedAll, setIsSelectedAll] = useState<boolean>(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [selectedFileCount, setSelectedFileCount] = useState(0);

  const debouncedValue = useDebounce(searchValue, 300);

  const handleNextBtn = async (isPageAction: boolean) => {
    const headers = {
      pan: panNumber,
      email,
      intermediateSave: isPageAction,
      selectAll: selectedFileCount === totalRecords,
    };
    if (allData) {
      const requestBody = {...allData, 'sb-list': sbList};
      const data = await saveSbList(headers, requestBody);
      if (data.status.toString() === ResponseStatus.SUCCESS && !isPageAction) {
        navigate(`${Path.DUTY_DRAWBACK}${Path.DBK_CLAIM}${Path.SB_SUMMARY}`);
      }
    }
  };

  const handlePageChange = (pageNumber: string | number) => {
    handleNextBtn(true);
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
    setPage(1);
  };

  const handleSortBy = (sortKey: string, order: 'asc' | 'desc') => {
    setSortBy(sortKey);
    setSortingOrder(order === 'asc' ? 1 : -1);
  };

  const toggleSelectItem = (id: string) => {
    const index = sbList.findIndex(
      (value: ISbListTableDetails) => value['sb-ref-id'] === id
    );

    sbList[index]['is-selected'] = !sbList[index]['is-selected'];
    sbList[index].selected = !sbList[index].selected;
    const selectedItems = sbList.filter((item) => item['is-selected']);
    const totalChecks = selectedItems.length;

    // select all logic here
    const isAllSelected = totalChecks === sbList.length;
    setIsSelectedAll(isAllSelected);
    setSelectedFileCount(totalChecks);
  };

  const handleHeaderToggle = () => {
    if (sbList.length) {
      const updatedList = sbList.map((value: ISbListTableDetails) => ({
        ...value,
        'is-selected': !isSelectedAll,
        selected: !isSelectedAll,
      }));
      setSbList(updatedList);
      setIsSelectedAll(!isSelectedAll);
      setSelectedFileCount(isSelectedAll ? 0 : sbList.length);
    }
  };

  // INFO: Below function to select all the records that present in backend
  const handleToggleSelectAll = (type: 'CLEAR_ALL' | 'SELECT_ALL') => {
    if (type === 'SELECT_ALL') {
      setSbList(
        sbList.map((value: ISbListTableDetails) => ({
          ...value,
          'is-selected': true,
          selected: true,
        }))
      );
      setIsSelectedAll(true);
      setSelectedFileCount(totalRecords);
    } else if (type === 'CLEAR_ALL') {
      setSbList(
        sbList.map((value: ISbListTableDetails) => ({
          ...value,
          'is-selected': false,
          selected: false,
        }))
      );
      setIsSelectedAll(false);
      setSelectedFileCount(0);
    }
  };

  const getSbList = useCallback(async () => {
    const headers = {
      claimTxnId,
      pan: panNumber,
      email,
      selected: false,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getDbkClaimSbList(headers, page, +showEntries);
    setSbList(response.data['sb-list']);
    setTotalRecords(response.data['total-records']);
    setAllData(response.data);
    const isAllValueSelected = response.data['sb-list'].every(
      (item: ISbListTableDetails) => item['is-selected'] === true
    );
    setIsSelectedAll(isAllValueSelected);
    setSelectedFileCount(
      response.data['sb-list']?.filter(
        (el: ISbListTableDetails) => el?.['is-selected']
      ).length
    );
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    claimTxnId,
    email,
    page,
    panNumber,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getSbList();
    dispatch(dutyDrawbackActions.setGetSbListFunc(getSbList));
  }, [getSbList]);

  useEffect(() => {
    if (claimTxnId && sbList.length) {
      const isAllValueSelected = sbList.every(
        (item) => item['is-selected'] === true
      );
      if (isAllValueSelected) setIsSelectedAll(true);
    }
  }, [claimTxnId, sbList]);

  return (
    <div className='shipping-bills-details'>
      <DbkClaimSubHeader step='1.1' subTitle='Shipping Bills'>
        <div className='next-button'>
          <EximButton onClick={() => handleNextBtn(false)}>Next</EximButton>
        </div>
      </DbkClaimSubHeader>
      <Stripe
        content='This page displays the Shipping bills based on the date range specified during claim creation.'
        variant='info'
      />
      <EximPaper>
        <div className='sb-table-wrapper'>
          {sbList.length > 0 && totalRecords > +showEntries ? (
            <AllFileSelectionStripe
              selectedFileCount={selectedFileCount}
              totalRecords={totalRecords}
              handleToggleSelectAll={handleToggleSelectAll}
            />
          ) : null}
          <TableSearchFilter
            isInputDisabled={!searchKey}
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}>
            <EximCustomDropdown
              placeholder='Search By Column'
              onSelect={({value}) => setSearchKey(value)}
              dataTestId='column-dropdown'
              optionsList={tableSearchDropdown}
            />
          </TableSearchFilter>
          <ShippingBillsTable
            data={sbList}
            selectItem={(id) => toggleSelectItem(id)}
            isSelectedAll={isSelectedAll}
            onSelectAll={handleHeaderToggle}
            isOptionsCheck
            handleSortBy={handleSortBy}
          />
          <TableFooter
            page={page}
            searchQuery={searchValue}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={sbList as []}
            renderData={sbList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ShippingBillsDetails;
