import EximSummaryCard from '@shared/components/EximSummaryCard';
import {memo} from 'react';

import './index.scss';

interface IContentTypesProps {
  contentData: {title: string; value: string | number}[];
}

function Content({contentData}: IContentTypesProps) {
  return (
    <div className='card-content-container'>
      {contentData?.map(({title, value}, index) => (
        <div key={`content${index + 1}`} className='card-content'>
          <p>{title}</p>
          <p>{value}</p>
        </div>
      ))}
    </div>
  );
}

function SummaryCards() {
  // TODO: Below data will be dynamic when we will have the API response
  const outWardData = [
    {
      title: 'Total Inv/SB',
      value: '-',
    },
    {
      title: 'Total value',
      value: '-',
    },
    {
      title: 'Total considered for calculation',
      value: '-',
    },
  ];
  const inWardData = [
    {
      title: 'Total Inv/BOE',
      value: '-',
    },
    {
      title: 'Total value',
      value: '-',
    },
    {
      title: 'Total considered for calculation',
      value: '-',
    },
  ];
  return (
    <div className='summary-cards-container'>
      <EximSummaryCard
        header='Outwards Supply'
        content={<Content contentData={outWardData} />}
        variant='success'
      />
      <EximSummaryCard
        header='Inward Supplies'
        content={<Content contentData={inWardData} />}
        variant='information'
      />
    </div>
  );
}

export default memo(SummaryCards);
