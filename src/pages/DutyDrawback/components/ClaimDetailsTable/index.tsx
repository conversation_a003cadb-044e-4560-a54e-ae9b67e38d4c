import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {formatAmount, formatDate} from '@common/helpers';
import {IFinalSummaryClaimDetails} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getDbkFinalSummary} from '@pages/DutyDrawback/api';
import {
  FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER,
  FINAL_SUMMARY_CLAIM_DETAILS_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {memo, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IClaimDetailsProps {
  claimId: string;
  setIsAnyNotQualified: (val: boolean) => void;
}

function ClaimDetailsTable({
  claimId,
  setIsAnyNotQualified,
}: IClaimDetailsProps) {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [claimDetailsData, setClaimDetailsData] = useState<
    IFinalSummaryClaimDetails[]
  >([]);
  const [totalRecords, setTotalRecords] = useState(0);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getDbkClaimDetails = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId: claimId,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getDbkFinalSummary(payload, page, +showEntries);
    setClaimDetailsData(data?.['dbk-summary']['prod-dtls-list']);
    setTotalRecords(data?.['total-records']);
    const isAnyNotQualified = data?.['dbk-summary']['prod-dtls-list']?.some(
      (prod: IFinalSummaryClaimDetails) =>
        prod.qualifyingStatus === 'NOT_QUALIFIED'
    );
    setIsAnyNotQualified(isAnyNotQualified);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    email,
    panNumber,
    claimId,
    page,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getDbkClaimDetails();
  }, [getDbkClaimDetails]);

  const showValue = (value: string) => {
    if (value === 'QUALIFIED') {
      return 'Qualified';
    }
    return 'Not-Qualified';
  };

  const getReason = (value: string) => {
    if (!value || value === 'N/A') return '';
    if (value === 'AIR_RATE') {
      return ' - 4th/5th Rule';
    }
    return ' - Shortfall Quantity';
  };

  return (
    <EximPaper>
      <div className='claim-details-table-wrapper'>
        <TableSearchFilter
          isInputDisabled={!searchKey}
          handleSearchQuery={handleSearchQuery}
          handleShowEntries={handleShowEntries}>
          <EximCustomDropdown
            placeholder='Search By Column'
            onSelect={({value}) => handleSearchKey(value)}
            dataTestId='column-dropdown'
            optionsList={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_SEARCH_DROPDOWN}
          />
        </TableSearchFilter>
        <table className='claim-details-table'>
          <TableHeader
            mainHeader={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER}
            handleSortBy={handleSortBy}
          />
          {claimDetailsData?.length > 0 ? (
            <TableBody className='claim-details-tbody'>
              {claimDetailsData?.map((item, index) => (
                <TableRow key={`claimDetails${index + 1}`}>
                  <TableCell>{item.sbNo ?? '-'}</TableCell>
                  <TableCell>{formatDate(item.sbDate) ?? '-'}</TableCell>
                  <TableCell>{item.prodCode ?? '-'}</TableCell>
                  <TableCell>{item.prodDesc ?? '-'}</TableCell>
                  <TableCell>{item.totalQtyConsidered ?? '-'}</TableCell>
                  <TableCell>{item.totalItemQtyShortfall ?? '-'}</TableCell>
                  <TableCell>
                    {formatAmount(item.totalFobVal as number) ?? '-'}
                  </TableCell>
                  <TableCell>{`${item.systemDbkRate}%`}</TableCell>
                  <TableCell>{item.systemDbkAmt ?? '-'}</TableCell>
                  <TableCell>{`${item.govDbkRate}%`}</TableCell>
                  <TableCell>
                    {formatAmount(item.govDbkAmt as number) ?? '-'}
                  </TableCell>
                  <TableCell
                    className={
                      item.qualifyingStatus === 'QUALIFIED'
                        ? 'cell-success'
                        : 'cell-error'
                    }>
                    {`${showValue(item.qualifyingStatus as string)} ${getReason(
                      item.disqualificationReason as string
                    )}`}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <EmptyTable
              colSpan={FINAL_SUMMARY_CLAIM_DETAILS_TABLE_HEADER.length}
            />
          )}
        </table>
        <TableFooter
          page={page}
          searchQuery={searchValue}
          showEntries={showEntries}
          totalRecords={totalRecords}
          searchData={claimDetailsData as []}
          renderData={claimDetailsData as []}
          handlePageChange={handlePageChange}
          hasBackendPagination
        />
      </div>
    </EximPaper>
  );
}

export default memo(ClaimDetailsTable);
