import BusinessHeader from '@common/components/BusinessHeader';
import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {
  AlertStatus,
  DUTY_DRAWBACK_FILE_TYPE,
  EximHeroDate,
  ResponseStatus,
} from '@common/constants';
import {formatDate} from '@common/helpers';
import {
  ICustomAxiosResp,
  IImportViewAllDetails,
  IImportViewInvoices,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  getBoeViewDetails,
  saveViewInvoiceDetails,
} from '@pages/DutyDrawback/api';
import {
  IMPORT_VIEW_DETAILS_TABLE_HEADER,
  VIEW_IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import {importInvoiceValidation} from '@pages/DutyDrawback/utils/importEditInvoiceValidation';
import EximButton from '@shared/components/EximButton';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximDatePicker from '@shared/components/EximDatePicker';
import EximDivider from '@shared/components/EximDivider';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import EximTooltip from '@shared/components/EximTooltip';
import EximTypography from '@shared/components/EximTypography';
import {RootState, dispatch} from '@store';
import {useFormik} from 'formik';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation, useNavigate} from 'react-router';

import './index.scss';

interface IImportViewDetailsProps {
  isViewValidRecord: boolean;
  isEditable: boolean;
}

function ImportDomesticViewDetails({
  isViewValidRecord,
  isEditable,
}: IImportViewDetailsProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const {refIds} = (location.state ?? {}) as {refIds: string[]};

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const initialImportInvoices = {
    'boe-no': {value: '', isValid: true, errorMessage: ''},
    'boe-date': {value: '', isValid: true, errorMessage: ''},
    'supplier-name': {value: '', isValid: true, errorMessage: ''},
    'imported-country': {value: '', isValid: true, errorMessage: ''},
    'custom-house-name': {value: '', isValid: true, errorMessage: ''},
    'is-final-assessment': {value: '', isValid: true, errorMessage: ''},
    'foreign-materials-sup-name': {value: '', isValid: true, errorMessage: ''},
    'purchase-inv-date': {value: '', isValid: true, errorMessage: ''},
    'purchase-inv-no': {value: '', isValid: true, errorMessage: ''},
    'total-available-qty': {value: '', isValid: true, errorMessage: ''},
    'total-utilized-qty': 0,
  };

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isEdit, setIsEdit] = useState<boolean>(isEditable);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [viewDetailsAllData, setViewDetailsAllData] =
    useState<IImportViewAllDetails>(initialImportInvoices);
  const [viewDetailsData, setViewDetailsData] = useState<IImportViewInvoices[]>(
    []
  );
  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const handleChangeItemValue = (
    event: ChangeEvent<HTMLInputElement>,
    index: number,
    key: keyof IImportViewInvoices
  ) => {
    setViewDetailsData((prevItem: IImportViewInvoices[]) => {
      const updatedItem = [...prevItem];
      const message = importInvoiceValidation(key, event.target.value);
      // Update the specific object at the given index and key
      (updatedItem[index][key] as {value: string}).value = event.target.value;
      (updatedItem[index][key] as {isValid: boolean}).isValid = message === '';
      (updatedItem[index][key] as {errorMessage: string}).errorMessage =
        message;
      return updatedItem;
    });
  };

  const formik = useFormik({
    initialValues: viewDetailsAllData,
    onSubmit: async (values: IImportViewAllDetails) => {
      if (isEdit) {
        const payload = {
          pan: panNumber,
          email,
          txnId: invoiceTxnId,
          fileType: DUTY_DRAWBACK_FILE_TYPE.BOE,
        };
        const body = {
          ...values,
          'boe-item-list': viewDetailsData,
        };
        const response = (await saveViewInvoiceDetails(
          payload,
          body
        )) as ICustomAxiosResp;
        if (response?.status?.toString() === ResponseStatus.SUCCESS) {
          setIsEdit(false);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: response?.msg,
              alertType: AlertStatus.SUCCESS,
            })
          );
          navigate(-1);
        }
        if (response?.status?.toString() === ResponseStatus.ERROR) {
          setViewDetailsAllData(response?.data);
          setViewDetailsData(response?.data?.['boe-item-list']);
          setTotalRecords(response?.data?.['total-records']);
          formik.setValues(response?.data);
          dispatch(
            alertActions.setAlertMsg({
              code: 200,
              message: 'Records are not valid',
              alertType: AlertStatus.DANGER,
            })
          );
        }
      }
      setIsEdit(true);
    },
  });

  // Access onChange values in the respective object
  const handleChange = (key: keyof IImportViewAllDetails, value: string) => {
    let date = null;
    if (key.includes('date')) {
      date = value?.replaceAll('/', '-');
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const updatedValues: any = {...formik.values};

    const message = importInvoiceValidation(key, value);
    updatedValues[key] = {
      value: !date ? value : date,
      isValid: message === '',
      errorMessage: message,
    };
    // Update the formik values
    formik.setValues(updatedValues);
    setViewDetailsAllData(updatedValues);
  };

  const getBoeViewDetailsData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const response = await getBoeViewDetails(
      payload,
      page,
      +showEntries,
      refIds as []
    );
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setViewDetailsAllData(response?.data);
      setViewDetailsData(response?.data?.['boe-item-list'] || []);
      setTotalRecords(response?.data?.['total-records'] || 0);
      formik.setValues(response?.data);
      setIsLoading(false);
    }
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    panNumber,
    refIds,
    page,
    showEntries,
    email,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    if (refIds !== undefined) getBoeViewDetailsData();
  }, [getBoeViewDetailsData, refIds]);

  const getDateValue = (
    formikVal: string,
    key: keyof IImportViewAllDetails
  ) => {
    const apiResVal = (
      viewDetailsAllData[key] as {value: string}
    )?.value?.toString();
    if (formikVal && formikVal.length > 4) {
      return formikVal?.replaceAll('-', '/');
    }
    if (apiResVal) {
      return apiResVal?.replaceAll('-', '/');
    }
    return undefined;
  };

  return isLoading ? null : (
    <form onSubmit={formik.handleSubmit}>
      <BusinessHeader>
        <div className='btn-container'>
          {/* INFO: If we are viewing valid record than no need to show edit button */}
          {!isViewValidRecord ? (
            <EximButton size='small' type='submit'>
              {`${isEdit ? 'Save' : 'Edit'}`}
            </EximButton>
          ) : null}
        </div>
      </BusinessHeader>
      <EximPaper>
        <div className='boe-view-details'>
          <div className='product-details-container'>
            <EximTypography variant='h4' fontWeight='bold'>
              Product Details
            </EximTypography>
            <div className='product-details-list'>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['supplier-name']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Name of supplier'
                    id='supplier-name'
                    name='supplier-name'
                    dataTestid='supplier-name'
                    disabled={!isEdit}
                    maxLength={150}
                    value={formik.values?.['supplier-name']?.value?.toString()}
                    isInvalid={!formik.values?.['supplier-name']?.isValid}
                    onChange={(event) =>
                      handleChange('supplier-name', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['custom-house-name']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Name of Customs House'
                    id='custom-house-name'
                    name='custom-house-name'
                    dataTestid='custom-house-name'
                    disabled={!isEdit}
                    value={formik.values?.[
                      'custom-house-name'
                    ]?.value?.toString()}
                    maxLength={7}
                    isInvalid={!formik.values?.['custom-house-name']?.isValid}
                    onChange={(event) =>
                      handleChange('custom-house-name', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit ? formik.values?.['boe-no']?.errorMessage : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Bill of Entry No.'
                    id='boe-no'
                    name='boe-no'
                    dataTestid='boe-no'
                    value={formik.values?.['boe-no']?.value?.toString()}
                    disabled={!isEdit}
                    maxLength={20}
                    isInvalid={!formik.values?.['boe-no']?.isValid}
                    onChange={(event) =>
                      handleChange('boe-no', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['is-final-assessment']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Is Assessment Final?'
                    id='is-final-assessment'
                    name='is-final-assessment'
                    dataTestid='is-final-assessment'
                    disabled={!isEdit}
                    maxLength={5}
                    value={formik.values?.[
                      'is-final-assessment'
                    ]?.value?.toString()}
                    isInvalid={!formik.values?.['is-final-assessment']?.isValid}
                    onChange={(event) =>
                      handleChange('is-final-assessment', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['imported-country']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Country from where imported'
                    id='imported-country'
                    name='imported-country'
                    dataTestid='imported-country'
                    disabled={!isEdit}
                    maxLength={20}
                    value={formik.values?.[
                      'imported-country'
                    ]?.value?.toString()}
                    isInvalid={!formik.values?.['imported-country']?.isValid}
                    onChange={(event) =>
                      handleChange('imported-country', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <div className='product-details-title'>Bill of Entry Date</div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={formik.values?.['boe-date']?.errorMessage}
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='boe-date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!formik.values?.['boe-date']?.isValid}
                      onChange={(value) => handleChange('boe-date', value)}
                      defaultValue={getDateValue(
                        formik.values?.['boe-date']?.value?.toString(),
                        'boe-date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(formik.values?.['boe-date']?.value.toString())}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['purchase-inv-no']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Purchase invoice No.'
                    id='purchase-inv-no'
                    name='purchase-inv-no'
                    dataTestid='purchase-inv-no'
                    disabled={!isEdit}
                    value={formik.values?.[
                      'purchase-inv-no'
                    ]?.value?.toString()}
                    isInvalid={!formik.values?.['purchase-inv-no']?.isValid}
                    onChange={(event) =>
                      handleChange('purchase-inv-no', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <div className='product-details-title'>
                  Purchase invoice date
                </div>
                {isEdit ? (
                  <EximTooltip
                    className='date-picker-tooltip'
                    content={formik.values?.['purchase-inv-date']?.errorMessage}
                    direction='top'
                    variant='secondary'>
                    <EximDatePicker
                      id='purchase-inv-date'
                      minDate={EximHeroDate.MIN_DATE}
                      calendarType='dateCalendar'
                      isInvalid={!formik.values?.['purchase-inv-date']?.isValid}
                      onChange={(value) =>
                        handleChange('purchase-inv-date', value)
                      }
                      defaultValue={getDateValue(
                        formik.values?.['purchase-inv-date']?.value?.toString(),
                        'purchase-inv-date'
                      )}
                    />
                  </EximTooltip>
                ) : (
                  <div className='product-details-value'>
                    {formatDate(
                      formik.values?.['purchase-inv-date']?.value.toString()
                    )}
                  </div>
                )}
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['total-available-qty']?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Available Qty'
                    id='total-available-qty'
                    name='total-available-qty'
                    dataTestid='total-available-qty'
                    disabled={!isEdit}
                    maxLength={15}
                    value={formik.values?.[
                      'total-available-qty'
                    ]?.value?.toString()}
                    isInvalid={!formik.values?.['total-available-qty']?.isValid}
                    onChange={(event) =>
                      handleChange('total-available-qty', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details'>
                <EximTooltip
                  className='input-tooltip-text'
                  content={null}
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Utilized Qty'
                    id='total-utilized-qty'
                    name='total-utilized-qty'
                    dataTestid='total-utilized-qty'
                    disabled
                    value={formik.values?.['total-utilized-qty']?.toString()}
                    isInvalid
                    onChange={(event) =>
                      handleChange('total-utilized-qty', event.target.value)
                    }
                  />
                </EximTooltip>
              </div>
              <div className='product-details address-input-container'>
                <p className='address-subtext'>
                  (in case the foreign materials/ components obtained locally)
                </p>
                <EximTooltip
                  className='input-tooltip-text'
                  content={
                    isEdit
                      ? formik.values?.['foreign-materials-sup-name']
                          ?.errorMessage
                      : null
                  }
                  direction='top'
                  variant='secondary'>
                  <EximInput
                    label='Name & full address of the supplier'
                    id='foreign-materials-sup-name'
                    name='foreign-materials-sup-name'
                    dataTestid='foreign-materials-sup-name'
                    disabled={!isEdit}
                    maxLength={300}
                    value={formik.values?.[
                      'foreign-materials-sup-name'
                    ]?.value?.toString()}
                    isInvalid={
                      !formik.values?.['foreign-materials-sup-name']?.isValid
                    }
                    onChange={(event) =>
                      handleChange(
                        'foreign-materials-sup-name',
                        event.target.value
                      )
                    }
                  />
                </EximTooltip>
              </div>
            </div>
          </div>
          <EximDivider type='dashed' />
          <div className='view-details-table-container'>
            <TableSearchFilter
              isInputDisabled={!searchKey}
              handleShowEntries={handleShowEntries}
              handleSearchQuery={handleSearchQuery}>
              <EximCustomDropdown
                placeholder='Search By Column'
                onSelect={({value}) => handleSearchKey(value)}
                dataTestId='column-dropdown'
                optionsList={VIEW_IMPORT_PURCHASE_TABLE_SEARCH_DROPDOWN}
              />
            </TableSearchFilter>
            <table className='boe-view-details-table'>
              <TableHeader
                mainHeader={IMPORT_VIEW_DETAILS_TABLE_HEADER}
                handleSortBy={handleSortBy}
              />
              {viewDetailsData?.length > 0 ? (
                <TableBody className='boe-view-list-table-body'>
                  {viewDetailsData?.map(
                    (item: IImportViewInvoices, index: number) => {
                      return (
                        <TableRow key={`importViewInvoice${index + 1}`}>
                          <TableCell>{index + 1}</TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit ? item['item-code']?.errorMessage : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='item-code'
                                name='item-code'
                                dataTestid='item-code'
                                disabled={!isEdit}
                                maxLength={20}
                                value={item['item-code']?.value?.toString()}
                                isInvalid={!item['item-code']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'item-code'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit ? item['item-desc']?.errorMessage : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='item-desc'
                                name='item-desc'
                                dataTestid='item-desc'
                                disabled={!isEdit}
                                maxLength={200}
                                value={item['item-desc']?.value?.toString()}
                                isInvalid={!item['item-desc']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'item-desc'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={isEdit ? item.hsn?.errorMessage : null}
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='hsn'
                                name='hsn'
                                dataTestid='hsn'
                                disabled={!isEdit}
                                maxLength={8}
                                value={item.hsn?.value?.toString()}
                                isInvalid={!item.hsn?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) => handleChangeItemValue(event, index, 'hsn')}
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['imported-qty']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='imported-qty'
                                name='imported-qty'
                                dataTestid='imported-qty'
                                disabled={!isEdit}
                                maxLength={15}
                                value={item['imported-qty']?.value?.toString()}
                                isInvalid={!item['imported-qty']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'imported-qty'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={isEdit ? item.uqc?.errorMessage : null}
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='uqc'
                                name='uqc'
                                dataTestid='uqc'
                                disabled={!isEdit}
                                maxLength={15}
                                value={item.uqc?.value?.toString()}
                                isInvalid={!item.uqc?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) => handleChangeItemValue(event, index, 'uqc')}
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['total-accessible-val']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='total-accessible-val'
                                name='total-accessible-val'
                                dataTestid='total-accessible-val'
                                disabled={!isEdit}
                                maxLength={18}
                                value={item[
                                  'total-accessible-val'
                                ]?.value?.toString()}
                                isInvalid={
                                  !item['total-accessible-val']?.isValid
                                }
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'total-accessible-val'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit ? item['bcd-rate']?.errorMessage : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='bcd-rate'
                                name='bcd-rate'
                                dataTestid='bcd-rate'
                                disabled={!isEdit}
                                maxLength={6}
                                value={item['bcd-rate']?.value?.toString()}
                                isInvalid={!item['bcd-rate']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'bcd-rate'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                          <TableCell className='editable-td'>
                            <EximTooltip
                              content={
                                isEdit
                                  ? item['custom-cess-rate']?.errorMessage
                                  : null
                              }
                              direction='top'
                              variant='secondary'>
                              <EximInput
                                id='custom-cess-rate'
                                name='custom-cess-rate'
                                dataTestid='custom-cess-rate'
                                disabled={!isEdit}
                                maxLength={6}
                                value={item[
                                  'custom-cess-rate'
                                ]?.value?.toString()}
                                isInvalid={!item['custom-cess-rate']?.isValid}
                                onChange={(
                                  event: ChangeEvent<HTMLInputElement>
                                ) =>
                                  handleChangeItemValue(
                                    event,
                                    index,
                                    'custom-cess-rate'
                                  )
                                }
                              />
                            </EximTooltip>
                          </TableCell>
                        </TableRow>
                      );
                    }
                  )}
                </TableBody>
              ) : (
                <EmptyTable colSpan={IMPORT_VIEW_DETAILS_TABLE_HEADER.length} />
              )}
            </table>
            <TableFooter
              page={page}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              searchData={viewDetailsData as []}
              renderData={viewDetailsData as []}
              handlePageChange={handlePageChange}
              hasBackendPagination
            />
          </div>
        </div>
      </EximPaper>
    </form>
  );
}

export default ImportDomesticViewDetails;
