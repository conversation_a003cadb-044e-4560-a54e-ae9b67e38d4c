import TableFooter from '@common/components/TableFooter';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path, ResponseStatus} from '@common/constants';
import {
  IDbkClaimProductList,
  ISaveSbProductList,
  ISbListTableDetails,
} from '@common/interfaces';
import {
  getDbkClaimProductList,
  saveSbProductList,
} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import EximButton from '@shared/components/EximButton';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import ProductSelectionTable from '../ProductSelectionTable';
import './index.scss';

function ProductSelectionDetails() {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);

  const [page, setPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [allData, setAllData] = useState<ISaveSbProductList>();
  const [productsList, setProductsList] = useState<IDbkClaimProductList[]>([]);
  const [isSelectedAll, setIsSelectedAll] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const {DUTY_DRAWBACK, DBK_CLAIM, SB_SUMMARY} = Path;
  const navigate = useNavigate();

  const getProductList = useCallback(async () => {
    setIsLoading(true);
    const headers = {
      pan: panNumber,
      email,
      claimTxnId,
      selected: false,
    };

    const response = await getDbkClaimProductList(headers, page, +showEntries);
    setProductsList(response.data['sb-prod-list']);
    setTotalRecords(response.data['total-records']);
    setAllData(response.data);
    const hasEveryTrueValue = response.data['sb-prod-list'].every(
      (item: ISbListTableDetails) => item['is-selected'] === true
    );
    if (hasEveryTrueValue) {
      setIsSelectedAll(true);
    } else {
      setIsSelectedAll(false);
    }
    setIsLoading(false);
  }, [panNumber, email, claimTxnId, showEntries, page]);

  useEffect(() => {
    getProductList();
  }, [getProductList]);

  const handleNextBtn = async (isPageAction: boolean) => {
    const headers = {
      pan: panNumber,
      email,
      intermediateSave: isPageAction,
    };
    if (allData) {
      const payload = {...allData, 'sb-prod-list': productsList};
      const data = await saveSbProductList(headers, payload);
      if (data.status.toString() === ResponseStatus.SUCCESS && !isPageAction) {
        navigate(`${DUTY_DRAWBACK}${DBK_CLAIM}${SB_SUMMARY}`);
      }
    }
  };

  const handlePageChange = (pageNumber: string | number) => {
    handleNextBtn(true);
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  const toggleSelectItem = (id: string, code: string) => {
    const index = productsList.findIndex(
      (value: ISbListTableDetails) =>
        value['sb-ref-id'] === id && value['sb-prod-code'] === code
    );

    productsList[index]['is-selected'] = !productsList[index]['is-selected'];
    productsList[index].selected = !productsList[index].selected;
    const selectedItems = productsList.filter((item) => item['is-selected']);
    const totalChecks = selectedItems.length;

    // select all logic here
    if (totalChecks === productsList.length) {
      setIsSelectedAll(true);
    } else {
      setIsSelectedAll(false);
    }
  };

  const handleHeaderToggle = () => {
    if (productsList.length) {
      setProductsList(
        productsList.map((value) => ({
          ...value,
          'is-selected': !isSelectedAll,
          selected: !isSelectedAll,
        }))
      );

      const updateList = productsList.map((item) => {
        const matchingPaginatedItem = productsList.find(
          (data) =>
            data['sb-ref-id'] === item['sb-ref-id'] &&
            data['sb-prod-code'] === item['sb-prod-code']
        );

        if (matchingPaginatedItem) {
          return {
            ...item,
            'is-selected': !isSelectedAll,
            selected: !isSelectedAll,
          };
        }
        return item;
      });
      setProductsList(updateList);
      setIsSelectedAll(!isSelectedAll);
    }
  };

  useEffect(() => {
    if (claimTxnId && productsList.length) {
      const hasEveryTrueValue = productsList.every(
        (item) => item['is-selected'] === true
      );
      if (hasEveryTrueValue) {
        setIsSelectedAll(true);
      }
    }
  }, [claimTxnId, productsList]);

  const handleChangeItemValue = (
    event: ChangeEvent<HTMLInputElement>,
    id: string,
    code: string
  ) => {
    const newValue = Number(event.target.value);
    if (!Number.isNaN(newValue) && !newValue.toString().includes('e')) {
      const updatedItemData = productsList.map(
        (item: IDbkClaimProductList) => ({
          ...item,
          'qty-considered':
            id === item['sb-ref-id'] && code === item['sb-prod-code']
              ? newValue
              : item['qty-considered'],
        })
      );
      setProductsList(updatedItemData);
    }
  };

  return (
    <div className='product-selection-details'>
      <DbkClaimSubHeader step='1.2' subTitle='Product Selection Table'>
        <div className='next-button'>
          <EximButton onClick={() => handleNextBtn(false)}>Next</EximButton>
        </div>
      </DbkClaimSubHeader>
      <EximPaper>
        <div className='product-table-wrapper'>
          <TableSearchFilter
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}
          />
          {isLoading ? null : (
            <ProductSelectionTable
              isOptionsCheck
              data={productsList}
              selectItem={(id, code) => toggleSelectItem(id, code)}
              isSelectedAll={isSelectedAll}
              onSelectAll={handleHeaderToggle}
              handleChangeItemValue={handleChangeItemValue}
              isEditableQty
            />
          )}
          <TableFooter
            page={page}
            searchQuery={searchQuery}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={[]}
            renderData={productsList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ProductSelectionDetails;
