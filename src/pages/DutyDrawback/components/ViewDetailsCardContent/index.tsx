import {IRegularDropdownData} from '@common/interfaces';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import EximTypography from '@shared/components/EximTypography';

import './index.scss';

interface ICardContentProps {
  title: string;
  data: string;
}

interface ICardBomVersionProps {
  title: string;
  defaultSelectedOptionId: number;
  dropDownValues: IRegularDropdownData[];
  handleSelect: (value: string) => void;
}

export function ViewDetailsCardContent({title, data}: ICardContentProps) {
  return (
    <div className='card-content-container'>
      <EximTypography variant='body1'>{title}</EximTypography>
      <EximTypography variant='h3' fontWeight='bold'>
        {data}
      </EximTypography>
    </div>
  );
}

export function BomVersionCardContent({
  title,
  dropDownValues,
  defaultSelectedOptionId,
  handleSelect,
}: ICardBomVersionProps) {
  return (
    <div className='card-content-container'>
      <EximTypography variant='body1'>{title}</EximTypography>
      <EximCustomDropdown
        placeholder='Select Version'
        onSelect={({value}) => handleSelect(value)}
        dataTestId='dropdown'
        optionsList={dropDownValues}
        defaultOption={defaultSelectedOptionId}
      />
    </div>
  );
}
