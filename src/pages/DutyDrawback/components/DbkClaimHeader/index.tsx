import Ellip<PERSON><PERSON><PERSON><PERSON> from '@common/components/EllipsisChecker';
import {EximHeroDate} from '@common/constants';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximMonthRangePicker from '@shared/components/EximMonthRangePicker';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import ClaimModalContent from '../ClaimModal';
import './index.scss';

interface IDbkClaimHeader {
  isEditBtn?: boolean;
}

function DbkClaimHeader(props: IDbkClaimHeader) {
  const {isEditBtn} = props;
  const [isEditClaimModal, setIsEditClaimModal] = useState(false);
  const [isOpenTextModal, setIsOpenTextModal] = useState(false);

  const {
    dutyDrawback: {
      dbkClaim: {claimTitle, applicantType, endPeriod, startPeriod},
    },
  } = useSelector((state: RootState) => state);

  return (
    <div className='dbk-claim-header-wrapper'>
      <EximPaper>
        <div className='main-header'>
          <div className='title-part'>
            <EllipsisChecker
              text={claimTitle}
              handleViewMore={() => setIsOpenTextModal(true)}
            />
            <span> | </span>
            <EximTypography fontWeight='bold'>
              Applicant Type
              <span className='inline'>&nbsp;- {applicantType}</span>
            </EximTypography>
            {isEditBtn && (
              <>
                <span> | </span>
                <EximButton
                  variant='text'
                  onClick={() => setIsEditClaimModal(true)}>
                  Edit
                </EximButton>
              </>
            )}
          </div>
          <div className='period'>
            <EximTypography>Export Period</EximTypography>
            <EximMonthRangePicker
              id='dbk-claim-period'
              minDate={EximHeroDate.MIN_MONTH}
              defaultStartDate={startPeriod.split('-').join('/')}
              defaultEndDate={endPeriod.split('-').join('/')}
              onSelect={() => undefined}
              disabled
            />
          </div>
        </div>
      </EximPaper>

      {/* Edit DBK Claim Modal */}
      <div className='edit-dbk-claim-modal'>
        <EximModal
          isOpen={isEditClaimModal}
          onClose={() => setIsEditClaimModal(false)}
          onOutSideClickClose={() => setIsEditClaimModal(false)}
          content={
            <ClaimModalContent onClose={() => setIsEditClaimModal(false)} />
          }
          footer={false}
          header={
            <EximTypography
              classNames='claim-modal-title'
              fontWeight='semi-bold'>
              Edit Claim
            </EximTypography>
          }
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* View Claim Title Modal */}
      <div className='view-claim-title-modal'>
        <EximModal
          isOpen={isOpenTextModal}
          onClose={() => setIsOpenTextModal(false)}
          onOutSideClickClose={() => setIsOpenTextModal(false)}
          content={<p>{claimTitle}</p>}
          footer={false}
          header={
            <EximTypography fontWeight='bold'>Claim Title</EximTypography>
          }
          closeIcon={<CloseIcon width={15} height={15} />}
        />
      </div>
    </div>
  );
}
DbkClaimHeader.defaultProps = {
  isEditBtn: false,
};
export default DbkClaimHeader;
