import {EximProducts} from '@common/constants';
import {alertActions} from '@core/api/store/alertReducer';
import {dataExtractorActions} from '@pages/DataExtractor/store/reducer';
import {dutyDrawbackActions} from '@pages/DutyDrawback/store/reducer';
import {ebrcActions} from '@pages/EBRC/store/reducer';
import {moowrActions} from '@pages/Moowr/store/reduce';
import {profileActions} from '@pages/Profile/store/reducer';
import {RootState, dispatch} from '@store';
import {useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

// TODO this file is removed once the flow of dbk claim complete
function FormWithTwoInputs() {
  const [pan, setPan] = useState('');
  const [iec, setIec] = useState('');

  const {
    profile: {selectedProductName},
    dutyDrawback: {panNumber: ddPan},
    dataExtractor: {panNumber: dePan},
    moowr: {panNumber: moowrPan},
    ebrc: {iecNumber},
  } = useSelector((state: RootState) => state);

  const handleSubmitForPan = (isEmpty: boolean) => {
    if (isEmpty && !pan) {
      dispatch(dataExtractorActions.setPanNumber(pan));
      dispatch(dutyDrawbackActions.setPanNumber(pan));
      dispatch(moowrActions.setPanNumber(pan));
      dispatch(
        alertActions.setAlertMsg({
          message: 'dbk claim pan number is empty now',
          alertType: 'success',
        })
      );
    }
    if (!isEmpty && pan) {
      dispatch(dataExtractorActions.setPanNumber(pan));
      dispatch(dutyDrawbackActions.setPanNumber(pan));
      dispatch(moowrActions.setPanNumber(pan));
      dispatch(
        alertActions.setAlertMsg({
          message: `Updated dbk claim pan number (${pan}) `,
          alertType: 'success',
        })
      );
    }
    if (!isEmpty && !pan) {
      dispatch(
        alertActions.setAlertMsg({
          message: 'Please enter a Pan',
          alertType: 'danger',
        })
      );
    }
  };

  const handleSubmitForIec = (isEmpty: boolean) => {
    if (isEmpty && !iec) {
      dispatch(ebrcActions.setIecNumber(iec));
      dispatch(
        alertActions.setAlertMsg({
          message: 'IEC number is empty now',
          alertType: 'success',
        })
      );
    }
    if (!isEmpty && iec) {
      dispatch(ebrcActions.setIecNumber(iec));
      dispatch(
        alertActions.setAlertMsg({
          message: `Updated IEC number (${iec}) `,
          alertType: 'success',
        })
      );
    }
    if (!isEmpty && !iec) {
      dispatch(
        alertActions.setAlertMsg({
          message: 'Please enter a IEC',
          alertType: 'danger',
        })
      );
    }
  };

  return (
    <div className='test-form-container'>
      <h2>Testing Details Page</h2>
      <div>
        <label htmlFor='pan'>ADD TEST PAN</label>
        <input
          type='text'
          id='pan'
          value={pan}
          onChange={(e) => setPan(e.target.value)}
        />
        <button type='button' onClick={() => handleSubmitForPan(false)}>
          ADD
        </button>
        <button type='button' onClick={() => handleSubmitForPan(true)}>
          Make Empty
        </button>
      </div>
      <div>
        <label htmlFor='iec'>ADD TEST IEC</label>
        <input
          type='text'
          id='iec'
          value={iec}
          onChange={(e) => setIec(e.target.value)}
        />
        <button type='button' onClick={() => handleSubmitForIec(false)}>
          ADD
        </button>
        <button type='button' onClick={() => handleSubmitForIec(true)}>
          Make Empty
        </button>
      </div>
      <div className='products'>
        <h3> Select Product</h3>
        <button
          type='button'
          onClick={() => {
            dispatch(profileActions.setSelectedProductName(EximProducts.EBRC));
          }}>
          eBRC
        </button>
      </div>
      <h3>Selected Data</h3>

      <table width='100%'>
        <thead>
          <tr>
            <th>Field</th>
            <th>Value</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product</td>
            <td>
              <strong>{selectedProductName}</strong>
            </td>
          </tr>
          <tr>
            <td>Data Extractor Pan Number</td>
            <td>
              <strong>{dePan}</strong>
            </td>
          </tr>
          <tr>
            <td>Duty Drawback Pan Number</td>
            <td>
              <strong>{ddPan}</strong>
            </td>
          </tr>
          <tr>
            <td>Moowr Pan Number</td>
            <td>
              <strong>{moowrPan}</strong>
            </td>
          </tr>
          <tr>
            <td>EBRC IEC Number</td>
            <td>
              <strong>{iecNumber}</strong>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}

export default FormWithTwoInputs;
