@import '@utils/main.scss';

.dbk-claim-boe-container {
  .paper-wrapper-rounded {
    box-shadow: 0px 3px 6px $box-shadow-color;
    border: none;
    @include margin(0);
  }
  .boe-table-container {
    @include padding(20px);
    @include margin(15px auto 32px);
    .boe-table {
      @include margin-top(16px);
      width: 100%;
      border-spacing: 0;
      .boe-tbody {
        .editable-td {
          @include padding-left(18px);
          @include padding-right(24px);
          .input-wrapper {
            .input-container {
              input {
                @include rfs(5px, border-radius);
              }
            }
          }
        }
      }
    }

    .table-search-container,
    .table-footer {
      @include padding(10px auto);
    }
  }
}
