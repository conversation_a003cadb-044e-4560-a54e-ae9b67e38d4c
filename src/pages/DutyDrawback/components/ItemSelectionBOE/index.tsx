import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path, ResponseStatus} from '@common/constants';
import {formatAmount, getRenderData, searchTableData} from '@common/helpers';
import {IBoeAllDataList, IBoeItemSelection} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getBoeItemList, saveBoeItemList} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import {ITEM_SELECTION_BOE_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximInput from '@shared/components/EximInput';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {ChangeEvent, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function ItemSelectionBOE() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);
  const tableHeaders = ITEM_SELECTION_BOE_TABLE_HEADER(false);
  const [page, setPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const [itemSelectionAllData, setItemSelectionAllData] =
    useState<IBoeAllDataList>();
  const [itemSelectionList, setItemSelectionList] = useState<
    IBoeItemSelection[]
  >([]);
  const [searchItemSelectionList, setSearchItemSelectionList] = useState<
    IBoeItemSelection[]
  >([]);
  const [currentData, setCurrentData] = useState<IBoeItemSelection[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);

  const handleNextBtn = async (isPaginate: boolean) => {
    const payload = {
      pan: panNumber,
      email,
      intermediateSave: isPaginate,
    };
    if (itemSelectionAllData) {
      const requestBody = {
        ...itemSelectionAllData,
        'boe-item-list': currentData,
      };
      const data = await saveBoeItemList(payload, requestBody);
      if (data.status.toString() === ResponseStatus.SUCCESS && !isPaginate) {
        navigate(`${Path.DUTY_DRAWBACK}${Path.DBK_CLAIM}${Path.BOE_SUMMARY}`);
      }
    }
  };

  const handlePageChange = (pageNumber: string | number) => {
    handleNextBtn(true); // function call for intermediate save
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    const {value} = event.target;
    setSearchQuery(value);
    setPage(1);
    const searchData = searchTableData(itemSelectionList as [], value);
    setSearchItemSelectionList(searchData);
  };

  const handleSingleSelect = (id: string) => {
    const updatedItemData = currentData?.map((item: IBoeItemSelection) =>
      item['boe-ref-id'] === id
        ? {...item, 'is-selected': !item['is-selected']}
        : item
    );

    const totalSelected = updatedItemData?.filter(
      (item) => item['is-selected']
    ).length;

    setIsSelectedAll(totalSelected === updatedItemData.length);
    if (searchQuery.length > 0) setSearchItemSelectionList(updatedItemData);
    else setItemSelectionList(updatedItemData);
  };

  const handleSelectAll = () => {
    const isAllSelected = currentData?.every((item) => item['is-selected']);

    const updatedItemData = currentData?.map((item: IBoeItemSelection) => ({
      ...item,
      'is-selected': !isAllSelected,
    }));
    setIsSelectedAll(!isAllSelected);
    if (searchQuery.length > 0) setSearchItemSelectionList(updatedItemData);
    else setItemSelectionList(updatedItemData);
  };

  const handleChangeItemValue = (
    event: ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newValue = Number(event.target.value);
    if (!Number.isNaN(newValue) && !newValue.toString().includes('e')) {
      const updatedItemData = currentData.map(
        (item: IBoeItemSelection, indx) => ({
          ...item,
          'qty-considered': index === indx ? newValue : item['qty-considered'],
        })
      );
      if (searchQuery.length > 0) setSearchItemSelectionList(updatedItemData);
      else setItemSelectionList(updatedItemData);
    }
  };

  const getItemSelectionData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId,
    };
    const {data} = await getBoeItemList(payload, page, +showEntries);
    const isAllSelected = data?.['boe-item-list']?.every(
      (item: IBoeItemSelection) => item['is-selected']
    );
    setIsSelectedAll(isAllSelected); // when all item selected then header should be checked
    setTotalRecords(data?.['total-records']);
    setItemSelectionAllData(data);
    setItemSelectionList(data?.['boe-item-list']);
  }, [email, claimTxnId, panNumber, page, showEntries]);

  useEffect(() => {
    getItemSelectionData();
  }, [getItemSelectionData]);

  useEffect(() => {
    const renderData = getRenderData(
      itemSelectionList as [],
      searchItemSelectionList as [],
      searchQuery
    );
    setCurrentData(renderData);
  }, [itemSelectionList, searchItemSelectionList, searchQuery]);

  return (
    <div className='dbk-claim-boe-container'>
      <DbkClaimSubHeader step='3.2' subTitle='Item Selection Table'>
        <EximButton size='small' onClick={() => handleNextBtn(false)}>
          Next
        </EximButton>
      </DbkClaimSubHeader>
      <EximPaper>
        <div className='boe-table-container'>
          <TableSearchFilter
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}
          />
          <table className='boe-table'>
            <TableHeader
              mainHeader={tableHeaders}
              checked={isSelectedAll}
              onChange={handleSelectAll}
            />
            {currentData?.length > 0 ? (
              <TableBody className='boe-tbody'>
                {currentData?.map((item: IBoeItemSelection, index: number) => (
                  <TableRow key={`itemSelection${index + 1}`}>
                    <TableCell className='checkbox-td'>
                      <EximCheckbox
                        id={`${item['boe-ref-id']}`}
                        name='invoiceRecord'
                        color='#2CB544'
                        size='medium'
                        checked={item['is-selected']}
                        onChange={() =>
                          handleSingleSelect(item?.['boe-ref-id'])
                        }
                      />
                    </TableCell>
                    <TableCell>{item['item-code']}</TableCell>
                    <TableCell>{item['item-desc']}</TableCell>
                    <TableCell>{formatAmount(item['total-value'])}</TableCell>
                    <TableCell>
                      {formatAmount(item['total-purchased-qty'])}
                    </TableCell>
                    <TableCell>
                      {formatAmount(item['total-available-qty'])}
                    </TableCell>
                    <TableCell className='editable-td'>
                      <EximInput
                        id='qty-considered'
                        name='qty-considered'
                        dataTestid='qty-considered'
                        value={item['qty-considered'].toString()}
                        onChange={(event: ChangeEvent<HTMLInputElement>) =>
                          handleChangeItemValue(event, index)
                        }
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={tableHeaders.length} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchQuery}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={searchItemSelectionList as []}
            renderData={itemSelectionList as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default ItemSelectionBOE;
