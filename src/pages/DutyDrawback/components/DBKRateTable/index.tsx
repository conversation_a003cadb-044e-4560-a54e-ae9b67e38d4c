import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {DDFileType, DUTY_DRAWBACK_FILE_TYPE} from '@common/constants';
import {getWordFromUrl} from '@common/helpers';
import {IDbkRateList} from '@common/interfaces';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getInvoices} from '@pages/DutyDrawback/api';
import {
  DBK_RATE_LIST_TABLE_HEADER,
  DBK_RATE_LIST_TABLE_SEARCH_DROPDOWN,
} from '@pages/DutyDrawback/utils';
import EximCustomDropdown from '@shared/components/EximCustomDropdown';
import {RootState} from '@store';
import {memo, useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useLocation} from 'react-router';

import './index.scss';

function DBKRateTable() {
  const {pathname} = useLocation();
  const fileType = getWordFromUrl(pathname, 1).toUpperCase();
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber, invoiceTxnId},
  } = useSelector((state: RootState) => state);

  const [totalRecords, setTotalRecords] = useState(0);
  const [DBKRateList, setDBKRateList] = useState<IDbkRateList[]>([]);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const getDBKRateList = useCallback(async () => {
    const payload = {
      txnId: '',
      pan: panNumber,
      email,
      startPeriod: '',
      endPeriod: '',
      invType: 'VALID',
      fileType: DUTY_DRAWBACK_FILE_TYPE[fileType?.toUpperCase() as DDFileType],
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getInvoices(payload, page, +showEntries);
    setDBKRateList(data?.records);
    setTotalRecords(data?.['total-records']);
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    invoiceTxnId,
    fileType,
    email,
    page,
    panNumber,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    getDBKRateList();
  }, [getDBKRateList]);

  return (
    <div className='dbk-rate-table-container'>
      <TableSearchFilter
        isInputDisabled={!searchKey}
        handleSearchQuery={handleSearchQuery}
        handleShowEntries={handleShowEntries}>
        <EximCustomDropdown
          placeholder='Search By Column'
          onSelect={({value}) => handleSearchKey(value)}
          dataTestId='column-dropdown'
          optionsList={DBK_RATE_LIST_TABLE_SEARCH_DROPDOWN}
        />
      </TableSearchFilter>
      <table className='dbk-rate-list-table'>
        <TableHeader
          mainHeader={DBK_RATE_LIST_TABLE_HEADER}
          handleSortBy={handleSortBy}
        />
        {DBKRateList?.length > 0 ? (
          <TableBody className='dbk-rate-list-tbody'>
            {DBKRateList?.map((item: IDbkRateList, index: number) => (
              <TableRow key={`${item['product-code'].value}${index + 1}`}>
                <TableCell>{item['product-code'].value}</TableCell>
                <TableCell>{item['product-description'].value}</TableCell>
                <TableCell>
                  {(item['dbk-rate'].value as number) * 100} %
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        ) : (
          <EmptyTable colSpan={DBK_RATE_LIST_TABLE_HEADER.length} />
        )}
      </table>
      <TableFooter
        page={page}
        searchQuery={searchValue}
        showEntries={showEntries}
        totalRecords={totalRecords}
        searchData={DBKRateList as []}
        renderData={DBKRateList as []}
        handlePageChange={handlePageChange}
        hasBackendPagination
      />
    </div>
  );
}

export default memo(DBKRateTable);
