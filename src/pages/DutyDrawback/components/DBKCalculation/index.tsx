import EmptyTable from '@common/components/EmptyTable';
import TableHeader from '@common/components/TableHeader';
import {AlertStatus, Path, ResponseStatus} from '@common/constants';
import {
  ICalculationSummary,
  IDbkCalculationData,
  IFinalSummaryClaimDetails,
} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import useDebounce from '@hooks/useDebounce';
import useFiltering from '@hooks/useFiltering';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {
  freezeDbkClaim,
  getDbkFinalSummary,
  getSummaryCalculation,
  saveSummaryCalculation,
} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import {
  dutyDrawbackActions,
  initialClaimDetails,
} from '@pages/DutyDrawback/store/reducer';
import {DBK_CALCULATION_TABLE_HERDER} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximModal from '@shared/components/EximModal';
import EximPaper from '@shared/components/EximPaper';
import EximTypography from '@shared/components/EximTypography';
import {CloseIcon} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {useCallback, useEffect, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import DownloadFilesModalContent from '../DownloadFilesModalContent';
import FinalSummary from '../FinalSummary';
import RecomputeModalContent from './RecomputeModalContent';
import './index.scss';

function DBKCalculation() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber: pan,
      dbkClaim: {claimTxnId, claimCalculationStatus},
    },
  } = useSelector((state: RootState) => state);

  const [summaryCalculationData, setSummaryCalculationData] = useState<
    ICalculationSummary[]
  >([]);
  const [totalClaimValue, setTotalClaimValue] = useState<number>();
  const [isFreezed, setIsFreezed] = useState(false);

  const [finalSummaryTableData, setFinalSummaryTableData] = useState<
    IFinalSummaryClaimDetails[]
  >([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isDownloadReport, setIsDownloadReport] = useState<boolean>(false);
  const [isFreezModal, setIsFreezModal] = useState<boolean>(false);
  const [isRecomputeModal, setIsRecomputeModal] = useState<boolean>(false);
  const [isUnqualifiedProds, setIsUnqualifiedProds] = useState(false);
  const [airDisqualificationCount, setAirDisqualificationCount] = useState(0);
  const [shortFallDisqualificationCount, setShortFallDisqualificationCount] =
    useState(0);
  const [isFourFifthRuleSelected, setIsFourFifthRuleSelected] =
    useState<boolean>(false);

  const {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchKey,
    handleSearchQuery,
    handleSortBy,
  } = useFiltering();
  const debouncedValue = useDebounce(searchValue, 300);

  const calculationProcess = async () => {
    const payload = {
      pan,
      email,
      claimTxnId,
    };
    const response = await saveSummaryCalculation(payload);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      dispatch(
        alertActions.setAlertMsg({
          message: 'Duty Drawback Calculation Process Started',
          alertType: AlertStatus.SUCCESS,
        })
      );
      setTimeout(() => navigate(`${Path.DUTY_DRAWBACK}`), 1000);
    }
  };

  const handleFreezDbkClaim = async () => {
    const payload = {
      pan,
      email,
      claimTxnId,
      considerDisqualifiedProds: !isFourFifthRuleSelected,
    };
    const response = await freezeDbkClaim(payload);
    if (response.status.toString() === ResponseStatus.SUCCESS) {
      setIsFreezed(true);
      dispatch(dutyDrawbackActions.setClaimCalculationStatus('Freezed'));
      dispatch(
        alertActions.setAlertMsg({
          message: 'DBK Claim File is Successfully Freezed',
          alertType: AlertStatus.SUCCESS,
        })
      );
      setTimeout(() => navigate(`${Path.DUTY_DRAWBACK}`), 1000);
      // INFO: Reset the current claim details after freezing it
      dispatch(
        dutyDrawbackActions.setDbkClaim({...initialClaimDetails, claimTxnId})
      );
    }
    setIsFreezModal(false); // Closing the Modal
  };

  const handleEdit = () => {
    navigate(`${Path.DUTY_DRAWBACK}${Path.DBK_CLAIM}${Path.SB}`);
  };

  const getFormatData = (data: IDbkCalculationData) => {
    return [
      {
        title: 'Total Available (Quantity)',
        sb: data['total-sb-available'],
        prod: data['total-prod-available'],
        prodQty: data['total-prod-qty-available'],
        boe: data['total-boe-available'],
        boeItems: data['total-boe-items-available'],
        itemsQty: data['total-boe-items-qty-available'],
      },
      {
        title: 'Considered for DBK (Quantity)',
        sb: data['total-sb-considered'],
        prod: data['total-prod-considered'],
        prodQty: data['total-prod-qty-considered'],
        boe: data['total-boe-considered'],
        boeItems: data['total-boe-items-considered'],
        itemsQty: data['total-boe-items-qty-considered'],
      },
    ];
  };

  const summaryCalculation = useCallback(async () => {
    const payload = {
      pan,
      email,
      claimTxnId,
    };
    const {data} = await getSummaryCalculation(payload);
    const formatData = getFormatData(data);
    setSummaryCalculationData(formatData);
  }, [email, claimTxnId, pan]);

  useEffect(() => {
    if (!claimCalculationStatus) {
      summaryCalculation();
    }
  }, [claimCalculationStatus, summaryCalculation]);

  const getFinalDbkSummary = useCallback(async () => {
    const headers = {
      claimTxnId,
      pan,
      email,
      searchKey,
      searchValue: debouncedValue,
      sortBy,
      sortingOrder,
    };
    const {data} = await getDbkFinalSummary(headers, page, +showEntries);

    const formatData = getFormatData(data);
    setSummaryCalculationData(formatData);
    setFinalSummaryTableData(data?.['dbk-summary']?.['prod-dtls-list']);
    setTotalRecords(data['total-records']);
    setTotalClaimValue(data?.['dbk-summary']?.['total-claim-value'] || 0);
    setIsUnqualifiedProds(
      data?.['dbk-summary']?.['contains-unqualified-prods']
    );
    setAirDisqualificationCount(
      data?.['dbk-summary']?.['air-disqualification-count'] || 0
    );
    setShortFallDisqualificationCount(
      data?.['dbk-summary']?.['shortfall-disqualification-count'] || 0
    );
    // INFO: Adding below line to avoid the warnings because searchKey not needed to add as dependency
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    claimTxnId,
    email,
    page,
    pan,
    showEntries,
    debouncedValue,
    sortBy,
    sortingOrder,
  ]);

  useEffect(() => {
    if (claimCalculationStatus) {
      getFinalDbkSummary();
    }
    if (claimCalculationStatus.includes('Freezed')) {
      setIsFreezed(true);
    }
  }, [claimCalculationStatus, getFinalDbkSummary]);

  return (
    <div className='calculation-summary-container'>
      {!claimCalculationStatus ? (
        <DbkClaimSubHeader step='4' subTitle='Duty Drawback Calculation'>
          <EximButton size='small' onClick={calculationProcess}>
            Start Process
          </EximButton>
        </DbkClaimSubHeader>
      ) : (
        <DbkClaimSubHeader
          step='4'
          subTitle='Final Summary'
          isSecondSub
          secondSubTitle={`Total DBK Claim value - INR ${totalClaimValue}`}>
          <EximButton
            size='small'
            color='secondary'
            onClick={() => setIsDownloadReport(true)}>
            Download Report
          </EximButton>
          {isUnqualifiedProds ? (
            <EximButton
              size='small'
              onClick={() => setIsRecomputeModal(true)}
              disabled={
                airDisqualificationCount + shortFallDisqualificationCount ===
                totalRecords
              }>
              Recompute & Freeze
            </EximButton>
          ) : (
            <EximButton
              size='small'
              onClick={() => setIsFreezModal(true)}
              disabled={isFreezed}>
              Freeze
            </EximButton>
          )}
        </DbkClaimSubHeader>
      )}

      <EximPaper>
        <div className='calculation-summary-table-container'>
          <div className='table-title-section'>
            <EximTypography
              variant='h3'
              fontWeight='semi-bold'
              classNames='table-title'>
              Duty Drawback Calculation Summary
            </EximTypography>
            <EximButton
              size='small'
              color='secondary'
              onClick={handleEdit}
              disabled={isFreezed}>
              Edit Selections
            </EximButton>
          </div>
          <table className='calculation-summary-table'>
            <TableHeader mainHeader={DBK_CALCULATION_TABLE_HERDER} />
            {summaryCalculationData?.length > 0 ? (
              <TableBody className='calculation-summary-tbody'>
                {summaryCalculationData?.map((item, index) => (
                  <TableRow key={`${item.title}`}>
                    <TableCell>{item.title}</TableCell>
                    <TableCell>{item.sb ?? '-'}</TableCell>
                    <TableCell>{item.prod ?? '-'}</TableCell>
                    <TableCell>{item.boe ?? '-'}</TableCell>
                    <TableCell>{item.itemsQty ?? '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={DBK_CALCULATION_TABLE_HERDER.length} />
            )}
          </table>
          {claimCalculationStatus && (
            <FinalSummary
              handleSearchQuery={handleSearchQuery}
              handleShowEntries={handleShowEntries}
              finalSummaryTableData={finalSummaryTableData}
              searchQuery={searchValue}
              showEntries={showEntries}
              totalRecords={totalRecords}
              handlePageChange={handlePageChange}
              handleSortBy={handleSortBy}
              handleSearchKey={handleSearchKey}
              searchKey={searchKey}
              page={page}
            />
          )}
        </div>
      </EximPaper>

      {/* Download Reports Modal */}
      <div className='download-report-modal'>
        <EximModal
          isOpen={isDownloadReport}
          onClose={() => setIsDownloadReport(false)}
          onOutSideClickClose={() => setIsDownloadReport(false)}
          content={
            <DownloadFilesModalContent
              claimId={claimTxnId}
              isQualificationError={claimCalculationStatus.includes(
                'Qualification Error'
              )}
            />
          }
          footer={false}
          header='Download Report'
          closeIcon={<CloseIcon width='16' height='16' />}
        />
      </div>

      {/* Freez DBK Claim Modal */}
      <div className='freez-claim-modal'>
        <EximModal
          isOpen={isFreezModal}
          onClose={() => setIsFreezModal(false)}
          onOutSideClickClose={() => setIsFreezModal(false)}
          content={
            <div className='freez-modal-container'>
              <EximTypography variant='h4'>
                Are you sure, you want to Freeze DBK Claim?
              </EximTypography>
              <span className='btn-container'>
                <EximButton
                  size='small'
                  color='secondary'
                  onClick={() => setIsFreezModal(false)}>
                  No
                </EximButton>
                <EximButton size='small' onClick={handleFreezDbkClaim}>
                  Yes
                </EximButton>
              </span>
            </div>
          }
          header={
            <EximTypography variant='h2' fontWeight='bold'>
              Freeze DBK Claim
            </EximTypography>
          }
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>

      {/* Recompute & Freez DBK Claim Modal */}
      <div className='recompute-freez-claim-modal'>
        <EximModal
          isOpen={isRecomputeModal}
          onClose={() => setIsRecomputeModal(false)}
          onOutSideClickClose={() => setIsRecomputeModal(false)}
          content={
            <RecomputeModalContent
              isChecked={isFourFifthRuleSelected}
              setIsChecked={setIsFourFifthRuleSelected}
              onClose={() => setIsRecomputeModal(false)}
              handleRecomputeAndFreeze={handleFreezDbkClaim}
              shortFallDisqualificationCount={shortFallDisqualificationCount}
              airDisqualificationCount={airDisqualificationCount}
            />
          }
          header={null}
          closeIcon={<CloseIcon width={17} height={17} />}
          footer={false}
        />
      </div>
    </div>
  );
}

export default DBKCalculation;
