@import '@utils/main.scss';

.upload-doc-table-container {
  width: 100%;
  .upload-doc-table {
    @include margin-top(16px);
    width: 100%;
    border-spacing: 0;
    .upload-doc-tbody {
      .browse-btn {
        .file-input-container {
          width: 67px;
          .file-button {
            background-color: $primary;
            color: $white;
          }
        }
      }
    }
  }
}

.upload-doc-modal {
  .modal-btn-container {
    @include flex-item(_, space-between, center);
    @include margin-top(30px);
    span {
      @include flex-item(_, space-between, center, _, 20px);
      .button-wrapper {
        width: 100px;
      }
    }
    .button-wrapper {
      width: 67px;
      .base-btn {
        font-size: $font-size-sm;
        @include padding(7px 5px);
      }
    }
  }
}
