import TableHeader from '@common/components/TableHeader';
import UploadFiles from '@common/components/UploadFiles';
import {
  AlertStatus,
  ResponseStatus,
  SupportedFileTypes,
} from '@common/constants';
import {checkFilesExtension, formatFilesData} from '@common/helpers';
import {ICustomAxiosResp} from '@common/interfaces';
import {alertActions} from '@core/api/store/alertReducer';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {uploadSupportedDoc} from '@pages/DutyDrawback/api';
import {UPLOAD_DOCUMENTS_MODAL_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximInput from '@shared/components/EximInput';
import {AccordionFile} from '@shared/icons';
import {RootState, dispatch} from '@store';
import {ChangeEvent, useState} from 'react';
import {useSelector} from 'react-redux';

import './index.scss';

interface IUploadDocModalProps {
  onClose: () => void;
  claimId: string;
  getSupportedDoc: () => void;
}

export default function UploadDocModalContent({
  onClose,
  claimId,
  getSupportedDoc,
}: IUploadDocModalProps) {
  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {panNumber},
  } = useSelector((state: RootState) => state);

  const [filesArray, setFilesArray] = useState<File[]>([]);
  const [fileIds, setFileIds] = useState<{
    [key: string]: boolean;
  }>({});
  const [filesCategory, setFilesCategory] = useState<{
    [key: string]: string;
  }>({});

  const handleFileSelect = async (event: ChangeEvent<HTMLInputElement>) => {
    const {files} = event.target;
    if (files) {
      const selectedFilesArray = Array.from(files);
      if (
        checkFilesExtension(selectedFilesArray, [
          SupportedFileTypes.EXCEL,
          SupportedFileTypes.JPEG,
          SupportedFileTypes.JPG,
          SupportedFileTypes.PDF,
          SupportedFileTypes.PNG,
          SupportedFileTypes.RAR,
          SupportedFileTypes.ZIP,
          SupportedFileTypes.PLAIN_TEXT,
        ])
      ) {
        setFilesArray(selectedFilesArray);
        setFileIds(
          selectedFilesArray.reduce(
            (acc, item) => ({...acc, [item.lastModified.toString()]: false}),
            {}
          )
        );
        setFilesCategory(
          selectedFilesArray.reduce(
            (acc, item) => ({...acc, [item.lastModified.toString()]: ''}),
            {}
          )
        );
      } else {
        dispatch(
          alertActions.setAlertMsg({
            code: 400,
            message:
              'Please select the txt,pdf,xlsx,jpg,jpeg,png,zip,rar files only!',
            alertType: 'danger',
          })
        );
      }
    }
  };

  const handleAddFileCategory = (
    event: ChangeEvent<HTMLInputElement>,
    id: string
  ) => {
    filesCategory[id] = event.target.value;
  };

  const handleSelectAll = () => {
    const isSelectedAll = Object.values(fileIds)?.includes(false);
    const updatedSelectedInvoice = Object.keys(fileIds).reduce(
      (acc, itemId) => ({
        ...acc,
        [itemId]: isSelectedAll ? true : !fileIds[itemId],
      }),
      {}
    );
    setFileIds(updatedSelectedInvoice);
  };

  const handleSingleSelect = (id: string) => {
    setFileIds({...fileIds, [id]: !fileIds[id]});
  };

  const handleDeleteFiles = () => {
    // Extracting the selected file Ids
    const filesIdToDelete = Object.keys(fileIds).filter(
      (key) => fileIds[key] === true
    );

    // Deleting the selected files
    const updatedFiles = filesArray.filter(
      (file) => !filesIdToDelete.includes(file.lastModified.toString())
    );
    setFilesArray(updatedFiles);

    // Updating file Ids in object
    setFileIds(
      updatedFiles.reduce(
        (acc, item) => ({...acc, [item.lastModified.toString()]: false}),
        {}
      )
    );

    // Updating the files category state after deleting files
    setFilesCategory(
      updatedFiles.reduce(
        (acc, item) => ({
          ...acc,
          [item.lastModified.toString()]: filesCategory[item.lastModified],
        }),
        {}
      )
    );
  };

  const handleFileUpload = async () => {
    // Extracting the selected files Ids
    const filesIdToUpload = Object.keys(fileIds).filter(
      (key) => fileIds[key] === true
    );
    const filesToUpload = filesArray.filter((file) =>
      filesIdToUpload.includes(file.lastModified.toString())
    );

    const payload = {
      pan: panNumber,
      email,
      claimTxnId: claimId,
    };
    if (filesToUpload.length === 0) {
      dispatch(
        alertActions.setAlertMsg({
          code: 400,
          message: 'Please select the file',
          alertType: AlertStatus.DANGER,
        })
      );
    } else {
      const {newFileArray, categoriesArray} = formatFilesData(
        filesArray,
        filesCategory
      );

      const response = (await uploadSupportedDoc(
        payload,
        newFileArray,
        categoriesArray
      )) as ICustomAxiosResp;

      if (response.status.toString() === ResponseStatus.SUCCESS) {
        dispatch(
          alertActions.setAlertMsg({
            code: response.statusCode,
            message: response.msg,
            alertType: AlertStatus.SUCCESS,
          })
        );
        onClose(); // closing the modal
        getSupportedDoc(); // Update table data after uploading files
      }
    }
  };

  return (
    <div className='upload-doc-table-container'>
      <table className='upload-doc-table'>
        <TableHeader
          checked={
            Object.keys(fileIds)?.length > 0
              ? !Object.values(fileIds)?.includes(false)
              : false
          }
          mainHeader={UPLOAD_DOCUMENTS_MODAL_TABLE_HEADER}
          onChange={handleSelectAll}
        />
        <TableBody className='upload-doc-tbody'>
          {filesArray.length > 0 ? (
            filesArray.map((file: File, index) => (
              <TableRow key={file.lastModified}>
                <TableCell className='checkbox-td'>
                  <EximCheckbox
                    id='fileName'
                    color='#2CB544'
                    size='medium'
                    checked={fileIds[file.lastModified]}
                    onChange={() =>
                      handleSingleSelect(file.lastModified.toString())
                    }
                  />
                </TableCell>
                <TableCell>
                  <AccordionFile fill='#012D66' /> &nbsp; {file.name}
                </TableCell>
                <TableCell className='editable-td'>
                  <EximInput
                    id='category'
                    name='category'
                    dataTestid='category'
                    placeholder='Enter Category'
                    maxLength={64}
                    onChange={(event: ChangeEvent<HTMLInputElement>) =>
                      handleAddFileCategory(event, file.lastModified.toString())
                    }
                  />
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell />
              <TableCell>
                <AccordionFile fill='#012D66' /> &nbsp; No files uploaded
              </TableCell>
              <TableCell className='editable-td'>
                <EximInput
                  id='category'
                  name='category'
                  dataTestid='category'
                  placeholder='Enter Category'
                  disabled
                  onChange={() => {
                    /* */
                  }}
                />
              </TableCell>
            </TableRow>
          )}
          <TableRow>
            <TableCell colSpan={3} className='browse-btn'>
              <UploadFiles
                icon={null}
                multiple
                title='Browse'
                onChange={handleFileSelect}
                accept='.txt,.pdf,.xlsx,.jpg,.jpeg,.png,.zip,.rar'
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </table>
      <div className='modal-btn-container'>
        <EximButton
          size='small'
          onClick={handleDeleteFiles}
          disabled={!Object.values(fileIds).includes(true)}>
          Delete
        </EximButton>
        <span>
          <EximButton size='small' color='secondary' onClick={onClose}>
            Cancel
          </EximButton>
          <EximButton size='small' onClick={handleFileUpload}>
            Confirm
          </EximButton>
        </span>
      </div>
    </div>
  );
}
