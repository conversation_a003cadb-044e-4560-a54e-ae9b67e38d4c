import EmptyTable from '@common/components/EmptyTable';
import TableFooter from '@common/components/TableFooter';
import TableHeader from '@common/components/TableHeader';
import TableSearchFilter from '@common/components/TableSearchFilter';
import {Path, ResponseStatus} from '@common/constants';
import {
  formatAmount,
  formatDate,
  getRenderData,
  searchTableData,
} from '@common/helpers';
import {IBoeAllDataList, IDbkBoeDetails} from '@common/interfaces';
import {TableBody, TableCell, TableRow} from '@mui/material';
import {getBoeList, saveBoeDetails} from '@pages/DutyDrawback/api';
import DbkClaimSubHeader from '@pages/DutyDrawback/components/DbkClaimSubHeader';
import {DBK_CLAIM_BOE_TABLE_HEADER} from '@pages/DutyDrawback/utils';
import EximButton from '@shared/components/EximButton';
import EximCheckbox from '@shared/components/EximCheckbox';
import EximPaper from '@shared/components/EximPaper';
import {RootState} from '@store';
import {ChangeEvent, useCallback, useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigate} from 'react-router';

import './index.scss';

function DbkClaimBOE() {
  const navigate = useNavigate();

  const {
    auth: {
      userData: {email},
    },
    dutyDrawback: {
      panNumber,
      dbkClaim: {claimTxnId},
    },
  } = useSelector((state: RootState) => state);
  const tableHeads = useMemo(() => DBK_CLAIM_BOE_TABLE_HEADER(false), []);
  const [page, setPage] = useState<number>(1);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const [boeAllData, setBoeAllData] = useState<IBoeAllDataList>();
  const [boeData, setBoeData] = useState<IDbkBoeDetails[]>([]);
  const [searchBoeData, setSearchBoeData] = useState<IDbkBoeDetails[]>([]);
  const [currentData, setCurrentData] = useState<IDbkBoeDetails[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);

  const handleNextBtn = async (isPaginate: boolean) => {
    const payload = {
      pan: panNumber,
      email,
      intermediateSave: isPaginate,
    };
    if (boeAllData) {
      const requestBody = {...boeAllData, 'boe-list': currentData};
      const data = await saveBoeDetails(payload, requestBody);
      if (data.status.toString() === ResponseStatus.SUCCESS && !isPaginate) {
        navigate(
          `${Path.DUTY_DRAWBACK}${Path.DBK_CLAIM}${Path.BOE_ITEM_SELECTION}`
        );
      }
    }
  };

  const handlePageChange = (pageNumber: string | number) => {
    handleNextBtn(true); // function call for intermediate save
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    const {value} = event.target;
    setSearchQuery(value);
    setPage(1);
    const searchData = searchTableData(boeData as [], value);
    setSearchBoeData(searchData);
  };

  const handleSingleSelect = (id: string) => {
    const updatedBoeData = currentData?.map((item: IDbkBoeDetails) =>
      item['boe-ref-id-list'][0] === id
        ? {...item, 'is-selected': !item['is-selected']}
        : item
    );

    const totalSelected = updatedBoeData?.filter(
      (item) => item['is-selected']
    ).length;

    setIsSelectedAll(totalSelected === updatedBoeData.length);
    if (searchQuery.length > 0) setSearchBoeData(updatedBoeData);
    else setBoeData(updatedBoeData);
  };

  const handleSelectAll = () => {
    const isAllSelected = currentData?.every((item) => item['is-selected']);

    const updatedBoeData = currentData?.map((item: IDbkBoeDetails) => ({
      ...item,
      'is-selected': !isAllSelected,
    }));

    setIsSelectedAll(!isAllSelected);
    if (searchQuery.length > 0) setSearchBoeData(updatedBoeData);
    else setBoeData(updatedBoeData);
  };

  const getBillOfEntriesData = useCallback(async () => {
    const payload = {
      pan: panNumber,
      email,
      claimTxnId,
    };
    const {data} = await getBoeList(payload, page, +showEntries);
    const isAllSelected = data?.['boe-list']?.every(
      (item: IDbkBoeDetails) => item['is-selected']
    );
    setIsSelectedAll(isAllSelected); // when all item selected then header should be checked
    setTotalRecords(data?.['total-records']);
    setBoeData(data?.['boe-list']);
    setBoeAllData(data);
  }, [claimTxnId, email, panNumber, page, showEntries]);

  useEffect(() => {
    getBillOfEntriesData();
  }, [getBillOfEntriesData]);

  useEffect(() => {
    const renderData = getRenderData(
      boeData as [],
      searchBoeData as [],
      searchQuery
    );
    setCurrentData(renderData);
  }, [boeData, searchBoeData, searchQuery]);

  return (
    <div className='dbk-claim-boe-container'>
      <DbkClaimSubHeader step='3.1' subTitle='Bill of Entries'>
        <EximButton size='small' onClick={() => handleNextBtn(false)}>
          Next
        </EximButton>
      </DbkClaimSubHeader>
      <EximPaper>
        <div className='boe-table-container'>
          <TableSearchFilter
            handleSearchQuery={handleSearchQuery}
            handleShowEntries={handleShowEntries}
          />
          <table className='boe-table'>
            <TableHeader
              mainHeader={tableHeads}
              checked={isSelectedAll}
              onChange={handleSelectAll}
            />
            {currentData?.length > 0 ? (
              <TableBody className='boe-tbody'>
                {currentData?.map((item: IDbkBoeDetails, index) => (
                  <TableRow key={`${item['boe-ref-id-list'][0]}`}>
                    <TableCell className='checkbox-td'>
                      <EximCheckbox
                        id='boeTdCheckbox'
                        name='invoiceRecord'
                        color='#2CB544'
                        size='medium'
                        checked={item['is-selected']}
                        onChange={() =>
                          handleSingleSelect(item?.['boe-ref-id-list'][0])
                        }
                      />
                    </TableCell>
                    <TableCell>{item['boe-no']}</TableCell>
                    <TableCell>{formatDate(item['boe-date'])}</TableCell>
                    <TableCell>{formatAmount(item['item-count'])}</TableCell>
                    <TableCell>
                      {formatAmount(item['total-item-qty-available'])}
                    </TableCell>
                    <TableCell>
                      {formatAmount(item['total-accessable-val'])}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            ) : (
              <EmptyTable colSpan={tableHeads.length} />
            )}
          </table>
          <TableFooter
            page={page}
            searchQuery={searchQuery}
            showEntries={showEntries}
            totalRecords={totalRecords}
            searchData={searchBoeData as []}
            renderData={boeData as []}
            handlePageChange={handlePageChange}
            hasBackendPagination
          />
        </div>
      </EximPaper>
    </div>
  );
}

export default DbkClaimBOE;
