<svg id="Group_12995" data-name="Group 12995" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="500" height="640.55" viewBox="0 0 500 640.55">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_14006" data-name="Rectangle 14006" width="500" height="640.55" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_14008" data-name="Rectangle 14008" width="19.5" height="209.483" transform="translate(205.079 0.25)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_14010" data-name="Rectangle 14010" width="310.08" height="170.44" transform="translate(44.14 265.139)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_14012" data-name="Rectangle 14012" width="310.08" height="170.629" transform="translate(149.85 386.905)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <rect id="Rectangle_14014" data-name="Rectangle 14014" width="13.55" height="120" transform="translate(199.078 401.082)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <rect id="Rectangle_14016" data-name="Rectangle 14016" width="13.55" height="115.852" transform="translate(185.528 408.251)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-13">
      <rect id="Rectangle_14018" data-name="Rectangle 14018" width="14.014" height="114.466" transform="translate(171.246 411.706)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-15">
      <rect id="Rectangle_14020" data-name="Rectangle 14020" width="13.983" height="110.793" transform="translate(158.735 417.564)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-17">
      <rect id="Rectangle_14022" data-name="Rectangle 14022" width="19.5" height="331.108" transform="translate(311.018 49.202)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-18">
      <rect id="Rectangle_14021" data-name="Rectangle 14021" width="19.5" height="333.464" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_12960" data-name="Group 12960">
    <g id="Group_12959" data-name="Group 12959" clip-path="url(#clip-path)">
      <path id="Path_4011" data-name="Path 4011" d="M440.5,390.55c0,105.211-85.289,190.5-190.5,190.5S59.5,495.761,59.5,390.55s85.29-190.5,190.5-190.5,190.5,85.29,190.5,190.5" fill="#fff"/>
    </g>
  </g>
  <path id="Path_4012" data-name="Path 4012" d="M55.923,295l-.748-.62,158.511-88.748.5.866-99.029,59.823-.516-.855,76.624-46.29Z" fill="#404040"/>
  <g id="Group_12994" data-name="Group 12994">
    <g id="Group_12993" data-name="Group 12993" clip-path="url(#clip-path)">
      <path id="Path_5674" data-name="Path 5674" d="M158.734,424.591l193.6-3.01,97.955,22.71,7.66,99.049-304.533-10.4Z" fill="#911f00"/>
      <g id="Group_12964" data-name="Group 12964">
        <g id="Group_12963" data-name="Group 12963" clip-path="url(#clip-path-3)">
          <g id="Group_12962" data-name="Group 12962">
            <g id="Group_12961" data-name="Group 12961" clip-path="url(#clip-path-3)">
              <path id="Path_4013" data-name="Path 4013" d="M224.568,173.71a1.693,1.693,0,0,1,.01.23c0,.75-.01,1.69-.02,2.72l-7.13,12.77q-.315-.555-.6-1.17Z" fill="#ffb450"/>
              <path id="Path_4014" data-name="Path 4014" d="M224.559,173.46a2.009,2.009,0,0,1,.01.25l-7.74,14.55a16.71,16.71,0,0,1-.9-2.4l8.02-13.88a4.56,4.56,0,0,1,.61,1.46Z" fill="#404040"/>
              <path id="Path_4015" data-name="Path 4015" d="M217.429,189.429l7.13-12.77c-.02,1.2-.04,2.52-.08,3.78l-5.94,10.63a12.341,12.341,0,0,1-1.11-1.64" fill="#404040"/>
              <path id="Path_4016" data-name="Path 4016" d="M218.539,191.069l5.94-10.629c-.02.97-.06,1.91-.1,2.74l-4.78,9.1a13.156,13.156,0,0,1-1.06-1.211" fill="#ffb450"/>
              <path id="Path_4017" data-name="Path 4017" d="M219.6,192.279l4.78-9.1c-.03.5-.06.959-.09,1.369-.06.83-.18,1.971-.34,3.2l-.01.01-3.12,5.581a10.713,10.713,0,0,1-1.22-1.061" fill="#404040"/>
              <path id="Path_4018" data-name="Path 4018" d="M211.619,166.17c.01-.25.02-.5.03-.73,0,0,0-4.75,2.81-3,1.06.66,2.09,1.29,3.1,1.98.27.19.54.38.8.57.33.24.65.49.97.76a1.149,1.149,0,0,1,.17.14l.63.54c.34.31.68.63,1.01.98l.78.84.02.02c.15.18.31.36.46.55a9.365,9.365,0,0,1,2.16,4.62,4.56,4.56,0,0,0-.61-1.46,10.609,10.609,0,0,0-.97-1.33c-.38-.46-.81-.91-1.26-1.36q-.48-.48-.99-.93c-.33-.3-.67-.59-1-.87-.36-.3-.71-.58-1.06-.85-.49-.39-.96-.74-1.38-1.05-.52-.38-.98-.7-1.34-.95-.22-.14-.39-.26-.53-.34-.09-.06-.16-.1-.21-.13a4.084,4.084,0,0,0-.47-.23c-.67-.26-1.33-.12-1.71,1.8v.01a15.487,15.487,0,0,0-.05,2.84c.04.87.1,1.87.18,2.96.07.99.16,2.04.25,3.12.08.91.17,1.83.26,2.75.08.9.17,1.8.26,2.67v.01c.09.86.18,1.68.27,2.45.13,1.21.25,2.28.35,3.12.07.51.12.93.16,1.25.04.3.08.58.12.83a5.686,5.686,0,0,0,.88,2.59v.01c.24.31.49.5.75.82a10.012,10.012,0,0,0,1.33,1.24c.38.3.81.62,1.25.94.43.3.86.61,1.28.88.22.14.44.28.64.4.15.09.28.17.4.23,1.05.6,1.1.44,1.1.4a6.419,6.419,0,0,1,.09,1.62c-.08.62-.36,1.06-1.15.5-1.69-1.19-4.82-3.56-4.82-3.56a7.057,7.057,0,0,1-2.81-4.88c-.16-1.09-.43-2.98-.74-5.28-.14-1.11-.3-2.31-.45-3.56-.34-2.91-.67-6.09-.85-8.97-.02-.38-.04-.75-.06-1.12-.04-.97-.07-1.89-.07-2.74v-.15c0-.33.01-.65.02-.95" fill="#d37e0d"/>
              <path id="Path_4019" data-name="Path 4019" d="M223.949,171.98l-8.02,13.88a18.155,18.155,0,0,1-.44-1.86l7.49-13.35a10.609,10.609,0,0,1,.97,1.33" fill="#ffb450"/>
              <path id="Path_4020" data-name="Path 4020" d="M220.818,193.34l3.12-5.58c-.17,1.38-.4,2.88-.66,4.24h-.01l-1.38,2.06a9.122,9.122,0,0,1-1.07-.72" fill="#ffb450"/>
              <path id="Path_4021" data-name="Path 4021" d="M223.269,192h.01c-.18.91-.37,1.76-.58,2.47a8.136,8.136,0,0,1-.81-.41Z" fill="#404040"/>
              <path id="Path_4022" data-name="Path 4022" d="M222.979,170.65,215.488,184c-.11-.55-.19-1.14-.259-1.75-.051-.42-.091-.85-.141-1.26l6.63-11.7c.45.45.88.9,1.261,1.36" fill="#404040"/>
              <path id="Path_4023" data-name="Path 4023" d="M222.7,194.469a8.146,8.146,0,0,1-.24.78v.01c0,.04-.05.2-1.1-.4l.53-.8a8.138,8.138,0,0,0,.81.41" fill="#232323"/>
              <path id="Path_4024" data-name="Path 4024" d="M221.4,197.38c.79.56,1.07.12,1.15-.5-.16,2.23-1.92.76-1.92.76l-3.22-2.33-2.16-1.56s-.77-.3-1.45-3.6c-.07-.33-.13-.69-.2-1.08-.14-.87-.28-1.9-.39-3.13-.07-.7-.13-1.45-.18-2.28.31,2.3.58,4.19.74,5.28a7.057,7.057,0,0,0,2.81,4.88s3.13,2.37,4.82,3.56" fill="#d37e0d"/>
              <path id="Path_4025" data-name="Path 4025" d="M221.939.6V168.27l-.02-.02c-.261-.29-.511-.57-.78-.84V.6Z" fill="#232323"/>
              <path id="Path_4026" data-name="Path 4026" d="M221.889,194.06l-.53.8c-.12-.06-.25-.14-.4-.23-.2-.12-.42-.26-.64-.4l.5-.89a9.121,9.121,0,0,0,1.07.72" fill="#e0902f"/>
              <path id="Path_4027" data-name="Path 4027" d="M221.718,169.29l-6.63,11.7c-.09-.81-.19-1.61-.28-2.4l5.92-10.23q.51.45.99.93" fill="#ffb450"/>
              <path id="Path_4028" data-name="Path 4028" d="M220.818,193.34l-.5.89c-.42-.27-.85-.58-1.28-.88l.56-1.07a10.8,10.8,0,0,0,1.22,1.06" fill="#232323"/>
              <path id="Path_4029" data-name="Path 4029" d="M220.729,168.36l-5.921,10.23q-.165-1.3-.3-2.55l5.221-8.55c.329.28.669.57,1,.87" fill="#404040"/>
              <path id="Path_4030" data-name="Path 4030" d="M220.128.6V166.43q-.315-.285-.63-.54a1.151,1.151,0,0,0-.17-.14V.6Z" fill="#232323"/>
              <path id="Path_4031" data-name="Path 4031" d="M219.729,167.49l-5.221,8.55c-.11-.89-.21-1.75-.31-2.58l4.47-6.82c.35.27.7.55,1.061.85" fill="#ffb450"/>
              <path id="Path_4032" data-name="Path 4032" d="M218.539,191.069a13.156,13.156,0,0,0,1.06,1.211l-.56,1.07c-.44-.32-.87-.64-1.25-.94Z" fill="#e0902f"/>
              <path id="Path_4033" data-name="Path 4033" d="M219.2,205.19a3.28,3.28,0,0,1-.05,1.65,3.617,3.617,0,0,1-3.44,1.85c-2.99.06-4.62-5.25-4.62-5.25a11.333,11.333,0,0,1,.87-5.43s-.54,4.43,1.07.68,2.54-2.75,2.92-2.44-1.43,3.26-1.11,5.5a9.555,9.555,0,0,0,.83,2.93c.04.1.08.2.13.3a.031.031,0,0,0,.01.02c.09.25,1.15,3.1,2.44.46.46-.92.8-.55.95-.27m-7.469-1.75a4.835,4.835,0,0,0,4.969,4.11c-3.52-1.75-3.1-7-3.1-7a6.457,6.457,0,0,0-1.02,1.33c-.2.5-1.53-2.25-.849,1.56" fill="#6d6d6d"/>
              <path id="Path_4034" data-name="Path 4034" d="M219.039,204.63a3.171,3.171,0,0,1,.16.56c-.15-.28-.49-.65-.95.27-1.29,2.64-2.35-.21-2.44-.46.04.08.09.17.14.25,1.32,2.13,1.78-.06,1.46-.74s.9-1.75,1.63.12" fill="#545454"/>
              <path id="Path_4035" data-name="Path 4035" d="M215.708,208.69a3.617,3.617,0,0,0,3.44-1.85c-.75,2.77-4.72,4.51-6.83.67a10.27,10.27,0,0,1-1.23-4.07s1.63,5.31,4.62,5.25" fill="#545454"/>
              <path id="Path_4036" data-name="Path 4036" d="M218.669,166.639l-4.47,6.82c-.11-1.07-.211-2.08-.28-3.01l3.369-4.86c.42.31.89.66,1.381,1.05" fill="#404040"/>
              <path id="Path_4037" data-name="Path 4037" d="M218.539,191.069l-.75,1.341a9.942,9.942,0,0,1-1.33-1.241l.97-1.739a12.328,12.328,0,0,0,1.11,1.639" fill="#232323"/>
              <path id="Path_4038" data-name="Path 4038" d="M218.358.6V164.99c-.26-.19-.53-.38-.8-.57V.6Z" fill="#232323"/>
              <path id="Path_4039" data-name="Path 4039" d="M217.429,189.429l-.97,1.74c-.26-.32-.51-.51-.75-.82v-.01l1.12-2.08q.285.615.6,1.17" fill="#e0902f"/>
              <path id="Path_4040" data-name="Path 4040" d="M217.409,195.31s.09,2.57-.71,3.44a7.069,7.069,0,0,0-1.03,5.93,9.555,9.555,0,0,1-.83-2.93c-.32-2.24,1.5-5.18,1.11-5.5s-1.3-1.31-2.92,2.44-1.07-.68-1.07-.68a8.929,8.929,0,0,0,.29-1.25,3.329,3.329,0,0,0-.29-2.29,11.15,11.15,0,0,1-1.85-2.85h.01s.46-1.47,3.68-1.47c.68,3.3,1.45,3.6,1.45,3.6Z" fill="#545454"/>
              <path id="Path_4041" data-name="Path 4041" d="M217.289,165.59l-3.37,4.86a29.051,29.051,0,0,1-.11-2.96l2.14-2.85c.36.25.82.57,1.34.95" fill="#ffb450"/>
              <path id="Path_4042" data-name="Path 4042" d="M216.829,188.259l-1.12,2.081a5.691,5.691,0,0,1-.88-2.59h.01l1.09-1.891a16.62,16.62,0,0,0,.9,2.4" fill="#232323"/>
              <path id="Path_4043" data-name="Path 4043" d="M216.7,207.549a4.835,4.835,0,0,1-4.97-4.109c-.68-3.81.65-1.06.85-1.56a6.473,6.473,0,0,1,1.02-1.331s-.42,5.25,3.1,7" fill="#7f7f7f"/>
              <path id="Path_4044" data-name="Path 4044" d="M215.949,164.639l-2.14,2.85c.03-2.21.46-3.45,1.61-3.19.14.08.31.2.53.34" fill="#404040"/>
              <path id="Path_4045" data-name="Path 4045" d="M215.929,185.86l-1.09,1.89h-.01c-.04-.25-.08-.53-.12-.83-.04-.32-.09-.74-.16-1.25l.94-1.67a18.155,18.155,0,0,0,.44,1.86" fill="#e0902f"/>
              <path id="Path_4046" data-name="Path 4046" d="M215.488,184l-.94,1.67c-.1-.84-.22-1.91-.35-3.12l.89-1.56c.05.41.09.84.14,1.26.07.61.15,1.2.26,1.75" fill="#232323"/>
              <path id="Path_4047" data-name="Path 4047" d="M215.419,164.3c-1.151-.259-1.58.981-1.61,3.191l-.83,1.1a15.421,15.421,0,0,1,.049-2.84v-.01l1.71-1.8a4.006,4.006,0,0,1,.47.23c.05.03.12.07.211.129" fill="#232323"/>
              <path id="Path_4048" data-name="Path 4048" d="M215.088,180.99l-.89,1.56c-.09-.77-.18-1.59-.27-2.45v-.01l.88-1.5c.09.79.19,1.59.28,2.4" fill="#e0902f"/>
              <path id="Path_4049" data-name="Path 4049" d="M214.809,178.59l-.88,1.5c-.09-.87-.18-1.77-.26-2.67l.84-1.38q.135,1.245.3,2.55" fill="#232323"/>
              <path id="Path_4050" data-name="Path 4050" d="M214.738,163.94l-1.71,1.8c.38-1.92,1.04-2.06,1.71-1.8" fill="#e0902f"/>
              <path id="Path_4051" data-name="Path 4051" d="M214.508,176.04l-.84,1.38c-.09-.92-.18-1.84-.26-2.75l.79-1.21c.1.83.2,1.69.31,2.58" fill="#e0902f"/>
              <path id="Path_4052" data-name="Path 4052" d="M214.2,173.46l-.79,1.21c-.09-1.08-.18-2.13-.25-3.12l.76-1.1c.07.93.17,1.94.28,3.01" fill="#232323"/>
              <path id="Path_4053" data-name="Path 4053" d="M213.919,170.449l-.761,1.1c-.08-1.09-.14-2.09-.179-2.96l.83-1.1a28.485,28.485,0,0,0,.11,2.96" fill="#e0902f"/>
              <path id="Path_4054" data-name="Path 4054" d="M213.6,189.069c.07.391.13.75.2,1.081-3.22,0-3.68,1.47-3.68,1.47h-.01a3.084,3.084,0,0,1-.16-.731c-.04-.5-.05-1.169-.05-1.82h.05s.43.811,3.65,0" fill="#232323"/>
              <path id="Path_4055" data-name="Path 4055" d="M213.208,185.94c.11,1.23.25,2.26.39,3.13-3.22.81-3.65,0-3.65,0h-.05c0-1.15.05-2.25.05-2.25s.07-.88.89-.88Z" fill="#404040"/>
              <path id="Path_4056" data-name="Path 4056" d="M213.208,185.94h-2.37a24.206,24.206,0,0,1-3.49-8.25c.19.35,4.43,7.71,5.23,2.41.15,1.25.31,2.45.45,3.56.05.83.11,1.58.18,2.28" fill="#dd9845"/>
              <path id="Path_4057" data-name="Path 4057" d="M207.349,177.69a24.206,24.206,0,0,0,3.49,8.25c-.82,0-.89.88-.89.88s-.05,1.1-.05,2.25c0,.65.01,1.32.05,1.82a3.076,3.076,0,0,0,.16.73,11.15,11.15,0,0,0,1.85,2.85,3.329,3.329,0,0,1,.29,2.29,11.357,11.357,0,0,1-2.76-3.32v-.01a12.232,12.232,0,0,1-1.22-3.42c-.51-2.43-1.16-9.58-1.58-14.59,0-.05-.01-.1-.01-.14l.25.08c.09.71.23,1.49.42,2.33" fill="#b56504"/>
              <path id="Path_4058" data-name="Path 4058" d="M212.249,196.759a8.8,8.8,0,0,1-.291,1.25l-2.009-1.379a4.27,4.27,0,0,1-2.11-2.91s1.86,2.19,1.649-.28a11.323,11.323,0,0,0,2.761,3.319" fill="#d37e0d"/>
              <path id="Path_4059" data-name="Path 4059" d="M206.929,175.36a24.664,24.664,0,0,1-.22-2.54,5.257,5.257,0,0,1,5.02-1.69c.18,2.88.51,6.06.85,8.97-.8,5.3-5.04-2.06-5.23-2.41-.19-.84-.33-1.62-.42-2.33" fill="#ffb450"/>
              <path id="Path_4060" data-name="Path 4060" d="M211.669,170.009c.019.371.039.741.06,1.121a5.258,5.258,0,0,0-5.021,1.69c-.03-.811,0-1.271,0-1.271l.8-.25.8-.25.839-.259.8-.25.849-.26.8-.25Z" fill="#ffc176"/>
              <path id="Path_4061" data-name="Path 4061" d="M211.669,170.009l-.071.021v-2.76c0,.85.03,1.77.071,2.739" fill="#d37e0d"/>
              <path id="Path_4062" data-name="Path 4062" d="M211.6,166.159c.01,0,.01.01.02.01-.01.3-.02.62-.02.95Z" fill="#d37e0d"/>
              <path id="Path_4063" data-name="Path 4063" d="M211.6,167.27v2.76l-.8.25v-4.59c.36.2.64.37.8.47v1.11Z" fill="#232323"/>
              <path id="Path_4064" data-name="Path 4064" d="M211.6.6V166.16c-.16-.1-.44-.27-.8-.47V.6Z" fill="#232323"/>
              <path id="Path_4065" data-name="Path 4065" d="M210.8,165.69v4.59l-.85.26v-5.29l.85.44" fill="#d37e0d"/>
              <path id="Path_4066" data-name="Path 4066" d="M209.949,165.25v5.29l-.8.25V164.9c.28.11.55.23.8.35" fill="#232323"/>
              <path id="Path_4067" data-name="Path 4067" d="M209.949.6V165.25c-.25-.12-.52-.24-.8-.35V.6Z" fill="#232323"/>
              <path id="Path_4068" data-name="Path 4068" d="M209.488,193.44c.21,2.47-1.65.28-1.65.28-.32-1.12-.62-2.78-1.14-5.46-1.27-6.55-1.62-17.82-1.62-22.07a3.482,3.482,0,0,1,.03-.47c.17-.13,1.05-.79,1.31-.13a7.342,7.342,0,0,0-.15,1.6v3s.16,2.16.41,5.09c0,.04.01.09.01.14.42,5.01,1.07,12.16,1.58,14.59a12.232,12.232,0,0,0,1.22,3.42Z" fill="#ffa624"/>
              <path id="Path_4069" data-name="Path 4069" d="M209.148,164.9v5.89l-.84.26v-6.46a8.46,8.46,0,0,1,.84.31" fill="#d37e0d"/>
              <path id="Path_4070" data-name="Path 4070" d="M208.309,164.59v6.46l-.8.25v-6.92a6.312,6.312,0,0,1,.8.21" fill="#232323"/>
              <path id="Path_4071" data-name="Path 4071" d="M208.309.25V164.59a6.312,6.312,0,0,0-.8-.21V.25Z" fill="#232323"/>
              <path id="Path_4072" data-name="Path 4072" d="M207.508,164.38v6.92l-.8.25s-.03.46,0,1.27a24.664,24.664,0,0,0,.22,2.54l-.25-.08c-.25-2.93-.41-5.09-.41-5.09v-3a7.342,7.342,0,0,1,.15-1.6c.18-.84.51-1.15.73-1.26.12.01.24.03.36.05" fill="#d37e0d"/>
              <path id="Path_4073" data-name="Path 4073" d="M205.108,165.719c.17-1.19,1.01-1.49,2.04-1.39-.22.11-.55.42-.73,1.26-.26-.66-1.14,0-1.31.13" fill="#ffb450"/>
            </g>
          </g>
        </g>
      </g>
      <path id="Path_4074" data-name="Path 4074" d="M340.042,317.859,218.052,207.39l39.988,93.751-1.225-.286-40.531-95.131a3.041,3.041,0,0,1-.336-.474c-.15-.27-.288-.583-.288-.583.611-.271,1.561.621,1.561.621L340.917,317.3a1.29,1.29,0,0,0-.629.113.553.553,0,0,0-.246.445" fill="#404040"/>
      <g id="Group_12968" data-name="Group 12968">
        <g id="Group_12967" data-name="Group 12967" clip-path="url(#clip-path-5)">
          <g id="Group_12966" data-name="Group 12966">
            <g id="Group_12965" data-name="Group 12965" clip-path="url(#clip-path-5)">
              <path id="Path_4075" data-name="Path 4075" d="M354.22,423.97v4.76h-3.28v-5.111l2.41.261Zm-.75,2.21c0-.91-.39-1.65-.87-1.65s-.87.74-.87,1.65.39,1.649.87,1.649.87-.739.87-1.649" fill="#d34216"/>
              <path id="Path_4076" data-name="Path 4076" d="M354.22,428.73l-77.41,6.85L54.25,419.91l-4.69-.33,55.5-10,1.25-.26,3.63-.74h7l81.45,6.85,2.35.2h.01l14.47,1.21h.01l5.35.45.18.02,14.3,1.2,24.98,2.1.361.03,13.329,1.12,5.02.42,1.96.17,13.02,1.09,55.16,4.64v.65h5.33Z" fill="#911f00"/>
              <path id="Path_4077" data-name="Path 4077" d="M347.12,326.719l6.229,97.161-2.41-.261-6.239-97.3Z" fill="#c53c08"/>
              <path id="Path_4078" data-name="Path 4078" d="M352.6,424.529c.48,0,.87.741.87,1.651s-.39,1.649-.87,1.649-.87-.739-.87-1.649.391-1.651.87-1.651" fill="#661300"/>
              <path id="Path_4079" data-name="Path 4079" d="M350.94,423.619v5.11h-2.05v-.65l.011-4.46h2.039Z" fill="#911f00"/>
              <path id="Path_4080" data-name="Path 4080" d="M344.7,326.319l6.239,97.3H349.88l-.42-5.29v-.04l.429.04v-1.17l-1-.1L343.76,326.4Z" fill="#af2d05"/>
              <path id="Path_4081" data-name="Path 4081" d="M349.89,417.159v1.17l-.43-.04L116.411,395.5l.089-1.5,1.82.18,2.33.23,3.761.37,1.19.12,4.679.47,2.67.261,3.95.389,1.25.13,6.241.62,1.969.19,3.96.4,1.97.191,4.23.42,2.811.279,3.419.341,2.02.2,4.4.44,2.79.27,3.391.341,1.98.2,4.48.44,2.79.281,3.389.33,1.98.2,3.57.35,2.791.28,3.379.341,1.96.189,2.721.271,2.79.279,3.389.33,1.98.2,3.18.32,2.78.27,3.39.34,1.991.2,2.779.27,2.8.28,3.4.341,1.99.2,2.56.25,2.8.28,3.4.33,1.99.2,2.61.26,2.8.28,3.41.34,2,.2,1.84.18,2.8.28,3.139.31,2.281.221,2.229.229,2.46.24,2.731.27,2.029.2,2.971.3,2.359.23,2.881.29,1.689.161,2.64.269,2.39.231,2.96.3,1.7.17,2.009.2,2.451.239,2.819.28,1.78.17v.01l2.47.24,2.3.23,2.819.28,1.66.16,1.061.11,2.319.23,2.88.28,1.6.16,1.359.14,2.27.221,2.69.269,1.4.141,2.08.21,2.21.21,2.691.269,1.579.16,2.82.281,2.08.2,2.361.24,1.879.19,1.77.17Z" fill="#af2d05"/>
              <path id="Path_4082" data-name="Path 4082" d="M349.46,418.329l.42,5.29h-.98l-.01,4.46-55.16-4.64v-6.6l-14.98-1.511v6.851l-5.02-.42V415l-13.69-1.5v7.11l-24.98-2.1v-7.351l-14.3-1.33v7.38l-.18.08-5.35-.449V409.5l-16.84-1.55v7.481l-81.45-6.851.12-6.67-7.12-.41.3-9.79h.01l1.84.21.36-.569.09-5.02-2.15-.6,1.36-29.74.78-.26,1.76.18.33-.6-.03-5.28-2.06-.58-.52.21,1.3-30.02h1.75l.33-.64-.03-5.3-2.05-.37,1.37-29.89h1.75l.33-.64-.03-5.3-1.6-.29v-2.2l.51-.35,5,1.59L116.5,394l-.09,1.5,233.05,22.79Z" fill="#c53c08"/>
              <path id="Path_4083" data-name="Path 4083" d="M343.76,326.4l5.13,90.66-1.77-.17-4.33-91.08h.01Z" fill="#d34216"/>
              <path id="Path_4084" data-name="Path 4084" d="M347.98,322.27v4.13l-3.28-.67V321l2.27.88a.11.11,0,0,0,.021.03v-.02Zm-.76,1.54c0-.78-.38-1.4-.84-1.4s-.84.62-.84,1.4.38,1.4.84,1.4.84-.63.84-1.4" fill="#d34216"/>
              <path id="Path_4085" data-name="Path 4085" d="M347.979,326.4l-.859.32-2.42-.4-.941.08-.96-.59,1.9-.08Z" fill="#661300"/>
              <path id="Path_4086" data-name="Path 4086" d="M342.79,325.81l4.33,91.08-1.88-.19-4.09-93.97,1.55,1.45h.01Z" fill="#af2d05"/>
              <path id="Path_4087" data-name="Path 4087" d="M346.99,321.889v.02a.11.11,0,0,1-.021-.03Z" fill="#d34216"/>
              <path id="Path_4088" data-name="Path 4088" d="M343.45,319.469s3.569.14,3.54,2.42l-.02-.01a3.786,3.786,0,0,0-3.72-1.84l-2.67-2.44a.654.654,0,0,0-.45.46l-.09-.09c-.08-.88.88-.76.88-.76Z" fill="#c2c2c2"/>
              <path id="Path_4089" data-name="Path 4089" d="M346.97,321.88,344.7,321l-1.82-.1-2.75-2.84a.656.656,0,0,1,.45-.46l2.67,2.44a3.784,3.784,0,0,1,3.72,1.84" fill="#999"/>
              <path id="Path_4090" data-name="Path 4090" d="M346.38,322.409c.46,0,.84.62.84,1.4s-.38,1.4-.84,1.4-.84-.63-.84-1.4.38-1.4.84-1.4" fill="#661300"/>
              <path id="Path_4091" data-name="Path 4091" d="M341.15,322.73l4.09,93.97-2.361-.24-4.139-94.29,2.41.55Z" fill="#c53c08"/>
              <path id="Path_4092" data-name="Path 4092" d="M342.88,320.9l1.82.1v4.73l-1.9.08h-.01l-.08-1.63-.05-1.11-.11-2.19Z" fill="#911f00"/>
              <path id="Path_4093" data-name="Path 4093" d="M338.74,322.17l4.14,94.29-2.08-.2-3.951-93.24Z" fill="#e24f2d"/>
              <path id="Path_4094" data-name="Path 4094" d="M342.66,323.069l.05,1.11h-.01l-1.55-1.45v-.01Z" fill="#911f00"/>
              <path id="Path_4095" data-name="Path 4095" d="M342.55,320.88l.11,2.19-1.509-.35-2.411-.55-1.94-.45-1.29-.3-1.57-.36-.079-.01-2.631-.61-2.22-.51-1.66-.38-1.62-.38-2.8-.64-.05-.01-2.269-.52-1.581-.37-1.62-.37-2.85-.65-.03-.01-2.3-.53-1.1-.25-1.63-.38-2.82-.65h-.01l-2.36-.54-1.78-.41-1.47-.34-.24-.25v.2l-2.86-.66h-.01l-2.39-.55-2.08-.48-1.72-.4-2.88-.66-.03-.01-2.37-.54-1.21-.28-1.68-.39-2.82-.65h-.01l-2.42-.56-3.85-.88-1.72-.4-2.88-.66h-.01l-2.36-.54-1.59-.37-2-.46-3.41-.78h-.01v-.01l-2.8-.64-1.82-.42-2.01-.46-3.42-.79-2.8-.64-1.54-.36-1.99-.45-3.4-.78v-.01l-2.8-.64-2.56-.59.01-1.51.23-1Z" fill="#c53c08"/>
              <path id="Path_4096" data-name="Path 4096" d="M336.85,323.02l3.95,93.24-2.82-.28-2.44-93.23h.01l.99.41Z" fill="#d34216"/>
              <path id="Path_4097" data-name="Path 4097" d="M338.74,322.17l-1.891.85-.049-1.3Z" fill="#911f00"/>
              <path id="Path_4098" data-name="Path 4098" d="M335.54,322.75l2.439,93.23-1.579-.16-2.461-94.69,1.6,1.61Z" fill="#af2d05"/>
              <path id="Path_4099" data-name="Path 4099" d="M336.8,321.719l.05,1.3-.31.14-.99-.41h-.01v-.01l-.03-1.32Z" fill="#911f00"/>
              <path id="Path_4100" data-name="Path 4100" d="M333.94,321.13l2.461,94.69-2.69-.27-2.481-95.11,2.631.61Z" fill="#c53c08"/>
              <path id="Path_4101" data-name="Path 4101" d="M335.51,321.42l.03,1.32-1.6-1.61v-.07Z" fill="#911f00"/>
              <path id="Path_4102" data-name="Path 4102" d="M331.229,320.44l2.48,95.11-2.209-.21L329.03,321h.01Z" fill="#e24f2d"/>
              <path id="Path_4103" data-name="Path 4103" d="M329.03,321l2.47,94.34-2.08-.21-2.05-94.31h.01Z" fill="#d34216"/>
              <path id="Path_4104" data-name="Path 4104" d="M331.229,320.44l-2.189.56h-.01l-.021-1.07Z" fill="#911f00"/>
              <path id="Path_4105" data-name="Path 4105" d="M327.37,320.819l2.05,94.311-1.4-.141-2.29-95.76,1.641,1.58Z" fill="#af2d05"/>
              <path id="Path_4106" data-name="Path 4106" d="M329.01,319.929l.021,1.07-1.651-.18h-.01v-.01l-.02-1.26Z" fill="#911f00"/>
              <path id="Path_4107" data-name="Path 4107" d="M325.729,319.23l2.29,95.76-2.689-.27-2.451-96.18.05-.01,2.8.64Z" fill="#c53c08"/>
              <path id="Path_4108" data-name="Path 4108" d="M327.35,319.549l.021,1.26-1.641-1.58v-.06Z" fill="#911f00"/>
              <path id="Path_4109" data-name="Path 4109" d="M322.88,318.54l2.45,96.18-2.27-.22-1.55-61.23h-.01l-.86-34.06.04.01Z" fill="#e24f2d"/>
              <path id="Path_4110" data-name="Path 4110" d="M321.51,353.27l1.55,61.23-1.36-.14-1.52-54.94.71.11.28-6.25.33-.01Z" fill="#d34216"/>
              <path id="Path_4111" data-name="Path 4111" d="M322.88,318.52v.02l-2.2.68-.04-.01-.03-1.21Z" fill="#911f00"/>
              <path id="Path_4112" data-name="Path 4112" d="M320.18,359.42l1.521,54.94-1.6-.16-1.529-55.02Z" fill="#af2d05"/>
              <path id="Path_4113" data-name="Path 4113" d="M320.64,319.21l.86,34.06-.33.01-1.16-.18-.95-34.25.03.03Z" fill="#d34216"/>
              <path id="Path_4114" data-name="Path 4114" d="M321.17,353.279l-.28,6.25-.71-.11-.17-6.32Z" fill="#dddfdf"/>
              <path id="Path_4115" data-name="Path 4115" d="M320.61,318l.029,1.21-1.55-.33-.03-.03-.029-1.22Z" fill="#911f00"/>
              <path id="Path_4116" data-name="Path 4116" d="M320.01,353.1l.17,6.32-1.61-.24-.169-6.32Z" fill="#c2c2c2"/>
              <path id="Path_4117" data-name="Path 4117" d="M318.57,359.179,320.1,414.2l-2.88-.28-1.53-55.17Z" fill="#c53c08"/>
              <path id="Path_4118" data-name="Path 4118" d="M319.06,318.85l.95,34.25-1.609-.24-.99-35.57Z" fill="#af2d05"/>
              <path id="Path_4119" data-name="Path 4119" d="M319.03,317.63l.029,1.22-1.649-1.56v-.03Z" fill="#911f00"/>
              <path id="Path_4120" data-name="Path 4120" d="M318.4,352.86l.17,6.32-2.881-.43-.17-6.33Z" fill="#dddfdf"/>
              <path id="Path_4121" data-name="Path 4121" d="M317.41,317.29l.99,35.57-2.881-.44-.989-35.8.029-.01,2.851.65Z" fill="#c53c08"/>
              <path id="Path_4122" data-name="Path 4122" d="M315.69,358.75l1.53,55.169-2.319-.229-1.511-55.29Z" fill="#e24f2d"/>
              <path id="Path_4123" data-name="Path 4123" d="M315.69,358.75l-2.3-.35-.179-6.33,2.309.35Z" fill="#f8f8f8"/>
              <path id="Path_4124" data-name="Path 4124" d="M314.53,316.62l.989,35.8-2.309-.35-.951-34.79Z" fill="#e24f2d"/>
              <path id="Path_4125" data-name="Path 4125" d="M313.39,358.4l1.511,55.29-1.061-.11-1.519-54.74.029-.6Z" fill="#d34216"/>
              <path id="Path_4126" data-name="Path 4126" d="M314.53,316.6v.02l-2.271.66-.03-1.21Z" fill="#911f00"/>
              <path id="Path_4127" data-name="Path 4127" d="M312.32,358.84l1.52,54.74-1.661-.161-.919-33.569.11.02Z" fill="#af2d05"/>
              <path id="Path_4128" data-name="Path 4128" d="M313.21,352.069l.18,6.33-1.04-.16-.03.6-.19-6.93Z" fill="#dddfdf"/>
              <path id="Path_4129" data-name="Path 4129" d="M312.26,317.279l.95,34.79-1.08-.16-.97-35.01Z" fill="#d34216"/>
              <path id="Path_4130" data-name="Path 4130" d="M312.13,351.909l.19,6.93-.95,21.03-.11-.02-.77-28.19Z" fill="#c2c2c2"/>
              <path id="Path_4131" data-name="Path 4131" d="M312.229,316.069l.03,1.21-1.1-.38-.031-1.08Z" fill="#911f00"/>
              <path id="Path_4132" data-name="Path 4132" d="M311.26,379.85l.92,33.569-2.819-.279-.921-33.71Z" fill="#c53c08"/>
              <path id="Path_4133" data-name="Path 4133" d="M311.16,316.9l.97,35.01-1.64-.25-.99-36.14,1.62,1.36Z" fill="#af2d05"/>
              <path id="Path_4134" data-name="Path 4134" d="M310.49,351.659l.77,28.19-2.821-.42-.769-28.19Z" fill="#dddfdf"/>
              <path id="Path_4135" data-name="Path 4135" d="M311.13,315.819l.03,1.08-.04-.02-1.62-1.36v-.08Z" fill="#911f00"/>
              <path id="Path_4136" data-name="Path 4136" d="M309.5,315.52l.99,36.14-2.82-.42-.99-36.45,2.82.65Z" fill="#c53c08"/>
              <path id="Path_4137" data-name="Path 4137" d="M308.44,379.429l.921,33.71-2.3-.23-.939-33.84Z" fill="#e24f2d"/>
              <path id="Path_4138" data-name="Path 4138" d="M307.67,351.24l.77,28.19-2.32-.36-.79-28.19Z" fill="#f8f8f8"/>
              <path id="Path_4139" data-name="Path 4139" d="M306.68,314.79l.99,36.45-2.34-.36-.99-35.55.03.01,2.3-.55Z" fill="#e24f2d"/>
              <path id="Path_4140" data-name="Path 4140" d="M306.12,379.069l.94,33.84-2.47-.24v-.01l-.71-33.93Z" fill="#d34216"/>
              <path id="Path_4141" data-name="Path 4141" d="M306.67,314.79l-2.3.55-.03-.01-.03-1.08Z" fill="#911f00"/>
              <path id="Path_4142" data-name="Path 4142" d="M305.33,350.88l.79,28.19-2.24-.34-.13-6.36.7-15.33-1.03-.16-.13-6.31Z" fill="#dddfdf"/>
              <path id="Path_4143" data-name="Path 4143" d="M304.34,315.33l.99,35.55-2.04-.31-.74-35.54.03.03Z" fill="#d34216"/>
              <path id="Path_4144" data-name="Path 4144" d="M303.88,378.73l.71,33.93-1.78-.17-1.12-55.87,1.73.26.33,15.49-.29,6.3Z" fill="#af2d05"/>
              <path id="Path_4145" data-name="Path 4145" d="M304.45,357.04l-.7,15.33-.33-15.49Z" fill="#d34216"/>
              <path id="Path_4146" data-name="Path 4146" d="M304.31,314.25l.03,1.08-1.76-.27-.03-.03-.019-1.19Z" fill="#911f00"/>
              <path id="Path_4147" data-name="Path 4147" d="M303.75,372.37l.13,6.36-.42-.06Z" fill="#c2c2c2"/>
              <path id="Path_4148" data-name="Path 4148" d="M303.29,350.569l.13,6.31-1.731-.26-.129-6.31Z" fill="#c2c2c2"/>
              <path id="Path_4149" data-name="Path 4149" d="M302.55,315.029l.74,35.54-1.73-.26-.74-36.86.24.05Z" fill="#af2d05"/>
              <path id="Path_4150" data-name="Path 4150" d="M301.69,356.62l1.12,55.87-2.819-.281-1.141-56.019Z" fill="#c53c08"/>
              <path id="Path_4151" data-name="Path 4151" d="M302.53,313.84l.02,1.19-1.491-1.53Z" fill="#911f00"/>
              <path id="Path_4152" data-name="Path 4152" d="M301.56,350.31l.13,6.31-2.84-.43-.13-6.31Z" fill="#dddfdf"/>
              <path id="Path_4153" data-name="Path 4153" d="M300.82,313.449l.739,36.86-2.84-.43-.759-37.09Z" fill="#c53c08"/>
              <path id="Path_4154" data-name="Path 4154" d="M300.82,313.25l.239.25-.239-.05Z" fill="#c53c08"/>
              <path id="Path_4155" data-name="Path 4155" d="M298.85,356.19l1.141,56.02-2.451-.24-1.11-56.15Z" fill="#e24f2d"/>
              <path id="Path_4156" data-name="Path 4156" d="M298.72,349.88l.13,6.31-2.42-.37-.13-6.31Z" fill="#f8f8f8"/>
              <path id="Path_4157" data-name="Path 4157" d="M297.96,312.79l.76,37.09-2.42-.37-.71-35.96,2.36-.76Z" fill="#e24f2d"/>
              <path id="Path_4158" data-name="Path 4158" d="M297.95,312.79l-2.36.76-.031-1.31Z" fill="#911f00"/>
              <path id="Path_4159" data-name="Path 4159" d="M296.43,355.819l1.11,56.15-2.009-.2-1.08-52.73-.941-46.01,2.021.54.059-.02.71,35.96-.149-.02-.281,6.25Z" fill="#d34216"/>
              <path id="Path_4160" data-name="Path 4160" d="M296.3,349.509l.13,6.31-.56-.08.281-6.25Z" fill="#dddfdf"/>
              <path id="Path_4161" data-name="Path 4161" d="M295.56,312.24l.03,1.31-.059.02-2.021-.54-.03-1.27Z" fill="#911f00"/>
              <path id="Path_4162" data-name="Path 4162" d="M294.45,359.04l1.08,52.73-1.7-.17-.71-34.49,1.01.15-1.05-1.8-.22-11a9.08,9.08,0,0,0,1.51-4.85c.01-.19.01-.38.01-.57Z" fill="#af2d05"/>
              <path id="Path_4163" data-name="Path 4163" d="M293.51,313.029l.94,46.01h-.07a10.439,10.439,0,0,0-1.76-5.94l-.86-41.74Z" fill="#af2d05"/>
              <path id="Path_4164" data-name="Path 4164" d="M294.38,359.04c0,.19,0,.38-.01.57a9.08,9.08,0,0,1-1.51,4.85l-.24-11.36a10.439,10.439,0,0,1,1.76,5.94" fill="#c2c2c2"/>
              <path id="Path_4165" data-name="Path 4165" d="M293.08,375.46l1.05,1.8-1.01-.15Z" fill="#f8f8f8"/>
              <path id="Path_4166" data-name="Path 4166" d="M293.12,377.11l.71,34.49-2.96-.3-.7-34.64Z" fill="#c53c08"/>
              <path id="Path_4167" data-name="Path 4167" d="M293.729,416.84v6.6c-7.89-1.521-4.109-4.75-3.859-4.96l.009-.01Z" fill="#911f00"/>
              <path id="Path_4168" data-name="Path 4168" d="M293.729,416.84l-3.85,1.63-11.129-1.021v-2.12Z" fill="#661300"/>
              <path id="Path_4169" data-name="Path 4169" d="M293.729,423.44l-13.02-1.09,9.161-3.87c-.25.21-4.031,3.439,3.859,4.96" fill="#661300"/>
              <path id="Path_4170" data-name="Path 4170" d="M293.479,311.759l.03,1.27-1.75-1.67Z" fill="#911f00"/>
              <path id="Path_4171" data-name="Path 4171" d="M293.08,375.46l.04,1.65-2.95-.45-.12-6.42Z" fill="#dddfdf"/>
              <path id="Path_4172" data-name="Path 4172" d="M293.08,375.46l-3.03-5.22-.071-3.28a7.684,7.684,0,0,0,2.881-2.5Z" fill="#c53c08"/>
              <path id="Path_4173" data-name="Path 4173" d="M292.62,353.1l.24,11.36a7.684,7.684,0,0,1-2.881,2.5l-.339-16.85a11.147,11.147,0,0,1,2.98,2.99" fill="#dddfdf"/>
              <path id="Path_4174" data-name="Path 4174" d="M291.76,311.36l.86,41.74a11.118,11.118,0,0,0-2.98-2.99l-.79-39.4.03-.01Z" fill="#c53c08"/>
              <path id="Path_4175" data-name="Path 4175" d="M290.17,376.659l.7,34.641-2.39-.231-.69-34.78Z" fill="#e24f2d"/>
              <path id="Path_4176" data-name="Path 4176" d="M290.05,370.24l.12,6.42-2.38-.37-.54-27.51a14.054,14.054,0,0,1,2.39,1.33l.34,16.85a8.528,8.528,0,0,1-1.48.62Z" fill="#f8f8f8"/>
              <path id="Path_4177" data-name="Path 4177" d="M289.979,366.96l.07,3.28-1.549-2.66a8.516,8.516,0,0,0,1.479-.62" fill="#e24f2d"/>
              <path id="Path_4178" data-name="Path 4178" d="M289.88,418.47l-.01.01-9.16,3.87-1.96-.17v-4.731Z" fill="#440800"/>
              <path id="Path_4179" data-name="Path 4179" d="M288.85,310.71l.79,39.4a14.015,14.015,0,0,0-2.39-1.33l-.75-37.61Z" fill="#e24f2d"/>
              <path id="Path_4180" data-name="Path 4180" d="M288.85,310.69v.02l-2.35.46-.02-1.02Z" fill="#911f00"/>
              <path id="Path_4181" data-name="Path 4181" d="M287.79,376.29l.689,34.78-2.639-.27-.2-34.87.03.04Z" fill="#d34216"/>
              <path id="Path_4182" data-name="Path 4182" d="M287.25,348.779l.54,27.51-2.12-.32-.03-.039-.08-15.2a3.606,3.606,0,0,0,.83-2.329,4.26,4.26,0,0,0-.86-2.94l-.04-7.351a17.708,17.708,0,0,1,1.76.67" fill="#dddfdf"/>
              <path id="Path_4183" data-name="Path 4183" d="M286.5,311.17l.75,37.61a17.47,17.47,0,0,0-1.76-.67l-.21-37.16h.01l1.18.23Z" fill="#d34216"/>
              <path id="Path_4184" data-name="Path 4184" d="M286.479,310.15l.021,1.02-.031.01-1.179-.23h-.01v-.01l-.011-1.07Z" fill="#911f00"/>
              <path id="Path_4185" data-name="Path 4185" d="M285.53,355.46a4.254,4.254,0,0,1,.859,2.94,3.6,3.6,0,0,1-.83,2.33Z" fill="#d34216"/>
              <path id="Path_4186" data-name="Path 4186" d="M285.64,375.929l.2,34.871-1.689-.161-.211-37.66Z" fill="#af2d05"/>
              <path id="Path_4187" data-name="Path 4187" d="M285.64,375.929l-1.7-2.95-.06-11.3a3.168,3.168,0,0,0,1.68-.95Z" fill="#c2c2c2"/>
              <path id="Path_4188" data-name="Path 4188" d="M285.53,355.46l.029,5.27a3.169,3.169,0,0,1-1.679.95l-.041-7.5a4.452,4.452,0,0,1,1.691,1.28" fill="#af2d05"/>
              <path id="Path_4189" data-name="Path 4189" d="M285.49,348.11l.04,7.35a4.456,4.456,0,0,0-1.69-1.28l-.04-6.52c.589.13,1.15.28,1.69.45" fill="#c2c2c2"/>
              <path id="Path_4190" data-name="Path 4190" d="M285.28,310.949l.21,37.16c-.54-.17-1.1-.32-1.69-.45l-.211-38.17,1.691,1.45Z" fill="#af2d05"/>
              <path id="Path_4191" data-name="Path 4191" d="M285.27,309.87l.011,1.07-1.691-1.45v-.01Z" fill="#911f00"/>
              <path id="Path_4192" data-name="Path 4192" d="M283.94,372.98l.211,37.66-2.881-.29-.239-42.39Z" fill="#c53c08"/>
              <path id="Path_4193" data-name="Path 4193" d="M283.88,361.679l.06,11.3-2.91-5.02-.03-6.29.3.05a6.647,6.647,0,0,0,2.58-.04" fill="#dddfdf"/>
              <path id="Path_4194" data-name="Path 4194" d="M283.84,354.179l.04,7.5a6.647,6.647,0,0,1-2.58.04l-.3-.05-.05-8.23.72.11a7.932,7.932,0,0,1,2.17.63" fill="#c53c08"/>
              <path id="Path_4195" data-name="Path 4195" d="M283.8,347.659l.04,6.52a7.932,7.932,0,0,0-2.17-.63l-.72-.11-.04-6.26,1.52.23c.471.07.931.16,1.37.25" fill="#dddfdf"/>
              <path id="Path_4196" data-name="Path 4196" d="M283.59,309.49l.21,38.17c-.439-.09-.9-.18-1.37-.25l-1.52-.23-.21-38.25-.02-.08.08-.02h.01l2.82.65Z" fill="#c53c08"/>
              <path id="Path_4197" data-name="Path 4197" d="M281.03,367.96l.239,42.39-2.359-.23-.24-42.66,2.269.34Z" fill="#e24f2d"/>
              <path id="Path_4198" data-name="Path 4198" d="M281,361.67l.03,6.29-.091-.16-2.269-.34-.03-6.15Z" fill="#f8f8f8"/>
              <path id="Path_4199" data-name="Path 4199" d="M280.95,353.44l.05,8.23-2.361-.36-.049-8.22Z" fill="#e24f2d"/>
              <path id="Path_4200" data-name="Path 4200" d="M280.91,347.179l.04,6.26-2.36-.35-.031-6.27Z" fill="#f8f8f8"/>
              <path id="Path_4201" data-name="Path 4201" d="M280.7,308.929l.21,38.25-2.351-.36-.21-37.41,2.33-.56Z" fill="#e24f2d"/>
              <path id="Path_4202" data-name="Path 4202" d="M280.76,308.83l-.08.02-2.33.56-.01-1.14Z" fill="#911f00"/>
              <path id="Path_4203" data-name="Path 4203" d="M278.67,367.46l.24,42.66-2.97-.3-.5-35.4.85.13.34-7.4Z" fill="#d34216"/>
              <path id="Path_4204" data-name="Path 4204" d="M278.64,361.31l.03,6.15-2.04-.31-.34,7.4-.85-.13-.4-28.13,3.52.53.03,6.27-1.309-.2-.37,8.16Z" fill="#dddfdf"/>
              <path id="Path_4205" data-name="Path 4205" d="M278.59,353.09l.05,8.22-1.73-.26.371-8.16Z" fill="#d34216"/>
              <path id="Path_4206" data-name="Path 4206" d="M278.35,309.409l.21,37.41-3.52-.53-.54-37.73,3.83.85Z" fill="#d34216"/>
              <path id="Path_4207" data-name="Path 4207" d="M278.34,308.27l.01,1.14h-.02l-3.83-.85-.01-1.17Z" fill="#911f00"/>
              <path id="Path_4208" data-name="Path 4208" d="M275.44,374.42l.5,35.4-2.029-.2-.391-35.49Z" fill="#af2d05"/>
              <path id="Path_4209" data-name="Path 4209" d="M275.04,346.29l.4,28.13-1.92-.29-.319-28.11Z" fill="#c2c2c2"/>
              <path id="Path_4210" data-name="Path 4210" d="M274.5,308.56l.54,37.73-1.84-.27L272.77,307l1.69,1.55Z" fill="#af2d05"/>
              <path id="Path_4211" data-name="Path 4211" d="M274.49,307.389l.01,1.17-.04-.01L272.769,307v-.01Z" fill="#911f00"/>
              <path id="Path_4212" data-name="Path 4212" d="M273.52,374.13l.391,35.49-2.731-.27-.45-35.64Z" fill="#c53c08"/>
              <path id="Path_4213" data-name="Path 4213" d="M273.729,415v6.761c-7.71-1.341-4.159-4.88-3.93-5.1l.01-.01Z" fill="#911f00"/>
              <path id="Path_4214" data-name="Path 4214" d="M273.729,415l-3.92,1.65-9.769-1.07V413.5Z" fill="#661300"/>
              <path id="Path_4215" data-name="Path 4215" d="M273.729,421.76,260.4,420.64l9.4-3.981c-.229.221-3.78,3.76,3.93,5.1" fill="#661300"/>
              <path id="Path_4216" data-name="Path 4216" d="M273.2,346.02l.319,28.11-2.79-.42-.349-28.12Z" fill="#dddfdf"/>
              <path id="Path_4217" data-name="Path 4217" d="M272.77,307l.431,39.02-2.821-.43-.49-39.26,2.88.66Z" fill="#c53c08"/>
              <path id="Path_4218" data-name="Path 4218" d="M270.729,373.71l.45,35.64-2.46-.24-.41-35.75h.08Z" fill="#e24f2d"/>
              <path id="Path_4219" data-name="Path 4219" d="M270.38,345.59l.35,28.12-2.34-.35,1.27-27.88Z" fill="#f8f8f8"/>
              <path id="Path_4220" data-name="Path 4220" d="M269.89,306.33l.49,39.26-.719-.11-1.271,27.88h-.08l-.77-66.02,2.34-1.01Z" fill="#e24f2d"/>
              <path id="Path_4221" data-name="Path 4221" d="M269.88,306.33l-2.34,1.01-.02-1.55Z" fill="#911f00"/>
              <path id="Path_4222" data-name="Path 4222" d="M269.81,416.65l-.01.01-9.4,3.98-.361-.03v-5.03Z" fill="#440800"/>
              <path id="Path_4223" data-name="Path 4223" d="M268.31,373.36l.41,35.75-2.229-.23-.551-101.77,1.57.24.03-.01Z" fill="#d34216"/>
              <path id="Path_4224" data-name="Path 4224" d="M267.52,305.79l.021,1.55-.031.01-1.57-.24-.01-1.69Z" fill="#911f00"/>
              <path id="Path_4225" data-name="Path 4225" d="M265.94,307.11l.551,101.77-2.28-.22-.121-45.75a14.183,14.183,0,0,0,.789-4.12,15.412,15.412,0,0,0-.819-5.74l-.13-48.09,2,2.15Z" fill="#af2d05"/>
              <path id="Path_4226" data-name="Path 4226" d="M265.93,305.42l.01,1.69h-.01l-2-2.15Z" fill="#911f00"/>
              <path id="Path_4227" data-name="Path 4227" d="M264.06,353.049a15.441,15.441,0,0,1,.82,5.741,14.216,14.216,0,0,1-.79,4.12Z" fill="#f8f8f8"/>
              <path id="Path_4228" data-name="Path 4228" d="M264.09,362.909l.12,45.75-3.14-.31-.22-40.55a11.891,11.891,0,0,0,3.24-4.89" fill="#c53c08"/>
              <path id="Path_4229" data-name="Path 4229" d="M264.06,353.049l.03,9.861a11.88,11.88,0,0,1-3.24,4.889l-.109-20.279a15.824,15.824,0,0,1,3.319,5.529" fill="#dddfdf"/>
              <path id="Path_4230" data-name="Path 4230" d="M263.93,304.96l.13,48.09a15.819,15.819,0,0,0-3.319-5.53l-.231-43.34h.01Z" fill="#c53c08"/>
              <path id="Path_4231" data-name="Path 4231" d="M260.85,367.8l.221,40.551-2.8-.28-.2-38.38a12.417,12.417,0,0,0,2.779-1.891" fill="#e24f2d"/>
              <path id="Path_4232" data-name="Path 4232" d="M260.74,347.52l.109,20.28a12.389,12.389,0,0,1-2.779,1.89l-.141-24.63a16.209,16.209,0,0,1,2.811,2.46" fill="#f8f8f8"/>
              <path id="Path_4233" data-name="Path 4233" d="M260.51,304.179l.23,43.34a16.2,16.2,0,0,0-2.81-2.46l-.21-40.04Z" fill="#e24f2d"/>
              <path id="Path_4234" data-name="Path 4234" d="M260.51,304.17v.01l-2.79.84-.01-1.49Z" fill="#911f00"/>
              <path id="Path_4235" data-name="Path 4235" d="M258.07,369.69l.2,38.38-1.84-.18-.2-37.46a12.367,12.367,0,0,0,1.841-.74" fill="#d34216"/>
              <path id="Path_4236" data-name="Path 4236" d="M257.93,345.06l.141,24.63a12.411,12.411,0,0,1-1.841.74l-.05-9.71a8.256,8.256,0,0,0,.721-3.14,9.429,9.429,0,0,0-.75-4.2l-.051-9.45a16.847,16.847,0,0,1,1.83,1.13" fill="#dddfdf"/>
              <path id="Path_4237" data-name="Path 4237" d="M257.72,305.02l.21,40.04a16.847,16.847,0,0,0-1.83-1.13l-.21-39.43Z" fill="#d34216"/>
              <path id="Path_4238" data-name="Path 4238" d="M257.71,303.529l.01,1.49-1.83-.52v-1.39Z" fill="#911f00"/>
              <path id="Path_4239" data-name="Path 4239" d="M256.15,353.38a9.429,9.429,0,0,1,.75,4.2,8.256,8.256,0,0,1-.721,3.14Z" fill="#d34216"/>
              <path id="Path_4240" data-name="Path 4240" d="M256.229,370.429l.2,37.46-2-.2-.189-36.76a15.667,15.667,0,0,0,1.989-.5" fill="#af2d05"/>
              <path id="Path_4241" data-name="Path 4241" d="M256.18,360.719l.05,9.71a15.668,15.668,0,0,1-1.989.5l-.04-7.7a6.208,6.208,0,0,0,1.979-2.51" fill="#c2c2c2"/>
              <path id="Path_4242" data-name="Path 4242" d="M256.15,353.38l.029,7.34a6.2,6.2,0,0,1-1.979,2.51l-.07-12.66a8.2,8.2,0,0,1,2.02,2.81" fill="#af2d05"/>
              <path id="Path_4243" data-name="Path 4243" d="M256.1,343.929l.051,9.45a8.2,8.2,0,0,0-2.021-2.81l-.04-7.55a16.637,16.637,0,0,1,2.01.91" fill="#c2c2c2"/>
              <path id="Path_4244" data-name="Path 4244" d="M255.89,304.5l.21,39.43a16.642,16.642,0,0,0-2.01-.91l-.21-40.37,2,1.85Z" fill="#af2d05"/>
              <path id="Path_4245" data-name="Path 4245" d="M255.89,303.11v1.39h-.01l-2-1.85Z" fill="#911f00"/>
              <path id="Path_4246" data-name="Path 4246" d="M254.24,370.929l.189,36.76-3.41-.34-.189-36.21a16.544,16.544,0,0,0,3.41-.21" fill="#c53c08"/>
              <path id="Path_4247" data-name="Path 4247" d="M254.2,363.23l.04,7.7a16.546,16.546,0,0,1-3.41.21l-.04-6.67a6.276,6.276,0,0,0,3.41-1.24" fill="#dddfdf"/>
              <path id="Path_4248" data-name="Path 4248" d="M254.13,350.569l.07,12.66a6.269,6.269,0,0,1-3.41,1.241l-.08-15.78a8.05,8.05,0,0,1,3.42,1.879" fill="#c53c08"/>
              <path id="Path_4249" data-name="Path 4249" d="M254.09,343.02l.04,7.55a8.041,8.041,0,0,0-3.42-1.88l-.04-6.64a18.957,18.957,0,0,1,3.42.97" fill="#dddfdf"/>
              <path id="Path_4250" data-name="Path 4250" d="M253.88,302.65l.21,40.37a18.98,18.98,0,0,0-3.42-.97l-.21-40.19Z" fill="#c53c08"/>
              <path id="Path_4251" data-name="Path 4251" d="M250.83,371.139l.189,36.21-2.8-.28-.19-36.25c.22.05.45.09.67.12a20.1,20.1,0,0,0,2.13.2" fill="#e24f2d"/>
              <path id="Path_4252" data-name="Path 4252" d="M250.79,364.469l.04,6.67a20.1,20.1,0,0,1-2.13-.2c-.22-.03-.45-.07-.67-.12l-.04-6.63a8.648,8.648,0,0,0,1.01.22,7.237,7.237,0,0,0,1.79.06" fill="#f8f8f8"/>
              <path id="Path_4253" data-name="Path 4253" d="M250.71,348.69l.08,15.78a7.237,7.237,0,0,1-1.79-.06,8.655,8.655,0,0,1-1.01-.22l-.08-15.78a7.383,7.383,0,0,1,1.81.06,6.458,6.458,0,0,1,.99.22" fill="#e24f2d"/>
              <path id="Path_4254" data-name="Path 4254" d="M250.67,342.049l.04,6.641a6.458,6.458,0,0,0-.99-.22,7.383,7.383,0,0,0-1.81-.06l-.03-6.67a20.041,20.041,0,0,1,2.14.2c.22.03.43.07.65.109" fill="#f8f8f8"/>
              <path id="Path_4255" data-name="Path 4255" d="M250.46,301.86l.21,40.19c-.22-.04-.43-.08-.65-.11a20.041,20.041,0,0,0-2.14-.2l-.21-39.29Z" fill="#e24f2d"/>
              <path id="Path_4256" data-name="Path 4256" d="M250.46,301.86l-2.79.59-.01-1.23Z" fill="#911f00"/>
              <path id="Path_4257" data-name="Path 4257" d="M248.03,370.819l.19,36.25-2.61-.26.18-36.539a17.7,17.7,0,0,0,2.24.549" fill="#d34216"/>
              <path id="Path_4258" data-name="Path 4258" d="M247.99,364.19l.04,6.63a17.872,17.872,0,0,1-2.24-.55l.03-7.04a7.561,7.561,0,0,0,2.17.96" fill="#dddfdf"/>
              <path id="Path_4259" data-name="Path 4259" d="M247.91,348.409l.08,15.78a7.561,7.561,0,0,1-2.17-.96l.07-14.36a6.625,6.625,0,0,1,2.02-.46" fill="#d34216"/>
              <path id="Path_4260" data-name="Path 4260" d="M247.88,341.74l.03,6.67a6.624,6.624,0,0,0-2.02.46l.03-7.09a15.869,15.869,0,0,1,1.96-.04" fill="#dddfdf"/>
              <path id="Path_4261" data-name="Path 4261" d="M247.67,302.449l.21,39.29a15.869,15.869,0,0,0-1.96.04l.19-39.34.01.01h1.55Z" fill="#d34216"/>
              <path id="Path_4262" data-name="Path 4262" d="M247.66,301.219l.01,1.23h-1.55l-.01-.01.01-1.58Z" fill="#911f00"/>
              <path id="Path_4263" data-name="Path 4263" d="M246.12,300.86l-.01,1.58-1.98-2.03Z" fill="#911f00"/>
              <path id="Path_4264" data-name="Path 4264" d="M244.13,300.409l1.98,2.03-.19,39.34a14.233,14.233,0,0,0-1.99.29Z" fill="#af2d05"/>
              <path id="Path_4265" data-name="Path 4265" d="M245.92,341.779l-.03,7.091a6.176,6.176,0,0,0-2,1.31l.04-8.111a14.384,14.384,0,0,1,1.99-.29" fill="#c2c2c2"/>
              <path id="Path_4266" data-name="Path 4266" d="M245.89,348.87l-.07,14.36a7.864,7.864,0,0,1-1.98-1.7l.05-11.35a6.192,6.192,0,0,1,2-1.31" fill="#af2d05"/>
              <path id="Path_4267" data-name="Path 4267" d="M245.82,363.23l-.03,7.04a20.447,20.447,0,0,1-1.99-.75l.04-7.99a7.864,7.864,0,0,0,1.98,1.7" fill="#c2c2c2"/>
              <path id="Path_4268" data-name="Path 4268" d="M245.79,370.27l-.18,36.54-1.99-.2.18-37.09a20.447,20.447,0,0,0,1.99.75" fill="#af2d05"/>
              <path id="Path_4269" data-name="Path 4269" d="M244.13,300.409l-.2,41.66a12.984,12.984,0,0,0-3.41,1.19l.21-43.63Z" fill="#c53c08"/>
              <path id="Path_4270" data-name="Path 4270" d="M243.93,342.069l-.04,8.111a7.515,7.515,0,0,0-2.07,5.119,8.9,8.9,0,0,0,2.02,6.231l-.04,7.99a17.993,17.993,0,0,1-3.39-1.971l.11-24.29a13.02,13.02,0,0,1,3.41-1.19" fill="#dddfdf"/>
              <path id="Path_4271" data-name="Path 4271" d="M243.89,350.179l-.05,11.35a8.892,8.892,0,0,1-2.02-6.23,7.518,7.518,0,0,1,2.07-5.12" fill="#c53c08"/>
              <path id="Path_4272" data-name="Path 4272" d="M243.8,369.52l-.18,37.09-3.4-.33.19-38.73a17.987,17.987,0,0,0,3.39,1.97" fill="#c53c08"/>
              <path id="Path_4273" data-name="Path 4273" d="M240.73,299.62v.01l-2.8.65v-1.3Z" fill="#911f00"/>
              <path id="Path_4274" data-name="Path 4274" d="M240.73,299.63l-.21,43.63a12.4,12.4,0,0,0-2.81,1.96l.22-44.94Z" fill="#e24f2d"/>
              <path id="Path_4275" data-name="Path 4275" d="M240.52,343.259l-.11,24.29a17.157,17.157,0,0,1-2.79-2.589l.09-19.74a12.405,12.405,0,0,1,2.81-1.961" fill="#f8f8f8"/>
              <path id="Path_4276" data-name="Path 4276" d="M240.41,367.549l-.19,38.731-2.8-.28v-.04l.2-41a17.157,17.157,0,0,0,2.79,2.589" fill="#e24f2d"/>
              <path id="Path_4277" data-name="Path 4277" d="M237.93,298.98v1.3h-.01l-2.55-.68v-1.21Z" fill="#911f00"/>
              <path id="Path_4278" data-name="Path 4278" d="M237.93,300.279l-.22,44.94a11.884,11.884,0,0,0-2.58,3.54l.24-49.16,2.55.68Z" fill="#d34216"/>
              <path id="Path_4279" data-name="Path 4279" d="M237.71,345.219l-.09,19.74a15.963,15.963,0,0,1-2.54-4.07l.05-12.13a11.871,11.871,0,0,1,2.58-3.54" fill="#dddfdf"/>
              <path id="Path_4280" data-name="Path 4280" d="M237.62,364.96l-.2,41V406l-2.56-.25.22-44.86a15.963,15.963,0,0,0,2.54,4.07" fill="#d34216"/>
              <path id="Path_4281" data-name="Path 4281" d="M235.61,295.88l-.23,1-.01,1.51-1.99-.46-3.39-.77v-.01l-2.8-.64-1.77-.41-1.97-.45h-.01l-3.36-.77-.01-.01-2.79-.64-3.19-.73-1.96-.46h-.01l-3.38-.77-2.78-.64-2.72-.63-1.98-.45h-.01v-.01l-3.38-.77-2.77-.64-3.6-.83-1.97-.45-3.37-.78h-.01l-2.78-.64-4.51-1.04-1.89-.43-.08-.08v.06l-3.38-.78-2.78-.64-3.51-.8-2.03-.47-3.39-.78-.04-.01-2.83-.65-3.72-.86-2.01-.46-3.91-.9h-.01l-1.98-.46-5.17-1.18-1.2-.28-.07-.02-3.96-.91h-.01l-2.65-.61-4.65-1.07-1.19-.27-3.78-.87-.21-.05-2.35-.54v-3Z" fill="#c53c08"/>
              <path id="Path_4282" data-name="Path 4282" d="M235.37,298.389v1.21h-.01l-1.98-1.66v-.01Z" fill="#911f00"/>
              <path id="Path_4283" data-name="Path 4283" d="M235.37,299.6l-.24,49.16a13.752,13.752,0,0,0-1.29,5.33c-.01.24-.02.48-.02.72h-.71l.03-5.32.24-51.55,1.98,1.66Z" fill="#af2d05"/>
              <path id="Path_4284" data-name="Path 4284" d="M235.13,348.759l-.05,12.13a15.176,15.176,0,0,1-1.26-6.079c0-.24.01-.481.02-.72a13.757,13.757,0,0,1,1.29-5.331" fill="#c2c2c2"/>
              <path id="Path_4285" data-name="Path 4285" d="M235.08,360.889l-.22,44.86-1.99-.2.24-50.741h.71a15.177,15.177,0,0,0,1.26,6.08" fill="#af2d05"/>
              <path id="Path_4286" data-name="Path 4286" d="M235.06,411.159v7.351c-7.8-1.7-4.09-5.481-3.84-5.721l.01-.01Z" fill="#911f00"/>
              <path id="Path_4287" data-name="Path 4287" d="M235.06,411.159l-3.83,1.62-10.47-1.07v-1.88Z" fill="#661300"/>
              <path id="Path_4288" data-name="Path 4288" d="M235.06,418.51l-14.3-1.2v-.1l10.46-4.42c-.25.241-3.96,4.021,3.84,5.721" fill="#661300"/>
              <path id="Path_4289" data-name="Path 4289" d="M233.38,297.929v.01l-.24,51.55-1.92-.01a10.468,10.468,0,0,0-1.46-5.46l.23-46.86Z" fill="#c53c08"/>
              <path id="Path_4290" data-name="Path 4290" d="M233.14,349.49l-.03,5.32-.24,50.74-3.4-.34.24-50.31a9.2,9.2,0,0,0,1.5-4.86c.01-.19.01-.38.01-.56Z" fill="#c53c08"/>
              <path id="Path_4291" data-name="Path 4291" d="M231.23,412.779l-.01.01-10.46,4.42v-5.5Z" fill="#440800"/>
              <path id="Path_4292" data-name="Path 4292" d="M231.22,349.48c0,.18,0,.37-.01.56a9.2,9.2,0,0,1-1.5,4.86l.05-10.88a10.468,10.468,0,0,1,1.46,5.46" fill="#dddfdf"/>
              <path id="Path_4293" data-name="Path 4293" d="M229.99,297.15v.01l-2.81.64.01-1.29Z" fill="#911f00"/>
              <path id="Path_4294" data-name="Path 4294" d="M229.99,297.159l-.23,46.86a10.676,10.676,0,0,0-2.79-3.11l.21-43.11Z" fill="#e24f2d"/>
              <path id="Path_4295" data-name="Path 4295" d="M229.76,344.02l-.05,10.88a7.662,7.662,0,0,1-2.81,2.51l.07-16.5a10.676,10.676,0,0,1,2.79,3.11" fill="#f8f8f8"/>
              <path id="Path_4296" data-name="Path 4296" d="M229.71,354.9l-.24,50.31-2.8-.28.23-47.52a7.662,7.662,0,0,0,2.81-2.51" fill="#e24f2d"/>
              <path id="Path_4297" data-name="Path 4297" d="M227.19,296.509l-.01,1.29-1.78-.37.02-1.33Z" fill="#911f00"/>
              <path id="Path_4298" data-name="Path 4298" d="M227.18,297.8l-.21,43.111a14.179,14.179,0,0,0-2.16-1.37l.59-42.11Z" fill="#d34216"/>
              <path id="Path_4299" data-name="Path 4299" d="M226.97,340.909l-.07,16.5a10.038,10.038,0,0,1-2.36.89l.27-18.76a14.179,14.179,0,0,1,2.16,1.37" fill="#dddfdf"/>
              <path id="Path_4300" data-name="Path 4300" d="M226.9,357.409l-.23,47.52-2.78-.27.65-46.36a10.038,10.038,0,0,0,2.36-.89" fill="#d34216"/>
              <path id="Path_4301" data-name="Path 4301" d="M225.42,296.1l-.02,1.33-1.95-1.78Z" fill="#911f00"/>
              <path id="Path_4302" data-name="Path 4302" d="M225.4,297.429l-.59,42.11a17.809,17.809,0,0,0-1.98-.82l.61-43.07h.01Z" fill="#af2d05"/>
              <path id="Path_4303" data-name="Path 4303" d="M224.81,339.54l-.27,18.76a12.177,12.177,0,0,1-1.99.31l.11-7.79a4.021,4.021,0,0,0,.57-1.99,4.51,4.51,0,0,0-.51-2.41l.11-7.7a17.8,17.8,0,0,1,1.98.82" fill="#c2c2c2"/>
              <path id="Path_4304" data-name="Path 4304" d="M224.54,358.3l-.65,46.361-1.99-.2.65-45.85a12.071,12.071,0,0,0,1.99-.311" fill="#af2d05"/>
              <path id="Path_4305" data-name="Path 4305" d="M223.44,295.65l-.61,43.07a18.775,18.775,0,0,0-3.38-.84l.61-43h.02Z" fill="#c53c08"/>
              <path id="Path_4306" data-name="Path 4306" d="M222.72,346.42a4.51,4.51,0,0,1,.51,2.41,4.021,4.021,0,0,1-.57,1.99Z" fill="#af2d05"/>
              <path id="Path_4307" data-name="Path 4307" d="M222.83,338.719l-.11,7.7a4.878,4.878,0,0,0-3.36-2.26l.09-6.28a18.775,18.775,0,0,1,3.38.84" fill="#dddfdf"/>
              <path id="Path_4308" data-name="Path 4308" d="M222.72,346.42l-.06,4.4a3.617,3.617,0,0,1-3.41,1.43l.11-8.09a4.877,4.877,0,0,1,3.36,2.26" fill="#c53c08"/>
              <path id="Path_4309" data-name="Path 4309" d="M222.66,350.819l-.11,7.79a17.586,17.586,0,0,1-3.39-.06l.09-6.3a3.62,3.62,0,0,0,3.41-1.431" fill="#dddfdf"/>
              <path id="Path_4310" data-name="Path 4310" d="M222.55,358.61l-.65,45.85-3.39-.34.65-45.57a17.663,17.663,0,0,0,3.39.06" fill="#c53c08"/>
              <path id="Path_4311" data-name="Path 4311" d="M220.07,294.87l-.01.01-2.79.6.01-1.25Z" fill="#911f00"/>
              <path id="Path_4312" data-name="Path 4312" d="M220.06,294.88l-.61,43-.18-.03-2.6-.39.6-41.98Z" fill="#e24f2d"/>
              <path id="Path_4313" data-name="Path 4313" d="M219.45,337.88l-.09,6.28a7.1,7.1,0,0,0-.85-.17l-1.93-.29.09-6.24,2.6.39.18.03" fill="#f8f8f8"/>
              <path id="Path_4314" data-name="Path 4314" d="M219.36,344.159l-.11,8.09a7.371,7.371,0,0,1-1.11-.1l-1.67-.25.11-8.2,1.93.29a7.093,7.093,0,0,1,.85.17" fill="#e24f2d"/>
              <path id="Path_4315" data-name="Path 4315" d="M219.25,352.25l-.09,6.3c-.27-.03-.55-.07-.83-.11l-1.95-.3.09-6.24,1.67.25a7.375,7.375,0,0,0,1.11.1" fill="#f8f8f8"/>
              <path id="Path_4316" data-name="Path 4316" d="M219.16,358.549l-.65,45.571-2.78-.27.65-45.71,1.95.3c.28.04.56.08.83.109" fill="#e24f2d"/>
              <path id="Path_4317" data-name="Path 4317" d="M217.28,294.23l-.01,1.25-3.17-.5-.03-.03.02-1.45Z" fill="#911f00"/>
              <path id="Path_4318" data-name="Path 4318" d="M217.27,295.48l-.6,41.98-3.19-.49.59-42.02.03.03Z" fill="#d34216"/>
              <path id="Path_4319" data-name="Path 4319" d="M216.67,337.46l-.09,6.24-2.46-.38-.37,8.17,2.72.41-.09,6.24-2.92-.44-.33,7.29-.04-.01.39-28.01Z" fill="#dddfdf"/>
              <path id="Path_4320" data-name="Path 4320" d="M216.58,343.7l-.11,8.2-2.72-.41.37-8.17Z" fill="#d34216"/>
              <path id="Path_4321" data-name="Path 4321" d="M216.38,358.139l-.65,45.71-3.18-.32.54-38.55.04.01.33-7.29Z" fill="#d34216"/>
              <path id="Path_4322" data-name="Path 4322" d="M215.23,409.5v7.341h-.01c-.29-.1-5.64-1.941-3.93-5.671v-.01Z" fill="#911f00"/>
              <path id="Path_4323" data-name="Path 4323" d="M215.23,409.5l-3.94,1.66-12.9-.83v-2.38Z" fill="#661300"/>
              <path id="Path_4324" data-name="Path 4324" d="M215.22,416.84l-14.47-1.21,10.54-4.461c-1.71,3.731,3.64,5.571,3.93,5.671" fill="#661300"/>
              <path id="Path_4325" data-name="Path 4325" d="M214.09,293.5l-.02,1.45-1.94-1.91Z" fill="#911f00"/>
              <path id="Path_4326" data-name="Path 4326" d="M214.07,294.949l-.59,42.02-1.97-.3.61-43.63h.01Z" fill="#af2d05"/>
              <path id="Path_4327" data-name="Path 4327" d="M213.48,336.969l-.39,28.01-1.98-.3.4-28.01Z" fill="#c2c2c2"/>
              <path id="Path_4328" data-name="Path 4328" d="M213.09,364.98l-.54,38.55-1.98-.2v-.03l.54-38.62Z" fill="#af2d05"/>
              <path id="Path_4329" data-name="Path 4329" d="M212.12,293.04l-.61,43.63-3.39-.51.62-43.89Z" fill="#c53c08"/>
              <path id="Path_4330" data-name="Path 4330" d="M211.51,336.67l-.4,28.01-3.38-.51.39-28.01Z" fill="#dddfdf"/>
              <path id="Path_4331" data-name="Path 4331" d="M211.29,411.159v.01l-10.54,4.461h-.01l-2.35-.2v-5.1Z" fill="#440800"/>
              <path id="Path_4332" data-name="Path 4332" d="M211.11,364.679l-.54,38.621v.029l-3.39-.33.55-38.83Z" fill="#c53c08"/>
              <path id="Path_4333" data-name="Path 4333" d="M208.74,292.27l-.62,43.89-1.62-.25-1.27,27.88h-.29l.4-28.23.6-42.53.02.01Z" fill="#e24f2d"/>
              <path id="Path_4334" data-name="Path 4334" d="M208.74,292.27l-2.78.77-.02-.01.02-1.4Z" fill="#911f00"/>
              <path id="Path_4335" data-name="Path 4335" d="M208.12,336.159l-.39,28.01-2.5-.38,1.27-27.88Z" fill="#f8f8f8"/>
              <path id="Path_4336" data-name="Path 4336" d="M207.73,364.17,207.18,403l-2.79-.28.55-38.93h.29Z" fill="#e24f2d"/>
              <path id="Path_4337" data-name="Path 4337" d="M205.96,291.63l-.02,1.4-2.72-.67.02-1.36Z" fill="#911f00"/>
              <path id="Path_4338" data-name="Path 4338" d="M205.94,293.029l-.6,42.53-1.31-.02-1.41-.21.6-42.97Z" fill="#d34216"/>
              <path id="Path_4339" data-name="Path 4339" d="M205.34,335.56l-.4,28.23-.55,38.93-2.72-.27.02-.16.54-38.95,1.17.18-1.14-1.88.34-24.44,1.43-1.66Z" fill="#d34216"/>
              <path id="Path_4340" data-name="Path 4340" d="M204.03,335.54l-1.43,1.66.02-1.87Z" fill="#dddfdf"/>
              <path id="Path_4341" data-name="Path 4341" d="M202.26,361.639l1.14,1.88-1.17-.18Z" fill="#dddfdf"/>
              <path id="Path_4342" data-name="Path 4342" d="M203.24,291l-.02,1.36-1.96-1.81Z" fill="#911f00"/>
              <path id="Path_4343" data-name="Path 4343" d="M203.22,292.36l-.6,42.97-1.98-.31.61-44.47h.01Z" fill="#af2d05"/>
              <path id="Path_4344" data-name="Path 4344" d="M202.62,335.33l-.02,1.87-2.03,2.34.07-4.52Z" fill="#c2c2c2"/>
              <path id="Path_4345" data-name="Path 4345" d="M202.6,337.2l-.34,24.44-1.95-3.2.26-18.9Z" fill="#af2d05"/>
              <path id="Path_4346" data-name="Path 4346" d="M202.26,361.639l-.03,1.7-1.98-.3.06-4.6Z" fill="#c2c2c2"/>
              <path id="Path_4347" data-name="Path 4347" d="M202.23,363.34l-.54,38.95-.02.16-1.96-.19.54-39.22Z" fill="#af2d05"/>
              <path id="Path_4348" data-name="Path 4348" d="M201.25,290.54v.01l-.61,44.47-3.38-.51.61-44.49v-.25Z" fill="#c53c08"/>
              <path id="Path_4349" data-name="Path 4349" d="M200.64,335.02l-.07,4.52-3.44,3.99.13-9.02Z" fill="#dddfdf"/>
              <path id="Path_4350" data-name="Path 4350" d="M197.13,343.529l3.44-3.99-.26,18.9L197,353Z" fill="#c53c08"/>
              <path id="Path_4351" data-name="Path 4351" d="M200.31,358.44l-.06,4.6-3.38-.51L197,353Z" fill="#dddfdf"/>
              <path id="Path_4352" data-name="Path 4352" d="M200.25,363.04l-.54,39.22-3.38-.341.54-39.389Z" fill="#c53c08"/>
              <path id="Path_4353" data-name="Path 4353" d="M197.87,289.77v.25l-2.76.57h-.03l.02-1.46Z" fill="#911f00"/>
              <path id="Path_4354" data-name="Path 4354" d="M197.87,290.02l-.61,44.49-1.77-.27-1.03,1.27.62-44.92h.03Z" fill="#e24f2d"/>
              <path id="Path_4355" data-name="Path 4355" d="M197.26,334.509l-.13,9.02-2.83,3.28.16-11.3,1.03-1.27Z" fill="#f8f8f8"/>
              <path id="Path_4356" data-name="Path 4356" d="M197.13,343.529,197,353l-2.72-4.49.02-1.7Z" fill="#e24f2d"/>
              <path id="Path_4357" data-name="Path 4357" d="M194.28,348.509,197,353l-.13,9.53-2.57-.39-.21-.37Z" fill="#f8f8f8"/>
              <path id="Path_4358" data-name="Path 4358" d="M196.87,362.529l-.54,39.39-2.79-.28.55-39.87.21.37Z" fill="#e24f2d"/>
              <path id="Path_4359" data-name="Path 4359" d="M195.1,289.13l-.02,1.46-3.57-.57-.03-.03.02-1.69Z" fill="#911f00"/>
              <path id="Path_4360" data-name="Path 4360" d="M195.08,290.59l-.62,44.92-3.66,4.5.68-50.02.03.03Z" fill="#d34216"/>
              <path id="Path_4361" data-name="Path 4361" d="M194.46,335.509l-.16,11.3-.62.72.6.98-.19,13.26-3.5-6.18.21-15.58Z" fill="#dddfdf"/>
              <path id="Path_4362" data-name="Path 4362" d="M194.3,346.81l-.02,1.7-.6-.98Z" fill="#d34216"/>
              <path id="Path_4363" data-name="Path 4363" d="M194.09,361.77l-.55,39.87-3.57-.35.62-45.7Z" fill="#d34216"/>
              <path id="Path_4364" data-name="Path 4364" d="M191.5,288.3l-.02,1.69-1.95-2.12v-.02Z" fill="#911f00"/>
              <path id="Path_4365" data-name="Path 4365" d="M189.53,287.87l1.95,2.12-.68,50.02-1.18,1.45-.8-1.36Z" fill="#af2d05"/>
              <path id="Path_4366" data-name="Path 4366" d="M190.8,340.009l-.21,15.58-1.68-2.97-.27.34.18-12.86.8,1.36Z" fill="#c2c2c2"/>
              <path id="Path_4367" data-name="Path 4367" d="M188.91,352.62l1.68,2.97-.621,45.7-1.98-.2.651-48.13Z" fill="#af2d05"/>
              <path id="Path_4368" data-name="Path 4368" d="M189.53,287.85v.02l-.71,52.23-3.32-5.65.65-47.38h.01Z" fill="#c53c08"/>
              <path id="Path_4369" data-name="Path 4369" d="M185.5,334.449l3.32,5.65-.18,12.86-3.45,4.4Z" fill="#dddfdf"/>
              <path id="Path_4370" data-name="Path 4370" d="M188.64,352.96l-.65,48.13-3.39-.33.59-43.4Z" fill="#c53c08"/>
              <path id="Path_4371" data-name="Path 4371" d="M186.15,287.069l-.65,47.38-1.1-1.88-1.66-.25.61-44.45Z" fill="#e24f2d"/>
              <path id="Path_4372" data-name="Path 4372" d="M186.15,287.069l-2.8.8.02-1.44Z" fill="#911f00"/>
              <path id="Path_4373" data-name="Path 4373" d="M184.4,332.569l1.1,1.88-.31,22.91-2.38,3.04-.45-.07.16-11.43,2.09-2.43-2.02-3.37.15-10.78Z" fill="#f8f8f8"/>
              <path id="Path_4374" data-name="Path 4374" d="M185.19,357.36l-.59,43.4-2.79-.28.55-40.15.45.07Z" fill="#e24f2d"/>
              <path id="Path_4375" data-name="Path 4375" d="M182.59,343.1l2.02,3.37-2.09,2.43Z" fill="#e24f2d"/>
              <path id="Path_4376" data-name="Path 4376" d="M183.37,286.429l-.02,1.44-4.48-.99-.03-.03.02-1.46Z" fill="#911f00"/>
              <path id="Path_4377" data-name="Path 4377" d="M183.35,287.87l-.61,44.45-4.5-.68.6-44.79.03.03Z" fill="#d34216"/>
              <path id="Path_4378" data-name="Path 4378" d="M182.74,332.319l-.15,10.78-4.4-7.38.05-4.08Z" fill="#dddfdf"/>
              <path id="Path_4379" data-name="Path 4379" d="M178.19,335.719l4.4,7.38-.07,5.8-4.58,5.31Z" fill="#d34216"/>
              <path id="Path_4380" data-name="Path 4380" d="M182.52,348.9l-.16,11.43-4.49-.68.07-5.44Z" fill="#dddfdf"/>
              <path id="Path_4381" data-name="Path 4381" d="M182.36,360.33l-.55,40.15-4.48-.44.54-40.39Z" fill="#d34216"/>
              <path id="Path_4382" data-name="Path 4382" d="M178.86,285.389l-.02,1.46-1.87-1.89Z" fill="#911f00"/>
              <path id="Path_4383" data-name="Path 4383" d="M176.97,284.96l1.87,1.89-.6,44.79-1.97-.3.62-46.4Z" fill="#af2d05"/>
              <path id="Path_4384" data-name="Path 4384" d="M178.24,331.639l-.05,4.08-1.94-3.24.02-1.14Z" fill="#c2c2c2"/>
              <path id="Path_4385" data-name="Path 4385" d="M176.25,332.48l1.94,3.24-.25,18.49-2.01,2.34Z" fill="#af2d05"/>
              <path id="Path_4386" data-name="Path 4386" d="M177.94,354.21l-.07,5.44-1.98-.3.04-2.8Z" fill="#c2c2c2"/>
              <path id="Path_4387" data-name="Path 4387" d="M177.87,359.65l-.54,40.39-1.98-.2.54-40.49Z" fill="#af2d05"/>
              <path id="Path_4388" data-name="Path 4388" d="M176.89,284.94l-.62,46.4-.77-.12.75,1.26-.32,24.07-2.14,2.48-1.28-.02v-.17h.01l.27-6.1-.2-.03.21-15.77.19.03.28-6.09-.39-.06.63-46.45v-.21Z" fill="#c53c08"/>
              <path id="Path_4389" data-name="Path 4389" d="M176.27,331.34l-.02,1.14-.75-1.26Z" fill="#dddfdf"/>
              <path id="Path_4390" data-name="Path 4390" d="M175.93,356.549l-.04,2.8-2.1-.32Z" fill="#dddfdf"/>
              <path id="Path_4391" data-name="Path 4391" d="M175.89,359.35l-.54,40.49-3.39-.341.55-40.489,1.28.02Z" fill="#c53c08"/>
              <path id="Path_4392" data-name="Path 4392" d="M173.51,284.159v.21h-.01l-2.79.51.02-1.36Z" fill="#911f00"/>
              <path id="Path_4393" data-name="Path 4393" d="M173.51,284.37l-.63,46.45-2.78-.42.61-45.52,2.79-.51Z" fill="#e24f2d"/>
              <path id="Path_4394" data-name="Path 4394" d="M173.27,330.88l-.28,6.09-.19-.03.08-6.12Z" fill="#dddfdf"/>
              <path id="Path_4395" data-name="Path 4395" d="M172.88,330.819l-.08,6.12-2.79-.42.09-6.12Z" fill="#f8f8f8"/>
              <path id="Path_4396" data-name="Path 4396" d="M172.8,336.94l-.21,15.77-2.79-.42.07-5.12.96.14.27-5.89-1.15-.17.06-4.73Z" fill="#e24f2d"/>
              <path id="Path_4397" data-name="Path 4397" d="M172.79,352.74l-.27,6.1h-.01l.08-6.13Z" fill="#dddfdf"/>
              <path id="Path_4398" data-name="Path 4398" d="M172.59,352.71l-.08,6.13-2.79-.42.08-6.13Z" fill="#f8f8f8"/>
              <path id="Path_4399" data-name="Path 4399" d="M172.51,358.84v.17l-.55,40.49-2.79-.27.55-40.81Z" fill="#e24f2d"/>
              <path id="Path_4400" data-name="Path 4400" d="M171.1,341.42l-.27,5.89-.96-.14.08-5.92Z" fill="#f8f8f8"/>
              <path id="Path_4401" data-name="Path 4401" d="M170.73,283.52l-.02,1.36-3.52-.73.03-1.43Z" fill="#911f00"/>
              <path id="Path_4402" data-name="Path 4402" d="M170.71,284.88l-.61,45.52-3.87-.59.96-45.66Z" fill="#d34216"/>
              <path id="Path_4403" data-name="Path 4403" d="M170.1,330.4l-.09,6.12-3.91-.59.13-6.12Z" fill="#dddfdf"/>
              <path id="Path_4404" data-name="Path 4404" d="M170.01,336.52l-.06,4.73-3.95-.6.1-4.72Z" fill="#d34216"/>
              <path id="Path_4405" data-name="Path 4405" d="M169.95,341.25l-.08,5.92-4-.61.13-5.91Z" fill="#dddfdf"/>
              <path id="Path_4406" data-name="Path 4406" d="M169.87,347.17l-.07,5.12-4.04-.61.11-5.12Z" fill="#d34216"/>
              <path id="Path_4407" data-name="Path 4407" d="M169.8,352.29l-.08,6.13-4.08-.62.12-6.12Z" fill="#dddfdf"/>
              <path id="Path_4408" data-name="Path 4408" d="M165.64,357.8l4.08.62-.55,40.81-4.4-.44Z" fill="#d34216"/>
              <path id="Path_4409" data-name="Path 4409" d="M167.22,282.719l-.03,1.43h-.01l-1.88-1.8-.11-.1Z" fill="#911f00"/>
              <path id="Path_4410" data-name="Path 4410" d="M167.19,284.15l-.96,45.66-2.03-.3.99-47.04.11-.12,1.88,1.8Z" fill="#af2d05"/>
              <path id="Path_4411" data-name="Path 4411" d="M166.23,329.81l-.13,6.12-2.03-.31.13-6.11Z" fill="#c2c2c2"/>
              <path id="Path_4412" data-name="Path 4412" d="M166.1,335.929l-.1,4.72-2.03-.31.1-4.72Z" fill="#af2d05"/>
              <path id="Path_4413" data-name="Path 4413" d="M166,340.65l-.13,5.91-2.02-.3.12-5.92Z" fill="#c2c2c2"/>
              <path id="Path_4414" data-name="Path 4414" d="M165.87,346.56l-.11,5.12-2.02-.31.11-5.11Z" fill="#af2d05"/>
              <path id="Path_4415" data-name="Path 4415" d="M165.76,351.679l-.12,6.12-2.03-.31.13-6.12Z" fill="#c2c2c2"/>
              <path id="Path_4416" data-name="Path 4416" d="M165.64,357.8l-.87,40.99-2.02-.2.86-41.1Z" fill="#af2d05"/>
              <path id="Path_4417" data-name="Path 4417" d="M165.3,282.35l-.11.12-.99,47.04-3.43-.52.99-47.5.04-.02,3.39.78Z" fill="#c53c08"/>
              <path id="Path_4418" data-name="Path 4418" d="M164.2,329.509l-.13,6.11-3.43-.52.13-6.11Z" fill="#dddfdf"/>
              <path id="Path_4419" data-name="Path 4419" d="M164.07,335.62l-.1,4.72-3.42-.52.09-4.72Z" fill="#c53c08"/>
              <path id="Path_4420" data-name="Path 4420" d="M163.97,340.34l-.12,5.92-3.43-.52.13-5.92Z" fill="#dddfdf"/>
              <path id="Path_4421" data-name="Path 4421" d="M163.85,346.259l-.11,5.11-3.42-.52.1-5.11Z" fill="#c53c08"/>
              <path id="Path_4422" data-name="Path 4422" d="M163.74,351.37l-.13,6.12-3.42-.52.13-6.12Z" fill="#dddfdf"/>
              <path id="Path_4423" data-name="Path 4423" d="M163.61,357.49l-.86,41.1-3.42-.34.86-41.28Z" fill="#c53c08"/>
              <path id="Path_4424" data-name="Path 4424" d="M161.76,281.46v.03l-2.83,1.01-.03-.01.03-1.67v-.01Z" fill="#911f00"/>
              <path id="Path_4425" data-name="Path 4425" d="M161.76,281.49l-.99,47.5-2.82-.43.95-46.07.03.01Z" fill="#e24f2d"/>
              <path id="Path_4426" data-name="Path 4426" d="M160.77,328.99l-.13,6.11-1.74-.26-.21,4.7,1.86.28-.13,5.92-2-.3-.23,5.09,2.13.32-.13,6.12-2.82-.43.58-27.98Z" fill="#f8f8f8"/>
              <path id="Path_4427" data-name="Path 4427" d="M160.64,335.1l-.09,4.72-1.86-.28.21-4.7Z" fill="#e24f2d"/>
              <path id="Path_4428" data-name="Path 4428" d="M160.42,345.74l-.1,5.11-2.13-.32.23-5.09Z" fill="#e24f2d"/>
              <path id="Path_4429" data-name="Path 4429" d="M160.19,356.969l-.86,41.28-2.81-.279.85-41.431Z" fill="#e24f2d"/>
              <path id="Path_4430" data-name="Path 4430" d="M158.93,280.81v.01l-.03,1.67-3.68-1.02-.05-.05.04-1.47Z" fill="#911f00"/>
              <path id="Path_4431" data-name="Path 4431" d="M158.9,282.49l-.95,46.07-3.93-.6,1.15-46.54.05.05Z" fill="#d34216"/>
              <path id="Path_4432" data-name="Path 4432" d="M157.95,328.56l-.58,27.98-4.05-.61.7-27.97Z" fill="#dddfdf"/>
              <path id="Path_4433" data-name="Path 4433" d="M157.37,356.54l-.85,41.43-4.23-.42,1.03-41.62Z" fill="#d34216"/>
              <path id="Path_4434" data-name="Path 4434" d="M155.21,279.949l-.04,1.47-1.97-1.93Z" fill="#911f00"/>
              <path id="Path_4435" data-name="Path 4435" d="M155.17,281.42l-1.15,46.54-2-.3,1.18-48.17Z" fill="#af2d05"/>
              <path id="Path_4436" data-name="Path 4436" d="M154.02,327.96l-.7,27.97-1.98-.3.68-27.97Z" fill="#c2c2c2"/>
              <path id="Path_4437" data-name="Path 4437" d="M153.32,355.929l-1.03,41.621-1.97-.191v-.01l1.02-41.72Z" fill="#af2d05"/>
              <path id="Path_4438" data-name="Path 4438" d="M153.2,279.49l-1.18,48.17-.66-.1-1.27,27.88-2.7-.07,1.89-76.77v-.01h.01Z" fill="#c53c08"/>
              <path id="Path_4439" data-name="Path 4439" d="M152.02,327.659l-.68,27.97-1.25-.19,1.27-27.88Z" fill="#dddfdf"/>
              <path id="Path_4440" data-name="Path 4440" d="M151.34,355.63l-1.02,41.72v.01l-3.96-.4,1.03-41.59,2.7.07Z" fill="#c53c08"/>
              <path id="Path_4441" data-name="Path 4441" d="M149.28,278.59v.01l-2.02,1.12.04-1.59Z" fill="#911f00"/>
              <path id="Path_4442" data-name="Path 4442" d="M149.28,278.6l-1.89,76.77-1.03,41.589-1.97-.189,2.87-117.05Z" fill="#e24f2d"/>
              <path id="Path_4443" data-name="Path 4443" d="M147.3,278.13l-.04,1.59-.05.03-5.13-1.4.05-1.4Z" fill="#911f00"/>
              <path id="Path_4444" data-name="Path 4444" d="M147.26,279.719l-2.87,117.05-6.24-.62,3.93-117.8,5.13,1.4Z" fill="#d34216"/>
              <path id="Path_4445" data-name="Path 4445" d="M142.13,276.949l-.05,1.4-.06-.02-1.09-1.66Z" fill="#911f00"/>
              <path id="Path_4446" data-name="Path 4446" d="M142.08,278.35l-3.93,117.8-1.25-.13,3.96-119.37.07.02,1.09,1.66Z" fill="#af2d05"/>
              <path id="Path_4447" data-name="Path 4447" d="M140.86,276.65,136.9,396.02l-3.95-.39v-.01l3.95-119.88Z" fill="#c53c08"/>
              <path id="Path_4448" data-name="Path 4448" d="M136.9,275.74l-3.95,119.88v.011l-2.67-.261,3.92-118.86,2.69-.77Z" fill="#e24f2d"/>
              <path id="Path_4449" data-name="Path 4449" d="M136.89,275.74l-2.69.77.04-1.38Z" fill="#911f00"/>
              <path id="Path_4450" data-name="Path 4450" d="M134.24,275.13l-.04,1.38-4.65-1.06.04-1.39Z" fill="#911f00"/>
              <path id="Path_4451" data-name="Path 4451" d="M134.2,276.509l-3.92,118.86-4.68-.47.01-.12,3.94-119.33Z" fill="#d34216"/>
              <path id="Path_4452" data-name="Path 4452" d="M129.59,274.06l-.04,1.39h-.01l-1.14-1.66Z" fill="#911f00"/>
              <path id="Path_4453" data-name="Path 4453" d="M129.55,275.449l-3.94,119.33-.01.12-1.19-.12,3.99-120.99,1.14,1.66Z" fill="#af2d05"/>
              <path id="Path_4454" data-name="Path 4454" d="M128.4,273.79l-3.99,120.99-3.76-.37,3.97-121.49Z" fill="#c53c08"/>
              <path id="Path_4455" data-name="Path 4455" d="M124.62,272.92l-3.97,121.49-2.33-.23,3.96-120.39h.02l2.11-.92Z" fill="#e24f2d"/>
              <path id="Path_4456" data-name="Path 4456" d="M124.41,272.87l-2.11.92h-.02l-3.96,120.39L116.5,394l4.14-117.749,1.42-.84v-3.08Z" fill="#af2d05"/>
              <path id="Path_4457" data-name="Path 4457" d="M122.06,272.329v3.08l-6.42-2.08.17-6.67,6.25,1.59v4.08Zm-1.75-1.38c0-1.31-.65-2.37-1.46-2.37s-1.46,1.06-1.46,2.37.66,2.38,1.46,2.38,1.46-1.06,1.46-2.38" fill="#d34216"/>
              <path id="Path_4458" data-name="Path 4458" d="M122.06,275.409l-1.42.84-5-1.59-.51.35-1.82,1.24-1.75-.25,4.08-2.67Z" fill="#911f00"/>
              <path id="Path_4459" data-name="Path 4459" d="M118.85,268.58c.81,0,1.46,1.06,1.46,2.37s-.65,2.38-1.46,2.38-1.46-1.06-1.46-2.38.66-2.37,1.46-2.37" fill="#661300"/>
              <path id="Path_4460" data-name="Path 4460" d="M117.06,401.91l-.12,6.67h-7V401.5Zm-1.5,3.32a1.8,1.8,0,1,0-1.75,2.22,2.033,2.033,0,0,0,1.75-2.22" fill="#d34216"/>
              <path id="Path_4461" data-name="Path 4461" d="M116.73,277.5l.03,5.3h-2.08v-5.67l.45.08Z" fill="#bc2e08"/>
              <path id="Path_4462" data-name="Path 4462" d="M116.76,282.8l-.33.64h-1.75l-1.66.72.02-.68,1.64-.68Z" fill="#911f00"/>
              <path id="Path_4463" data-name="Path 4463" d="M115.92,266.11l-1.39,1.27-1.15.65a.946.946,0,0,0-1.03.58l-1.29.72.08,1.16a1.429,1.429,0,0,1-.36-.69,2.943,2.943,0,0,1,3-2.75l1.48-1.49Z" fill="#999"/>
              <path id="Path_4464" data-name="Path 4464" d="M111.3,270.65a2.093,2.093,0,0,0,.95.45c-.01.13-.02.26-.02.4,0,1.01.48,1.83,1.08,1.83s1.08-.82,1.08-1.83a3.247,3.247,0,0,0-.12-.86,1.936,1.936,0,0,0-.26-.54.922.922,0,0,0-.39-.35.586.586,0,0,0-.31-.09.769.769,0,0,0-.56.27c-.27-.06-.5-.31-.48-.94a.888.888,0,0,1,.08-.38l1.03-.58,1.15-.65,1.28-.72-.17,6.67L111.56,276l-.42-5.5a1.1,1.1,0,0,0,.16.15" fill="#c53c08"/>
              <path id="Path_4465" data-name="Path 4465" d="M115.36,313.7l.03,5.3h-2.08v-5.67Z" fill="#bc2e08"/>
              <path id="Path_4466" data-name="Path 4466" d="M115.39,319l-.33.64h-1.75l-1.63.71.03-.69,1.6-.66Z" fill="#911f00"/>
              <path id="Path_4467" data-name="Path 4467" d="M113.81,403a2.289,2.289,0,1,1-1.75,2.23,2.033,2.033,0,0,1,1.75-2.23" fill="#661300"/>
              <path id="Path_4468" data-name="Path 4468" d="M115.26,265.56l-1.48,1.49a2.943,2.943,0,0,0-3,2.75,2.246,2.246,0,0,1,.01-1.18c.66-2.77,2.79-2.28,2.79-2.28l1.17-1.2Z" fill="#c2c2c2"/>
              <path id="Path_4469" data-name="Path 4469" d="M115.13,275.009v2.2l-.45-.08-1.43.75.06-1.63Z" fill="#bc2e08"/>
              <path id="Path_4470" data-name="Path 4470" d="M114.68,277.13v5.67l-1.64.68.21-5.6Z" fill="#c53c08"/>
              <path id="Path_4471" data-name="Path 4471" d="M114.68,283.44l-1.37,29.89-1.39.73,1.1-29.9Z" fill="#bc2e08"/>
              <path id="Path_4472" data-name="Path 4472" d="M114.59,350.029l.03,5.28-2.09-.22v-5.64Z" fill="#bc2e08"/>
              <path id="Path_4473" data-name="Path 4473" d="M114.62,355.31l-.33.6-1.76-.18-.78.26.03-.67.75-.23Z" fill="#911f00"/>
              <path id="Path_4474" data-name="Path 4474" d="M114.27,270.639a3.247,3.247,0,0,1,.12.86c0,1.01-.48,1.83-1.08,1.83s-1.08-.82-1.08-1.83c0-.14.01-.27.02-.4a2.3,2.3,0,0,0,2.02-.46" fill="#661300"/>
              <path id="Path_4475" data-name="Path 4475" d="M114.01,270.1a1.936,1.936,0,0,1,.26.54,2.3,2.3,0,0,1-2.02.46c.02-.12.04-.22.06-.33a3.094,3.094,0,0,0,1.7-.67" fill="#404040"/>
              <path id="Path_4476" data-name="Path 4476" d="M113.62,269.75a.922.922,0,0,1,.39.35,3.094,3.094,0,0,1-1.7.67,1.964,1.964,0,0,1,.44-.84,1.424,1.424,0,0,0,.87-.18" fill="#999"/>
              <path id="Path_4477" data-name="Path 4477" d="M113.31,269.659a.586.586,0,0,1,.31.09,1.424,1.424,0,0,1-.87.18.769.769,0,0,1,.56-.27" fill="#661300"/>
              <path id="Path_4478" data-name="Path 4478" d="M113.31,313.33V319l-1.6.66.21-5.6Z" fill="#c53c08"/>
              <path id="Path_4479" data-name="Path 4479" d="M113.31,276.25l-.06,1.63-.87.45.06-1.66Z" fill="#911f00"/>
              <path id="Path_4480" data-name="Path 4480" d="M113.31,319.639l-1.3,30.02-1.43.6,1.1-29.91Z" fill="#bc2e08"/>
              <path id="Path_4481" data-name="Path 4481" d="M113.31,276.25l-.87.42-12.18,5.93.05-1.28L111.56,276Z" fill="#911f00"/>
              <path id="Path_4482" data-name="Path 4482" d="M113.25,277.88l-.21,5.6-.86.35.2-5.5Z" fill="#c53c08"/>
              <path id="Path_4483" data-name="Path 4483" d="M113.04,283.48l-.02.68-.87.38.03-.71Z" fill="#911f00"/>
              <path id="Path_4484" data-name="Path 4484" d="M113.02,284.159l-1.1,29.9-.86.44.47-12.99.62-16.97Z" fill="#911f00"/>
              <path id="Path_4485" data-name="Path 4485" d="M112.75,269.929a1.964,1.964,0,0,0-.44.84,2.142,2.142,0,0,1-1.01-.12,1.1,1.1,0,0,1-.16-.15v-.01l-.08-1.16,1.29-.72a.887.887,0,0,0-.08.38c-.02.63.21.88.48.94" fill="#999"/>
              <path id="Path_4486" data-name="Path 4486" d="M112.54,386.33l-.09,5.02-2.19-.25.13-5.37Z" fill="#bc2e08"/>
              <path id="Path_4487" data-name="Path 4487" d="M112.53,349.449v5.64l-.75.23-1.4.43.2-5.49,1.43-.6Z" fill="#c53c08"/>
              <path id="Path_4488" data-name="Path 4488" d="M112.45,391.35l-.36.569-1.84-.21h-.01l.02-.609Z" fill="#911f00"/>
              <path id="Path_4489" data-name="Path 4489" d="M112.44,276.67l-.06,1.66-4.26,2.22v4.96l1.12.3,2.91-1.27-.62,16.97-1.59-.06v-7.63l-10.32,4.4.27-6.59a3.65,3.65,0,0,0,.4-.14h1.84l.21-6.06-.14-.23h-1.66a3.518,3.518,0,0,0-.38-.25l.1-2.35Z" fill="#bc2e08"/>
              <path id="Path_4490" data-name="Path 4490" d="M112.38,278.33l-.2,5.5-4.06,1.68v-4.96Z" fill="#c53c08"/>
              <path id="Path_4491" data-name="Path 4491" d="M112.31,270.77c-.02.11-.04.21-.06.33a2.093,2.093,0,0,1-.95-.45,2.142,2.142,0,0,0,1.01.12" fill="#404040"/>
              <path id="Path_4492" data-name="Path 4492" d="M112.18,283.83l-.03.71-2.911,1.27-1.119-.3Z" fill="#911f00"/>
              <path id="Path_4493" data-name="Path 4493" d="M111.92,314.06l-.21,5.6-.85.35.2-5.51Z" fill="#c53c08"/>
              <path id="Path_4494" data-name="Path 4494" d="M111.78,355.319l-.03.67-1.4.46.03-.7Z" fill="#911f00"/>
              <path id="Path_4495" data-name="Path 4495" d="M111.75,355.99l-1.36,29.74-1.13.4,1.09-29.68Z" fill="#bc2e08"/>
              <path id="Path_4496" data-name="Path 4496" d="M111.71,319.659l-.03.69-.85.37.03-.71Z" fill="#911f00"/>
              <path id="Path_4497" data-name="Path 4497" d="M111.68,320.35l-1.1,29.91-.841.35.651-17.73.44-12.16Z" fill="#911f00"/>
              <path id="Path_4498" data-name="Path 4498" d="M111.14,270.5l.42,5.5-11.25,5.32.05-1.15a1,1,0,0,0-.96-1.04.986.986,0,0,0-1.04.96l-.09,2.2-11.48,5.43.02-.42a1,1,0,0,0-2-.07l-.06,1.46-7.02,3.32.07-1.25a.942.942,0,0,0-.86-1.01.933.933,0,0,0-.97.91l-.12,2.24-10.66,5.05.07-1.36a.935.935,0,0,0-.87-.98.907.907,0,0,0-.96.88l-.13,2.35-6.57,3.11.46-5.67Z" fill="#ad2900"/>
              <path id="Path_4499" data-name="Path 4499" d="M111.53,301.509l-.47,12.99-4.31,2.25v4.96l1.12.3,2.96-1.29-.44,12.16-1.45-.05-.66.23.04-1.52-10.21,3.44,1.2-29.19,10.63-4.34Z" fill="#bc2e08"/>
              <path id="Path_4500" data-name="Path 4500" d="M111.06,314.5l-.2,5.51-4.11,1.7v-4.96Z" fill="#c53c08"/>
              <path id="Path_4501" data-name="Path 4501" d="M110.86,320.009l-.03.71-2.96,1.29-1.12-.3Z" fill="#911f00"/>
              <path id="Path_4502" data-name="Path 4502" d="M110.58,350.259l-.2,5.49-.84.26.2-5.4Z" fill="#c53c08"/>
              <path id="Path_4503" data-name="Path 4503" d="M110.39,332.88l-.65,17.73-3.8,1.57v4.93l1.13.42,2.45-.81L109,370.86l-1.63-.06-.66.18.05-1.47-10.18,2.78.23-5.66s2.03-.72,4.29-1.51a2.5,2.5,0,0,0,.52.82v1.44h1.22l.05-1.97a2.709,2.709,0,0,1-.25-.81c1.26-.43,2.46-.83,3.3-1.09.72-.22.99-.51.98-.75-.01-.3-.46-.53-.98-.44-.71.12-1.88.33-3.14.57a4.083,4.083,0,0,1,.27-.73v-2.31h-1.45v2.45a3.338,3.338,0,0,0-.51.9c-2.19.42-4.19.81-4.19.81l.62-15.03a2.463,2.463,0,0,0,.35-.12h1.84l-.05-1.77-.15-.23H97.88a1.844,1.844,0,0,0-.25-.17l.16-3.98,10.33-3.32.16-6.33.66-.23Z" fill="#bc2e08"/>
              <path id="Path_4504" data-name="Path 4504" d="M110.39,385.73l-.13,5.37-1.2.31.2-5.28Z" fill="#c53c08"/>
              <path id="Path_4505" data-name="Path 4505" d="M110.38,355.75l-.03.7-.83.27.02-.71Z" fill="#911f00"/>
              <path id="Path_4506" data-name="Path 4506" d="M110.35,356.449l-1.09,29.68-.83.29.57-15.56.52-14.14Z" fill="#911f00"/>
              <path id="Path_4507" data-name="Path 4507" d="M110.26,391.1l-.02.609-1.2.33.02-.629Z" fill="#911f00"/>
              <path id="Path_4508" data-name="Path 4508" d="M110.24,391.709l-.3,9.79-3.63,1.91v5.91l-1.25.26-55.5,10-.12-6.37H48.2l-.66.131.5-8,.08.05,1.73-.441-.189,2.74,8.109-1.77a.862.862,0,0,0,.75.48h.03a.906.906,0,0,0,.93-.849l10.67-2.32a.939.939,0,0,0,.87.99h.03a.931.931,0,0,0,.93-.91l.03-.491,7.96-1.729v.059a.98.98,0,0,0,.961,1h.039a.976.976,0,0,0,1-.929l.02-.571,11.53-2.509a1,1,0,0,0,.96.99h.04a1,1,0,0,0,1-.961l.02-.47,12.58-2.74.82-.18.07-2.03.03-.71Z" fill="#ad2900"/>
              <path id="Path_4509" data-name="Path 4509" d="M109.94,401.5v7.08l-3.63.741v-5.91Zm-1,4.181c0-1.12-.4-2.03-.88-2.03s-.88.91-.88,2.03.4,2.029.88,2.029.88-.909.88-2.029" fill="#c53c08"/>
              <path id="Path_4510" data-name="Path 4510" d="M109.94,295.259v6.19l-10.63,4.34.25-6.02Z" fill="#af2205"/>
              <path id="Path_4511" data-name="Path 4511" d="M109.94,293.819v1.44l-10.38,4.51.06-1.55Z" fill="#911f00"/>
              <path id="Path_4512" data-name="Path 4512" d="M109.74,350.61l-.2,5.4-3.6,1.1v-4.93Z" fill="#c53c08"/>
              <path id="Path_4513" data-name="Path 4513" d="M109.54,356.009l-.02.71-2.45.81-1.13-.42Z" fill="#911f00"/>
              <path id="Path_4514" data-name="Path 4514" d="M109.26,386.13l-.2,5.28-.82.21.19-5.2Z" fill="#c53c08"/>
              <path id="Path_4515" data-name="Path 4515" d="M109.06,391.409l-.02.63-.82.23.02-.65Z" fill="#911f00"/>
              <path id="Path_4516" data-name="Path 4516" d="M109.04,392.039l-.03.71-.82.181.03-.661Z" fill="#911f00"/>
              <path id="Path_4517" data-name="Path 4517" d="M109.01,392.749l-.07,2.03-.82.18.07-2.029Z" fill="#911f00"/>
              <path id="Path_4518" data-name="Path 4518" d="M109,370.86l-.57,15.56-5.02,1.76-.12,4.7,1.18.42,3.75-1.03-.03.66-12.57,2.8.12-2.811c.23-.069.38-.129.38-.129h1.83l.22-6.05-.15-.24H96.36a3.638,3.638,0,0,0-.35-.24l.24-5.78L106.47,378l.03-.84.21-6.18.66-.18Z" fill="#bc2e08"/>
              <path id="Path_4519" data-name="Path 4519" d="M108.06,403.65c.48,0,.88.91.88,2.03s-.4,2.03-.88,2.03-.88-.91-.88-2.03.4-2.03.88-2.03" fill="#661300"/>
              <path id="Path_4520" data-name="Path 4520" d="M108.43,386.42l-.191,5.2-4.949,1.26.12-4.7Z" fill="#c53c08"/>
              <path id="Path_4521" data-name="Path 4521" d="M108.32,331.54l-.04,1.52-10.23,3.51.06-1.59Z" fill="#911f00"/>
              <path id="Path_4522" data-name="Path 4522" d="M108.28,333.06l-.16,6.33-10.33,3.32.26-6.14Z" fill="#af2205"/>
              <path id="Path_4523" data-name="Path 4523" d="M108.24,391.619l-.02.65-3.75,1.031-1.18-.42Z" fill="#911f00"/>
              <path id="Path_4524" data-name="Path 4524" d="M108.19,392.93l-.07,2.029L95.54,397.7l.08-1.97Z" fill="#911f00"/>
              <path id="Path_4525" data-name="Path 4525" d="M106.92,362.759c.01.241-.26.531-.98.75-.84.261-2.04.66-3.3,1.091a3.331,3.331,0,0,1-.02-.771Z" fill="#999"/>
              <path id="Path_4526" data-name="Path 4526" d="M106.92,362.759l-4.3,1.07a4.772,4.772,0,0,1,.18-.94c1.26-.239,2.43-.449,3.14-.569.52-.091.97.14.98.439" fill="#c2c2c2"/>
              <path id="Path_4527" data-name="Path 4527" d="M106.76,369.509l-.05,1.47-10.19,2.84.06-1.53Z" fill="#911f00"/>
              <path id="Path_4528" data-name="Path 4528" d="M106.71,370.98l-.21,6.18-10.22,2.48.24-5.82Z" fill="#af2205"/>
              <path id="Path_4529" data-name="Path 4529" d="M106.5,377.159l-.03.84-10.22,2.48.03-.84Z" fill="#9b1d00"/>
              <path id="Path_4530" data-name="Path 4530" d="M103.07,359.85v2.31a4.083,4.083,0,0,0-.27.73c-.16.03-.33.06-.5.09a2.134,2.134,0,0,1,.42-.82v-2.31Z" fill="#999"/>
              <path id="Path_4531" data-name="Path 4531" d="M102.89,365.409l-.05,1.97h-.4V365.6a5.958,5.958,0,0,1-.2-.87c.13-.04.26-.09.4-.13a2.709,2.709,0,0,0,.25.81" fill="#999"/>
              <path id="Path_4532" data-name="Path 4532" d="M102.3,362.98c.17-.03.34-.06.5-.09a4.757,4.757,0,0,0-.18.94l-.44.11a3.51,3.51,0,0,1,.12-.96" fill="#999"/>
              <path id="Path_4533" data-name="Path 4533" d="M102.72,359.85v2.31l-1.1.14v-2.45Z" fill="#dddfdf"/>
              <path id="Path_4534" data-name="Path 4534" d="M101.62,362.3l1.1-.139a2.134,2.134,0,0,0-.42.82c-.39.069-.79.15-1.19.22a3.355,3.355,0,0,1,.51-.9" fill="#c2c2c2"/>
              <path id="Path_4535" data-name="Path 4535" d="M102.62,363.83a3.322,3.322,0,0,0,.02.77c-.14.04-.27.09-.4.13a4.878,4.878,0,0,1-.06-.79Z" fill="#999"/>
              <path id="Path_4536" data-name="Path 4536" d="M102.44,365.69v1.69h-.82v-1.44Z" fill="#dddfdf"/>
              <path id="Path_4537" data-name="Path 4537" d="M102.44,365.6v.09l-.82.25a2.5,2.5,0,0,1-.52-.82l1.14-.39a5.959,5.959,0,0,0,.2.87" fill="#c2c2c2"/>
              <path id="Path_4538" data-name="Path 4538" d="M102.2,285.2l.14.23-.21,6.06h-.17Z" fill="#999"/>
              <path id="Path_4539" data-name="Path 4539" d="M101.11,363.2c.4-.07.8-.15,1.19-.22a3.51,3.51,0,0,0-.12.96,4.878,4.878,0,0,0,.06.79l-1.14.39a2.31,2.31,0,0,1-.19-.93,2.723,2.723,0,0,1,.2-.99" fill="#dddfdf"/>
              <path id="Path_4540" data-name="Path 4540" d="M102.2,285.2l-.24,6.29h-1.67l.25-6.29Z" fill="#c2c2c2"/>
              <path id="Path_4541" data-name="Path 4541" d="M101.11,363.2a2.723,2.723,0,0,0-.2.99l-4.04,1.08.05-1.26s2-.39,4.19-.81" fill="#c2c2c2"/>
              <path id="Path_4542" data-name="Path 4542" d="M100.91,364.19a2.31,2.31,0,0,0,.19.93c-2.26.79-4.29,1.51-4.29,1.51l.06-1.36Z" fill="#999"/>
              <path id="Path_4543" data-name="Path 4543" d="M100.54,285.2l-.25,6.29a3.654,3.654,0,0,1-.4.14l.27-6.68a3.523,3.523,0,0,1,.38.25" fill="#dddfdf"/>
              <path id="Path_4544" data-name="Path 4544" d="M99.4,279.13a1,1,0,0,1,.96,1.04l-.05,1.15-1.02.49Z" fill="#999"/>
              <path id="Path_4545" data-name="Path 4545" d="M100.31,281.319l-.05,1.28-1.02.49.05-1.28Z" fill="#999"/>
              <path id="Path_4546" data-name="Path 4546" d="M100.26,282.6l-.1,2.35a2.066,2.066,0,0,0-.98-.3l.06-1.56Z" fill="#999"/>
              <path id="Path_4547" data-name="Path 4547" d="M100.16,284.949l-.27,6.68a4.507,4.507,0,0,1-1.01.17l.3-7.15a2.066,2.066,0,0,1,.98.3" fill="#dddfdf"/>
              <path id="Path_4548" data-name="Path 4548" d="M99.89,291.63l-.27,6.59-1.02.44.28-6.86a4.506,4.506,0,0,0,1.01-.17" fill="#999"/>
              <path id="Path_4549" data-name="Path 4549" d="M99.68,347.09l.05,1.77h-.16l-.04-2Z" fill="#999"/>
              <path id="Path_4550" data-name="Path 4550" d="M99.62,298.219l-.06,1.55-1.02.44.06-1.55Z" fill="#999"/>
              <path id="Path_4551" data-name="Path 4551" d="M99.53,346.86l.04,2H97.89l-.01-2Z" fill="#c2c2c2"/>
              <path id="Path_4552" data-name="Path 4552" d="M99.56,299.77l-.25,6.02-1.01.41.24-5.99Z" fill="#999"/>
              <path id="Path_4553" data-name="Path 4553" d="M99.4,279.13l-.11,2.68-1.02.48.09-2.2a.986.986,0,0,1,1.04-.96" fill="#c2c2c2"/>
              <path id="Path_4554" data-name="Path 4554" d="M99.31,305.79l-1.2,29.19-.99.34L98.3,306.2Z" fill="#999"/>
              <path id="Path_4555" data-name="Path 4555" d="M99.29,281.81l-.05,1.28-1.03.5.06-1.3Z" fill="#c2c2c2"/>
              <path id="Path_4556" data-name="Path 4556" d="M99.24,283.09l-.059,1.56a1.928,1.928,0,0,0-1.02.27l.049-1.33Z" fill="#c2c2c2"/>
              <path id="Path_4557" data-name="Path 4557" d="M99.18,284.65l-.3,7.15a1.745,1.745,0,0,1-.99-.22l.27-6.66a1.933,1.933,0,0,1,1.02-.27" fill="#f8f8f8"/>
              <path id="Path_4558" data-name="Path 4558" d="M98.88,291.8l-.28,6.861-1.02.43.31-7.51a1.737,1.737,0,0,0,.99.219" fill="#c2c2c2"/>
              <path id="Path_4559" data-name="Path 4559" d="M98.6,298.659l-.06,1.55-1.02.45.06-1.57Z" fill="#c2c2c2"/>
              <path id="Path_4560" data-name="Path 4560" d="M97.52,300.659l1.02-.45-.24,5.99-1.03.42Z" fill="#c2c2c2"/>
              <path id="Path_4561" data-name="Path 4561" d="M98.3,306.2l-1.18,29.12-1.04.35,1.19-29.05Z" fill="#c2c2c2"/>
              <path id="Path_4562" data-name="Path 4562" d="M98.27,282.29l-.06,1.3-11.48,5.59.06-1.46Z" fill="#911f00"/>
              <path id="Path_4563" data-name="Path 4563" d="M98.21,283.59l-.05,1.33a1.665,1.665,0,0,0-.35.25l-.26,6.08a1.068,1.068,0,0,0,.34.33l-.31,7.51-11.48,4.9.24-5.62a3.654,3.654,0,0,0,.4-.14h1.84l.21-5.85-.14-.22H86.99a3.356,3.356,0,0,0-.38-.24v-.01l.12-2.73Z" fill="#bc2e08"/>
              <path id="Path_4564" data-name="Path 4564" d="M98.02,386.5l.15.24-.22,6.05h-.16Z" fill="#999"/>
              <path id="Path_4565" data-name="Path 4565" d="M98.16,284.92l-.27,6.66a1.068,1.068,0,0,1-.34-.33l.26-6.08a1.665,1.665,0,0,1,.35-.25" fill="#dddfdf"/>
              <path id="Path_4566" data-name="Path 4566" d="M98.11,334.98l-.06,1.59-1,.34.07-1.59Z" fill="#999"/>
              <path id="Path_4567" data-name="Path 4567" d="M98.05,336.569l-.26,6.14-.99.31.25-6.11Z" fill="#999"/>
              <path id="Path_4568" data-name="Path 4568" d="M98.02,386.5l-.23,6.29H96.12l.24-6.29Z" fill="#c2c2c2"/>
              <path id="Path_4569" data-name="Path 4569" d="M97.88,346.86l.01,2a2.463,2.463,0,0,1-.35.12l.09-2.29a1.844,1.844,0,0,1,.25.17" fill="#dddfdf"/>
              <path id="Path_4570" data-name="Path 4570" d="M97.79,342.71l-.16,3.98a2.011,2.011,0,0,0-.96-.36l.13-3.31Z" fill="#999"/>
              <path id="Path_4571" data-name="Path 4571" d="M97.63,346.69l-.09,2.29a4.636,4.636,0,0,1-.99.18l.12-2.83a2.011,2.011,0,0,1,.96.36" fill="#dddfdf"/>
              <path id="Path_4572" data-name="Path 4572" d="M97.58,299.09l-.06,1.57-11.49,4.99.07-1.66Z" fill="#911f00"/>
              <path id="Path_4573" data-name="Path 4573" d="M97.54,348.98l-.62,15.03a1.611,1.611,0,0,0-.58,1.4,2.446,2.446,0,0,0,.47,1.22l-.23,5.66-.98.26.95-23.39a4.636,4.636,0,0,0,.99-.18" fill="#999"/>
              <path id="Path_4574" data-name="Path 4574" d="M97.52,300.659l-.25,5.96L85.79,311.3l.24-5.65Z" fill="#af2205"/>
              <path id="Path_4575" data-name="Path 4575" d="M97.27,306.62l-1.19,29.05-11.49,3.87,1.2-28.24Z" fill="#bc2e08"/>
              <path id="Path_4576" data-name="Path 4576" d="M97.12,335.319l-.07,1.59-1.03.35.06-1.59Z" fill="#c2c2c2"/>
              <path id="Path_4577" data-name="Path 4577" d="M97.05,336.909l-.25,6.11-1.03.34.25-6.1Z" fill="#c2c2c2"/>
              <path id="Path_4578" data-name="Path 4578" d="M96.92,364.009l-.05,1.261-.53.14a1.614,1.614,0,0,1,.58-1.4" fill="#c2c2c2"/>
              <path id="Path_4579" data-name="Path 4579" d="M96.87,365.27l-.06,1.36a2.446,2.446,0,0,1-.47-1.22Z" fill="#999"/>
              <path id="Path_4580" data-name="Path 4580" d="M95.77,343.36l1.03-.34-.13,3.31a1.728,1.728,0,0,0-1.03.17Z" fill="#c2c2c2"/>
              <path id="Path_4581" data-name="Path 4581" d="M96.67,346.33l-.12,2.83a1.828,1.828,0,0,1-1.02-.17l.11-2.49a1.728,1.728,0,0,1,1.03-.17" fill="#f8f8f8"/>
              <path id="Path_4582" data-name="Path 4582" d="M96.58,372.29l-.06,1.53-.98.27.06-1.54Z" fill="#999"/>
              <path id="Path_4583" data-name="Path 4583" d="M96.55,349.159l-.95,23.39-1.04.29.97-23.85a1.828,1.828,0,0,0,1.02.17" fill="#c2c2c2"/>
              <path id="Path_4584" data-name="Path 4584" d="M96.52,373.819l-.24,5.82-.97.24.23-5.79Z" fill="#999"/>
              <path id="Path_4585" data-name="Path 4585" d="M96.36,386.5l-.24,6.29s-.15.06-.38.129l.27-6.659a3.638,3.638,0,0,1,.35.24" fill="#dddfdf"/>
              <path id="Path_4586" data-name="Path 4586" d="M96.28,379.639l-.03.84-.98.23.04-.83Z" fill="#999"/>
              <path id="Path_4587" data-name="Path 4587" d="M96.25,380.48l-.24,5.78a2.011,2.011,0,0,0-.95-.3l.21-5.25Z" fill="#999"/>
              <path id="Path_4588" data-name="Path 4588" d="M96.08,335.67l-.06,1.59-11.5,3.94.07-1.66Z" fill="#911f00"/>
              <path id="Path_4589" data-name="Path 4589" d="M96.02,337.259l-.25,6.1-11.5,3.69.25-5.85Z" fill="#af2205"/>
              <path id="Path_4590" data-name="Path 4590" d="M96.01,386.259l-.27,6.66a4.476,4.476,0,0,1-.97.17l.29-7.129a2,2,0,0,1,.95.3" fill="#dddfdf"/>
              <path id="Path_4591" data-name="Path 4591" d="M95.77,343.36l-.13,3.14a1.953,1.953,0,0,0-.49.33v1.8a1.186,1.186,0,0,0,.38.36l-.97,23.85-11.52,3.14.22-5.21s2.03-.69,4.29-1.45a2.757,2.757,0,0,0,.52.79v1.39h1.22l.05-1.9a2.509,2.509,0,0,1-.25-.79c1.26-.41,2.46-.8,3.3-1.05.72-.21.99-.49.98-.72-.01-.29-.46-.51-.98-.43-.72.12-1.88.32-3.14.55a3.894,3.894,0,0,1,.27-.7v-2.23H88.07v2.36a3.24,3.24,0,0,0-.51.87c-2.19.41-4.19.79-4.19.79l.62-14.52c.22-.06.35-.11.35-.11h1.84l-.05-1.71-.15-.22H84.33a1.231,1.231,0,0,0-.25-.17l.19-4.47Z" fill="#bc2e08"/>
              <path id="Path_4592" data-name="Path 4592" d="M95.74,392.919l-.12,2.811-.97.21.12-2.85a4.66,4.66,0,0,0,.97-.171" fill="#999"/>
              <path id="Path_4593" data-name="Path 4593" d="M95.64,346.5l-.11,2.49a1.186,1.186,0,0,1-.38-.36v-1.8a1.953,1.953,0,0,1,.49-.33" fill="#dddfdf"/>
              <path id="Path_4594" data-name="Path 4594" d="M95.62,395.73l-.08,1.97-.97.21.08-1.97Z" fill="#999"/>
              <path id="Path_4595" data-name="Path 4595" d="M95.6,372.549l-.06,1.54-1.05.3.07-1.55Z" fill="#c2c2c2"/>
              <path id="Path_4596" data-name="Path 4596" d="M95.54,374.09l-.23,5.79-1.05.25.23-5.74Z" fill="#c2c2c2"/>
              <path id="Path_4597" data-name="Path 4597" d="M95.54,397.7l-.02.47a1,1,0,0,1-1,.961l.05-1.221Z" fill="#999"/>
              <path id="Path_4598" data-name="Path 4598" d="M95.31,379.88l-.04.83-1.05.25.04-.83Z" fill="#c2c2c2"/>
              <path id="Path_4599" data-name="Path 4599" d="M95.27,380.71l-.21,5.25a1.879,1.879,0,0,0-1.05.24l.21-5.24Z" fill="#c2c2c2"/>
              <path id="Path_4600" data-name="Path 4600" d="M95.06,385.96l-.29,7.13a1.754,1.754,0,0,1-1.04-.19l.28-6.7a1.879,1.879,0,0,1,1.05-.24" fill="#f8f8f8"/>
              <path id="Path_4601" data-name="Path 4601" d="M94.77,393.09l-.12,2.85-1.05.24.13-3.28a1.77,1.77,0,0,0,1.04.19" fill="#c2c2c2"/>
              <path id="Path_4602" data-name="Path 4602" d="M94.65,395.94l-.08,1.97-1.05.23v-.05l.08-1.91Z" fill="#c2c2c2"/>
              <path id="Path_4603" data-name="Path 4603" d="M94.57,397.909l-.05,1.221h-.04a1,1,0,0,1-.96-.991Z" fill="#c2c2c2"/>
              <path id="Path_4604" data-name="Path 4604" d="M94.56,372.84l-.07,1.55L82.97,377.6l.07-1.62Z" fill="#911f00"/>
              <path id="Path_4605" data-name="Path 4605" d="M94.49,374.389l-.23,5.74-11.51,2.8.22-5.33Z" fill="#af2205"/>
              <path id="Path_4606" data-name="Path 4606" d="M94.26,380.13l-.04.83-11.51,2.79.04-.82Z" fill="#9b1d00"/>
              <path id="Path_4607" data-name="Path 4607" d="M82.71,383.75l11.51-2.79-.21,5.24a1.551,1.551,0,0,0-.37.27l-.27,6.09a1.066,1.066,0,0,0,.36.34l-.13,3.28-11.52,2.56.11-2.58c.23-.07.38-.13.38-.13H84.4l.22-5.84-.15-.23H82.81a2.334,2.334,0,0,0-.35-.23Z" fill="#bc2e08"/>
              <path id="Path_4608" data-name="Path 4608" d="M94.01,386.2l-.28,6.7a1.057,1.057,0,0,1-.36-.34l.27-6.09a1.552,1.552,0,0,1,.37-.27" fill="#dddfdf"/>
              <path id="Path_4609" data-name="Path 4609" d="M93.6,396.18l-.08,1.91v.05l-11.53,2.51.09-1.91Z" fill="#911f00"/>
              <path id="Path_4610" data-name="Path 4610" d="M93.37,367.04c.01.23-.26.51-.98.72-.84.25-2.04.64-3.3,1.05a3.071,3.071,0,0,1-.02-.74Z" fill="#999"/>
              <path id="Path_4611" data-name="Path 4611" d="M93.37,367.04l-4.3,1.03a4.44,4.44,0,0,1,.18-.91c1.26-.23,2.42-.43,3.14-.55.52-.08.97.14.98.43" fill="#c2c2c2"/>
              <path id="Path_4612" data-name="Path 4612" d="M89.52,364.23v2.23a3.894,3.894,0,0,0-.27.7c-.16.03-.33.06-.5.09a1.956,1.956,0,0,1,.42-.79v-2.23Z" fill="#999"/>
              <path id="Path_4613" data-name="Path 4613" d="M89.34,369.6l-.05,1.9h-.4v-1.72a5.688,5.688,0,0,1-.2-.84c.13-.04.26-.08.4-.13a2.509,2.509,0,0,0,.25.79" fill="#999"/>
              <path id="Path_4614" data-name="Path 4614" d="M88.75,367.25c.17-.03.34-.06.5-.09a4.44,4.44,0,0,0-.18.91l-.44.11a3.3,3.3,0,0,1,.12-.93" fill="#999"/>
              <path id="Path_4615" data-name="Path 4615" d="M89.17,364.23v2.23l-1.1.13v-2.36Z" fill="#dddfdf"/>
              <path id="Path_4616" data-name="Path 4616" d="M88.07,366.59l1.1-.13a1.956,1.956,0,0,0-.42.79c-.39.07-.79.15-1.19.21a3.24,3.24,0,0,1,.51-.87" fill="#c2c2c2"/>
              <path id="Path_4617" data-name="Path 4617" d="M89.07,368.069a3.078,3.078,0,0,0,.02.741c-.14.049-.27.09-.4.13a5.874,5.874,0,0,1-.06-.76Z" fill="#999"/>
              <path id="Path_4618" data-name="Path 4618" d="M88.89,369.87v1.63h-.82v-1.39Z" fill="#dddfdf"/>
              <path id="Path_4619" data-name="Path 4619" d="M88.89,369.779v.091l-.82.239a2.758,2.758,0,0,1-.52-.79c.38-.12.76-.24,1.14-.38a5.646,5.646,0,0,0,.2.84" fill="#c2c2c2"/>
              <path id="Path_4620" data-name="Path 4620" d="M88.65,292.159l.14.22-.21,5.85h-.17Z" fill="#999"/>
              <path id="Path_4621" data-name="Path 4621" d="M88.75,367.25a3.3,3.3,0,0,0-.12.93,5.859,5.859,0,0,0,.06.76c-.38.14-.76.26-1.14.38a2.176,2.176,0,0,1-.19-.9,2.56,2.56,0,0,1,.2-.96c.4-.06.8-.14,1.19-.21" fill="#dddfdf"/>
              <path id="Path_4622" data-name="Path 4622" d="M88.65,292.159l-.24,6.07H86.74l.25-6.07Z" fill="#c2c2c2"/>
              <path id="Path_4623" data-name="Path 4623" d="M87.56,367.46a2.56,2.56,0,0,0-.2.96l-4.04,1.04.05-1.21s2-.38,4.19-.79" fill="#c2c2c2"/>
              <path id="Path_4624" data-name="Path 4624" d="M87.36,368.42a2.176,2.176,0,0,0,.19.9c-2.26.76-4.29,1.45-4.29,1.45l.06-1.31Z" fill="#999"/>
              <path id="Path_4625" data-name="Path 4625" d="M86.99,292.159l-.25,6.07a3.646,3.646,0,0,1-.4.14l.27-6.45a3.36,3.36,0,0,1,.38.24" fill="#dddfdf"/>
              <path id="Path_4626" data-name="Path 4626" d="M85.85,286.3a.982.982,0,0,1,.96,1l-.02.421-1.02.49Z" fill="#999"/>
              <path id="Path_4627" data-name="Path 4627" d="M86.79,287.719l-.06,1.46-1.02.5.06-1.47Z" fill="#999"/>
              <path id="Path_4628" data-name="Path 4628" d="M86.73,289.179l-.12,2.73a2.264,2.264,0,0,0-.98-.28l.08-1.95Z" fill="#999"/>
              <path id="Path_4629" data-name="Path 4629" d="M86.61,291.909v.01l-.27,6.45a5.217,5.217,0,0,1-1.01.16l.3-6.9a2.264,2.264,0,0,1,.98.28" fill="#dddfdf"/>
              <path id="Path_4630" data-name="Path 4630" d="M86.34,298.37l-.24,5.62-1.02.44.25-5.9a5.219,5.219,0,0,0,1.01-.16" fill="#999"/>
              <path id="Path_4631" data-name="Path 4631" d="M86.13,351.909l.05,1.71h-.16l-.04-1.93Z" fill="#999"/>
              <path id="Path_4632" data-name="Path 4632" d="M86.1,303.99l-.07,1.66-1.02.44.07-1.66Z" fill="#999"/>
              <path id="Path_4633" data-name="Path 4633" d="M86.03,305.65l-.24,5.65-1.01.41.23-5.62Z" fill="#999"/>
              <path id="Path_4634" data-name="Path 4634" d="M85.98,351.69l.04,1.93H84.34l-.01-1.93Z" fill="#c2c2c2"/>
              <path id="Path_4635" data-name="Path 4635" d="M85.85,286.3l-.08,1.911-1.02.48.06-1.46a.976.976,0,0,1,1.04-.931" fill="#c2c2c2"/>
              <path id="Path_4636" data-name="Path 4636" d="M85.79,311.3l-1.2,28.24-1,.34,1.19-28.17Z" fill="#999"/>
              <path id="Path_4637" data-name="Path 4637" d="M85.77,288.21l-.06,1.47-1.03.5.07-1.49Z" fill="#c2c2c2"/>
              <path id="Path_4638" data-name="Path 4638" d="M85.71,289.679l-.08,1.95a1.936,1.936,0,0,0-1.02.26l.07-1.71Z" fill="#c2c2c2"/>
              <path id="Path_4639" data-name="Path 4639" d="M85.63,291.63l-.3,6.9a1.826,1.826,0,0,1-.99-.21l.27-6.43a1.936,1.936,0,0,1,1.02-.26" fill="#f8f8f8"/>
              <path id="Path_4640" data-name="Path 4640" d="M85.33,298.529l-.25,5.9-1.02.429.28-6.54a1.817,1.817,0,0,0,.99.21" fill="#c2c2c2"/>
              <path id="Path_4641" data-name="Path 4641" d="M85.08,304.429l-.07,1.66-1.02.45.07-1.68Z" fill="#c2c2c2"/>
              <path id="Path_4642" data-name="Path 4642" d="M85.01,306.09l-.23,5.62-1.03.42.24-5.59Z" fill="#c2c2c2"/>
              <path id="Path_4643" data-name="Path 4643" d="M84.78,311.71l-1.19,28.17-1.03.35,1.19-28.1Z" fill="#c2c2c2"/>
              <path id="Path_4644" data-name="Path 4644" d="M84.75,288.69l-.07,1.49-1.36.66-.98.48-4.69,2.28.08-1.59Z" fill="#911f00"/>
              <path id="Path_4645" data-name="Path 4645" d="M84.68,290.179l-.07,1.71a1.908,1.908,0,0,0-.35.24L84,298a1.035,1.035,0,0,0,.34.32l-.28,6.54-1.38.59.64-14.61Z" fill="#bc2e08"/>
              <path id="Path_4646" data-name="Path 4646" d="M84.47,389.96l.15.23-.22,5.84h-.16Z" fill="#999"/>
              <path id="Path_4647" data-name="Path 4647" d="M84.61,291.889l-.27,6.43A1.035,1.035,0,0,1,84,298l.26-5.87a1.908,1.908,0,0,1,.35-.24" fill="#dddfdf"/>
              <path id="Path_4648" data-name="Path 4648" d="M84.59,339.54l-.07,1.66-1,.35.07-1.67Z" fill="#999"/>
              <path id="Path_4649" data-name="Path 4649" d="M83.52,341.549l1-.35-.25,5.85-.99.32Z" fill="#999"/>
              <path id="Path_4650" data-name="Path 4650" d="M84.47,389.96l-.23,6.07H82.57l.24-6.07Z" fill="#c2c2c2"/>
              <path id="Path_4651" data-name="Path 4651" d="M84.33,351.69l.01,1.93s-.13.05-.35.11l.09-2.21a1.231,1.231,0,0,1,.25.17" fill="#dddfdf"/>
              <path id="Path_4652" data-name="Path 4652" d="M84.27,347.049l-.19,4.471a2.161,2.161,0,0,0-.96-.35l.16-3.8Z" fill="#999"/>
              <path id="Path_4653" data-name="Path 4653" d="M84.08,351.52l-.09,2.21a4.636,4.636,0,0,1-.99.18l.12-2.74a2.173,2.173,0,0,1,.96.35" fill="#dddfdf"/>
              <path id="Path_4654" data-name="Path 4654" d="M84.06,304.86l-.07,1.68-1.39.6.08-1.69Z" fill="#911f00"/>
              <path id="Path_4655" data-name="Path 4655" d="M83.99,306.54l-.24,5.59-1.39.57.24-5.56Z" fill="#af2205"/>
              <path id="Path_4656" data-name="Path 4656" d="M83.99,353.73l-.62,14.52a1.53,1.53,0,0,0-.58,1.35,2.4,2.4,0,0,0,.47,1.17l-.22,5.21-.98.26L83,353.91a4.636,4.636,0,0,0,.99-.18" fill="#999"/>
              <path id="Path_4657" data-name="Path 4657" d="M83.75,312.13l-1.19,28.1-1.43.48,1.23-28.01Z" fill="#bc2e08"/>
              <path id="Path_4658" data-name="Path 4658" d="M83.59,339.88l-.07,1.67-1.03.35.07-1.67Z" fill="#c2c2c2"/>
              <path id="Path_4659" data-name="Path 4659" d="M83.52,341.549l-.24,5.82-1.04.33.25-5.8Z" fill="#c2c2c2"/>
              <path id="Path_4660" data-name="Path 4660" d="M83.37,368.25l-.05,1.21-.53.14a1.53,1.53,0,0,1,.58-1.35" fill="#c2c2c2"/>
              <path id="Path_4661" data-name="Path 4661" d="M83.32,290.84l-.64,14.61-1,.43.66-14.56Z" fill="#661300"/>
              <path id="Path_4662" data-name="Path 4662" d="M83.32,369.46l-.06,1.31a2.4,2.4,0,0,1-.47-1.17Z" fill="#999"/>
              <path id="Path_4663" data-name="Path 4663" d="M83.28,347.37l-.16,3.8a1.9,1.9,0,0,0-1.03.16l.15-3.63Z" fill="#c2c2c2"/>
              <path id="Path_4664" data-name="Path 4664" d="M83.12,351.17,83,353.91a1.828,1.828,0,0,1-1.02-.17l.11-2.41a1.9,1.9,0,0,1,1.03-.16" fill="#f8f8f8"/>
              <path id="Path_4665" data-name="Path 4665" d="M83.04,375.98l-.07,1.62-.97.27.06-1.63Z" fill="#999"/>
              <path id="Path_4666" data-name="Path 4666" d="M83,353.909l-.94,22.33-1.04.29.96-22.79a1.828,1.828,0,0,0,1.02.17" fill="#c2c2c2"/>
              <path id="Path_4667" data-name="Path 4667" d="M82.97,377.6l-.22,5.33-.98.24.23-5.3Z" fill="#999"/>
              <path id="Path_4668" data-name="Path 4668" d="M82.81,389.96l-.24,6.07s-.15.06-.38.13l.27-6.43a2.334,2.334,0,0,1,.35.23" fill="#dddfdf"/>
              <path id="Path_4669" data-name="Path 4669" d="M82.75,382.929l-.04.82-.97.24.03-.82Z" fill="#999"/>
              <path id="Path_4670" data-name="Path 4670" d="M82.71,383.75l-.25,5.98a1.988,1.988,0,0,0-.95-.29l.23-5.45Z" fill="#999"/>
              <path id="Path_4671" data-name="Path 4671" d="M82.68,305.449l-.08,1.69-.99.44.07-1.7Z" fill="#661300"/>
              <path id="Path_4672" data-name="Path 4672" d="M82.6,307.139l-.24,5.56-1,.41.25-5.53Z" fill="#661300"/>
              <path id="Path_4673" data-name="Path 4673" d="M82.56,340.23l-.07,1.67-1.43.49.07-1.68Z" fill="#911f00"/>
              <path id="Path_4674" data-name="Path 4674" d="M82.49,341.9l-.25,5.8-1.43.46.25-5.77Z" fill="#af2205"/>
              <path id="Path_4675" data-name="Path 4675" d="M82.46,389.73l-.27,6.43a5.434,5.434,0,0,1-.97.16l.29-6.88a1.988,1.988,0,0,1,.95.29" fill="#dddfdf"/>
              <path id="Path_4676" data-name="Path 4676" d="M82.36,312.7l-1.23,28.01-1.03.35,1.26-27.95Z" fill="#661300"/>
              <path id="Path_4677" data-name="Path 4677" d="M82.34,291.319l-.66,14.561-4.76,2.03.32-6.23a1.747,1.747,0,0,0,.36-.12l1.53.019h.16l.26-5.78-.12-.22-1.52-.019a3.159,3.159,0,0,0-.35-.241l.09-1.719Z" fill="#bc2e08"/>
              <path id="Path_4678" data-name="Path 4678" d="M82.24,347.7l-.15,3.63a2.114,2.114,0,0,0-.49.32v1.75a1.125,1.125,0,0,0,.38.34l-.96,22.79-1.47.4,1.26-28.77Z" fill="#bc2e08"/>
              <path id="Path_4679" data-name="Path 4679" d="M82.19,396.159l-.11,2.58-.97.21.11-2.63a5.258,5.258,0,0,0,.97-.16" fill="#999"/>
              <path id="Path_4680" data-name="Path 4680" d="M82.09,351.33l-.11,2.41a1.125,1.125,0,0,1-.38-.34v-1.75a2.115,2.115,0,0,1,.49-.32" fill="#dddfdf"/>
              <path id="Path_4681" data-name="Path 4681" d="M82.08,398.74l-.09,1.91-.96.21.08-1.91Z" fill="#999"/>
              <path id="Path_4682" data-name="Path 4682" d="M81.02,376.529l1.04-.29L82,377.87l-1.05.29Z" fill="#c2c2c2"/>
              <path id="Path_4683" data-name="Path 4683" d="M82,377.87l-.23,5.3-1.05.25.23-5.26Z" fill="#c2c2c2"/>
              <path id="Path_4684" data-name="Path 4684" d="M81.99,400.65l-.02.57a.977.977,0,0,1-1,.93l.06-1.29Z" fill="#999"/>
              <path id="Path_4685" data-name="Path 4685" d="M81.77,383.17l-.03.82-1.05.25.03-.82Z" fill="#c2c2c2"/>
              <path id="Path_4686" data-name="Path 4686" d="M81.74,383.99l-.23,5.45a1.8,1.8,0,0,0-1.05.24l.23-5.44Z" fill="#c2c2c2"/>
              <path id="Path_4687" data-name="Path 4687" d="M81.68,305.88l-.07,1.7-4.78,2.08.09-1.75Z" fill="#911f00"/>
              <path id="Path_4688" data-name="Path 4688" d="M81.61,307.58l-.25,5.53-4.81,1.97.28-5.42Z" fill="#af2205"/>
              <path id="Path_4689" data-name="Path 4689" d="M81.51,389.44l-.29,6.88a1.83,1.83,0,0,1-1.04-.18l.28-6.46a1.8,1.8,0,0,1,1.05-.24" fill="#f8f8f8"/>
              <path id="Path_4690" data-name="Path 4690" d="M81.36,313.11,80.1,341.06l-4.99,1.68,1.44-27.66Z" fill="#bc2e08"/>
              <path id="Path_4691" data-name="Path 4691" d="M81.22,396.319l-.11,2.63-1.06.24.13-3.05a1.821,1.821,0,0,0,1.04.18" fill="#c2c2c2"/>
              <path id="Path_4692" data-name="Path 4692" d="M81.13,340.71l-.07,1.68-1.04.36.08-1.69Z" fill="#661300"/>
              <path id="Path_4693" data-name="Path 4693" d="M81.11,398.949l-.08,1.91-1.06.231.08-1.9Z" fill="#c2c2c2"/>
              <path id="Path_4694" data-name="Path 4694" d="M81.06,342.389l-.25,5.77-1.05.34.26-5.75Z" fill="#661300"/>
              <path id="Path_4695" data-name="Path 4695" d="M81.03,400.86l-.06,1.29h-.04a.981.981,0,0,1-.96-1v-.06Z" fill="#c2c2c2"/>
              <path id="Path_4696" data-name="Path 4696" d="M81.02,376.529l-.07,1.63-1.47.41.07-1.64Z" fill="#911f00"/>
              <path id="Path_4697" data-name="Path 4697" d="M80.95,378.159l-.23,5.26-1.47.36.23-5.21Z" fill="#af2205"/>
              <path id="Path_4698" data-name="Path 4698" d="M80.81,348.159l-1.26,28.77-1.08.3,1.29-28.73Z" fill="#661300"/>
              <path id="Path_4699" data-name="Path 4699" d="M80.72,383.42l-.03.82-1.48.36.04-.82Z" fill="#9b1d00"/>
              <path id="Path_4700" data-name="Path 4700" d="M80.69,384.24l-.23,5.44a1.256,1.256,0,0,0-.37.25l-.27,5.88a1.129,1.129,0,0,0,.36.33l-.13,3.05-1.49.33.65-14.92Z" fill="#bc2e08"/>
              <path id="Path_4701" data-name="Path 4701" d="M80.46,389.679l-.28,6.46a1.129,1.129,0,0,1-.36-.33l.27-5.88a1.256,1.256,0,0,1,.37-.25" fill="#dddfdf"/>
              <path id="Path_4702" data-name="Path 4702" d="M80.1,341.06l-.08,1.69-5,1.71.09-1.72Z" fill="#911f00"/>
              <path id="Path_4703" data-name="Path 4703" d="M80.05,399.19l-.08,1.9-7.96,1.73.09-1.86,5.35-1.19,1.11-.25Z" fill="#911f00"/>
              <path id="Path_4704" data-name="Path 4704" d="M80.02,342.75l-.26,5.75-5.03,1.61.29-5.65Z" fill="#af2205"/>
              <path id="Path_4705" data-name="Path 4705" d="M79.76,348.5l-1.29,28.73-5.21,1.42,1.15-22.26c.2-.06.32-.11.32-.11l1.54.02h.14l-.02-1.69-.14-.22-1.51-.02a1.527,1.527,0,0,0-.22-.16l.21-4.1Z" fill="#bc2e08"/>
              <path id="Path_4706" data-name="Path 4706" d="M79.43,295.58l.12.22-.26,5.78h-.16Z" fill="#999"/>
              <path id="Path_4707" data-name="Path 4707" d="M79.55,376.929l-.07,1.64-1.09.3.08-1.64Z" fill="#661300"/>
              <path id="Path_4708" data-name="Path 4708" d="M79.48,378.569l-.23,5.21-1.09.26.23-5.17Z" fill="#661300"/>
              <path id="Path_4709" data-name="Path 4709" d="M79.43,295.58l-.3,6-1.53-.02.31-6Z" fill="#c2c2c2"/>
              <path id="Path_4710" data-name="Path 4710" d="M79.25,383.779l-.04.82-1.09.27.04-.83Z" fill="#440800"/>
              <path id="Path_4711" data-name="Path 4711" d="M79.21,384.6l-.65,14.92-1.11.25.67-14.9Z" fill="#661300"/>
              <path id="Path_4712" data-name="Path 4712" d="M78.47,377.23l-.08,1.64-5.22,1.45.09-1.67Z" fill="#911f00"/>
              <path id="Path_4713" data-name="Path 4713" d="M78.39,378.87l-.23,5.17-5.25,1.27.26-4.99Z" fill="#af2205"/>
              <path id="Path_4714" data-name="Path 4714" d="M78.16,384.04l-.04.83-5.25,1.27.04-.83Z" fill="#9b1d00"/>
              <path id="Path_4715" data-name="Path 4715" d="M78.12,384.87l-.67,14.9-5.35,1.189.14-2.649c.21-.07.35-.12.35-.12l1.53.019h.15l.27-5.769-.14-.231-1.51-.019a3.331,3.331,0,0,0-.32-.24l.3-5.81Z" fill="#bc2e08"/>
              <path id="Path_4716" data-name="Path 4716" d="M77.91,295.56l-.31,6a1.744,1.744,0,0,1-.361.12l.321-6.36a3.143,3.143,0,0,1,.35.24" fill="#dddfdf"/>
              <path id="Path_4717" data-name="Path 4717" d="M76.94,289.75a.942.942,0,0,1,.86,1.01l-.07,1.25-.93.44Z" fill="#999"/>
              <path id="Path_4718" data-name="Path 4718" d="M77.73,292.009l-.08,1.59-.93.46.08-1.61Z" fill="#999"/>
              <path id="Path_4719" data-name="Path 4719" d="M77.65,293.6l-.09,1.72a1.714,1.714,0,0,0-.89-.3l.05-.96Z" fill="#999"/>
              <path id="Path_4720" data-name="Path 4720" d="M77.56,295.319l-.32,6.361a3.453,3.453,0,0,1-.93.16l.36-6.82a1.712,1.712,0,0,1,.89.3" fill="#dddfdf"/>
              <path id="Path_4721" data-name="Path 4721" d="M77.24,301.679l-.32,6.23-.94.4.33-6.47a3.446,3.446,0,0,0,.93-.16" fill="#999"/>
              <path id="Path_4722" data-name="Path 4722" d="M76.94,289.75l-.14,2.7-.95.45.12-2.24a.933.933,0,0,1,.97-.91" fill="#c2c2c2"/>
              <path id="Path_4723" data-name="Path 4723" d="M76.92,307.909l-.09,1.75-.94.41.09-1.76Z" fill="#999"/>
              <path id="Path_4724" data-name="Path 4724" d="M76.83,309.659l-.28,5.42-.94.38.28-5.39Z" fill="#999"/>
              <path id="Path_4725" data-name="Path 4725" d="M76.8,292.449l-.08,1.61-.95.46.08-1.62Z" fill="#c2c2c2"/>
              <path id="Path_4726" data-name="Path 4726" d="M76.72,294.06l-.05.96a1.779,1.779,0,0,0-.94.25l.04-.75Z" fill="#c2c2c2"/>
              <path id="Path_4727" data-name="Path 4727" d="M76.67,295.02l-.36,6.82a1.529,1.529,0,0,1-.9-.22v-.01l.32-6.34a1.779,1.779,0,0,1,.94-.25" fill="#f8f8f8"/>
              <path id="Path_4728" data-name="Path 4728" d="M76.55,315.08l-1.44,27.66-.92.31,1.42-27.59Z" fill="#999"/>
              <path id="Path_4729" data-name="Path 4729" d="M76.39,354.61l.02,1.69h-.14l-.02-1.91Z" fill="#999"/>
              <path id="Path_4730" data-name="Path 4730" d="M76.31,301.84l-.33,6.47-.94.4.37-7.09a1.529,1.529,0,0,0,.9.22" fill="#c2c2c2"/>
              <path id="Path_4731" data-name="Path 4731" d="M76.25,354.389l.02,1.91-1.54-.02.01-1.91Z" fill="#c2c2c2"/>
              <path id="Path_4732" data-name="Path 4732" d="M75.98,308.31l-.09,1.76-.94.4.09-1.76Z" fill="#c2c2c2"/>
              <path id="Path_4733" data-name="Path 4733" d="M75.89,310.069l-.28,5.39-.94.38.28-5.37Z" fill="#c2c2c2"/>
              <path id="Path_4734" data-name="Path 4734" d="M75.85,292.9l-.08,1.62-10.68,5.19.1-1.76Z" fill="#911f00"/>
              <path id="Path_4735" data-name="Path 4735" d="M75.77,294.52l-.04.75a1.545,1.545,0,0,0-.32.23l-.31,5.8a.919.919,0,0,0,.31.31v.01l-.37,7.09-10.66,4.55.32-6.1a3.773,3.773,0,0,0,.37-.12l1.52.02h.16l.26-5.59-.13-.22-1.51-.02a2.984,2.984,0,0,0-.35-.23l.07-1.29Z" fill="#bc2e08"/>
              <path id="Path_4736" data-name="Path 4736" d="M75.73,295.27l-.32,6.34a.919.919,0,0,1-.31-.31l.31-5.8a1.545,1.545,0,0,1,.32-.23" fill="#dddfdf"/>
              <path id="Path_4737" data-name="Path 4737" d="M75.61,315.46l-1.42,27.59-.94.32,1.42-27.53Z" fill="#c2c2c2"/>
              <path id="Path_4738" data-name="Path 4738" d="M75.11,342.74l-.09,1.72-.91.32.08-1.73Z" fill="#999"/>
              <path id="Path_4739" data-name="Path 4739" d="M75.04,308.71l-.09,1.76-10.67,4.64.1-1.85Z" fill="#911f00"/>
              <path id="Path_4740" data-name="Path 4740" d="M75.02,344.46l-.29,5.65-.91.3.29-5.63Z" fill="#999"/>
              <path id="Path_4741" data-name="Path 4741" d="M74.95,310.469l-.28,5.37-10.66,4.35.27-5.08Z" fill="#af2205"/>
              <path id="Path_4742" data-name="Path 4742" d="M74.74,354.37l-.01,1.91s-.12.05-.319.11l.109-2.18a1.527,1.527,0,0,1,.22.16" fill="#dddfdf"/>
              <path id="Path_4743" data-name="Path 4743" d="M74.73,350.11l-.21,4.1a1.892,1.892,0,0,0-.88-.36l.18-3.44Z" fill="#999"/>
              <path id="Path_4744" data-name="Path 4744" d="M74.67,315.84l-1.42,27.53-10.66,3.6,1.42-26.78Z" fill="#bc2e08"/>
              <path id="Path_4745" data-name="Path 4745" d="M74.4,392.209l.14.23-.27,5.77h-.15Z" fill="#999"/>
              <path id="Path_4746" data-name="Path 4746" d="M74.52,354.21l-.11,2.18a3.551,3.551,0,0,1-.91.16l.14-2.7a1.892,1.892,0,0,1,.88.36" fill="#dddfdf"/>
              <path id="Path_4747" data-name="Path 4747" d="M74.41,356.389l-1.15,22.26-.91.25.33-6.4.82-15.95a3.551,3.551,0,0,0,.91-.16" fill="#999"/>
              <path id="Path_4748" data-name="Path 4748" d="M74.4,392.209l-.28,6-1.53-.02.3-6Z" fill="#c2c2c2"/>
              <path id="Path_4749" data-name="Path 4749" d="M74.19,343.049l-.08,1.73-.95.32.09-1.73Z" fill="#c2c2c2"/>
              <path id="Path_4750" data-name="Path 4750" d="M74.11,344.779l-.29,5.63-.95.3.29-5.61Z" fill="#c2c2c2"/>
              <path id="Path_4751" data-name="Path 4751" d="M73.82,350.409l-.18,3.44a1.538,1.538,0,0,0-.94.15l.17-3.29Z" fill="#c2c2c2"/>
              <path id="Path_4752" data-name="Path 4752" d="M73.64,353.85l-.14,2.7a1.53,1.53,0,0,1-.93-.17v-.01L72.7,354a1.538,1.538,0,0,1,.94-.15" fill="#f8f8f8"/>
              <path id="Path_4753" data-name="Path 4753" d="M73.5,356.549,72.68,372.5l-.22-.01a.375.375,0,0,0-.01-.11,1.8,1.8,0,0,0-.29-.91s-.14.05-.37.14l.78-15.23a1.523,1.523,0,0,0,.93.169" fill="#c2c2c2"/>
              <path id="Path_4754" data-name="Path 4754" d="M73.26,378.65l-.09,1.67-.9.25.08-1.67Z" fill="#999"/>
              <path id="Path_4755" data-name="Path 4755" d="M73.25,343.37l-.09,1.73-10.67,3.66.1-1.79Z" fill="#911f00"/>
              <path id="Path_4756" data-name="Path 4756" d="M73.17,380.319l-.26,4.99-.9.22.26-4.96Z" fill="#999"/>
              <path id="Path_4757" data-name="Path 4757" d="M73.16,345.1l-.29,5.61-10.66,3.43.279-5.38Z" fill="#af2205"/>
              <path id="Path_4758" data-name="Path 4758" d="M72.01,385.529l.9-.22-.04.83-.9.22Z" fill="#999"/>
              <path id="Path_4759" data-name="Path 4759" d="M72.89,392.19l-.3,6s-.14.05-.35.12l.33-6.36a3.331,3.331,0,0,1,.32.24" fill="#dddfdf"/>
              <path id="Path_4760" data-name="Path 4760" d="M72.87,386.139l-.3,5.81a1.8,1.8,0,0,0-.87-.29l.27-5.3Z" fill="#999"/>
              <path id="Path_4761" data-name="Path 4761" d="M72.87,350.71,72.7,354a1.65,1.65,0,0,0-.45.31l-.02,1.72a.924.924,0,0,0,.34.34v.01l-.78,15.23c-.53.21-1.53.6-2.59,1a1.569,1.569,0,0,1,.1-.28l-.05-1.47-.26-.01-.61.16.01,1.12a2.652,2.652,0,0,0-.38.94c-.7.25-1.35.49-1.82.66s-.65.36-.65.55c0,.23.29.4.63.34.45-.08,1.2-.23,2-.39a1.523,1.523,0,0,0,.33.44l.02,1.65.72.17h.22l-.01-1.73a4.046,4.046,0,0,1-.24-.74c1.01-.21,1.97-.41,2.48-.52l-.29,5.66-10.68,2.91,1.17-21.96c.2-.06.32-.1.32-.1l1.54.02h.15l-.03-1.64-.13-.21-1.51-.02A1.381,1.381,0,0,0,62,358l.21-3.86Z" fill="#bc2e08"/>
              <path id="Path_4762" data-name="Path 4762" d="M72.7,354l-.13,2.37a.924.924,0,0,1-.34-.34l.02-1.72a1.65,1.65,0,0,1,.45-.31" fill="#dddfdf"/>
              <path id="Path_4763" data-name="Path 4763" d="M72.68,372.5l-.33,6.4-.95.26.29-5.66.38-.08a1.29,1.29,0,0,0,.39-.93Z" fill="#c2c2c2"/>
              <path id="Path_4764" data-name="Path 4764" d="M72.57,391.949l-.33,6.36a4.683,4.683,0,0,1-.89.15l.35-6.8a1.8,1.8,0,0,1,.87.29" fill="#dddfdf"/>
              <path id="Path_4765" data-name="Path 4765" d="M72.45,372.38a.375.375,0,0,1,.01.11,1.29,1.29,0,0,1-.39.93l-.38.08.05-.92Z" fill="#999"/>
              <path id="Path_4766" data-name="Path 4766" d="M72.45,372.38l-.71.2.05-.97c.23-.09.37-.14.37-.14a1.8,1.8,0,0,1,.29.91" fill="#c2c2c2"/>
              <path id="Path_4767" data-name="Path 4767" d="M72.35,378.9l-.08,1.67-.96.27.09-1.68Z" fill="#c2c2c2"/>
              <path id="Path_4768" data-name="Path 4768" d="M71.31,380.84l.96-.27-.26,4.96-.95.24Z" fill="#c2c2c2"/>
              <path id="Path_4769" data-name="Path 4769" d="M72.24,398.31l-.14,2.649-.89.2.14-2.7a4.611,4.611,0,0,0,.89-.149" fill="#999"/>
              <path id="Path_4770" data-name="Path 4770" d="M72.1,400.959l-.09,1.86-.9.2.1-1.861Z" fill="#999"/>
              <path id="Path_4771" data-name="Path 4771" d="M72.01,385.529l-.04.83-.96.23.05-.82Z" fill="#c2c2c2"/>
              <path id="Path_4772" data-name="Path 4772" d="M72.01,402.819l-.03.49a.93.93,0,0,1-.93.91l.06-1.2Z" fill="#999"/>
              <path id="Path_4773" data-name="Path 4773" d="M71.97,386.36l-.27,5.3a1.613,1.613,0,0,0-.96.21l.27-5.28Z" fill="#c2c2c2"/>
              <path id="Path_4774" data-name="Path 4774" d="M71.79,371.61l-.05.97-2.61.71a2.283,2.283,0,0,1,.07-.68c1.06-.4,2.06-.79,2.59-1" fill="#c2c2c2"/>
              <path id="Path_4775" data-name="Path 4775" d="M71.74,372.58l-.05.92c-.509.11-1.47.31-2.48.52a3.394,3.394,0,0,1-.08-.73Z" fill="#999"/>
              <path id="Path_4776" data-name="Path 4776" d="M71.7,391.659l-.35,6.8a1.57,1.57,0,0,1-.95-.2l.34-6.391a1.614,1.614,0,0,1,.96-.21" fill="#f8f8f8"/>
              <path id="Path_4777" data-name="Path 4777" d="M71.4,379.159l-.09,1.68-10.68,2.97.09-1.74Z" fill="#911f00"/>
              <path id="Path_4778" data-name="Path 4778" d="M71.35,398.459l-.14,2.7-.97.21.16-3.109a1.563,1.563,0,0,0,.95.2" fill="#c2c2c2"/>
              <path id="Path_4779" data-name="Path 4779" d="M71.31,380.84l-.25,4.93-10.67,2.59.24-4.55Z" fill="#af2205"/>
              <path id="Path_4780" data-name="Path 4780" d="M71.21,401.159l-.1,1.86-.96.21v-.009l.09-1.851Z" fill="#c2c2c2"/>
              <path id="Path_4781" data-name="Path 4781" d="M71.11,403.02l-.06,1.2h-.03a.938.938,0,0,1-.87-.99Z" fill="#c2c2c2"/>
              <path id="Path_4782" data-name="Path 4782" d="M71.06,385.77l-.05.82-10.67,2.58.05-.81Z" fill="#9b1d00"/>
              <path id="Path_4783" data-name="Path 4783" d="M71.01,386.59l-.27,5.28a1.622,1.622,0,0,0-.34.26l-.32,5.81a.977.977,0,0,0,.32.32l-.16,3.11-10.66,2.38.16-3.07c.21-.06.35-.11.35-.11l1.53.02h.14l.28-5.59-.14-.22-1.52-.02a3.038,3.038,0,0,0-.32-.23l.28-5.36Z" fill="#bc2e08"/>
              <path id="Path_4784" data-name="Path 4784" d="M70.74,391.869l-.34,6.391a.99.99,0,0,1-.32-.321l.32-5.809a1.6,1.6,0,0,1,.34-.261" fill="#dddfdf"/>
              <path id="Path_4785" data-name="Path 4785" d="M70.24,401.369l-.09,1.851v.009L59.48,405.55v-.021l.1-1.78Z" fill="#911f00"/>
              <path id="Path_4786" data-name="Path 4786" d="M69.45,374.759l.01,1.731h-.22l-.02-1.731a1.66,1.66,0,0,1-.29-.68c.09-.019.18-.039.28-.059a4.016,4.016,0,0,0,.24.739" fill="#999"/>
              <path id="Path_4787" data-name="Path 4787" d="M69.25,370.86l.05,1.47a1.569,1.569,0,0,0-.1.28c-.11.05-.21.09-.32.13a5.34,5.34,0,0,1,.13-.7l-.02-1.19Z" fill="#999"/>
              <path id="Path_4788" data-name="Path 4788" d="M69.22,374.759l.02,1.73-.72-.17-.02-1.65Z" fill="#dddfdf"/>
              <path id="Path_4789" data-name="Path 4789" d="M69.22,374.759l-.72-.09a1.5,1.5,0,0,1-.33-.44c.24-.039.49-.1.75-.15h.01a1.66,1.66,0,0,0,.29.68" fill="#c2c2c2"/>
              <path id="Path_4790" data-name="Path 4790" d="M69.21,374.02c-.1.02-.19.04-.28.06h-.01a4.172,4.172,0,0,1-.08-.71v-.01l.29-.07a3.332,3.332,0,0,0,.08.73" fill="#999"/>
              <path id="Path_4791" data-name="Path 4791" d="M68.88,372.74c.11-.04.21-.08.32-.13a2.283,2.283,0,0,0-.07.68l-.29.07c.01-.22.02-.43.04-.62" fill="#999"/>
              <path id="Path_4792" data-name="Path 4792" d="M68.99,370.85l.02,1.19-.62.09-.01-1.12Z" fill="#dddfdf"/>
              <path id="Path_4793" data-name="Path 4793" d="M68.39,372.13l.62-.09a5.34,5.34,0,0,0-.13.7c-.29.11-.59.22-.87.33a2.652,2.652,0,0,1,.38-.94" fill="#c2c2c2"/>
              <path id="Path_4794" data-name="Path 4794" d="M68.88,372.74c-.02.19-.03.4-.04.62v.01l-.85.23a1.373,1.373,0,0,1,.02-.53c.28-.11.58-.22.87-.33" fill="#dddfdf"/>
              <path id="Path_4795" data-name="Path 4795" d="M68.84,373.37a4.172,4.172,0,0,0,.08.71c-.26.05-.51.11-.75.15a1.683,1.683,0,0,1-.18-.63Z" fill="#dddfdf"/>
              <path id="Path_4796" data-name="Path 4796" d="M67.99,373.6a1.683,1.683,0,0,0,.18.63c-.8.16-1.55.31-2,.39-.34.06-.63-.11-.63-.34Z" fill="#999"/>
              <path id="Path_4797" data-name="Path 4797" d="M68.01,373.069a1.378,1.378,0,0,0-.02.531l-2.45.68c0-.19.18-.391.65-.551s1.12-.41,1.82-.66" fill="#c2c2c2"/>
              <path id="Path_4798" data-name="Path 4798" d="M66.88,301.25l.13.22-.26,5.59h-.16Z" fill="#999"/>
              <path id="Path_4799" data-name="Path 4799" d="M66.88,301.25l-.29,5.81-1.52-.02.3-5.81Z" fill="#c2c2c2"/>
              <path id="Path_4800" data-name="Path 4800" d="M65.37,301.23l-.3,5.81a3.773,3.773,0,0,1-.37.12l.32-6.16a2.984,2.984,0,0,1,.35.23" fill="#dddfdf"/>
              <path id="Path_4801" data-name="Path 4801" d="M64.39,295.61a.935.935,0,0,1,.87.98l-.07,1.36-.94.44Z" fill="#999"/>
              <path id="Path_4802" data-name="Path 4802" d="M65.19,297.949l-.1,1.76-.93.46.09-1.78Z" fill="#999"/>
              <path id="Path_4803" data-name="Path 4803" d="M64.16,300.17l.93-.46L65.02,301a1.849,1.849,0,0,0-.89-.29Z" fill="#999"/>
              <path id="Path_4804" data-name="Path 4804" d="M65.02,301l-.32,6.16a3.478,3.478,0,0,1-.93.15l.36-6.6a1.849,1.849,0,0,1,.89.29" fill="#dddfdf"/>
              <path id="Path_4805" data-name="Path 4805" d="M64.7,307.159l-.32,6.1-.94.4.33-6.35a3.478,3.478,0,0,0,.93-.15" fill="#999"/>
              <path id="Path_4806" data-name="Path 4806" d="M64.39,295.61l-.14,2.78-.95.45.13-2.35a.907.907,0,0,1,.96-.88" fill="#c2c2c2"/>
              <path id="Path_4807" data-name="Path 4807" d="M64.38,313.259l-.1,1.85-.94.41.1-1.86Z" fill="#999"/>
              <path id="Path_4808" data-name="Path 4808" d="M64.28,315.11l-.27,5.08-.94.39.27-5.06Z" fill="#999"/>
              <path id="Path_4809" data-name="Path 4809" d="M64.25,298.389l-.09,1.78-.95.46.09-1.79Z" fill="#c2c2c2"/>
              <path id="Path_4810" data-name="Path 4810" d="M64.16,300.17l-.03.54a1.756,1.756,0,0,0-.94.24l.02-.32Z" fill="#c2c2c2"/>
              <path id="Path_4811" data-name="Path 4811" d="M64.13,300.71l-.36,6.6a1.524,1.524,0,0,1-.9-.21l.32-6.15a1.756,1.756,0,0,1,.94-.24" fill="#f8f8f8"/>
              <path id="Path_4812" data-name="Path 4812" d="M63.07,320.58l.94-.39-1.42,26.78-.92.3Z" fill="#999"/>
              <path id="Path_4813" data-name="Path 4813" d="M63.87,358.389l.03,1.64h-.15l-.01-1.85Z" fill="#999"/>
              <path id="Path_4814" data-name="Path 4814" d="M63.77,307.31l-.33,6.35-.94.4.37-6.96a1.524,1.524,0,0,0,.9.21" fill="#c2c2c2"/>
              <path id="Path_4815" data-name="Path 4815" d="M63.74,358.179l.01,1.85-1.54-.02.02-1.85Z" fill="#c2c2c2"/>
              <path id="Path_4816" data-name="Path 4816" d="M62.5,314.06l.94-.4-.1,1.86-.94.41Z" fill="#c2c2c2"/>
              <path id="Path_4817" data-name="Path 4817" d="M63.34,315.52l-.27,5.06-.94.38.27-5.03Z" fill="#c2c2c2"/>
              <path id="Path_4818" data-name="Path 4818" d="M63.3,298.84l-.09,1.79-5.9,2.87-.58.28-2.24-.69,2.24-1.14Z" fill="#911f00"/>
              <path id="Path_4819" data-name="Path 4819" d="M63.21,300.63l-.02.32a1.8,1.8,0,0,0-.32.22l-.31,5.62a1.019,1.019,0,0,0,.31.31l-.37,6.96-5.31,2.27-.46,6.83-.65-.04.87-13.89,2.61-1.38V303.5h-.5l-1.81.95.06-.95Z" fill="#bc2e08"/>
              <path id="Path_4820" data-name="Path 4820" d="M63.19,300.949l-.32,6.15a1.018,1.018,0,0,1-.31-.31l.31-5.62a1.8,1.8,0,0,1,.32-.22" fill="#dddfdf"/>
              <path id="Path_4821" data-name="Path 4821" d="M63.07,320.58l-1.4,26.69-.95.32,1.41-26.63Z" fill="#c2c2c2"/>
              <path id="Path_4822" data-name="Path 4822" d="M62.59,346.969l-.1,1.79-.91.31.09-1.8Z" fill="#999"/>
              <path id="Path_4823" data-name="Path 4823" d="M62.5,314.06l-.1,1.87-2.84,1.23-2.37-.83Z" fill="#911f00"/>
              <path id="Path_4824" data-name="Path 4824" d="M62.49,348.759l-.28,5.38-.91.29.28-5.36Z" fill="#999"/>
              <path id="Path_4825" data-name="Path 4825" d="M62.4,315.929l-.27,5.03-2.57,1.05v-4.85Z" fill="#af2205"/>
              <path id="Path_4826" data-name="Path 4826" d="M62.23,358.159l-.02,1.85s-.12.04-.32.1L62,358a1.381,1.381,0,0,1,.23.16" fill="#dddfdf"/>
              <path id="Path_4827" data-name="Path 4827" d="M62.21,354.139,62,358a1.874,1.874,0,0,0-.87-.35l.17-3.22Z" fill="#999"/>
              <path id="Path_4828" data-name="Path 4828" d="M62.13,320.96l-1.41,26.63-4.97,1.68-1.31-.08.52-8.22,2.74-1.33v-4.36l-.5-.02-1.94.94.82-13.08.65.04,2.83-1.15Z" fill="#bc2e08"/>
              <path id="Path_4829" data-name="Path 4829" d="M61.9,394.779l.14.22-.28,5.59h-.14Z" fill="#999"/>
              <path id="Path_4830" data-name="Path 4830" d="M62,358l-.11,2.11a4.044,4.044,0,0,1-.91.16l.15-2.62A1.874,1.874,0,0,1,62,358" fill="#dddfdf"/>
              <path id="Path_4831" data-name="Path 4831" d="M61.9,394.779l-.28,5.81-1.53-.02.29-5.81Z" fill="#c2c2c2"/>
              <path id="Path_4832" data-name="Path 4832" d="M61.89,360.11l-1.17,21.96-.9.24.35-6.6.81-15.44a4.044,4.044,0,0,0,.91-.16" fill="#999"/>
              <path id="Path_4833" data-name="Path 4833" d="M61.67,347.27l-.09,1.8-.95.33.09-1.81Z" fill="#c2c2c2"/>
              <path id="Path_4834" data-name="Path 4834" d="M60.63,349.4l.95-.33-.28,5.36-.96.3Z" fill="#c2c2c2"/>
              <path id="Path_4835" data-name="Path 4835" d="M61.3,354.429l-.17,3.22a1.724,1.724,0,0,0-.95.14l.16-3.06Z" fill="#c2c2c2"/>
              <path id="Path_4836" data-name="Path 4836" d="M61.13,357.65l-.15,2.62a1.689,1.689,0,0,1-.93-.17l.13-2.31a1.724,1.724,0,0,1,.95-.14" fill="#f8f8f8"/>
              <path id="Path_4837" data-name="Path 4837" d="M60.98,360.27l-.81,15.44-.22-.02a.449.449,0,0,0-.01-.11,1.733,1.733,0,0,0-.29-.88s-.14.05-.38.14l.78-14.74a1.689,1.689,0,0,0,.93.17" fill="#c2c2c2"/>
              <path id="Path_4838" data-name="Path 4838" d="M60.72,382.069l-.09,1.74-.9.25.09-1.75Z" fill="#999"/>
              <path id="Path_4839" data-name="Path 4839" d="M60.72,347.59l-.09,1.81-3.44,1.18-1.44-1.31Z" fill="#911f00"/>
              <path id="Path_4840" data-name="Path 4840" d="M59.73,384.06l.9-.25-.24,4.55-.89.21Z" fill="#999"/>
              <path id="Path_4841" data-name="Path 4841" d="M60.63,349.4l-.29,5.33-3.35,1.08.2-5.23Z" fill="#af2205"/>
              <path id="Path_4842" data-name="Path 4842" d="M60.39,388.36l-.05.81-.89.22.05-.82Z" fill="#999"/>
              <path id="Path_4843" data-name="Path 4843" d="M60.38,394.76l-.29,5.809s-.14.05-.35.111l.32-6.15a3.038,3.038,0,0,1,.32.23" fill="#dddfdf"/>
              <path id="Path_4844" data-name="Path 4844" d="M59.45,389.389l.89-.22-.28,5.36a1.758,1.758,0,0,0-.86-.28Z" fill="#999"/>
              <path id="Path_4845" data-name="Path 4845" d="M60.34,354.73l-.16,3.06a2.055,2.055,0,0,0-.45.3l-.02,1.67a1.1,1.1,0,0,0,.34.34l-.78,14.74c-.52.2-1.52.58-2.58.97a1.9,1.9,0,0,1,.1-.28l-.05-1.42h-.27l-.6.15.01,1.08a2.77,2.77,0,0,0-.39.91c-.69.25-1.34.48-1.82.64-.4.13-.59.3-.63.46l-.37-.02.31-4.99,2.77-1.03v-4.36l-.51-.08-1.96.73,1.16-18.41,1.31.08-.31,7.04,1.55-.5Z" fill="#bc2e08"/>
              <path id="Path_4846" data-name="Path 4846" d="M60.18,357.79l-.13,2.31a1.1,1.1,0,0,1-.34-.34l.02-1.67a2.055,2.055,0,0,1,.45-.3" fill="#dddfdf"/>
              <path id="Path_4847" data-name="Path 4847" d="M60.17,375.71l-.35,6.6-.96.26.32-5.9c.24-.05.37-.08.37-.08a1.2,1.2,0,0,0,.4-.9Z" fill="#c2c2c2"/>
              <path id="Path_4848" data-name="Path 4848" d="M60.06,394.529l-.32,6.151a4.611,4.611,0,0,1-.89.149l.35-6.58a1.758,1.758,0,0,1,.86.28" fill="#dddfdf"/>
              <path id="Path_4849" data-name="Path 4849" d="M59.94,375.58a.449.449,0,0,1,.01.11,1.2,1.2,0,0,1-.4.9s-.13.03-.37.08l.05-.89Z" fill="#999"/>
              <path id="Path_4850" data-name="Path 4850" d="M59.94,375.58l-.71.2.04-.94c.24-.09.38-.14.38-.14a1.733,1.733,0,0,1,.29.88" fill="#c2c2c2"/>
              <path id="Path_4851" data-name="Path 4851" d="M59.82,382.31l-.09,1.75-.96.27.09-1.76Z" fill="#c2c2c2"/>
              <path id="Path_4852" data-name="Path 4852" d="M59.74,400.68l-.16,3.069-.9.2.169-3.121a4.611,4.611,0,0,0,.89-.149" fill="#999"/>
              <path id="Path_4853" data-name="Path 4853" d="M59.73,384.06l-.23,4.51-.97.24.24-4.48Z" fill="#c2c2c2"/>
              <path id="Path_4854" data-name="Path 4854" d="M59.58,403.749l-.1,1.78v.021l-.89.2.09-1.8Z" fill="#999"/>
              <path id="Path_4855" data-name="Path 4855" d="M59.56,317.159v4.85l-2.83,1.15.46-6.83Z" fill="#9b1d00"/>
              <path id="Path_4856" data-name="Path 4856" d="M59.56,303.5v4.35l-.5-.49V303.5Z" fill="#911f00"/>
              <path id="Path_4857" data-name="Path 4857" d="M59.56,307.85l-2.61,1.38.05-.8,2.06-1.07Z" fill="#911f00"/>
              <path id="Path_4858" data-name="Path 4858" d="M59.5,388.569l-.05.82-.96.23.04-.81Z" fill="#c2c2c2"/>
              <path id="Path_4859" data-name="Path 4859" d="M58.59,405.749l.89-.2a.906.906,0,0,1-.93.849Z" fill="#999"/>
              <path id="Path_4860" data-name="Path 4860" d="M59.45,389.389l-.25,4.86a1.731,1.731,0,0,0-.97.2l.26-4.83Z" fill="#c2c2c2"/>
              <path id="Path_4861" data-name="Path 4861" d="M59.27,374.84l-.04.94-2.61.69a2.122,2.122,0,0,1,.07-.66c1.06-.39,2.06-.77,2.58-.97" fill="#c2c2c2"/>
              <path id="Path_4862" data-name="Path 4862" d="M59.23,375.779l-.05.89c-.52.11-1.47.3-2.48.5a2.627,2.627,0,0,1-.08-.7Z" fill="#999"/>
              <path id="Path_4863" data-name="Path 4863" d="M59.2,394.249l-.35,6.58a1.608,1.608,0,0,1-.95-.189l.33-6.191a1.731,1.731,0,0,1,.97-.2" fill="#f8f8f8"/>
              <path id="Path_4864" data-name="Path 4864" d="M59.18,376.67l-.32,5.9-6.07,1.66-.551-.03.431-6.87.37.02a.17.17,0,0,0-.01.07c-.01.22.29.39.63.33.45-.08,1.2-.22,1.99-.37h.01a1.417,1.417,0,0,0,.329.42l.021,1.6.72.16h.22l-.01-1.67a4.157,4.157,0,0,1-.24-.72c1.01-.2,1.96-.39,2.48-.5" fill="#bc2e08"/>
              <path id="Path_4865" data-name="Path 4865" d="M59.06,303.5v3.86L57,308.43l.25-3.98Z" fill="#c53c08"/>
              <path id="Path_4866" data-name="Path 4866" d="M52.79,384.23l6.07-1.66-.09,1.76-4.52,1.26Z" fill="#911f00"/>
              <path id="Path_4867" data-name="Path 4867" d="M58.85,400.829l-.17,3.12-.96.21.18-3.519a1.616,1.616,0,0,0,.95.189" fill="#c2c2c2"/>
              <path id="Path_4868" data-name="Path 4868" d="M58.77,384.33l-.24,4.48-4.54,1.1.26-4.32Z" fill="#af2205"/>
              <path id="Path_4869" data-name="Path 4869" d="M58.68,403.949l-.09,1.8-.82.17a.893.893,0,0,1-.12-.49l.069-1.27Z" fill="#c2c2c2"/>
              <path id="Path_4870" data-name="Path 4870" d="M58.59,405.749l-.04.65h-.03a.861.861,0,0,1-.75-.48Z" fill="#c2c2c2"/>
              <path id="Path_4871" data-name="Path 4871" d="M58.53,388.81l-.04.81-6.1,1.48,1.6-1.19Z" fill="#9b1d00"/>
              <path id="Path_4872" data-name="Path 4872" d="M58.49,389.62l-.26,4.83a1.43,1.43,0,0,0-.34.25l-.31,5.62a.988.988,0,0,0,.32.32l-.18,3.52-6.83,1.52.06-1.011,2.78-.71.01-4.309-.51-.13-1.98.5.99-15.82.55.03-.4,6.87Z" fill="#bc2e08"/>
              <path id="Path_4873" data-name="Path 4873" d="M58.23,394.449l-.33,6.19a.978.978,0,0,1-.32-.32l.31-5.62a1.43,1.43,0,0,1,.34-.25" fill="#dddfdf"/>
              <path id="Path_4874" data-name="Path 4874" d="M57.77,405.919l-8.11,1.771,1.23-2.01,6.83-1.521-.07,1.271a.889.889,0,0,0,.12.489" fill="#911f00"/>
              <path id="Path_4875" data-name="Path 4875" d="M57.7,335.279v4.36l-.5-.52v-3.86Z" fill="#911f00"/>
              <path id="Path_4876" data-name="Path 4876" d="M57.2,339.12l.5.52-2.74,1.33.05-.81Z" fill="#911f00"/>
              <path id="Path_4877" data-name="Path 4877" d="M57.31,303.5l-.06.95-.59.31.07-.98Z" fill="#911f00"/>
              <path id="Path_4878" data-name="Path 4878" d="M57.25,304.449l-.25,3.98-.61.32.27-3.99Z" fill="#c53c08"/>
              <path id="Path_4879" data-name="Path 4879" d="M57.2,335.259v3.86l-2.19,1.04.25-3.96Z" fill="#c53c08"/>
              <path id="Path_4880" data-name="Path 4880" d="M57.19,296.279l-.46,5.67-2.24,1.14-1.35.69-.92-.17-.62-.11,3.96-2.14.41-5.56Z" fill="#911f00"/>
              <path id="Path_4881" data-name="Path 4881" d="M57.19,350.58l-.2,5.23-1.55.5.31-7.04Z" fill="#9b1d00"/>
              <path id="Path_4882" data-name="Path 4882" d="M57,308.429l-.05.8-.61.33.05-.81Z" fill="#911f00"/>
              <path id="Path_4883" data-name="Path 4883" d="M56.95,309.23l-.87,13.89-.82,13.08-.76.36,1.84-27Z" fill="#911f00"/>
              <path id="Path_4884" data-name="Path 4884" d="M56.94,377.889l.01,1.67h-.22l-.02-1.67a1.646,1.646,0,0,1-.29-.66c.09-.02.18-.03.28-.06a4.157,4.157,0,0,0,.24.72" fill="#999"/>
              <path id="Path_4885" data-name="Path 4885" d="M56.74,374.11l.05,1.42a1.9,1.9,0,0,0-.1.28c-.11.05-.21.08-.32.13a5.383,5.383,0,0,1,.13-.69l-.03-1.14Z" fill="#999"/>
              <path id="Path_4886" data-name="Path 4886" d="M56.73,303.779l-.07.98-2.35,1.25.18-2.92Z" fill="#ad2900"/>
              <path id="Path_4887" data-name="Path 4887" d="M56.71,377.889l.02,1.67-.72-.16-.02-1.6Z" fill="#dddfdf"/>
              <path id="Path_4888" data-name="Path 4888" d="M56.71,377.889l-.72-.09a1.41,1.41,0,0,1-.33-.42c.25-.05.5-.1.76-.15a1.646,1.646,0,0,0,.29.66" fill="#c2c2c2"/>
              <path id="Path_4889" data-name="Path 4889" d="M56.66,304.759l-.27,3.99-2.33,1.21.25-3.95Z" fill="#c53c08"/>
              <path id="Path_4890" data-name="Path 4890" d="M56.62,376.469a2.636,2.636,0,0,0,.08.7c-.1.03-.19.04-.28.06a3.182,3.182,0,0,1-.09-.69Z" fill="#999"/>
              <path id="Path_4891" data-name="Path 4891" d="M56.69,375.81a2.122,2.122,0,0,0-.07.66l-.29.07c.01-.22.02-.42.04-.6.11-.05.21-.08.32-.13" fill="#999"/>
              <path id="Path_4892" data-name="Path 4892" d="M56.47,374.11l.03,1.14-.62.09-.01-1.08Z" fill="#dddfdf"/>
              <path id="Path_4893" data-name="Path 4893" d="M55.88,375.34l.62-.09a5.383,5.383,0,0,0-.13.69c-.3.1-.59.21-.88.31a2.77,2.77,0,0,1,.39-.91" fill="#c2c2c2"/>
              <path id="Path_4894" data-name="Path 4894" d="M56.42,377.23c-.26.05-.51.1-.76.15h-.01a1.513,1.513,0,0,1-.17-.61l.85-.23a3.182,3.182,0,0,0,.09.69" fill="#dddfdf"/>
              <path id="Path_4895" data-name="Path 4895" d="M56.39,308.75l-.05.81-2.33,1.23H54l.06-.83Z" fill="#911f00"/>
              <path id="Path_4896" data-name="Path 4896" d="M55.49,376.25c.29-.1.58-.21.88-.31-.02.18-.03.38-.04.6l-.85.23a1.522,1.522,0,0,1,.01-.52" fill="#dddfdf"/>
              <path id="Path_4897" data-name="Path 4897" d="M56.34,309.56l-1.84,27-2.19,1.05L54,310.79h.01Z" fill="#ad2900"/>
              <path id="Path_4898" data-name="Path 4898" d="M56.17,295.2l-1.24,1.14-1.49.78a.855.855,0,0,0-.85.45l-.68.35-.1,1.87a1.772,1.772,0,0,1-.44-.11,1.863,1.863,0,0,1-.35-.72,2.975,2.975,0,0,1,3.01-2.82l1.48-1.49Z" fill="#999"/>
              <path id="Path_4899" data-name="Path 4899" d="M54.45,299.09a2.873,2.873,0,0,0-.18-.79c-.16-.36-.38-.59-.62-.59-.34,0-.64.47-.77,1.13-.18-.1-.31-.33-.36-.76a.88.88,0,0,1,.07-.51l.85-.45,1.49-.78,1.04-.54-.41,5.56L51.6,303.5l.19-3.44a2.378,2.378,0,0,0,1.12.38c.14.6.42,1.01.74,1.01.42,0,.77-.68.82-1.57l.01-.01a2.81,2.81,0,0,0,.01-.29,2.629,2.629,0,0,0-.04-.49" fill="#c53c08"/>
              <path id="Path_4900" data-name="Path 4900" d="M55.75,366.949v4.36l-.51-.57v-3.87Z" fill="#911f00"/>
              <path id="Path_4901" data-name="Path 4901" d="M55.24,370.74l.51.57-2.77,1.03.05-.79Z" fill="#911f00"/>
              <path id="Path_4902" data-name="Path 4902" d="M55.51,294.65l-1.48,1.49a2.975,2.975,0,0,0-3.01,2.82,2.529,2.529,0,0,1,.02-1.25c.66-2.77,2.79-2.28,2.79-2.28l1.17-1.2Z" fill="#c2c2c2"/>
              <path id="Path_4903" data-name="Path 4903" d="M55.48,376.77a1.513,1.513,0,0,0,.17.61c-.79.15-1.54.29-1.99.37-.34.06-.64-.11-.63-.33Z" fill="#999"/>
              <path id="Path_4904" data-name="Path 4904" d="M55.49,376.25a1.522,1.522,0,0,0-.01.52l-2.45.65a.17.17,0,0,1,.01-.07c.04-.16.23-.33.63-.46.48-.16,1.13-.39,1.82-.64" fill="#c2c2c2"/>
              <path id="Path_4905" data-name="Path 4905" d="M55.26,336.2l-.25,3.96-.78.36.27-3.96Z" fill="#c53c08"/>
              <path id="Path_4906" data-name="Path 4906" d="M55.24,366.87v3.87l-2.21.81.25-3.95Z" fill="#c53c08"/>
              <path id="Path_4907" data-name="Path 4907" d="M55.01,340.159l-.05.81-.79.37.06-.82Z" fill="#911f00"/>
              <path id="Path_4908" data-name="Path 4908" d="M54.96,340.969l-.52,8.22L53.28,367.6l-.92.35,1.81-26.61Z" fill="#911f00"/>
              <path id="Path_4909" data-name="Path 4909" d="M54.5,336.56l-.27,3.96-2.16,1.03.24-3.94Z" fill="#c53c08"/>
              <path id="Path_4910" data-name="Path 4910" d="M54.49,299.58a2.81,2.81,0,0,1-.01.29l-.01.01a1.733,1.733,0,0,1-1.559.56,3.215,3.215,0,0,1-.091-.66,4.2,4.2,0,0,0,1.63-.69,2.629,2.629,0,0,1,.04.49" fill="#404040"/>
              <path id="Path_4911" data-name="Path 4911" d="M54.49,303.09l-.18,2.92-.94.49-.23,3.94.86.35-1.69,26.82-.8.39-.23,3.92.74.34L50.35,368.7l-.79.3-.23,3.9.73.42-1.73,27.44-.85.22-.23,3.83.79.53-.5,8v.01l-1.37.27,6.97-109.84Z" fill="#911f00"/>
              <path id="Path_4912" data-name="Path 4912" d="M54.27,298.3a2.889,2.889,0,0,1,.18.791,4.2,4.2,0,0,1-1.63.69,1.341,1.341,0,0,1-.01-.2,3.509,3.509,0,0,1,.07-.74c.52.29,1.39-.541,1.39-.541" fill="#999"/>
              <path id="Path_4913" data-name="Path 4913" d="M54.31,306.009l-.25,3.95-.92.48.23-3.94Z" fill="#c53c08"/>
              <path id="Path_4914" data-name="Path 4914" d="M54.25,385.59l-.26,4.32-1.6,1.19.4-6.87Z" fill="#9b1d00"/>
              <path id="Path_4915" data-name="Path 4915" d="M54.25,419.909l-3.45.41-6.66-.34,4.06-.24v-6.53h1.24l.12,6.37Z" fill="#911f00"/>
              <path id="Path_4916" data-name="Path 4916" d="M54.23,340.52l-.06.82-2.02.98-.13-.06.05-.71Z" fill="#911f00"/>
              <path id="Path_4917" data-name="Path 4917" d="M54.17,341.34l-1.81,26.61-2.01.75,1.67-26.44.13.06Z" fill="#ad2900"/>
              <path id="Path_4918" data-name="Path 4918" d="M54.06,309.96l-.06.83-.86-.35Z" fill="#911f00"/>
              <path id="Path_4919" data-name="Path 4919" d="M53.65,297.71c.24,0,.46.23.62.59,0,0-.87.83-1.39.54.13-.66.43-1.13.77-1.13" fill="#661300"/>
              <path id="Path_4920" data-name="Path 4920" d="M54.47,299.88c-.05.89-.4,1.57-.82,1.57-.32,0-.6-.41-.739-1.01a1.733,1.733,0,0,0,1.559-.56" fill="#661300"/>
              <path id="Path_4921" data-name="Path 4921" d="M53.74,399.65l-.01,4.31-.5-.62v-3.82Z" fill="#911f00"/>
              <path id="Path_4922" data-name="Path 4922" d="M53.23,403.34l.5.619-2.78.71.05-.779Z" fill="#911f00"/>
              <path id="Path_4923" data-name="Path 4923" d="M53.28,367.6l-.25,3.95-.93.34.26-3.94Z" fill="#c53c08"/>
              <path id="Path_4924" data-name="Path 4924" d="M53.23,399.52v3.82l-2.23.55.25-3.87Z" fill="#c53c08"/>
              <path id="Path_4925" data-name="Path 4925" d="M53.14,303.779l-6.97,109.84v.011l-.6.119,6.65-110.14Z" fill="#ad2900"/>
              <path id="Path_4926" data-name="Path 4926" d="M53.03,371.549l-.05.79-.94.36.06-.81Z" fill="#911f00"/>
              <path id="Path_4927" data-name="Path 4927" d="M52.98,372.34l-.31,4.99-.43,6.87-.99,15.82-1.09.28,1.88-27.6Z" fill="#911f00"/>
              <path id="Path_4928" data-name="Path 4928" d="M52.88,298.84a3.509,3.509,0,0,0-.07.74,1.341,1.341,0,0,0,.01.2,2.845,2.845,0,0,1-1.01.01l.1-1.87.68-.35a.88.88,0,0,0-.07.51c.05.43.18.66.36.76" fill="#999"/>
              <path id="Path_4929" data-name="Path 4929" d="M52.82,299.779a3.268,3.268,0,0,0,.09.66,2.388,2.388,0,0,1-1.12-.379l.02-.271a2.822,2.822,0,0,0,1.01-.01" fill="#404040"/>
              <path id="Path_4930" data-name="Path 4930" d="M52.36,367.949l-.26,3.94-1.99.73.24-3.92Z" fill="#c53c08"/>
              <path id="Path_4931" data-name="Path 4931" d="M52.31,337.61l-.24,3.94-.79.37.23-3.92Z" fill="#c53c08"/>
              <path id="Path_4932" data-name="Path 4932" d="M52.1,371.889l-.06.81-1.85.69-.13-.07.05-.7Z" fill="#911f00"/>
              <path id="Path_4933" data-name="Path 4933" d="M52.07,341.549l-.05.71-.74-.34Z" fill="#911f00"/>
              <path id="Path_4934" data-name="Path 4934" d="M52.04,372.7l-1.88,27.6-1.83.46,1.73-27.441.13.07Z" fill="#ad2900"/>
              <path id="Path_4935" data-name="Path 4935" d="M51.81,299.79l-.02.27a2.187,2.187,0,0,1-.42-.38,1.775,1.775,0,0,0,.44.11" fill="#404040"/>
              <path id="Path_4936" data-name="Path 4936" d="M51.25,400.02,51,403.89l-1.1.27.26-3.86Z" fill="#c53c08"/>
              <path id="Path_4937" data-name="Path 4937" d="M51,403.89l-.05.779-1.1.281.05-.79Z" fill="#911f00"/>
              <path id="Path_4938" data-name="Path 4938" d="M50.95,404.669l-.06,1.011-1.23,2.01.19-2.741Z" fill="#911f00"/>
              <path id="Path_4939" data-name="Path 4939" d="M50.35,368.7l-.24,3.92-.78.28.23-3.9Z" fill="#c53c08"/>
              <path id="Path_4940" data-name="Path 4940" d="M50.16,400.3l-.26,3.859-1.81.441.24-3.84Z" fill="#c53c08"/>
              <path id="Path_4941" data-name="Path 4941" d="M50.11,372.62l-.05.7-.73-.42Z" fill="#911f00"/>
              <path id="Path_4942" data-name="Path 4942" d="M49.9,404.159l-.05.79-1.73.44-.08-.049.05-.741Z" fill="#911f00"/>
              <path id="Path_4943" data-name="Path 4943" d="M48.33,400.76l-.24,3.84-.84.21.23-3.83Z" fill="#c53c08"/>
              <path id="Path_4944" data-name="Path 4944" d="M48.2,413.209v6.531l-4.06.24v-5.941l1.43-.29.6-.119v-.01l1.37-.27v-.01Zm-1.43,3.641c0-1.09-.4-1.98-.9-1.98s-.89.89-.89,1.98.4,1.979.89,1.979.9-.889.9-1.979" fill="#c53c08"/>
              <path id="Path_4945" data-name="Path 4945" d="M48.09,404.6l-.05.74-.79-.53Z" fill="#911f00"/>
              <path id="Path_4946" data-name="Path 4946" d="M45.87,414.869c.5,0,.9.891.9,1.98s-.4,1.98-.9,1.98-.89-.89-.89-1.98.4-1.98.89-1.98" fill="#661300"/>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_12972" data-name="Group 12972">
        <g id="Group_12971" data-name="Group 12971" clip-path="url(#clip-path-7)">
          <g id="Group_12970" data-name="Group 12970">
            <g id="Group_12969" data-name="Group 12969" clip-path="url(#clip-path-7)">
              <path id="Path_4947" data-name="Path 4947" d="M459.93,545.925v4.76h-3.28v-5.111l2.41.261Zm-.75,2.21c0-.91-.391-1.65-.87-1.65s-.87.74-.87,1.65.389,1.649.87,1.649.87-.739.87-1.649" fill="#2a82a8"/>
              <path id="Path_4948" data-name="Path 4948" d="M386.42,544.3l13.02,1.09,55.16,4.639v.651h5.33l-77.41,6.849L159.96,541.865l-4.69-.331,55.5-10,1.25-.259,3.63-.741h7l81.45,6.851,2.349.2h.011l14.48,1.21,5.349.45.181.02,14.3,1.2,24.98,2.1.36.03,13.33,1.12,5.02.42Z" fill="#0e5c77"/>
              <path id="Path_4949" data-name="Path 4949" d="M452.829,448.675l6.23,97.16-2.41-.261-6.24-97.3Z" fill="#2377a4"/>
              <path id="Path_4950" data-name="Path 4950" d="M458.31,546.485c.479,0,.87.74.87,1.65s-.391,1.65-.87,1.65-.87-.739-.87-1.65.39-1.65.87-1.65" fill="#003a49"/>
              <path id="Path_4951" data-name="Path 4951" d="M455.59,545.574h1.06v5.11H454.6v-.65l.01-4.46Z" fill="#0e5c77"/>
              <path id="Path_4952" data-name="Path 4952" d="M450.409,448.275l6.24,97.3H455.59l-.42-5.29v-.039l.429.039v-1.17l-1-.1-5.129-90.66Z" fill="#16647f"/>
              <path id="Path_4953" data-name="Path 4953" d="M442.109,537.775l1.58.16,2.82.28,2.08.2,2.36.239,1.88.19,1.77.17,1,.1v1.17l-.429-.039-233.051-22.79.09-1.5,1.82.179,2.33.23,3.76.37,1.19.12,4.68.47,2.67.261,3.95.389,1.25.13,6.24.62,1.97.191,3.96.4,1.971.19,4.229.42,2.81.281,3.421.34,2.019.2,4.4.439,2.79.271,3.389.34,1.98.2,4.48.439,2.79.28,3.39.331,1.98.2,3.571.349,2.789.281,3.381.339,1.96.19,2.719.27,2.79.281,3.39.329,1.98.2,3.18.32,2.78.27,3.39.341,1.99.2,2.781.27,2.8.281,3.4.339,1.99.2,2.561.25,2.8.279,3.4.33,1.99.2,2.61.26,2.8.28,3.41.34,2,.2,1.84.18,2.8.28,3.141.31,2.279.221,2.23.229,2.46.24,2.73.27,2.03.2,2.97.3,2.36.23,2.88.29,1.69.16,2.64.27,2.39.23,2.96.3,1.7.17,2.011.2,2.449.241,2.82.279,1.78.17v.011l2.47.239,2.3.23,2.82.281,1.66.159,1.06.11,2.32.231,2.88.279,1.6.16,1.36.141,2.27.219,2.69.27,1.4.141,2.08.21,2.21.209Z" fill="#16647f"/>
              <path id="Path_4954" data-name="Path 4954" d="M455.17,540.284l.42,5.29h-.981l-.009,4.46-55.16-4.64v-6.6l-14.98-1.511v6.851l-5.02-.42v-6.76l-13.69-1.5v7.109l-24.98-2.1v-7.351l-14.3-1.33v7.381l-.181.08-5.349-.45v-7.34L304.1,529.9v7.481l-81.45-6.851.12-6.67-7.12-.409.3-9.79h.01l1.839.21.361-.57.09-5.021-2.15-.6,1.36-29.74.78-.26,1.76.18.33-.6-.031-5.28-2.059-.58-.52.21,1.3-30.019h1.75l.33-.64-.031-5.3-2.049-.37,1.37-29.89h1.75l.33-.639-.03-5.3-1.6-.29v-2.2l.51-.351,5,1.591-4.14,117.75-.09,1.5,233.05,22.79Z" fill="#2377a4"/>
              <path id="Path_4955" data-name="Path 4955" d="M449.47,448.355l5.13,90.66-1.771-.17-4.329-91.08h.01Z" fill="#2a82a8"/>
              <path id="Path_4956" data-name="Path 4956" d="M453.69,444.225v4.13l-3.281-.67v-4.729l2.521.969Zm-.76,1.54c0-.78-.38-1.4-.84-1.4s-.84.62-.84,1.4.38,1.4.84,1.4.84-.63.84-1.4" fill="#2a82a8"/>
              <path id="Path_4957" data-name="Path 4957" d="M453.69,448.355l-.86.32-2.42-.4-.94.08-.96-.59,1.9-.08Z" fill="#003a49"/>
              <path id="Path_4958" data-name="Path 4958" d="M449.39,441.495s3.58.14,3.54,2.43a3.82,3.82,0,0,0-3.74-1.861l-2.67-2.439a.654.654,0,0,0-.45.46l-.09-.09c-.08-.881.88-.761.88-.761Z" fill="#c2c2c2"/>
              <path id="Path_4959" data-name="Path 4959" d="M452.93,443.925l-2.521-.97-1.659-.1-2.68-2.77a.654.654,0,0,1,.45-.46l2.67,2.44a3.82,3.82,0,0,1,3.74,1.86" fill="#999"/>
              <path id="Path_4960" data-name="Path 4960" d="M452.829,538.845l-1.88-.19-4.09-93.97,1.55,1.45h.011l.08,1.63Z" fill="#1a6a8e"/>
              <path id="Path_4961" data-name="Path 4961" d="M452.09,444.364c.46,0,.84.62.84,1.4s-.38,1.4-.84,1.4-.84-.63-.84-1.4.38-1.4.84-1.4" fill="#003a49"/>
              <path id="Path_4962" data-name="Path 4962" d="M446.859,444.685l4.09,93.97-2.36-.24-4.14-94.29,2.41.55Z" fill="#2377a4"/>
              <path id="Path_4963" data-name="Path 4963" d="M448.75,442.855l1.659.1v4.729l-1.9.08h-.01l-.08-1.63-.051-1.11-.109-2.2Z" fill="#0e5c77"/>
              <path id="Path_4964" data-name="Path 4964" d="M444.449,444.125l4.141,94.29-2.08-.2-3.951-93.24Z" fill="#3b93b2"/>
              <path id="Path_4965" data-name="Path 4965" d="M448.369,445.025l.051,1.11h-.011l-1.55-1.45v-.01Z" fill="#0e5c77"/>
              <path id="Path_4966" data-name="Path 4966" d="M346.44,421.574l-2.8-.64-2.56-.589.01-1.51.24-1.031,106.93,24.97v.05l.11,2.2-1.51-.349-2.41-.55-1.94-.45-1.29-.29v-.01l-1.57-.361-.08-.009-2.63-.611-2.22-.509-1.66-.38-1.62-.37v-.01l-2.8-.641-.05-.01-2.27-.519-1.58-.37-1.62-.37-2.85-.651-.03-.01-2.3-.53-1.1-.25-1.629-.38-.121-.019-2.7-.63h-.01l-2.36-.54-1.78-.41-1.46-.34h-.01l-.24-.25v.2l-2.86-.659h-.01l-2.39-.551-2.08-.479-1.72-.4-2.88-.66-.03-.01-2.37-.54-1.21-.28-1.68-.39-2.82-.65h-.01l-2.42-.55v-.01l-3.85-.879-1.72-.39v-.011l-2.88-.66h-.01l-2.36-.539-1.59-.37-2-.46-3.41-.78h-.01v-.01l-2.8-.641-1.82-.42-2.01-.46-3.42-.79h-.01l-2.79-.639-1.54-.361-1.99-.45-3.4-.779Z" fill="#2377a4"/>
              <path id="Path_4967" data-name="Path 4967" d="M442.56,444.975l3.95,93.24-2.82-.28-2.44-93.24.01.01.99.41Z" fill="#2a82a8"/>
              <path id="Path_4968" data-name="Path 4968" d="M444.449,444.125l-1.89.85-.049-1.3Z" fill="#0e5c77"/>
              <path id="Path_4969" data-name="Path 4969" d="M441.25,444.694l2.439,93.24-1.58-.16-2.46-94.689Z" fill="#1a6a8e"/>
              <path id="Path_4970" data-name="Path 4970" d="M442.51,443.675l.05,1.3-.31.139-.99-.409-.01-.01-.03-1.31Z" fill="#0e5c77"/>
              <path id="Path_4971" data-name="Path 4971" d="M439.649,443.085l2.46,94.689-2.689-.269-2.481-95.111,2.63.611Z" fill="#2377a4"/>
              <path id="Path_4972" data-name="Path 4972" d="M441.22,443.385l.03,1.309-1.6-1.609v-.07l1.57.36Z" fill="#0e5c77"/>
              <path id="Path_4973" data-name="Path 4973" d="M436.94,442.395l2.48,95.11-2.209-.21-2.471-94.339h.011Z" fill="#3b93b2"/>
              <path id="Path_4974" data-name="Path 4974" d="M437.21,537.3l-2.08-.21-2.051-94.32.011.009,1.649.181Z" fill="#2a82a8"/>
              <path id="Path_4975" data-name="Path 4975" d="M436.94,442.395l-2.189.561h-.011l-.02-1.071Z" fill="#0e5c77"/>
              <path id="Path_4976" data-name="Path 4976" d="M433.079,442.765l2.051,94.32-1.4-.14-2.29-95.76Z" fill="#1a6a8e"/>
              <path id="Path_4977" data-name="Path 4977" d="M434.72,441.885l.02,1.07-1.65-.18-.011-.01-.019-1.26Z" fill="#0e5c77"/>
              <path id="Path_4978" data-name="Path 4978" d="M431.44,441.185l2.29,95.76-2.69-.27L428.59,440.5l.05-.011,2.8.64v.06Z" fill="#2377a4"/>
              <path id="Path_4979" data-name="Path 4979" d="M433.06,441.505l.02,1.26-1.64-1.58v-.05Z" fill="#0e5c77"/>
              <path id="Path_4980" data-name="Path 4980" d="M428.59,440.5l2.449,96.18-2.269-.22-2.42-95.29.04.01Z" fill="#3b93b2"/>
              <path id="Path_4981" data-name="Path 4981" d="M426.35,441.165l2.42,95.29-1.36-.141-2.64-95.509.03.03Z" fill="#2a82a8"/>
              <path id="Path_4982" data-name="Path 4982" d="M428.59,440.475v.02l-2.2.68-.04-.01-.031-1.21Z" fill="#0e5c77"/>
              <path id="Path_4983" data-name="Path 4983" d="M424.77,440.8l2.64,95.51-1.6-.16-1.7-61.38v-1.82l-.05-.01-.94-33.7Z" fill="#1a6a8e"/>
              <path id="Path_4984" data-name="Path 4984" d="M426.319,439.955l.03,1.21-1.55-.33-.03-.03-.03-1.22Z" fill="#0e5c77"/>
              <path id="Path_4985" data-name="Path 4985" d="M424.109,474.775l1.7,61.38-2.88-.279-1.58-56.991,2.75.411Z" fill="#2377a4"/>
              <path id="Path_4986" data-name="Path 4986" d="M424.739,439.585l.03,1.22-1.65-1.56v-.03Z" fill="#0e5c77"/>
              <path id="Path_4987" data-name="Path 4987" d="M424.109,472.955v1.819l-.05-1.83Z" fill="#fff"/>
              <path id="Path_4988" data-name="Path 4988" d="M424.06,472.944l.05,1.83-.01,4.521-2.75-.41-.17-6.371Z" fill="#dddfdf"/>
              <path id="Path_4989" data-name="Path 4989" d="M423.119,439.245l.941,33.7-2.88-.429-.941-33.941.03-.01,2.85.651Z" fill="#2377a4"/>
              <path id="Path_4990" data-name="Path 4990" d="M421.35,478.885l1.58,56.99-2.32-.23-1.56-57.111Z" fill="#3b93b2"/>
              <path id="Path_4991" data-name="Path 4991" d="M421.18,472.515l.17,6.37-2.3-.35-.181-6.37Z" fill="#fff"/>
              <path id="Path_4992" data-name="Path 4992" d="M420.239,438.574l.94,33.94-2.31-.349-.9-32.931Z" fill="#3b93b2"/>
              <path id="Path_4993" data-name="Path 4993" d="M419.05,478.534l1.56,57.11-1.06-.11-1.58-57.159Z" fill="#2a82a8"/>
              <path id="Path_4994" data-name="Path 4994" d="M420.239,438.555v.02l-2.27.66-.03-1.21Z" fill="#0e5c77"/>
              <path id="Path_4995" data-name="Path 4995" d="M417.97,478.375l1.58,57.159-1.66-.159-1.57-57.25Z" fill="#1a6a8e"/>
              <path id="Path_4996" data-name="Path 4996" d="M418.869,472.165l.181,6.369-1.08-.159-.181-6.37Z" fill="#dddfdf"/>
              <path id="Path_4997" data-name="Path 4997" d="M417.97,439.235l.9,32.931-1.08-.161-.92-33.15Z" fill="#2a82a8"/>
              <path id="Path_4998" data-name="Path 4998" d="M417.94,438.025l.03,1.21-1.1-.38-.03-1.08Z" fill="#0e5c77"/>
              <path id="Path_4999" data-name="Path 4999" d="M417.789,472.005l.181,6.37-1.651-.25-.17-6.37Z" fill="#c2c2c2"/>
              <path id="Path_5000" data-name="Path 5000" d="M416.319,478.125l1.57,57.25-2.82-.28-.97-35.41,1.491.23.019-21.9Z" fill="#2377a4"/>
              <path id="Path_5001" data-name="Path 5001" d="M416.869,438.855l.92,33.15-1.64-.25-.939-34.28,1.619,1.36Z" fill="#1a6a8e"/>
              <path id="Path_5002" data-name="Path 5002" d="M416.869,438.855l-.04-.02-1.619-1.36v-.08l1.63.38Z" fill="#0e5c77"/>
              <path id="Path_5003" data-name="Path 5003" d="M416.149,471.755l.17,6.37-.71-.11-.02,21.9-1.49-.23-.77-28.35Z" fill="#dddfdf"/>
              <path id="Path_5004" data-name="Path 5004" d="M415.21,437.475l.939,34.28-2.82-.42-.939-34.59,2.7.63Z" fill="#2377a4"/>
              <path id="Path_5005" data-name="Path 5005" d="M415.21,437.395v.08l-.12-.1Z" fill="#2377a4"/>
              <path id="Path_5006" data-name="Path 5006" d="M414.1,499.685l.97,35.41-2.3-.23-.99-35.53Z" fill="#3b93b2"/>
              <path id="Path_5007" data-name="Path 5007" d="M413.329,471.335l.77,28.35-2.32-.35-.79-28.351Z" fill="#fff"/>
              <path id="Path_5008" data-name="Path 5008" d="M412.39,436.745l.94,34.59-2.34-.351-.94-33.7.03.011,2.3-.55Z" fill="#3b93b2"/>
              <path id="Path_5009" data-name="Path 5009" d="M411.779,499.335l.99,35.529-2.469-.239v-.011l-.741-35.609Z" fill="#2a82a8"/>
              <path id="Path_5010" data-name="Path 5010" d="M412.38,436.745l-2.3.55-.029-.011-.03-1.079Z" fill="#0e5c77"/>
              <path id="Path_5011" data-name="Path 5011" d="M410.989,470.985l.79,28.351-2.22-.331-.6-28.33Z" fill="#dddfdf"/>
              <path id="Path_5012" data-name="Path 5012" d="M410.05,437.284l.939,33.7-2.029-.309-.7-33.691.029.03Z" fill="#2a82a8"/>
              <path id="Path_5013" data-name="Path 5013" d="M409.56,499.005l.74,35.609-1.78-.17-.72-35.709Z" fill="#1a6a8e"/>
              <path id="Path_5014" data-name="Path 5014" d="M410.02,436.205l.03,1.079-1.76-.27-.03-.03-.02-1.189Z" fill="#0e5c77"/>
              <path id="Path_5015" data-name="Path 5015" d="M408.96,470.675l.6,28.33-1.76-.27-.061-3.05.021-18.861-.4-.059-.12-6.35Z" fill="#c2c2c2"/>
              <path id="Path_5016" data-name="Path 5016" d="M408.26,436.985l.7,33.69-1.721-.259L406.53,435.4l.24.051h.01Z" fill="#1a6a8e"/>
              <path id="Path_5017" data-name="Path 5017" d="M407.8,498.735l.72,35.71-2.821-.279-1.179-57.83,2.84.429.379,18.92v3.04Z" fill="#2377a4"/>
              <path id="Path_5018" data-name="Path 5018" d="M408.239,435.8l.021,1.189-1.481-1.529Z" fill="#0e5c77"/>
              <path id="Path_5019" data-name="Path 5019" d="M407.739,495.685l.061,3.05-.061-.01Z" fill="#fff"/>
              <path id="Path_5020" data-name="Path 5020" d="M407.76,476.824l-.021,18.86-.379-18.92Z" fill="#1a6a8e"/>
              <path id="Path_5021" data-name="Path 5021" d="M407.239,470.415l.12,6.35-2.84-.43-.13-6.351Z" fill="#dddfdf"/>
              <path id="Path_5022" data-name="Path 5022" d="M406.529,435.4l.71,35.011-2.85-.431-.719-35.239Z" fill="#2377a4"/>
              <path id="Path_5023" data-name="Path 5023" d="M406.529,435.205l.24.25-.24-.051Z" fill="#2377a4"/>
              <path id="Path_5024" data-name="Path 5024" d="M404.52,476.335l1.18,57.83-2.45-.24-1.15-57.95Z" fill="#3b93b2"/>
              <path id="Path_5025" data-name="Path 5025" d="M404.39,469.985l.13,6.351-2.42-.361-.12-6.349Z" fill="#fff"/>
              <path id="Path_5026" data-name="Path 5026" d="M403.67,434.745l.72,35.239-2.41-.359-.68-34.12,2.359-.76Z" fill="#3b93b2"/>
              <path id="Path_5027" data-name="Path 5027" d="M403.659,434.745l-2.359.76-.031-1.311Z" fill="#0e5c77"/>
              <path id="Path_5028" data-name="Path 5028" d="M403.25,533.925l-2.011-.2-1.189-58.06,2.05.31Z" fill="#2a82a8"/>
              <path id="Path_5029" data-name="Path 5029" d="M401.979,469.625l.12,6.35-2.05-.31-.13-6.351Z" fill="#dddfdf"/>
              <path id="Path_5030" data-name="Path 5030" d="M401.3,435.505l.68,34.12-2.06-.31-.7-34.33,2.019.54Z" fill="#2a82a8"/>
              <path id="Path_5031" data-name="Path 5031" d="M401.27,434.194l.03,1.311-.06.019-2.02-.54-.03-1.27Z" fill="#0e5c77"/>
              <path id="Path_5032" data-name="Path 5032" d="M400.05,475.665l1.189,58.06-1.7-.17-.75-36.241-.369-17.859-.95-46.141,1.75,1.67.7,34.33-.681-.1-.009,6.33Z" fill="#1a6a8e"/>
              <path id="Path_5033" data-name="Path 5033" d="M399.92,469.315l.13,6.351-.82-.121.009-6.33Z" fill="#c2c2c2"/>
              <path id="Path_5034" data-name="Path 5034" d="M398.789,497.315l.75,36.24-2.96-.3-.73-36.32,2.611.39Z" fill="#2377a4"/>
              <path id="Path_5035" data-name="Path 5035" d="M399.44,538.795v6.6c-7.891-1.52-4.111-4.75-3.861-4.96l.011-.01Z" fill="#0e5c77"/>
              <path id="Path_5036" data-name="Path 5036" d="M399.44,538.8l-3.85,1.63L384.461,539.4v-2.12Z" fill="#003a49"/>
              <path id="Path_5037" data-name="Path 5037" d="M399.44,545.4l-13.021-1.09,9.16-3.87c-.25.21-4.03,3.44,3.861,4.96" fill="#003a49"/>
              <path id="Path_5038" data-name="Path 5038" d="M399.19,433.715l.03,1.27-1.75-1.67Z" fill="#0e5c77"/>
              <path id="Path_5039" data-name="Path 5039" d="M398.42,479.455l.369,17.859-.329.01-2.69-4.4-.151-7.26a8.357,8.357,0,0,0,2.3-6.2Z" fill="#2377a4"/>
              <path id="Path_5040" data-name="Path 5040" d="M395.77,492.925l2.69,4.4-2.61-.389Z" fill="#fff"/>
              <path id="Path_5041" data-name="Path 5041" d="M397.47,433.315l.95,46.141-.5.009a11.283,11.283,0,0,0-2.57-7.33l-.79-39.469.03-.011Z" fill="#2377a4"/>
              <path id="Path_5042" data-name="Path 5042" d="M395.35,472.135a11.283,11.283,0,0,1,2.57,7.33,8.357,8.357,0,0,1-2.3,6.2Z" fill="#dddfdf"/>
              <path id="Path_5043" data-name="Path 5043" d="M395.85,496.935l.73,36.32-2.39-.23-.72-36.45Z" fill="#3b93b2"/>
              <path id="Path_5044" data-name="Path 5044" d="M395.77,492.925l.08,4.01-2.38-.361-.16-7.659Z" fill="#fff"/>
              <path id="Path_5045" data-name="Path 5045" d="M395.619,485.665l.15,7.26-2.459-4.01-.031-1.681a7.559,7.559,0,0,0,2.34-1.569" fill="#3b93b2"/>
              <path id="Path_5046" data-name="Path 5046" d="M395.35,472.135l.27,13.53a7.572,7.572,0,0,1-2.34,1.57l-.34-17.3a12.066,12.066,0,0,1,2.41,2.2" fill="#fff"/>
              <path id="Path_5047" data-name="Path 5047" d="M395.59,540.425l-.011.01-9.159,3.87-1.96-.17V539.4Z" fill="#002730"/>
              <path id="Path_5048" data-name="Path 5048" d="M394.56,432.665l.79,39.47a12.036,12.036,0,0,0-2.41-2.2l-.73-36.81Z" fill="#3b93b2"/>
              <path id="Path_5049" data-name="Path 5049" d="M394.56,432.645v.021l-2.35.459-.02-1.02Z" fill="#0e5c77"/>
              <path id="Path_5050" data-name="Path 5050" d="M393.47,496.574l.72,36.45-2.64-.269-.21-36.5Z" fill="#2a82a8"/>
              <path id="Path_5051" data-name="Path 5051" d="M393.31,488.915l.16,7.659-2.13-.319-.15-27.37a12.572,12.572,0,0,1,1.75,1.05l.34,17.3a6.355,6.355,0,0,1-.8.321Z" fill="#dddfdf"/>
              <path id="Path_5052" data-name="Path 5052" d="M393.279,487.235l.03,1.681-.83-1.361a6.347,6.347,0,0,0,.8-.32" fill="#2a82a8"/>
              <path id="Path_5053" data-name="Path 5053" d="M392.21,433.125l.729,36.81a12.513,12.513,0,0,0-1.75-1.05l-.2-35.99.011.009,1.18.231Z" fill="#2a82a8"/>
              <path id="Path_5054" data-name="Path 5054" d="M392.19,432.1l.021,1.02-.031.01L391,432.9l-.011-.01-.01-1.07Z" fill="#0e5c77"/>
              <path id="Path_5055" data-name="Path 5055" d="M391.34,496.255l.21,36.5-1.69-.16-.21-37.18.4.65Z" fill="#1a6a8e"/>
              <path id="Path_5056" data-name="Path 5056" d="M391.19,468.885l.15,27.37-1.291-.19-.4-.65-.081-15.24a3.994,3.994,0,0,0,.421-1.91,4.931,4.931,0,0,0-.441-2.11l-.049-8.01a14.961,14.961,0,0,1,1.69.74" fill="#c2c2c2"/>
              <path id="Path_5057" data-name="Path 5057" d="M390.989,432.895l.2,35.99a15.2,15.2,0,0,0-1.689-.74l-.2-36.7Z" fill="#1a6a8e"/>
              <path id="Path_5058" data-name="Path 5058" d="M390.979,431.824l.01,1.07-1.69-1.45v-.01Z" fill="#0e5c77"/>
              <path id="Path_5059" data-name="Path 5059" d="M389.649,495.415l.21,37.18-2.88-.29-.229-41.67Z" fill="#2377a4"/>
              <path id="Path_5060" data-name="Path 5060" d="M389.55,476.154a4.949,4.949,0,0,1,.439,2.11,3.982,3.982,0,0,1-.42,1.91Z" fill="#1a6a8e"/>
              <path id="Path_5061" data-name="Path 5061" d="M389.649,495.415l-2.9-4.78-.051-8.9a3.15,3.15,0,0,0,2.87-1.559Z" fill="#dddfdf"/>
              <path id="Path_5062" data-name="Path 5062" d="M389.55,476.154l.019,4.02a3.144,3.144,0,0,1-2.87,1.56l-.049-7.978a4.645,4.645,0,0,1,2.9,2.4" fill="#2377a4"/>
              <path id="Path_5063" data-name="Path 5063" d="M389.5,468.145l.05,8.01a4.653,4.653,0,0,0-2.9-2.4l-.04-6.41a16.988,16.988,0,0,1,2.891.8" fill="#dddfdf"/>
              <path id="Path_5064" data-name="Path 5064" d="M389.3,431.444l.2,36.7a17,17,0,0,0-2.89-.8l-.2-36.459-.019-.081.08-.019h.01l2.82.649Z" fill="#2377a4"/>
              <path id="Path_5065" data-name="Path 5065" d="M386.75,490.635l.229,41.67-2.36-.231-.25-44.369.651.1Z" fill="#3b93b2"/>
              <path id="Path_5066" data-name="Path 5066" d="M386.7,481.735l.051,8.9-1.731-2.83-.65-.1-.029-6.181.759.12a7.5,7.5,0,0,0,1.6.09" fill="#fff"/>
              <path id="Path_5067" data-name="Path 5067" d="M386.649,473.755l.05,7.98a7.5,7.5,0,0,1-1.6-.09l-.76-.12-.05-8.27.82.12a8.439,8.439,0,0,1,1.54.38" fill="#3b93b2"/>
              <path id="Path_5068" data-name="Path 5068" d="M386.609,467.345l.04,6.41a8.439,8.439,0,0,0-1.54-.38l-.82-.12-.03-6.3,1.33.2c.35.05.69.11,1.02.19" fill="#fff"/>
              <path id="Path_5069" data-name="Path 5069" d="M386.409,430.885l.2,36.46c-.33-.08-.67-.14-1.019-.19l-1.33-.2-.2-35.591,2.33-.559Z" fill="#3b93b2"/>
              <path id="Path_5070" data-name="Path 5070" d="M386.47,430.784l-.08.02-2.33.56-.01-1.13Z" fill="#0e5c77"/>
              <path id="Path_5071" data-name="Path 5071" d="M384.369,487.705l.25,44.369-2.97-.3-.63-44.58Z" fill="#2a82a8"/>
              <path id="Path_5072" data-name="Path 5072" d="M384.34,481.525l.029,6.181-3.349-.511-.09-6.18Z" fill="#dddfdf"/>
              <path id="Path_5073" data-name="Path 5073" d="M384.289,473.255l.051,8.27-3.41-.51-.12-8.28Z" fill="#2a82a8"/>
              <path id="Path_5074" data-name="Path 5074" d="M384.26,466.955l.029,6.3-3.479-.521-.09-6.309Z" fill="#dddfdf"/>
              <path id="Path_5075" data-name="Path 5075" d="M384.06,431.365l.2,35.591-3.54-.53-.51-35.911,3.829.85Z" fill="#2a82a8"/>
              <path id="Path_5076" data-name="Path 5076" d="M384.05,430.235l.01,1.13h-.021l-3.829-.85-.011-1.17,3.851.88Z" fill="#0e5c77"/>
              <path id="Path_5077" data-name="Path 5077" d="M381.02,487.194l.63,44.58-2.03-.2-.41-37.15,1.52.23.01-7.5Z" fill="#1a6a8e"/>
              <path id="Path_5078" data-name="Path 5078" d="M380.93,481.015l.09,6.18-.28-.04-.01,7.5-1.52-.23-.31-28.28,1.82.28.09,6.31-.06-.01-.01,8.26Z" fill="#c2c2c2"/>
              <path id="Path_5079" data-name="Path 5079" d="M380.81,472.735l.12,8.28-.19-.03.01-8.26Z" fill="#1a6a8e"/>
              <path id="Path_5080" data-name="Path 5080" d="M380.21,430.515l.51,35.91-1.821-.28-.419-37.189,1.69,1.549Z" fill="#1a6a8e"/>
              <path id="Path_5081" data-name="Path 5081" d="M380.2,429.345l.011,1.17-.04-.01-1.691-1.55Z" fill="#0e5c77"/>
              <path id="Path_5082" data-name="Path 5082" d="M379.21,494.425l.409,37.149-2.729-.269-.47-37.3Z" fill="#2377a4"/>
              <path id="Path_5083" data-name="Path 5083" d="M379.44,536.955v6.76c-7.711-1.34-4.16-4.88-3.931-5.1Z" fill="#0e5c77"/>
              <path id="Path_5084" data-name="Path 5084" d="M379.44,536.955l-3.93,1.659.01-.01-9.769-1.07v-2.079Z" fill="#003a49"/>
              <path id="Path_5085" data-name="Path 5085" d="M379.44,543.715l-13.331-1.12,9.4-3.98c-.229.22-3.78,3.76,3.931,5.1" fill="#003a49"/>
              <path id="Path_5086" data-name="Path 5086" d="M378.9,466.145l.311,28.28-2.79-.42-.351-28.28Z" fill="#dddfdf"/>
              <path id="Path_5087" data-name="Path 5087" d="M378.479,428.955l.42,37.189-2.83-.419-.47-37.441,2.88.66Z" fill="#2377a4"/>
              <path id="Path_5088" data-name="Path 5088" d="M376.42,494.005l.47,37.3-2.46-.24-.43-37.42Z" fill="#3b93b2"/>
              <path id="Path_5089" data-name="Path 5089" d="M376.069,465.725,376.42,494l-2.42-.36-.33-28.28Z" fill="#fff"/>
              <path id="Path_5090" data-name="Path 5090" d="M375.6,428.284l.47,37.44-2.4-.36-.42-36.069,2.34-1.011Z" fill="#3b93b2"/>
              <path id="Path_5091" data-name="Path 5091" d="M375.59,428.284l-2.34,1.011-.02-1.55Z" fill="#0e5c77"/>
              <path id="Path_5092" data-name="Path 5092" d="M375.52,538.6l-.01.01-9.4,3.98-.36-.03v-5.03Z" fill="#002730"/>
              <path id="Path_5093" data-name="Path 5093" d="M374.43,531.065l-2.23-.229-.55-101.771,1.57.24.03-.01.42,36.07-.77-.119-.031,28.229,1.131.17Z" fill="#2a82a8"/>
              <path id="Path_5094" data-name="Path 5094" d="M373.67,465.365l.33,28.28-1.131-.17.03-28.229Z" fill="#dddfdf"/>
              <path id="Path_5095" data-name="Path 5095" d="M373.229,427.745l.021,1.55-.031.01-1.57-.241-.01-1.689Z" fill="#0e5c77"/>
              <path id="Path_5096" data-name="Path 5096" d="M371.649,429.065l.55,101.771-2.279-.221-.141-51.88-.14-51.819,2,2.149Z" fill="#1a6a8e"/>
              <path id="Path_5097" data-name="Path 5097" d="M371.64,427.375l.01,1.689h-.01l-2-2.149Z" fill="#0e5c77"/>
              <path id="Path_5098" data-name="Path 5098" d="M369.779,478.735l.141,51.88-3.141-.31-.229-44.03a12.936,12.936,0,0,0,2.219-7.54Z" fill="#2377a4"/>
              <path id="Path_5099" data-name="Path 5099" d="M369.64,426.915l.14,51.819h-1.01a16.284,16.284,0,0,0-2.31-8.359l-.24-44.24h.01Z" fill="#2377a4"/>
              <path id="Path_5100" data-name="Path 5100" d="M366.55,486.275l.229,44.03-2.8-.28-.22-40.979a10.983,10.983,0,0,0,2.79-2.771" fill="#3b93b2"/>
              <path id="Path_5101" data-name="Path 5101" d="M368.77,478.735a12.93,12.93,0,0,1-2.221,7.54l-.09-15.9a16.282,16.282,0,0,1,2.311,8.359" fill="#dddfdf"/>
              <path id="Path_5102" data-name="Path 5102" d="M366.46,470.375l.09,15.9a10.983,10.983,0,0,1-2.79,2.771l-.12-22.25a17.068,17.068,0,0,1,2.82,3.58" fill="#fff"/>
              <path id="Path_5103" data-name="Path 5103" d="M366.22,426.135l.24,44.24a17.03,17.03,0,0,0-2.82-3.58l-.21-39.82Z" fill="#3b93b2"/>
              <path id="Path_5104" data-name="Path 5104" d="M366.22,426.125v.01l-2.79.84-.01-1.491Z" fill="#0e5c77"/>
              <path id="Path_5105" data-name="Path 5105" d="M363.76,489.045l.22,40.979-1.84-.179-.21-39.76a11.062,11.062,0,0,0,1.83-1.04" fill="#2a82a8"/>
              <path id="Path_5106" data-name="Path 5106" d="M363.64,466.8l.12,22.25a11.022,11.022,0,0,1-1.83,1.04l-.13-24.86a16.217,16.217,0,0,1,1.84,1.57" fill="#dddfdf"/>
              <path id="Path_5107" data-name="Path 5107" d="M363.43,426.975l.21,39.82a16.217,16.217,0,0,0-1.84-1.57l-.2-38.77Z" fill="#2a82a8"/>
              <path id="Path_5108" data-name="Path 5108" d="M363.42,425.485l.01,1.49-1.83-.519v-1.391Z" fill="#0e5c77"/>
              <path id="Path_5109" data-name="Path 5109" d="M361.93,490.085l.21,39.76-2-.2-.2-38.839a12.786,12.786,0,0,0,1.99-.72" fill="#1a6a8e"/>
              <path id="Path_5110" data-name="Path 5110" d="M361.8,465.225l.13,24.86a12.687,12.687,0,0,1-1.99.72l-.05-9.29a8.1,8.1,0,0,0,.939-3.97,9.808,9.808,0,0,0-.979-4.36l-.05-9.26a18.043,18.043,0,0,1,2,1.3" fill="#c2c2c2"/>
              <path id="Path_5111" data-name="Path 5111" d="M361.6,426.455l.2,38.77a17.9,17.9,0,0,0-2-1.3l-.21-39.321,2,1.851Z" fill="#1a6a8e"/>
              <path id="Path_5112" data-name="Path 5112" d="M361.6,425.065v1.391h-.01l-2-1.851Z" fill="#0e5c77"/>
              <path id="Path_5113" data-name="Path 5113" d="M359.85,473.185a9.809,9.809,0,0,1,.98,4.36,8.092,8.092,0,0,1-.94,3.97Z" fill="#1a6a8e"/>
              <path id="Path_5114" data-name="Path 5114" d="M359.94,490.8l.2,38.84-3.41-.34-.2-38.01a14.246,14.246,0,0,0,3.41-.49" fill="#2377a4"/>
              <path id="Path_5115" data-name="Path 5115" d="M359.89,481.515l.05,9.29a14.239,14.239,0,0,1-3.41.49l-.04-7.01a5.723,5.723,0,0,0,3.4-2.77" fill="#dddfdf"/>
              <path id="Path_5116" data-name="Path 5116" d="M359.85,473.185l.04,8.33a5.723,5.723,0,0,1-3.4,2.77l-.08-14.84a8.839,8.839,0,0,1,3.44,3.74" fill="#2377a4"/>
              <path id="Path_5117" data-name="Path 5117" d="M359.8,463.925l.05,9.26a8.841,8.841,0,0,0-3.441-3.74l-.029-7a19.4,19.4,0,0,1,3.42,1.48" fill="#dddfdf"/>
              <path id="Path_5118" data-name="Path 5118" d="M359.59,424.605l.21,39.32a19.4,19.4,0,0,0-3.42-1.48l-.21-38.63Z" fill="#2377a4"/>
              <path id="Path_5119" data-name="Path 5119" d="M356.529,491.3l.2,38.01-2.8-.281-.2-37.879a17.044,17.044,0,0,0,2.8.15" fill="#3b93b2"/>
              <path id="Path_5120" data-name="Path 5120" d="M356.489,484.284l.04,7.011a16.94,16.94,0,0,1-2.8-.151l-.03-6.62a6.727,6.727,0,0,0,2.79-.24" fill="#fff"/>
              <path id="Path_5121" data-name="Path 5121" d="M356.409,469.444l.08,14.841a6.745,6.745,0,0,1-2.79.239l-.09-16.12a8.086,8.086,0,0,1,2.8,1.04" fill="#3b93b2"/>
              <path id="Path_5122" data-name="Path 5122" d="M356.38,462.444l.029,7a8.074,8.074,0,0,0-2.8-1.04l-.031-6.63a18.447,18.447,0,0,1,2.8.67" fill="#fff"/>
              <path id="Path_5123" data-name="Path 5123" d="M356.17,423.815l.21,38.63a18.443,18.443,0,0,0-2.8-.67l-.2-37.37,2.779-.59Z" fill="#3b93b2"/>
              <path id="Path_5124" data-name="Path 5124" d="M356.159,423.815l-2.779.59-.011-1.23Z" fill="#0e5c77"/>
              <path id="Path_5125" data-name="Path 5125" d="M353.729,491.145l.2,37.88-2.61-.26.181-38.04a17.688,17.688,0,0,0,1.819.37c.14.02.28.04.41.05" fill="#2a82a8"/>
              <path id="Path_5126" data-name="Path 5126" d="M353.7,484.525l.03,6.62c-.13-.01-.269-.03-.41-.05a17.477,17.477,0,0,1-1.819-.37l.029-6.729a8.468,8.468,0,0,0,1.8.489c.131.02.25.03.37.04" fill="#dddfdf"/>
              <path id="Path_5127" data-name="Path 5127" d="M353.609,468.4l.09,16.121c-.12-.011-.239-.019-.37-.041a8.353,8.353,0,0,1-1.8-.489l.08-15.72a7.872,7.872,0,0,1,1.73.069,1.543,1.543,0,0,1,.27.06" fill="#2a82a8"/>
              <path id="Path_5128" data-name="Path 5128" d="M353.579,461.775l.03,6.63a1.37,1.37,0,0,0-.269-.06,7.82,7.82,0,0,0-1.731-.07l.03-6.72q.84.047,1.71.18a1.981,1.981,0,0,1,.23.04" fill="#dddfdf"/>
              <path id="Path_5129" data-name="Path 5129" d="M353.38,424.4l.2,37.371a2.245,2.245,0,0,0-.229-.041q-.87-.134-1.71-.179l.179-37.16.01.009h1.551Z" fill="#2a82a8"/>
              <path id="Path_5130" data-name="Path 5130" d="M353.369,423.175l.011,1.23h-1.551l-.01-.01.01-1.58Z" fill="#0e5c77"/>
              <path id="Path_5131" data-name="Path 5131" d="M351.829,422.815l-.01,1.58-1.979-2.03Z" fill="#0e5c77"/>
              <path id="Path_5132" data-name="Path 5132" d="M351.819,424.395l-.18,37.16a16.521,16.521,0,0,0-1.99,0l.19-39.19Z" fill="#1a6a8e"/>
              <path id="Path_5133" data-name="Path 5133" d="M351.64,461.555l-.03,6.72a6.183,6.183,0,0,0-1.991.47l.031-7.19a16.521,16.521,0,0,1,1.99,0" fill="#c2c2c2"/>
              <path id="Path_5134" data-name="Path 5134" d="M351.609,468.275,351.529,484a8,8,0,0,1-1.98-1.08l.07-14.17a6.145,6.145,0,0,1,1.99-.471" fill="#1a6a8e"/>
              <path id="Path_5135" data-name="Path 5135" d="M351.529,483.995l-.029,6.73a16.532,16.532,0,0,1-1.991-.611l.041-7.2a7.979,7.979,0,0,0,1.979,1.08" fill="#c2c2c2"/>
              <path id="Path_5136" data-name="Path 5136" d="M351.5,490.725l-.181,38.04-1.99-.2.181-38.45a16.274,16.274,0,0,0,1.99.61" fill="#1a6a8e"/>
              <path id="Path_5137" data-name="Path 5137" d="M349.84,422.364l-.19,39.19a14.262,14.262,0,0,0-3.41.611l.2-40.58Z" fill="#2377a4"/>
              <path id="Path_5138" data-name="Path 5138" d="M349.649,461.555l-.03,7.19a5.959,5.959,0,0,0-3.43,3.98l.05-10.56a14.212,14.212,0,0,1,3.41-.61" fill="#dddfdf"/>
              <path id="Path_5139" data-name="Path 5139" d="M349.619,468.745l-.069,14.17a9,9,0,0,1-3.38-5.011l.019-5.179a5.959,5.959,0,0,1,3.43-3.98" fill="#2377a4"/>
              <path id="Path_5140" data-name="Path 5140" d="M349.55,482.915l-.04,7.2a18.1,18.1,0,0,1-3.391-1.659l.051-10.551a9.011,9.011,0,0,0,3.38,5.011" fill="#dddfdf"/>
              <path id="Path_5141" data-name="Path 5141" d="M349.51,490.114l-.181,38.45-3.4-.33.189-39.779a18.1,18.1,0,0,0,3.391,1.659" fill="#2377a4"/>
              <path id="Path_5142" data-name="Path 5142" d="M346.44,421.574v.011l-2.8.649v-1.3Z" fill="#0e5c77"/>
              <path id="Path_5143" data-name="Path 5143" d="M346.44,421.585l-.2,40.58a12.166,12.166,0,0,0-2.8,1.27l.2-41.2Z" fill="#3b93b2"/>
              <path id="Path_5144" data-name="Path 5144" d="M346.239,462.165l-.05,10.56a8.935,8.935,0,0,0-.35,2.559,10.057,10.057,0,0,0,.331,2.62l-.051,10.551a17.734,17.734,0,0,1-2.79-2.131l.11-22.889a12.2,12.2,0,0,1,2.8-1.27" fill="#fff"/>
              <path id="Path_5145" data-name="Path 5145" d="M346.19,472.725l-.021,5.18a10.112,10.112,0,0,1-.329-2.62,8.937,8.937,0,0,1,.35-2.56" fill="#3b93b2"/>
              <path id="Path_5146" data-name="Path 5146" d="M346.119,488.455l-.189,39.779-2.8-.279v-.04l.2-41.591a17.78,17.78,0,0,0,2.79,2.131" fill="#3b93b2"/>
              <path id="Path_5147" data-name="Path 5147" d="M343.64,420.935v1.3h-.01l-2.55-.68v-1.21Z" fill="#0e5c77"/>
              <path id="Path_5148" data-name="Path 5148" d="M343.64,422.235l-.2,41.2a11.036,11.036,0,0,0-2.571,2.18l.211-44.06,2.55.68Z" fill="#2a82a8"/>
              <path id="Path_5149" data-name="Path 5149" d="M343.44,463.435l-.111,22.89a17.448,17.448,0,0,1-2.549-2.97l.09-17.74a10.99,10.99,0,0,1,2.57-2.18" fill="#dddfdf"/>
              <path id="Path_5150" data-name="Path 5150" d="M343.329,486.324l-.2,41.591v.04l-2.561-.25.21-44.351a17.467,17.467,0,0,0,2.55,2.97" fill="#2a82a8"/>
              <path id="Path_5151" data-name="Path 5151" d="M322.989,416.185l-3.189-.729L317.839,415l-.01-.009-3.38-.771-2.779-.64-2.721-.629-1.98-.451h-.009V412.5l-3.381-.771-2.77-.64-3.6-.83-1.971-.45-3.37-.78h-.01l-2.78-.64-4.51-1.03v-.01l-1.89-.43-.08-.08v.06l-3.38-.78-2.78-.64-3.51-.8-2.03-.469-3.389-.781-.041-.01-2.83-.65-3.719-.86-2.011-.46-3.909-.9h-.011l-1.979-.45v-.01l-5.171-1.18-1.2-.28-.07-.02-3.96-.91h-.01l-2.65-.61-4.65-1.07-1.19-.269-3.78-.871-.21-.05-2.35-.54v-3l113.56,26.52-.24,1.03-.01,1.51-1.99-.45v-.01l-3.39-.77v-.01l-2.8-.64-1.769-.41-1.971-.45h-.01l-3.36-.77-.01-.01Z" fill="#2377a4"/>
              <path id="Path_5152" data-name="Path 5152" d="M341.079,420.345v1.21h-.01l-1.979-1.66Z" fill="#0e5c77"/>
              <path id="Path_5153" data-name="Path 5153" d="M341.079,421.555l-.21,44.06a10.936,10.936,0,0,0-2.01,3.31l.231-49.03,1.979,1.66Z" fill="#1a6a8e"/>
              <path id="Path_5154" data-name="Path 5154" d="M340.869,465.614l-.09,17.74a16.782,16.782,0,0,1-1.969-3.9l.049-10.53a10.928,10.928,0,0,1,2.01-3.311" fill="#c2c2c2"/>
              <path id="Path_5155" data-name="Path 5155" d="M340.779,483.355l-.21,44.351-1.99-.2.23-48.049a16.865,16.865,0,0,0,1.97,3.9" fill="#1a6a8e"/>
              <path id="Path_5156" data-name="Path 5156" d="M340.77,533.114v7.35c-7.8-1.7-4.091-5.48-3.841-5.719l.01-.011Z" fill="#0e5c77"/>
              <path id="Path_5157" data-name="Path 5157" d="M340.77,533.115l-3.83,1.62-10.47-1.069v-1.881Z" fill="#003a49"/>
              <path id="Path_5158" data-name="Path 5158" d="M340.77,540.465l-14.3-1.2v-.1l10.46-4.42c-.25.24-3.96,4.02,3.841,5.72" fill="#003a49"/>
              <path id="Path_5159" data-name="Path 5159" d="M339.09,419.885v.01l-.23,49.03a13.718,13.718,0,0,0-.951,5.17v.019l-2.469-.009.02-4.1.239-50.891Z" fill="#2377a4"/>
              <path id="Path_5160" data-name="Path 5160" d="M338.859,468.925l-.05,10.53a16.4,16.4,0,0,1-.9-5.341V474.1a13.721,13.721,0,0,1,.95-5.17" fill="#dddfdf"/>
              <path id="Path_5161" data-name="Path 5161" d="M338.81,479.455l-.23,48.05-3.4-.34.26-53.061,2.47.01a16.4,16.4,0,0,0,.9,5.341" fill="#2377a4"/>
              <path id="Path_5162" data-name="Path 5162" d="M336.94,534.735l-.01.011-10.46,4.42v-5.5Z" fill="#002730"/>
              <path id="Path_5163" data-name="Path 5163" d="M335.7,419.1v.01l-2.81.64.01-1.29Z" fill="#0e5c77"/>
              <path id="Path_5164" data-name="Path 5164" d="M335.7,419.114l-.239,50.891h-.321a11.249,11.249,0,0,0-2.45-7.161l.2-43.089Z" fill="#3b93b2"/>
              <path id="Path_5165" data-name="Path 5165" d="M335.46,470.005l-.021,4.1-.259,53.06-2.8-.28.239-50.43a8.449,8.449,0,0,0,2.521-6.45Z" fill="#3b93b2"/>
              <path id="Path_5166" data-name="Path 5166" d="M332.69,462.845a11.246,11.246,0,0,1,2.449,7.16,8.448,8.448,0,0,1-2.519,6.45Z" fill="#fff"/>
              <path id="Path_5167" data-name="Path 5167" d="M332.9,418.465l-.01,1.29-1.78-.37.02-1.33Z" fill="#0e5c77"/>
              <path id="Path_5168" data-name="Path 5168" d="M332.89,419.755l-.2,43.09a11.935,11.935,0,0,0-2.16-2.1l.58-41.36Z" fill="#2a82a8"/>
              <path id="Path_5169" data-name="Path 5169" d="M332.69,462.845l-.07,13.61a7.763,7.763,0,0,1-2.34,1.48l.25-17.19a12,12,0,0,1,2.16,2.1" fill="#dddfdf"/>
              <path id="Path_5170" data-name="Path 5170" d="M332.619,476.455l-.239,50.43-2.781-.271.68-48.68a7.768,7.768,0,0,0,2.34-1.479" fill="#2a82a8"/>
              <path id="Path_5171" data-name="Path 5171" d="M331.13,418.055l-.02,1.33-1.951-1.78Z" fill="#0e5c77"/>
              <path id="Path_5172" data-name="Path 5172" d="M331.109,419.385l-.58,41.36a14.321,14.321,0,0,0-1.97-1.24l.59-41.9h.01Z" fill="#1a6a8e"/>
              <path id="Path_5173" data-name="Path 5173" d="M330.529,460.745l-.25,17.189a9.819,9.819,0,0,1-1.99.6l.27-19.029a14.321,14.321,0,0,1,1.97,1.24" fill="#c2c2c2"/>
              <path id="Path_5174" data-name="Path 5174" d="M330.279,477.935l-.68,48.68-1.99-.2.68-47.88a9.821,9.821,0,0,0,1.99-.6" fill="#1a6a8e"/>
              <path id="Path_5175" data-name="Path 5175" d="M329.149,417.605l-.59,41.9a17.5,17.5,0,0,0-3.37-1.31l.58-41.36h.02Z" fill="#2377a4"/>
              <path id="Path_5176" data-name="Path 5176" d="M328.56,459.505l-.271,19.029a15.161,15.161,0,0,1-3.389.231l.09-6.66c1.48-.411,2.21-1.56,2.21-3.3a4.373,4.373,0,0,0-2.11-3.95l.1-6.661a17.525,17.525,0,0,1,3.37,1.311" fill="#dddfdf"/>
              <path id="Path_5177" data-name="Path 5177" d="M328.289,478.534l-.68,47.881-3.389-.341.679-47.31a15.226,15.226,0,0,0,3.39-.23" fill="#2377a4"/>
              <path id="Path_5178" data-name="Path 5178" d="M325.09,464.855a4.372,4.372,0,0,1,2.109,3.95c0,1.74-.729,2.89-2.209,3.3Z" fill="#2377a4"/>
              <path id="Path_5179" data-name="Path 5179" d="M325.779,416.824l-.01.011-2.79.6.01-1.25Z" fill="#0e5c77"/>
              <path id="Path_5180" data-name="Path 5180" d="M325.77,416.835l-.581,41.359a19.255,19.255,0,0,0-2.38-.5l-.4-.059.57-40.2Z" fill="#3b93b2"/>
              <path id="Path_5181" data-name="Path 5181" d="M325.19,458.194l-.1,6.66a7.355,7.355,0,0,0-2.761-.939h-.01l.09-6.28.4.059a19.294,19.294,0,0,1,2.38.5" fill="#fff"/>
              <path id="Path_5182" data-name="Path 5182" d="M325.09,464.855l-.1,7.25a6.27,6.27,0,0,1-2.67.08l-.121-.019.121-8.25h.009a7.332,7.332,0,0,1,2.761.939" fill="#3b93b2"/>
              <path id="Path_5183" data-name="Path 5183" d="M324.989,472.105l-.09,6.66c-.67-.04-1.38-.11-2.11-.22l-.68-.1.09-6.279.12.019a6.269,6.269,0,0,0,2.67-.08" fill="#fff"/>
              <path id="Path_5184" data-name="Path 5184" d="M324.9,478.765l-.68,47.31-2.78-.27.67-47.36.68.1c.73.11,1.44.18,2.11.22" fill="#3b93b2"/>
              <path id="Path_5185" data-name="Path 5185" d="M322.989,416.185l-.01,1.25-3.17-.5-.03-.03.021-1.449Z" fill="#0e5c77"/>
              <path id="Path_5186" data-name="Path 5186" d="M322.979,417.435l-.57,40.2-3.19-.48.56-40.25.03.03Z" fill="#2a82a8"/>
              <path id="Path_5187" data-name="Path 5187" d="M322.409,457.635l-.09,6.28-3.189-.48.09-6.28Z" fill="#dddfdf"/>
              <path id="Path_5188" data-name="Path 5188" d="M322.319,463.915l-.12,8.25-3.19-.48.121-8.25Z" fill="#2a82a8"/>
              <path id="Path_5189" data-name="Path 5189" d="M322.2,472.165l-.09,6.279-3.18-.469.081-6.29Z" fill="#dddfdf"/>
              <path id="Path_5190" data-name="Path 5190" d="M322.109,478.444l-.67,47.36-3.18-.32.67-47.51Z" fill="#2a82a8"/>
              <path id="Path_5191" data-name="Path 5191" d="M320.94,531.455v7.34s-5.7-1.84-3.94-5.67v-.011Z" fill="#0e5c77"/>
              <path id="Path_5192" data-name="Path 5192" d="M320.94,531.455,317,533.114l-12.9-.83V529.9Z" fill="#003a49"/>
              <path id="Path_5193" data-name="Path 5193" d="M320.94,538.795l-14.479-1.21L317,533.125c-1.76,3.83,3.94,5.67,3.94,5.67" fill="#003a49"/>
              <path id="Path_5194" data-name="Path 5194" d="M319.8,415.455l-.021,1.449-1.939-1.9Z" fill="#0e5c77"/>
              <path id="Path_5195" data-name="Path 5195" d="M319.779,416.9l-.56,40.25-1.98-.3.59-41.859.01.01Z" fill="#1a6a8e"/>
              <path id="Path_5196" data-name="Path 5196" d="M319.22,457.154l-.09,6.28-1.17-.17-.011,8.26,1.061.16-.08,6.29-.981-.15-.009,7.381-1.1-.171.4-28.18Z" fill="#c2c2c2"/>
              <path id="Path_5197" data-name="Path 5197" d="M319.13,463.435l-.12,8.25-1.061-.16.011-8.26Z" fill="#1a6a8e"/>
              <path id="Path_5198" data-name="Path 5198" d="M318.93,477.975l-.67,47.51-1.98-.2v-.03l.56-40.22,1.1.17.01-7.38Z" fill="#1a6a8e"/>
              <path id="Path_5199" data-name="Path 5199" d="M317.829,415l-.59,41.859-3.39-.509.6-42.12Z" fill="#2377a4"/>
              <path id="Path_5200" data-name="Path 5200" d="M317.239,456.855l-.4,28.18-3.379-.51.389-28.18Z" fill="#dddfdf"/>
              <path id="Path_5201" data-name="Path 5201" d="M317,533.115v.011l-10.54,4.46h-.011l-2.349-.2v-5.1Z" fill="#002730"/>
              <path id="Path_5202" data-name="Path 5202" d="M316.84,485.034l-.56,40.221v.029l-3.39-.329.57-40.431Z" fill="#2377a4"/>
              <path id="Path_5203" data-name="Path 5203" d="M314.449,414.225l-.6,42.12-2.78-.42.58-40.94.021.01Z" fill="#3b93b2"/>
              <path id="Path_5204" data-name="Path 5204" d="M314.449,414.225l-2.779.77-.021-.01.021-1.4Z" fill="#0e5c77"/>
              <path id="Path_5205" data-name="Path 5205" d="M313.85,456.345l-.39,28.18-2.79-.42.4-28.18Z" fill="#fff"/>
              <path id="Path_5206" data-name="Path 5206" d="M313.46,484.525l-.57,40.431-2.79-.281.57-40.57Z" fill="#3b93b2"/>
              <path id="Path_5207" data-name="Path 5207" d="M311.67,413.585l-.021,1.4-2.719-.67.019-1.359Z" fill="#0e5c77"/>
              <path id="Path_5208" data-name="Path 5208" d="M311.649,414.985l-.58,40.94-.96-.14-1.76-.02.58-41.45Z" fill="#2a82a8"/>
              <path id="Path_5209" data-name="Path 5209" d="M311.069,455.925l-.4,28.18-.581-.09.02-28.231Z" fill="#dddfdf"/>
              <path id="Path_5210" data-name="Path 5210" d="M310.67,484.1l-.57,40.57-2.72-.27.019-.159.951-68.481,1.76.02-.02,28.23Z" fill="#2a82a8"/>
              <path id="Path_5211" data-name="Path 5211" d="M308.949,412.955l-.02,1.359-1.959-1.809Z" fill="#0e5c77"/>
              <path id="Path_5212" data-name="Path 5212" d="M308.93,414.315l-.58,41.45-.95,68.481-.02.159-1.96-.19.57-41.009.97-70.7h.01Z" fill="#1a6a8e"/>
              <path id="Path_5213" data-name="Path 5213" d="M303.579,411.975v-.25l3.381.77v.01l-.971,70.7-1.45-.02-.05-28.25-1.5-.22Z" fill="#2377a4"/>
              <path id="Path_5214" data-name="Path 5214" d="M305.989,483.205l-.569,41.01-3.381-.34.56-40.981,1.94.29Z" fill="#2377a4"/>
              <path id="Path_5215" data-name="Path 5215" d="M304.489,454.935l.05,28.25-1.94-.29.39-28.18Z" fill="#dddfdf"/>
              <path id="Path_5216" data-name="Path 5216" d="M303.579,411.725v.25l-2.76.57h-.03l.02-1.46Z" fill="#0e5c77"/>
              <path id="Path_5217" data-name="Path 5217" d="M300.819,412.545l2.76-.57-.59,42.74-2.779-.42.579-41.75Z" fill="#3b93b2"/>
              <path id="Path_5218" data-name="Path 5218" d="M302.989,454.715l-.39,28.18-2.78-.42.391-28.18Z" fill="#fff"/>
              <path id="Path_5219" data-name="Path 5219" d="M302.6,482.895l-.56,40.98-2.79-.28.57-41.12Z" fill="#3b93b2"/>
              <path id="Path_5220" data-name="Path 5220" d="M300.81,411.085l-.021,1.46-3.569-.57-.03-.031.02-1.689Z" fill="#0e5c77"/>
              <path id="Path_5221" data-name="Path 5221" d="M300.789,412.545l-.579,41.75-2.191-.33-1.429,2.229.6-44.25.031.031Z" fill="#2a82a8"/>
              <path id="Path_5222" data-name="Path 5222" d="M300.21,454.3l-.391,28.18-2.5-.38-.059-14.97-.84,1.3.17-12.231,1.43-2.229Z" fill="#dddfdf"/>
              <path id="Path_5223" data-name="Path 5223" d="M299.819,482.475l-.569,41.12-3.571-.35.741-54.82.839-1.3.06,14.97Z" fill="#2a82a8"/>
              <path id="Path_5224" data-name="Path 5224" d="M297.21,410.255l-.021,1.689-1.95-2.12v-.019Z" fill="#0e5c77"/>
              <path id="Path_5225" data-name="Path 5225" d="M297.19,411.944l-.6,44.25-2.03,3.16.68-49.53Z" fill="#1a6a8e"/>
              <path id="Path_5226" data-name="Path 5226" d="M294.56,459.355l2.03-3.16-.17,12.23-2.02,3.14Z" fill="#c2c2c2"/>
              <path id="Path_5227" data-name="Path 5227" d="M296.42,468.425l-.74,54.82-1.981-.2.7-51.48Z" fill="#1a6a8e"/>
              <path id="Path_5228" data-name="Path 5228" d="M295.239,409.8v.02l-.68,49.53-3.46,5.4.76-55.73h.01Z" fill="#2377a4"/>
              <path id="Path_5229" data-name="Path 5229" d="M291.1,464.755l3.46-5.4-.16,12.21-3.47,5.37Z" fill="#dddfdf"/>
              <path id="Path_5230" data-name="Path 5230" d="M294.4,471.565l-.7,51.48-3.39-.33.62-45.78Z" fill="#2377a4"/>
              <path id="Path_5231" data-name="Path 5231" d="M291.859,409.025l-.76,55.73-2.61,4.07-.23-.42.8-58.58Z" fill="#3b93b2"/>
              <path id="Path_5232" data-name="Path 5232" d="M291.859,409.025l-2.8.8.02-1.44Z" fill="#0e5c77"/>
              <path id="Path_5233" data-name="Path 5233" d="M288.489,468.824l2.61-4.069-.17,12.179-.83,1.29-1.969-.3.13-9.521Z" fill="#fff"/>
              <path id="Path_5234" data-name="Path 5234" d="M290.93,476.935l-.62,45.78-2.79-.28.61-44.51,1.97.3Z" fill="#3b93b2"/>
              <path id="Path_5235" data-name="Path 5235" d="M289.079,408.385l-.02,1.439-4.48-.989-.029-.03.019-1.45Z" fill="#0e5c77"/>
              <path id="Path_5236" data-name="Path 5236" d="M289.06,409.824l-.8,58.58-4.4-8.01.69-51.59.03.031Z" fill="#2a82a8"/>
              <path id="Path_5237" data-name="Path 5237" d="M283.859,460.395l4.4,8.01-.13,9.52-1.52-.23-2.91-5.27Z" fill="#dddfdf"/>
              <path id="Path_5238" data-name="Path 5238" d="M288.13,477.925l-.61,44.51-4.481-.44.66-49.57,2.911,5.27Z" fill="#2a82a8"/>
              <path id="Path_5239" data-name="Path 5239" d="M284.569,407.345v.01l-.02,1.45-1.87-1.89Z" fill="#0e5c77"/>
              <path id="Path_5240" data-name="Path 5240" d="M284.55,408.8l-.69,51.59-1.93-3.52.67-49.98.08.02Z" fill="#1a6a8e"/>
              <path id="Path_5241" data-name="Path 5241" d="M283.859,460.395l-.16,12.03-1.93-3.5.16-12.05Z" fill="#c2c2c2"/>
              <path id="Path_5242" data-name="Path 5242" d="M283.7,472.425l-.66,49.57-1.98-.2.71-52.87Z" fill="#1a6a8e"/>
              <path id="Path_5243" data-name="Path 5243" d="M282.6,406.895l-.67,49.98-3.19-5.81-.13-.02.61-44.72v-.21Z" fill="#2377a4"/>
              <path id="Path_5244" data-name="Path 5244" d="M278.739,451.065l3.19,5.811-.16,12.049-2.25-4.07-.009,14.561-1.271-.191.37-28.18Z" fill="#dddfdf"/>
              <path id="Path_5245" data-name="Path 5245" d="M281.77,468.925l-.71,52.87-3.39-.34.57-42.23,1.27.19.01-14.56Z" fill="#2377a4"/>
              <path id="Path_5246" data-name="Path 5246" d="M279.22,406.115v.21h-.01l-2.79.511.02-1.361Z" fill="#0e5c77"/>
              <path id="Path_5247" data-name="Path 5247" d="M279.22,406.324l-.61,44.721-2.781-.42.591-43.79,2.79-.511Z" fill="#3b93b2"/>
              <path id="Path_5248" data-name="Path 5248" d="M278.609,451.045l-.37,28.18-2.79-.42.38-28.18Z" fill="#fff"/>
              <path id="Path_5249" data-name="Path 5249" d="M278.239,479.225l-.569,42.23-2.79-.27.569-42.38Z" fill="#3b93b2"/>
              <path id="Path_5250" data-name="Path 5250" d="M276.44,405.475l-.02,1.36-3.52-.73.03-1.43Z" fill="#0e5c77"/>
              <path id="Path_5251" data-name="Path 5251" d="M276.42,406.835l-.591,43.79-3.55-.54-.309-.011.929-43.969Z" fill="#2a82a8"/>
              <path id="Path_5252" data-name="Path 5252" d="M275.829,450.625l-.38,28.18-3.2-.491.029-28.229Z" fill="#dddfdf"/>
              <path id="Path_5253" data-name="Path 5253" d="M272.25,478.315l3.2.49-.569,42.38-4.4-.439,1.491-70.671.309.011Z" fill="#2a82a8"/>
              <path id="Path_5254" data-name="Path 5254" d="M272.93,404.675l-.03,1.43h-.01l-1.88-1.8-.11-.1Z" fill="#0e5c77"/>
              <path id="Path_5255" data-name="Path 5255" d="M272.9,406.1l-.93,43.97-1.49,70.671-2.019-.2.9-43.01,1.54-73.11.11-.12,1.88,1.8Z" fill="#1a6a8e"/>
              <path id="Path_5256" data-name="Path 5256" d="M271.01,404.3l-.11.12-1.54,73.11-2.7-.06.031-28.23-.17-.03.95-45.77.04-.02,3.39.78Z" fill="#2377a4"/>
              <path id="Path_5257" data-name="Path 5257" d="M269.359,477.534l-.9,43.011-3.421-.34.89-42.841.73.11Z" fill="#2377a4"/>
              <path id="Path_5258" data-name="Path 5258" d="M267.47,403.415v.029l-2.83,1.011-.03-.011.03-1.67v-.009Z" fill="#0e5c77"/>
              <path id="Path_5259" data-name="Path 5259" d="M267.47,403.444l-.95,45.77-2.83-.419.92-44.351.03.011Z" fill="#3b93b2"/>
              <path id="Path_5260" data-name="Path 5260" d="M266.69,449.245l-.03,28.23-.73-.111.59-28.149Z" fill="#dddfdf"/>
              <path id="Path_5261" data-name="Path 5261" d="M266.52,449.215l-.59,28.15-2.82-.42.58-28.15Z" fill="#fff"/>
              <path id="Path_5262" data-name="Path 5262" d="M265.93,477.365l-.891,42.841-2.809-.28.88-42.981Z" fill="#3b93b2"/>
              <path id="Path_5263" data-name="Path 5263" d="M264.64,402.765v.01l-.03,1.67-3.68-1.02-.05-.05.04-1.47Z" fill="#0e5c77"/>
              <path id="Path_5264" data-name="Path 5264" d="M264.609,404.444l-.92,44.351-3.92-.59,1.11-44.83.05.049Z" fill="#2a82a8"/>
              <path id="Path_5265" data-name="Path 5265" d="M263.69,448.8l-.58,28.149-4.04-.609.7-28.13Z" fill="#dddfdf"/>
              <path id="Path_5266" data-name="Path 5266" d="M263.109,476.944l-.88,42.98L258,519.505l1.069-43.17Z" fill="#2a82a8"/>
              <path id="Path_5267" data-name="Path 5267" d="M260.92,401.9l-.04,1.471-1.971-1.931Z" fill="#0e5c77"/>
              <path id="Path_5268" data-name="Path 5268" d="M260.88,403.375l-1.11,44.83-.941-.141L258.81,476.3l.259.04L258,519.505l-1.971-.191v-.009l2.88-117.861Z" fill="#1a6a8e"/>
              <path id="Path_5269" data-name="Path 5269" d="M259.77,448.205l-.7,28.13-.26-.04.02-28.231Z" fill="#c2c2c2"/>
              <path id="Path_5270" data-name="Path 5270" d="M258.909,401.444l-2.88,117.86v.01l-3.96-.4,2.92-118.361v-.009H255Z" fill="#2377a4"/>
              <path id="Path_5271" data-name="Path 5271" d="M254.989,400.545v.01l-2.02,1.12.041-1.58Z" fill="#0e5c77"/>
              <path id="Path_5272" data-name="Path 5272" d="M254.989,400.555l-2.92,118.36-1.97-.19,2.87-117.05Z" fill="#3b93b2"/>
              <path id="Path_5273" data-name="Path 5273" d="M253.01,400.085v.01l-.04,1.58-.05.03-5.13-1.4.05-1.4Z" fill="#0e5c77"/>
              <path id="Path_5274" data-name="Path 5274" d="M252.97,401.675l-2.87,117.05-6.24-.62,3.93-117.8,5.13,1.4Z" fill="#2a82a8"/>
              <path id="Path_5275" data-name="Path 5275" d="M247.839,398.9l-.05,1.4-.06-.02-1.09-1.659Z" fill="#0e5c77"/>
              <path id="Path_5276" data-name="Path 5276" d="M247.79,400.3l-3.93,117.8-1.25-.13,3.96-119.37.07.02,1.09,1.66Z" fill="#1a6a8e"/>
              <path id="Path_5277" data-name="Path 5277" d="M246.569,398.6l-3.96,119.37-3.95-.39v-.01l3.95-119.88Z" fill="#2377a4"/>
              <path id="Path_5278" data-name="Path 5278" d="M242.609,397.694l-3.95,119.88v.011l-2.67-.261,3.92-118.86,2.69-.77Z" fill="#3b93b2"/>
              <path id="Path_5279" data-name="Path 5279" d="M242.6,397.694l-2.69.77.04-1.379Z" fill="#0e5c77"/>
              <path id="Path_5280" data-name="Path 5280" d="M239.95,397.085l-.04,1.38-4.65-1.061.04-1.389Z" fill="#0e5c77"/>
              <path id="Path_5281" data-name="Path 5281" d="M239.91,398.465l-3.92,118.859-4.68-.469.01-.12,3.94-119.33Z" fill="#2a82a8"/>
              <path id="Path_5282" data-name="Path 5282" d="M235.3,396.015l-.04,1.39h-.01l-1.14-1.66Z" fill="#0e5c77"/>
              <path id="Path_5283" data-name="Path 5283" d="M235.26,397.4l-3.941,119.33-.009.12-1.19-.12,3.99-120.989,1.14,1.659Z" fill="#1a6a8e"/>
              <path id="Path_5284" data-name="Path 5284" d="M234.109,395.745l-3.99,120.989-3.76-.37,3.97-121.489Z" fill="#2377a4"/>
              <path id="Path_5285" data-name="Path 5285" d="M230.33,394.875l-3.97,121.489-2.33-.229,3.96-120.39h.02l2.11-.921Z" fill="#3b93b2"/>
              <path id="Path_5286" data-name="Path 5286" d="M227.99,395.745l-3.96,120.39-1.82-.18,4.14-117.75,1.42-.841v-3.08l2.35.54-2.11.921Z" fill="#1a6a8e"/>
              <path id="Path_5287" data-name="Path 5287" d="M227.77,391.285v6.08l-6.421-2.08.171-6.67,6.25,1.589Zm-1.75,1.619c0-1.31-.651-2.369-1.461-2.369s-1.46,1.059-1.46,2.369.66,2.381,1.46,2.381,1.461-1.06,1.461-2.381" fill="#2a82a8"/>
              <path id="Path_5288" data-name="Path 5288" d="M227.77,397.365l-1.42.841-5-1.591-.51.35-1.82,1.241-1.75-.25,4.08-2.671Z" fill="#0e5c77"/>
              <path id="Path_5289" data-name="Path 5289" d="M224.56,390.535c.81,0,1.46,1.06,1.46,2.37s-.65,2.38-1.46,2.38-1.46-1.06-1.46-2.38.66-2.37,1.46-2.37" fill="#003a49"/>
              <path id="Path_5290" data-name="Path 5290" d="M222.77,523.865l-.121,6.67h-7v-7.08Zm-1.5,3.32a1.8,1.8,0,1,0-1.75,2.22,2.033,2.033,0,0,0,1.75-2.22" fill="#2a82a8"/>
              <path id="Path_5291" data-name="Path 5291" d="M222.439,399.455l.03,5.3h-2.08v-5.67l.45.08Z" fill="#196c89"/>
              <path id="Path_5292" data-name="Path 5292" d="M222.47,404.755l-.33.64h-1.75l-1.66.719.02-.679,1.64-.68Z" fill="#0e5b70"/>
              <path id="Path_5293" data-name="Path 5293" d="M222.01,387.875l-1.29,1.19-1.32.74a.86.86,0,0,0-.94.53l-1.52.85a2.841,2.841,0,0,1,2.93-2.37l1.48-1.49Z" fill="#999"/>
              <path id="Path_5294" data-name="Path 5294" d="M221.52,388.615l-.171,6.67-4.079,2.67-.421-5.5-.079-1.17.009-.01a2.118,2.118,0,0,0,.07.45v.01a1.661,1.661,0,0,0,.26.59,1.793,1.793,0,0,0,.86.71,2.767,2.767,0,0,0-.03.42c0,1.01.48,1.83,1.081,1.83s1.079-.82,1.079-1.83a2.729,2.729,0,0,0-.12-.83,1.917,1.917,0,0,0-.24-.54,1.14,1.14,0,0,0-.34-.35.681.681,0,0,0-.379-.12.435.435,0,0,0-.191.04.808.808,0,0,1-.47-.9,1.2,1.2,0,0,1,.1-.42l.94-.53,1.32-.74Z" fill="#2377a4"/>
              <path id="Path_5295" data-name="Path 5295" d="M221.35,387.325l-1.48,1.49a2.841,2.841,0,0,0-2.93,2.37l-.16.09a3.236,3.236,0,0,1,.1-.89c.66-2.77,2.79-2.28,2.79-2.28l1.17-1.2Z" fill="#c2c2c2"/>
              <path id="Path_5296" data-name="Path 5296" d="M221.069,435.654l.03,5.3h-2.08v-5.671Z" fill="#196c89"/>
              <path id="Path_5297" data-name="Path 5297" d="M221.1,440.955l-.33.64h-1.75l-1.63.71.03-.691,1.6-.659Z" fill="#0e5b70"/>
              <path id="Path_5298" data-name="Path 5298" d="M219.52,524.955a2.289,2.289,0,1,1-1.75,2.23,2.033,2.033,0,0,1,1.75-2.23" fill="#003a49"/>
              <path id="Path_5299" data-name="Path 5299" d="M220.839,396.965v2.2l-.45-.08-1.43.75.06-1.63Z" fill="#196c89"/>
              <path id="Path_5300" data-name="Path 5300" d="M220.39,399.085v5.67l-1.64.68.21-5.6Z" fill="#2377a4"/>
              <path id="Path_5301" data-name="Path 5301" d="M220.39,405.395l-1.37,29.89-1.39.73,1.1-29.9Z" fill="#196c89"/>
              <path id="Path_5302" data-name="Path 5302" d="M220.3,471.985l.03,5.28-2.09-.219V471.4Z" fill="#196c89"/>
              <path id="Path_5303" data-name="Path 5303" d="M220.33,477.265l-.33.6-1.76-.18-.78.26.03-.67.75-.23Z" fill="#0e5b70"/>
              <path id="Path_5304" data-name="Path 5304" d="M219.98,392.625a2.755,2.755,0,0,1,.12.83c0,1.01-.48,1.829-1.08,1.829s-1.081-.819-1.081-1.829a2.791,2.791,0,0,1,.03-.421,1.7,1.7,0,0,0,2.011-.409" fill="#003a49"/>
              <path id="Path_5305" data-name="Path 5305" d="M219.74,392.085a1.905,1.905,0,0,1,.24.54,1.7,1.7,0,0,1-2.01.409,2.34,2.34,0,0,1,.11-.489,2.323,2.323,0,0,0,1.66-.46" fill="#404040"/>
              <path id="Path_5306" data-name="Path 5306" d="M219.4,391.735a1.133,1.133,0,0,1,.34.351,2.323,2.323,0,0,1-1.66.46,1.261,1.261,0,0,1,.75-.891,1.365,1.365,0,0,0,.57.08" fill="#999"/>
              <path id="Path_5307" data-name="Path 5307" d="M219.4,391.735a1.365,1.365,0,0,1-.57-.08.448.448,0,0,1,.19-.04.682.682,0,0,1,.38.12" fill="#003a49"/>
              <path id="Path_5308" data-name="Path 5308" d="M219.02,435.284v5.671l-1.6.659.21-5.6Z" fill="#2377a4"/>
              <path id="Path_5309" data-name="Path 5309" d="M219.02,398.205l-.06,1.63-.87.449.06-1.659Z" fill="#0e5b70"/>
              <path id="Path_5310" data-name="Path 5310" d="M219.02,441.595l-1.3,30.02-1.43.6,1.1-29.91Z" fill="#196c89"/>
              <path id="Path_5311" data-name="Path 5311" d="M189.029,412.8l-.98.479-25.03,12.181-.58.279-2.24-.689,2.24-1.141,54.83-25.949,1.75.25-.87.42Z" fill="#0e5b70"/>
              <path id="Path_5312" data-name="Path 5312" d="M218.96,399.835l-.21,5.6-.86.349.2-5.5Z" fill="#2377a4"/>
              <path id="Path_5313" data-name="Path 5313" d="M218.749,405.435l-.02.68-.87.381.03-.711Z" fill="#0e5b70"/>
              <path id="Path_5314" data-name="Path 5314" d="M217.859,406.5l.87-.381-1.1,29.9-.86.44.47-12.99Z" fill="#0e5b70"/>
              <path id="Path_5315" data-name="Path 5315" d="M216.939,391.185l1.52-.85a1.178,1.178,0,0,0-.1.42.808.808,0,0,0,.47.9,1.261,1.261,0,0,0-.75.89,3.251,3.251,0,0,1-.97-.22,1.653,1.653,0,0,1-.26-.59v-.01c.01-.06.03-.26.09-.54" fill="#999"/>
              <path id="Path_5316" data-name="Path 5316" d="M218.249,508.284l-.09,5.02-2.19-.25.13-5.37Z" fill="#196c89"/>
              <path id="Path_5317" data-name="Path 5317" d="M217.72,471.615l.52-.21v5.641l-.75.229-1.4.431.2-5.491Z" fill="#2377a4"/>
              <path id="Path_5318" data-name="Path 5318" d="M218.16,513.3l-.36.57-1.84-.21h-.01l.02-.61Z" fill="#0e5b70"/>
              <path id="Path_5319" data-name="Path 5319" d="M189.029,412.8l29.12-14.17-.06,1.659-4.26,2.221v4.96l1.12.3,2.91-1.27-.62,16.97-1.59-.061v-7.63l-27.26,11.63Z" fill="#196c89"/>
              <path id="Path_5320" data-name="Path 5320" d="M218.089,400.284l-.2,5.5-4.06,1.681v-4.96Z" fill="#2377a4"/>
              <path id="Path_5321" data-name="Path 5321" d="M218.08,392.545a2.34,2.34,0,0,0-.11.489,1.782,1.782,0,0,1-.86-.71,3.256,3.256,0,0,0,.97.221" fill="#404040"/>
              <path id="Path_5322" data-name="Path 5322" d="M217.89,405.784l-.03.711-2.91,1.269-1.12-.3Z" fill="#0e5b70"/>
              <path id="Path_5323" data-name="Path 5323" d="M217.629,436.015l-.21,5.6-.85.35.2-5.509Z" fill="#2377a4"/>
              <path id="Path_5324" data-name="Path 5324" d="M217.49,477.275l-.03.67-1.4.46.03-.7Z" fill="#0e5b70"/>
              <path id="Path_5325" data-name="Path 5325" d="M217.46,477.944l-1.36,29.74-1.13.4,1.09-29.681Z" fill="#196c89"/>
              <path id="Path_5326" data-name="Path 5326" d="M217.419,441.615l-.03.69-.85.371.03-.711Z" fill="#0e5b70"/>
              <path id="Path_5327" data-name="Path 5327" d="M217.39,442.3l-1.1,29.91-.84.35.65-17.73.44-12.16Z" fill="#0e5b70"/>
              <path id="Path_5328" data-name="Path 5328" d="M216.85,392.455l.42,5.5L162.44,423.9l.46-5.67Z" fill="#196a8e"/>
              <path id="Path_5329" data-name="Path 5329" d="M217.24,423.465l-.47,12.99-4.31,2.25v4.96l1.12.3,2.96-1.29-.44,12.16-1.45-.051-.66.231.04-1.52-27.19,9.17,1.23-28.01,27.58-11.25Z" fill="#196c89"/>
              <path id="Path_5330" data-name="Path 5330" d="M216.779,391.275l.16-.09c-.06.28-.08.48-.09.54a2.118,2.118,0,0,1-.07-.45" fill="#c2c2c2"/>
              <path id="Path_5331" data-name="Path 5331" d="M212.46,438.705l4.31-2.25-.2,5.51-4.11,1.7Z" fill="#2377a4"/>
              <path id="Path_5332" data-name="Path 5332" d="M216.569,441.965l-.03.71-2.96,1.29-1.12-.3Z" fill="#0e5b70"/>
              <path id="Path_5333" data-name="Path 5333" d="M216.29,472.215l-.2,5.49-.84.26.2-5.4Z" fill="#2377a4"/>
              <path id="Path_5334" data-name="Path 5334" d="M216.1,507.685l-.13,5.37-1.2.31.2-5.28Z" fill="#2377a4"/>
              <path id="Path_5335" data-name="Path 5335" d="M216.1,454.835l-.65,17.729-3.8,1.571v4.929l1.13.42,2.45-.809-.52,14.139-1.63-.059-.66.18.05-1.47-27.21,7.42,1.26-28.771,27.31-8.769.16-6.33.66-.231Z" fill="#196c89"/>
              <path id="Path_5336" data-name="Path 5336" d="M216.089,477.705l-.03.7-.83.271.02-.71Z" fill="#0e5b70"/>
              <path id="Path_5337" data-name="Path 5337" d="M216.06,478.4l-1.09,29.681-.83.29.57-15.561.52-14.14Z" fill="#0e5b70"/>
              <path id="Path_5338" data-name="Path 5338" d="M215.97,513.055l-.02.61-1.2.33.02-.63Z" fill="#0e5b70"/>
              <path id="Path_5339" data-name="Path 5339" d="M215.95,513.665l-.3,9.79-3.63,1.909v5.91l-1.25.26-55.5,10-.12-6.369h-1.24l-.66.13.5-8,.08.05,1.73-.441-.19,2.741,58.46-12.73.82-.181.07-2.029.03-.71Z" fill="#196a8e"/>
              <path id="Path_5340" data-name="Path 5340" d="M215.649,523.455v7.079l-3.63.741v-5.91Zm-1,4.18c0-1.12-.4-2.03-.88-2.03s-.88.91-.88,2.03.4,2.03.88,2.03.88-.91.88-2.03" fill="#2377a4"/>
              <path id="Path_5341" data-name="Path 5341" d="M215.649,417.215v6.19l-27.58,11.25.24-5.56Z" fill="#1d7399"/>
              <path id="Path_5342" data-name="Path 5342" d="M215.649,415.775v1.44l-27.34,11.88.08-1.69Z" fill="#0e5b70"/>
              <path id="Path_5343" data-name="Path 5343" d="M215.45,472.565l-.2,5.4-3.6,1.1v-4.93Z" fill="#2377a4"/>
              <path id="Path_5344" data-name="Path 5344" d="M215.249,477.965l-.02.71-2.45.81-1.13-.42Z" fill="#0e5b70"/>
              <path id="Path_5345" data-name="Path 5345" d="M214.97,508.085l-.2,5.279-.82.21.19-5.2Z" fill="#2377a4"/>
              <path id="Path_5346" data-name="Path 5346" d="M214.77,513.365l-.02.631-.82.229.02-.65Z" fill="#0e5b70"/>
              <path id="Path_5347" data-name="Path 5347" d="M214.749,514l-.03.71-.82.18.03-.661Z" fill="#0e5b70"/>
              <path id="Path_5348" data-name="Path 5348" d="M214.72,514.705l-.07,2.029-.82.181.07-2.03Z" fill="#0e5b70"/>
              <path id="Path_5349" data-name="Path 5349" d="M214.71,492.815l-.57,15.561-5.02,1.759-.12,4.7,1.18.419,3.75-1.03-.03.66-29.63,6.59.65-14.92,27.26-6.6.03-.841.21-6.18.66-.18Z" fill="#196c89"/>
              <path id="Path_5350" data-name="Path 5350" d="M213.77,525.6c.479,0,.879.91.879,2.03s-.4,2.03-.879,2.03-.881-.91-.881-2.03.4-2.03.881-2.03" fill="#003a49"/>
              <path id="Path_5351" data-name="Path 5351" d="M214.14,508.375l-.19,5.2L209,514.835l.12-4.7Z" fill="#2377a4"/>
              <path id="Path_5352" data-name="Path 5352" d="M214.029,453.5l-.04,1.52-27.22,9.33.07-1.68Z" fill="#0e5b70"/>
              <path id="Path_5353" data-name="Path 5353" d="M213.99,455.015l-.16,6.33-27.31,8.77.25-5.77Z" fill="#1d7399"/>
              <path id="Path_5354" data-name="Path 5354" d="M213.95,513.574l-.02.65-3.75,1.031-1.18-.42Z" fill="#0e5b70"/>
              <path id="Path_5355" data-name="Path 5355" d="M213.9,514.885l-.07,2.03-58.46,12.73,1.23-2.01,26.56-5.91,1.11-.25Z" fill="#0e5b70"/>
              <path id="Path_5356" data-name="Path 5356" d="M212.47,491.465l-.05,1.47-27.23,7.59.07-1.64Z" fill="#0e5b70"/>
              <path id="Path_5357" data-name="Path 5357" d="M185.189,500.525l27.23-7.59-.21,6.18-27.25,6.62Z" fill="#1d7399"/>
              <path id="Path_5358" data-name="Path 5358" d="M212.21,499.115l-.03.841-27.26,6.6.04-.82Z" fill="#186577"/>
              <path id="Path_5359" data-name="Path 5359" d="M189.029,412.8l-.64,14.609-1,.431.66-14.561Z" fill="#003a49"/>
              <path id="Path_5360" data-name="Path 5360" d="M188.39,427.4l-.08,1.69-.99.44.07-1.7Z" fill="#003a49"/>
              <path id="Path_5361" data-name="Path 5361" d="M188.31,429.095l-.24,5.56-1,.41.25-5.531Z" fill="#003a49"/>
              <path id="Path_5362" data-name="Path 5362" d="M188.069,434.654l-1.23,28.011-1.03.349,1.26-27.95Z" fill="#003a49"/>
              <path id="Path_5363" data-name="Path 5363" d="M188.049,413.275l-.66,14.561L162.9,438.285l-.46,6.83-.65-.04.87-13.89,2.61-1.38v-4.349h-.5l-1.81.949.06-.949Z" fill="#196c89"/>
              <path id="Path_5364" data-name="Path 5364" d="M187.39,427.835l-.07,1.7-22.05,9.58-2.37-.83Z" fill="#0e5b70"/>
              <path id="Path_5365" data-name="Path 5365" d="M187.319,429.534l-.25,5.53-21.8,8.9v-4.851Z" fill="#1d7399"/>
              <path id="Path_5366" data-name="Path 5366" d="M187.069,435.065l-1.26,27.95-24.35,8.21-1.31-.08.52-8.22,2.74-1.33v-4.36l-.5-.02-1.94.94.82-13.08.65.04,2.83-1.15Z" fill="#196c89"/>
              <path id="Path_5367" data-name="Path 5367" d="M186.839,462.665l-.07,1.68-1.04.36.08-1.69Z" fill="#003a49"/>
              <path id="Path_5368" data-name="Path 5368" d="M186.77,464.345l-.25,5.77-1.05.34.26-5.75Z" fill="#003a49"/>
              <path id="Path_5369" data-name="Path 5369" d="M186.52,470.115l-1.26,28.771-1.08.3,1.29-28.729Z" fill="#003a49"/>
              <path id="Path_5370" data-name="Path 5370" d="M185.81,463.015l-.08,1.691-22.83,7.829-1.44-1.31Z" fill="#0e5b70"/>
              <path id="Path_5371" data-name="Path 5371" d="M185.73,464.705l-.26,5.75-22.77,7.309.2-5.23Z" fill="#1d7399"/>
              <path id="Path_5372" data-name="Path 5372" d="M185.47,470.455l-1.29,28.729-25.68,7-.55-.03.74-11.859,2.77-1.031V488.9l-.51-.08-1.96.731,1.16-18.411,1.31.081-.31,7.039,1.55-.5Z" fill="#196c89"/>
              <path id="Path_5373" data-name="Path 5373" d="M185.26,498.885l-.07,1.64-1.09.3.08-1.639Z" fill="#003a49"/>
              <path id="Path_5374" data-name="Path 5374" d="M185.189,500.525l-.23,5.21-1.09.261.23-5.171Z" fill="#003a49"/>
              <path id="Path_5375" data-name="Path 5375" d="M184.96,505.735l-.04.82-1.09.27.04-.829Z" fill="#002730"/>
              <path id="Path_5376" data-name="Path 5376" d="M184.919,506.555l-.65,14.92-1.11.25.67-14.9Z" fill="#003a49"/>
              <path id="Path_5377" data-name="Path 5377" d="M184.18,499.185l-.08,1.64-24.14,6.72-1.46-1.36Z" fill="#0e5b70"/>
              <path id="Path_5378" data-name="Path 5378" d="M184.1,500.824,183.87,506l-24.17,5.869.26-4.319Z" fill="#1d7399"/>
              <path id="Path_5379" data-name="Path 5379" d="M183.87,506l-.04.829-25.73,6.231,1.6-1.191Z" fill="#186577"/>
              <path id="Path_5380" data-name="Path 5380" d="M183.83,506.824l-.67,14.9-26.56,5.911.06-1.01,2.78-.71.01-4.311-.51-.13-1.98.5.99-15.82.55.03-.4,6.87Z" fill="#196c89"/>
              <path id="Path_5381" data-name="Path 5381" d="M165.27,439.115v4.85l-2.83,1.15.46-6.83Z" fill="#186577"/>
              <path id="Path_5382" data-name="Path 5382" d="M165.27,425.455v4.35l-.5-.491v-3.859Z" fill="#0e5b70"/>
              <path id="Path_5383" data-name="Path 5383" d="M165.27,429.8l-2.61,1.38.05-.8,2.06-1.07Z" fill="#0e5b70"/>
              <path id="Path_5384" data-name="Path 5384" d="M164.77,425.455v3.859l-2.06,1.071.25-3.981Z" fill="#2377a4"/>
              <path id="Path_5385" data-name="Path 5385" d="M163.649,415.775l-1.42,1.74s-1.83-.67-2.87.24a1.108,1.108,0,0,0-.35,1.4l-.16.08c-.35-.42-.6-1.14.34-2.09.91-.91,2.87-.11,2.87-.11l1.38-1.63a.648.648,0,0,1,.21.37" fill="#404040"/>
              <path id="Path_5386" data-name="Path 5386" d="M163.439,415.4l-1.38,1.631s-1.96-.8-2.87.11c-.94.949-.69,1.669-.34,2.089l-1.23.642-.03.46a2.516,2.516,0,0,1-1.21-1.6c0-.019,0-.028.01-.039.11-.3,1.24-2.989,5.05-2.359l1.35-1.531a4.681,4.681,0,0,1,.65.6" fill="#999"/>
              <path id="Path_5387" data-name="Path 5387" d="M163.41,457.235v4.36l-.5-.52v-3.86Z" fill="#0e5b70"/>
              <path id="Path_5388" data-name="Path 5388" d="M162.91,461.074l.5.521-2.74,1.33.05-.811Z" fill="#0e5b70"/>
              <path id="Path_5389" data-name="Path 5389" d="M163.02,425.455l-.06.949-.59.311.07-.981Z" fill="#0e5b70"/>
              <path id="Path_5390" data-name="Path 5390" d="M162.96,426.4l-.25,3.98-.61.321.27-3.991Z" fill="#2377a4"/>
              <path id="Path_5391" data-name="Path 5391" d="M162.91,457.215v3.859l-2.19,1.041.25-3.96Z" fill="#2377a4"/>
              <path id="Path_5392" data-name="Path 5392" d="M157.93,425.565l-.62-.109,3.96-2.141.41-5.56,1.22.48-.46,5.67-2.24,1.14-1.35.69Z" fill="#0e5c77"/>
              <path id="Path_5393" data-name="Path 5393" d="M162.9,472.534l-.2,5.23-1.55.5.31-7.04Z" fill="#186577"/>
              <path id="Path_5394" data-name="Path 5394" d="M162.79,414.8l-1.35,1.53c-3.81-.63-4.94,2.06-5.05,2.36a2.607,2.607,0,0,1,.55-1.48,3.659,3.659,0,0,1,4.17-1.44l1.33-1.23s.15.11.35.26" fill="#c2c2c2"/>
              <path id="Path_5395" data-name="Path 5395" d="M162.71,430.385l-.05.8-.61.33.05-.81Z" fill="#0e5b70"/>
              <path id="Path_5396" data-name="Path 5396" d="M162.049,431.515l.61-.33-.87,13.89-.82,13.08-.76.36Z" fill="#0e5b70"/>
              <path id="Path_5397" data-name="Path 5397" d="M162.439,425.735l-.07.98-2.35,1.25.18-2.919Z" fill="#196a8e"/>
              <path id="Path_5398" data-name="Path 5398" d="M160.02,427.965l2.35-1.25-.27,3.99-2.33,1.21Z" fill="#2377a4"/>
              <path id="Path_5399" data-name="Path 5399" d="M162.1,430.705l-.05.809-2.33,1.231h-.01l.06-.83Z" fill="#0e5b70"/>
              <path id="Path_5400" data-name="Path 5400" d="M162.049,431.515l-1.84,27-2.19,1.05,1.69-26.82h.01Z" fill="#196a8e"/>
              <path id="Path_5401" data-name="Path 5401" d="M161.68,417.755l-.41,5.56-3.96,2.14.24-4.31a2.993,2.993,0,0,0,.98.52c.02.97.39,1.739.83,1.739.47,0,.84-.839.84-1.87a3.7,3.7,0,0,0-.14-1.049c-.16-.5-.41-.82-.7-.82a1.587,1.587,0,0,1-.35-.511Z" fill="#2377a4"/>
              <path id="Path_5402" data-name="Path 5402" d="M161.46,488.9v4.36l-.51-.57v-3.87Z" fill="#0e5b70"/>
              <path id="Path_5403" data-name="Path 5403" d="M160.95,492.694l.51.57-2.77,1.031.05-.79Z" fill="#0e5b70"/>
              <path id="Path_5404" data-name="Path 5404" d="M160.97,458.154l-.25,3.96-.78.36.27-3.96Z" fill="#2377a4"/>
              <path id="Path_5405" data-name="Path 5405" d="M160.95,488.824v3.87l-2.21.811.25-3.951Z" fill="#2377a4"/>
              <path id="Path_5406" data-name="Path 5406" d="M160.72,462.115l-.05.811-.79.37.06-.821Z" fill="#0e5b70"/>
              <path id="Path_5407" data-name="Path 5407" d="M160.669,462.925l-.52,8.22-1.16,18.41-.92.35,1.81-26.61Z" fill="#0e5b70"/>
              <path id="Path_5408" data-name="Path 5408" d="M160.21,458.515l-.27,3.96-2.16,1.03.24-3.94Z" fill="#2377a4"/>
              <path id="Path_5409" data-name="Path 5409" d="M160.2,425.045l-.18,2.92-.94.49-.23,3.94.86.35-1.69,26.82-.8.39-.23,3.92.74.34-1.67,26.439-.79.3-.23,3.9.73.419-1.73,27.441-.85.22-.23,3.83.79.53-.5,8v.01l-1.37.269,6.97-109.84Z" fill="#0e5c77"/>
              <path id="Path_5410" data-name="Path 5410" d="M160.06,420.485a3.708,3.708,0,0,1,.14,1.05,2.169,2.169,0,0,1-1.67.131.579.579,0,0,1-.01-.131,3.651,3.651,0,0,1,.11-.909,3.78,3.78,0,0,0,1.43-.141" fill="#404040"/>
              <path id="Path_5411" data-name="Path 5411" d="M160.02,427.965l-.25,3.95-.92.48.23-3.94Z" fill="#2377a4"/>
              <path id="Path_5412" data-name="Path 5412" d="M159.96,507.545l-.26,4.319-1.6,1.191.4-6.87Z" fill="#186577"/>
              <path id="Path_5413" data-name="Path 5413" d="M159.96,541.865l-3.45.41-6.66-.34,4.06-.24v-6.529h1.24l.12,6.369Z" fill="#0e5c77"/>
              <path id="Path_5414" data-name="Path 5414" d="M157.779,463.505l2.16-1.03-.06.82-2.02.98-.13-.06Z" fill="#0e5b70"/>
              <path id="Path_5415" data-name="Path 5415" d="M159.879,463.3l-1.81,26.609-2.01.75,1.67-26.439.13.059Z" fill="#196a8e"/>
              <path id="Path_5416" data-name="Path 5416" d="M159.77,431.915l-.06.83-.86-.351Z" fill="#0e5b70"/>
              <path id="Path_5417" data-name="Path 5417" d="M159.359,419.665c.29,0,.54.319.7.819a3.781,3.781,0,0,1-1.43.141c.14-.57.42-.96.73-.96" fill="#999"/>
              <path id="Path_5418" data-name="Path 5418" d="M160.2,421.534c0,1.03-.37,1.87-.84,1.87-.44,0-.81-.769-.83-1.739a2.169,2.169,0,0,0,1.67-.131" fill="#003a49"/>
              <path id="Path_5419" data-name="Path 5419" d="M159.45,521.6l-.01,4.31-.5-.62v-3.82Z" fill="#0e5b70"/>
              <path id="Path_5420" data-name="Path 5420" d="M158.939,525.3l.5.62-2.78.71.05-.78Z" fill="#0e5b70"/>
              <path id="Path_5421" data-name="Path 5421" d="M159.01,419.154a1.587,1.587,0,0,0,.35.511,2.115,2.115,0,0,1-.51-.431Z" fill="#404040"/>
              <path id="Path_5422" data-name="Path 5422" d="M158.069,489.9l.92-.35-.25,3.951-.93.339Z" fill="#2377a4"/>
              <path id="Path_5423" data-name="Path 5423" d="M158.85,419.235a2.114,2.114,0,0,0,.51.431c-.31,0-.59.389-.73.96a2.579,2.579,0,0,1-1.04-.29l.03-.46Z" fill="#999"/>
              <path id="Path_5424" data-name="Path 5424" d="M158.939,521.475v3.82l-2.23.55.25-3.87Z" fill="#2377a4"/>
              <path id="Path_5425" data-name="Path 5425" d="M158.85,425.735l-6.97,109.84v.011l-.6.12,6.65-110.141Z" fill="#196a8e"/>
              <path id="Path_5426" data-name="Path 5426" d="M158.74,493.505l-.05.79-.94.359.06-.809Z" fill="#0e5b70"/>
              <path id="Path_5427" data-name="Path 5427" d="M157.749,494.654l.94-.359-.74,11.859-.99,15.82-1.09.281Z" fill="#0e5b70"/>
              <path id="Path_5428" data-name="Path 5428" d="M158.629,420.625a3.658,3.658,0,0,0-.11.909.578.578,0,0,0,.01.131,3.012,3.012,0,0,1-.98-.52l.04-.81a2.547,2.547,0,0,0,1.04.29" fill="#404040"/>
              <path id="Path_5429" data-name="Path 5429" d="M158.069,489.9l-.26,3.94-1.99.73.24-3.92Z" fill="#2377a4"/>
              <path id="Path_5430" data-name="Path 5430" d="M158.02,459.565l-.24,3.94-.79.371.23-3.92Z" fill="#2377a4"/>
              <path id="Path_5431" data-name="Path 5431" d="M157.81,493.845l-.06.81-1.85.69-.13-.07.05-.7Z" fill="#0e5b70"/>
              <path id="Path_5432" data-name="Path 5432" d="M157.779,463.505l-.05.71-.74-.34Z" fill="#0e5b70"/>
              <path id="Path_5433" data-name="Path 5433" d="M157.749,494.654l-1.88,27.6-1.83.459,1.73-27.44.13.07Z" fill="#196a8e"/>
              <path id="Path_5434" data-name="Path 5434" d="M157.589,420.335l-.04.809a3.06,3.06,0,0,1-1.17-2.41,2.514,2.514,0,0,0,1.21,1.6" fill="#404040"/>
              <path id="Path_5435" data-name="Path 5435" d="M156.96,521.975l-.25,3.87-1.1.27.26-3.86Z" fill="#2377a4"/>
              <path id="Path_5436" data-name="Path 5436" d="M156.71,525.845l-.05.78-1.1.28.05-.79Z" fill="#0e5b70"/>
              <path id="Path_5437" data-name="Path 5437" d="M156.66,526.625l-.06,1.01-1.23,2.01.19-2.741Z" fill="#0e5b70"/>
              <path id="Path_5438" data-name="Path 5438" d="M156.06,490.654l-.24,3.92-.78.28.23-3.9Z" fill="#2377a4"/>
              <path id="Path_5439" data-name="Path 5439" d="M155.87,522.255l-.26,3.859-1.81.441.24-3.84Z" fill="#2377a4"/>
              <path id="Path_5440" data-name="Path 5440" d="M155.819,494.574l-.05.7-.73-.42Z" fill="#0e5b70"/>
              <path id="Path_5441" data-name="Path 5441" d="M155.609,526.115l-.05.79-1.73.44-.08-.049.05-.741Z" fill="#0e5b70"/>
              <path id="Path_5442" data-name="Path 5442" d="M154.04,522.715l-.24,3.84-.84.21.23-3.83Z" fill="#2377a4"/>
              <path id="Path_5443" data-name="Path 5443" d="M153.91,535.165v6.53l-4.06.24v-5.94l1.429-.29.6-.12v-.01l1.369-.27v-.01Zm-1.43,3.64c0-1.09-.4-1.98-.9-1.98s-.89.89-.89,1.98.4,1.979.89,1.979.9-.889.9-1.979" fill="#2377a4"/>
              <path id="Path_5444" data-name="Path 5444" d="M153.8,526.555l-.05.74-.79-.53Z" fill="#0e5b70"/>
              <path id="Path_5445" data-name="Path 5445" d="M151.58,536.824c.5,0,.9.891.9,1.98s-.4,1.98-.9,1.98-.89-.89-.89-1.98.4-1.98.89-1.98" fill="#003a49"/>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_12976" data-name="Group 12976">
        <g id="Group_12975" data-name="Group 12975" clip-path="url(#clip-path-9)">
          <g id="Group_12974" data-name="Group 12974">
            <g id="Group_12973" data-name="Group 12973" clip-path="url(#clip-path-9)">
              <path id="Path_5446" data-name="Path 5446" d="M212.627,484.712c.01.24-.26.53-.98.75-.84.26-2.04.66-3.3,1.09a3.323,3.323,0,0,1-.02-.77Z" fill="#999"/>
              <path id="Path_5447" data-name="Path 5447" d="M212.627,484.712l-4.3,1.07a4.757,4.757,0,0,1,.18-.94c1.26-.24,2.43-.45,3.14-.569.52-.091.97.139.98.439" fill="#c2c2c2"/>
              <path id="Path_5448" data-name="Path 5448" d="M208.777,481.8v2.311a4.051,4.051,0,0,0-.27.729c-.16.03-.33.06-.5.09a2.058,2.058,0,0,1,.42-.819V481.8Z" fill="#999"/>
              <path id="Path_5449" data-name="Path 5449" d="M208.6,487.362l-.05,1.97h-.4v-1.781a5.918,5.918,0,0,1-.2-.87c.13-.04.26-.079.4-.13a2.706,2.706,0,0,0,.25.811" fill="#999"/>
              <path id="Path_5450" data-name="Path 5450" d="M208.427,481.8v2.311l-1.1.139V481.8Z" fill="#dddfdf"/>
              <path id="Path_5451" data-name="Path 5451" d="M207.328,484.252l1.1-.14a2.069,2.069,0,0,0-.42.82c-.4.07-.8.15-1.19.22a3.338,3.338,0,0,1,.51-.9" fill="#c2c2c2"/>
              <path id="Path_5452" data-name="Path 5452" d="M208.507,484.842a4.757,4.757,0,0,0-.18.94l-.44.11a3.51,3.51,0,0,1,.12-.96c.17-.03.34-.06.5-.09" fill="#999"/>
              <path id="Path_5453" data-name="Path 5453" d="M208.328,485.782a3.322,3.322,0,0,0,.02.77c-.14.049-.27.089-.4.129a6.32,6.32,0,0,1-.06-.79Z" fill="#999"/>
              <path id="Path_5454" data-name="Path 5454" d="M208.148,487.642v1.69h-.82v-1.44Z" fill="#dddfdf"/>
              <path id="Path_5455" data-name="Path 5455" d="M208.148,487.552v.09l-.821.25a2.885,2.885,0,0,1-.52-.819c.381-.121.761-.25,1.14-.391a5.831,5.831,0,0,0,.2.87" fill="#c2c2c2"/>
              <path id="Path_5456" data-name="Path 5456" d="M207.907,407.152l.14.23-.21,6.06h-.17Z" fill="#999"/>
              <path id="Path_5457" data-name="Path 5457" d="M207.907,407.152l-.24,6.29H206l.25-6.29Z" fill="#c2c2c2"/>
              <path id="Path_5458" data-name="Path 5458" d="M207.888,485.892a6.335,6.335,0,0,0,.06.79q-.57.211-1.14.391a2.318,2.318,0,0,1-.19-.931,2.718,2.718,0,0,1,.2-.989c.39-.071.79-.151,1.19-.221a3.515,3.515,0,0,0-.12.96" fill="#dddfdf"/>
              <path id="Path_5459" data-name="Path 5459" d="M202.627,485.962s2-.39,4.19-.81a2.728,2.728,0,0,0-.2.99l-4.04,1.08Z" fill="#c2c2c2"/>
              <path id="Path_5460" data-name="Path 5460" d="M206.618,486.142a2.318,2.318,0,0,0,.19.931c-2.26.79-4.29,1.509-4.29,1.509l.06-1.36Z" fill="#999"/>
              <path id="Path_5461" data-name="Path 5461" d="M206.248,407.152l-.25,6.29a3.93,3.93,0,0,1-.4.14l.27-6.68a3.377,3.377,0,0,1,.38.25" fill="#dddfdf"/>
              <path id="Path_5462" data-name="Path 5462" d="M205.107,401.082a1,1,0,0,1,.96,1.04l-.2,4.78a2.066,2.066,0,0,0-.98-.3Z" fill="#999"/>
              <path id="Path_5463" data-name="Path 5463" d="M205.868,406.9l-.27,6.68a4.507,4.507,0,0,1-1.01.17l.3-7.151a2.079,2.079,0,0,1,.98.3" fill="#dddfdf"/>
              <path id="Path_5464" data-name="Path 5464" d="M205.6,413.582l-2.26,55.06a2.094,2.094,0,0,0-.96-.369l2.21-54.521a4.507,4.507,0,0,0,1.01-.17" fill="#999"/>
              <path id="Path_5465" data-name="Path 5465" d="M205.388,469.042l.05,1.77h-.16l-.04-2Z" fill="#999"/>
              <path id="Path_5466" data-name="Path 5466" d="M205.237,468.813l.04,2H203.6l-.01-2Z" fill="#c2c2c2"/>
              <path id="Path_5467" data-name="Path 5467" d="M205.107,401.082l-.22,5.52a1.933,1.933,0,0,0-1.02.27l.2-4.83a.986.986,0,0,1,1.04-.96" fill="#c2c2c2"/>
              <path id="Path_5468" data-name="Path 5468" d="M204.888,406.6l-.3,7.15a1.753,1.753,0,0,1-.99-.219l.27-6.661a1.943,1.943,0,0,1,1.02-.27" fill="#f8f8f8"/>
              <path id="Path_5469" data-name="Path 5469" d="M204.587,413.752l-2.21,54.521a1.886,1.886,0,0,0-1.03.169l2.25-54.91a1.746,1.746,0,0,0,.99.22" fill="#c2c2c2"/>
              <path id="Path_5470" data-name="Path 5470" d="M203.727,508.452l.15.24-.22,6.05h-.16Z" fill="#999"/>
              <path id="Path_5471" data-name="Path 5471" d="M203.868,406.872l-.27,6.66a1.068,1.068,0,0,1-.34-.33l.26-6.08a1.664,1.664,0,0,1,.35-.25" fill="#dddfdf"/>
              <path id="Path_5472" data-name="Path 5472" d="M203.727,508.452l-.23,6.29h-1.67l.24-6.29Z" fill="#c2c2c2"/>
              <path id="Path_5473" data-name="Path 5473" d="M203.587,468.813l.01,2s-.13.05-.35.119l.09-2.29a1.239,1.239,0,0,1,.25.171" fill="#dddfdf"/>
              <path id="Path_5474" data-name="Path 5474" d="M203.337,468.642l-.09,2.29a4.692,4.692,0,0,1-.99.181l.12-2.84a2.106,2.106,0,0,1,.96.369" fill="#dddfdf"/>
              <path id="Path_5475" data-name="Path 5475" d="M203.248,470.932l-.62,15.03a1.613,1.613,0,0,0-.58,1.4,2.443,2.443,0,0,0,.47,1.219l-.8,19.63a2.011,2.011,0,0,0-.95-.3l1.49-36.8a4.692,4.692,0,0,0,.99-.181" fill="#999"/>
              <path id="Path_5476" data-name="Path 5476" d="M202.627,485.962l-.05,1.26-.53.14a1.611,1.611,0,0,1,.58-1.4" fill="#c2c2c2"/>
              <path id="Path_5477" data-name="Path 5477" d="M202.578,487.222l-.06,1.36a2.443,2.443,0,0,1-.47-1.219Z" fill="#999"/>
              <path id="Path_5478" data-name="Path 5478" d="M202.377,468.273l-.12,2.84a1.845,1.845,0,0,1-1.02-.17l.11-2.5a1.886,1.886,0,0,1,1.03-.17" fill="#f8f8f8"/>
              <path id="Path_5479" data-name="Path 5479" d="M202.257,471.112l-1.49,36.8a1.87,1.87,0,0,0-1.05.24l1.52-37.21a1.837,1.837,0,0,0,1.02.17" fill="#c2c2c2"/>
              <path id="Path_5480" data-name="Path 5480" d="M202.067,508.452l-.24,6.29s-.15.06-.38.13l.27-6.66a3.641,3.641,0,0,1,.35.24" fill="#dddfdf"/>
              <path id="Path_5481" data-name="Path 5481" d="M201.717,508.212l-.27,6.66a4.537,4.537,0,0,1-.97.17l.29-7.13a2.011,2.011,0,0,1,.95.3" fill="#dddfdf"/>
              <path id="Path_5482" data-name="Path 5482" d="M201.447,514.872l-.22,5.25a1,1,0,0,1-1,.96l.25-6.04a4.6,4.6,0,0,0,.97-.17" fill="#999"/>
              <path id="Path_5483" data-name="Path 5483" d="M201.348,468.443l-.11,2.5a1.2,1.2,0,0,1-.38-.36v-1.81a1.922,1.922,0,0,1,.49-.33" fill="#dddfdf"/>
              <path id="Path_5484" data-name="Path 5484" d="M200.768,507.912l-.29,7.13a1.747,1.747,0,0,1-1.04-.191l.28-6.7a1.879,1.879,0,0,1,1.05-.24" fill="#f8f8f8"/>
              <path id="Path_5485" data-name="Path 5485" d="M200.477,515.042l-.25,6.04h-.04a1,1,0,0,1-.96-1.04l.21-5.19a1.754,1.754,0,0,0,1.04.19" fill="#c2c2c2"/>
              <path id="Path_5486" data-name="Path 5486" d="M199.717,508.152l-.28,6.7a1.039,1.039,0,0,1-.36-.34l.27-6.089a1.552,1.552,0,0,1,.37-.27" fill="#dddfdf"/>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_12980" data-name="Group 12980">
        <g id="Group_12979" data-name="Group 12979" clip-path="url(#clip-path-11)">
          <g id="Group_12978" data-name="Group 12978">
            <g id="Group_12977" data-name="Group 12977" clip-path="url(#clip-path-11)">
              <path id="Path_5487" data-name="Path 5487" d="M199.078,488.99c.01.232-.26.512-.98.725-.84.251-2.04.636-3.3,1.051a3.094,3.094,0,0,1-.02-.743Z" fill="#999"/>
              <path id="Path_5488" data-name="Path 5488" d="M199.078,488.99l-4.3,1.033a4.438,4.438,0,0,1,.18-.907c1.26-.232,2.42-.435,3.14-.551.52-.087.97.135.98.425" fill="#c2c2c2"/>
              <path id="Path_5489" data-name="Path 5489" d="M195.228,486.181v2.23a3.891,3.891,0,0,0-.27.706l-.5.086a1.976,1.976,0,0,1,.42-.792v-2.23Z" fill="#999"/>
              <path id="Path_5490" data-name="Path 5490" d="M195.048,491.549l-.05,1.9h-.4v-1.717a5.6,5.6,0,0,1-.2-.84c.13-.039.26-.077.4-.126a2.547,2.547,0,0,0,.25.782" fill="#999"/>
              <path id="Path_5491" data-name="Path 5491" d="M194.878,486.181v2.23l-1.1.135v-2.365Z" fill="#dddfdf"/>
              <path id="Path_5492" data-name="Path 5492" d="M193.778,488.546l1.1-.135a1.976,1.976,0,0,0-.42.792c-.39.068-.79.145-1.19.212a3.2,3.2,0,0,1,.51-.869" fill="#c2c2c2"/>
              <path id="Path_5493" data-name="Path 5493" d="M194.958,489.116a4.438,4.438,0,0,0-.18.907l-.44.107a3.27,3.27,0,0,1,.12-.927l.5-.087" fill="#999"/>
              <path id="Path_5494" data-name="Path 5494" d="M194.778,490.024a3.094,3.094,0,0,0,.02.743c-.14.049-.27.087-.4.126a5.937,5.937,0,0,1-.06-.763Z" fill="#999"/>
              <path id="Path_5495" data-name="Path 5495" d="M194.6,491.819v1.631h-.82v-1.39Z" fill="#dddfdf"/>
              <path id="Path_5496" data-name="Path 5496" d="M194.6,491.733v.087l-.82.241a2.765,2.765,0,0,1-.52-.792q.57-.173,1.14-.376a5.6,5.6,0,0,0,.2.84" fill="#c2c2c2"/>
              <path id="Path_5497" data-name="Path 5497" d="M194.358,414.111l.14.223-.21,5.85h-.17Z" fill="#999"/>
              <path id="Path_5498" data-name="Path 5498" d="M194.358,414.111l-.24,6.073h-1.67l.25-6.073Z" fill="#c2c2c2"/>
              <path id="Path_5499" data-name="Path 5499" d="M194.338,490.13a5.951,5.951,0,0,0,.06.763q-.57.2-1.14.376a2.164,2.164,0,0,1-.19-.9,2.556,2.556,0,0,1,.2-.956c.4-.067.8-.144,1.19-.212a3.275,3.275,0,0,0-.12.927" fill="#dddfdf"/>
              <path id="Path_5500" data-name="Path 5500" d="M189.078,490.2s2-.377,4.19-.782a2.557,2.557,0,0,0-.2.957l-4.04,1.041Z" fill="#c2c2c2"/>
              <path id="Path_5501" data-name="Path 5501" d="M193.068,490.371a2.16,2.16,0,0,0,.19.9c-2.26.763-4.29,1.458-4.29,1.458l.06-1.313Z" fill="#999"/>
              <path id="Path_5502" data-name="Path 5502" d="M192.7,414.111l-.25,6.073a3.6,3.6,0,0,1-.4.135l.27-6.449a3.38,3.38,0,0,1,.38.241" fill="#dddfdf"/>
              <path id="Path_5503" data-name="Path 5503" d="M191.558,408.252a.983.983,0,0,1,.96,1l-.2,4.6a2.22,2.22,0,0,0-.98-.279Z" fill="#999"/>
              <path id="Path_5504" data-name="Path 5504" d="M192.318,413.87l-.27,6.449a4.591,4.591,0,0,1-1.01.164l.3-6.9a2.22,2.22,0,0,1,.98.279Z" fill="#dddfdf"/>
              <path id="Path_5505" data-name="Path 5505" d="M192.048,420.319l-2.26,53.156a2.18,2.18,0,0,0-.96-.357l2.21-52.635a4.713,4.713,0,0,0,1.01-.164" fill="#999"/>
              <path id="Path_5506" data-name="Path 5506" d="M191.838,473.862l.05,1.709h-.16l-.04-1.932Z" fill="#999"/>
              <path id="Path_5507" data-name="Path 5507" d="M191.688,473.64l.04,1.932h-1.68l-.01-1.932Z" fill="#c2c2c2"/>
              <path id="Path_5508" data-name="Path 5508" d="M191.558,408.252l-.22,5.329a1.974,1.974,0,0,0-1.02.261l.2-4.663a.972.972,0,0,1,1.04-.927" fill="#c2c2c2"/>
              <path id="Path_5509" data-name="Path 5509" d="M191.338,413.581l-.3,6.9a1.791,1.791,0,0,1-.99-.212l.27-6.429a1.974,1.974,0,0,1,1.02-.261" fill="#f8f8f8"/>
              <path id="Path_5510" data-name="Path 5510" d="M191.038,420.484l-2.21,52.635a1.956,1.956,0,0,0-1.03.165l2.25-53.012a1.792,1.792,0,0,0,.99.212" fill="#c2c2c2"/>
              <path id="Path_5511" data-name="Path 5511" d="M190.178,511.909l.15.232-.22,5.841h-.16Z" fill="#999"/>
              <path id="Path_5512" data-name="Path 5512" d="M190.318,413.842l-.27,6.43a1.056,1.056,0,0,1-.34-.32l.26-5.869a1.632,1.632,0,0,1,.35-.241" fill="#dddfdf"/>
              <path id="Path_5513" data-name="Path 5513" d="M190.178,511.909l-.23,6.073h-1.67l.24-6.073Z" fill="#c2c2c2"/>
              <path id="Path_5514" data-name="Path 5514" d="M190.038,473.64l.01,1.932s-.13.048-.35.115l.09-2.211a1.275,1.275,0,0,1,.25.164" fill="#dddfdf"/>
              <path id="Path_5515" data-name="Path 5515" d="M189.788,473.476l-.09,2.211a4.87,4.87,0,0,1-.99.174l.12-2.742a2.155,2.155,0,0,1,.96.357" fill="#dddfdf"/>
              <path id="Path_5516" data-name="Path 5516" d="M189.7,475.687l-.62,14.511a1.536,1.536,0,0,0-.58,1.351,2.409,2.409,0,0,0,.47,1.178l-.8,18.951a2.054,2.054,0,0,0-.95-.289l1.49-35.528a4.87,4.87,0,0,0,.99-.174" fill="#999"/>
              <path id="Path_5517" data-name="Path 5517" d="M189.078,490.2l-.05,1.216-.53.136a1.538,1.538,0,0,1,.58-1.352" fill="#c2c2c2"/>
              <path id="Path_5518" data-name="Path 5518" d="M189.028,491.413l-.06,1.314a2.412,2.412,0,0,1-.47-1.178Z" fill="#999"/>
              <path id="Path_5519" data-name="Path 5519" d="M188.828,473.118l-.12,2.742a1.889,1.889,0,0,1-1.02-.164l.11-2.413a1.955,1.955,0,0,1,1.03-.165" fill="#f8f8f8"/>
              <path id="Path_5520" data-name="Path 5520" d="M188.708,475.86l-1.49,35.528a1.837,1.837,0,0,0-1.05.242l1.52-35.933a1.9,1.9,0,0,0,1.02.163" fill="#c2c2c2"/>
              <path id="Path_5521" data-name="Path 5521" d="M188.518,511.909l-.24,6.073s-.15.058-.38.125l.27-6.43a3.712,3.712,0,0,1,.35.232" fill="#dddfdf"/>
              <path id="Path_5522" data-name="Path 5522" d="M188.168,511.678l-.27,6.43a4.836,4.836,0,0,1-.97.164l.29-6.883a2.054,2.054,0,0,1,.95.289" fill="#dddfdf"/>
              <path id="Path_5523" data-name="Path 5523" d="M187.9,518.108l-.22,5.068a.98.98,0,0,1-1,.927l.25-5.831a4.767,4.767,0,0,0,.97-.164" fill="#999"/>
              <path id="Path_5524" data-name="Path 5524" d="M187.8,473.283l-.11,2.413a1.17,1.17,0,0,1-.38-.348V473.6a1.97,1.97,0,0,1,.49-.318" fill="#dddfdf"/>
              <path id="Path_5525" data-name="Path 5525" d="M187.218,511.389l-.29,6.883a1.806,1.806,0,0,1-1.04-.184l.28-6.458a1.845,1.845,0,0,1,1.05-.241" fill="#f8f8f8"/>
              <path id="Path_5526" data-name="Path 5526" d="M186.928,518.272l-.25,5.831h-.04a.983.983,0,0,1-.96-1l.21-5.011a1.806,1.806,0,0,0,1.04.184" fill="#c2c2c2"/>
              <path id="Path_5527" data-name="Path 5527" d="M186.168,511.63l-.28,6.458a1.038,1.038,0,0,1-.36-.328l.27-5.879a1.366,1.366,0,0,1,.37-.251" fill="#dddfdf"/>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_12984" data-name="Group 12984">
        <g id="Group_12983" data-name="Group 12983" clip-path="url(#clip-path-13)">
          <g id="Group_12982" data-name="Group 12982">
            <g id="Group_12981" data-name="Group 12981" clip-path="url(#clip-path-13)">
              <path id="Path_5528" data-name="Path 5528" d="M185.134,417.531l.126.221L185,423.533l-.156,0Z" fill="#999"/>
              <path id="Path_5529" data-name="Path 5529" d="M185.134,417.531l-.294,6-1.528-.018.3-6Z" fill="#c2c2c2"/>
              <path id="Path_5530" data-name="Path 5530" d="M183.615,417.513l-.3,6a2.569,2.569,0,0,1-.368.119l.326-6.362a3.2,3.2,0,0,1,.345.243" fill="#dddfdf"/>
              <path id="Path_5531" data-name="Path 5531" d="M182.644,411.707a.942.942,0,0,1,.866,1l-.24,4.559a1.824,1.824,0,0,0-.893-.3Z" fill="#999"/>
              <path id="Path_5532" data-name="Path 5532" d="M183.27,417.27l-.326,6.362a3.459,3.459,0,0,1-.926.161l.359-6.82a1.841,1.841,0,0,1,.893.3" fill="#dddfdf"/>
              <path id="Path_5533" data-name="Path 5533" d="M182.944,423.632l-2.719,52.529a1.883,1.883,0,0,0-.874-.363l2.667-52.006a3.5,3.5,0,0,0,.926-.16" fill="#999"/>
              <path id="Path_5534" data-name="Path 5534" d="M182.1,476.567l.025,1.689-.147,0-.012-1.908Z" fill="#999"/>
              <path id="Path_5535" data-name="Path 5535" d="M181.962,476.346l.013,1.908-1.537-.018.014-1.91Z" fill="#c2c2c2"/>
              <path id="Path_5536" data-name="Path 5536" d="M182.644,411.707l-.267,5.266a1.707,1.707,0,0,0-.936.246l.24-4.608a.918.918,0,0,1,.963-.9" fill="#c2c2c2"/>
              <path id="Path_5537" data-name="Path 5537" d="M182.377,416.973l-.359,6.819a1.55,1.55,0,0,1-.9-.22v-.01l.326-6.343a1.707,1.707,0,0,1,.936-.246" fill="#f8f8f8"/>
              <path id="Path_5538" data-name="Path 5538" d="M182.018,423.792,179.351,475.8a1.671,1.671,0,0,0-.944.15l2.708-52.377a1.54,1.54,0,0,0,.9.221" fill="#c2c2c2"/>
              <path id="Path_5539" data-name="Path 5539" d="M180.112,514.158l.135.23-.273,5.772-.147,0Z" fill="#999"/>
              <path id="Path_5540" data-name="Path 5540" d="M181.441,417.219l-.326,6.343a.869.869,0,0,1-.307-.31l.31-5.8a1.485,1.485,0,0,1,.323-.234" fill="#dddfdf"/>
              <path id="Path_5541" data-name="Path 5541" d="M180.112,514.158l-.285,6-1.528-.02.294-6Z" fill="#c2c2c2"/>
              <path id="Path_5542" data-name="Path 5542" d="M180.452,476.326l-.014,1.909s-.12.046-.322.111l.109-2.185a1.132,1.132,0,0,1,.227.165" fill="#dddfdf"/>
              <path id="Path_5543" data-name="Path 5543" d="M180.226,476.161l-.109,2.185a4.157,4.157,0,0,1-.909.161l.144-2.709a1.876,1.876,0,0,1,.874.363" fill="#dddfdf"/>
              <path id="Path_5544" data-name="Path 5544" d="M180.116,478.346l-1.841,35.561a1.761,1.761,0,0,0-.865-.3l.978-19.155.82-15.948a4.1,4.1,0,0,0,.908-.161" fill="#999"/>
              <path id="Path_5545" data-name="Path 5545" d="M179.352,475.8l-.144,2.709a1.616,1.616,0,0,1-.931-.174v-.01l.13-2.375a1.675,1.675,0,0,1,.945-.15" fill="#f8f8f8"/>
              <path id="Path_5546" data-name="Path 5546" d="M179.208,478.507l-.82,15.948-.219-.011a.561.561,0,0,0-.008-.116,1.929,1.929,0,0,0-.291-.91s-.138.056-.377.149l.784-15.234a1.614,1.614,0,0,0,.931.174" fill="#c2c2c2"/>
              <path id="Path_5547" data-name="Path 5547" d="M178.593,514.139l-.294,6s-.138.056-.349.12l.326-6.352a3.236,3.236,0,0,1,.317.232" fill="#dddfdf"/>
              <path id="Path_5548" data-name="Path 5548" d="M178.275,513.906l-.326,6.353a4.042,4.042,0,0,1-.889.151l.349-6.8a1.787,1.787,0,0,1,.866.3" fill="#dddfdf"/>
              <path id="Path_5549" data-name="Path 5549" d="M178.388,494.455l-.979,19.154a1.656,1.656,0,0,0-.963.217l.95-18.371c.239-.055.377-.082.377-.082a1.271,1.271,0,0,0,.4-.93Z" fill="#c2c2c2"/>
              <path id="Path_5550" data-name="Path 5550" d="M177.95,520.259l-.263,5.008a.93.93,0,0,1-.927.9l.3-5.762a4,4,0,0,0,.89-.151" fill="#999"/>
              <path id="Path_5551" data-name="Path 5551" d="M178.407,475.948l-.13,2.375a.986.986,0,0,1-.344-.339l.022-1.726a1.8,1.8,0,0,1,.452-.31" fill="#dddfdf"/>
              <path id="Path_5552" data-name="Path 5552" d="M178.161,494.328a.551.551,0,0,1,.008.115,1.271,1.271,0,0,1-.4.93s-.138.027-.377.082l.049-.926Z" fill="#999"/>
              <path id="Path_5553" data-name="Path 5553" d="M178.161,494.328l-.716.2.048-.963.377-.148a1.926,1.926,0,0,1,.291.91" fill="#c2c2c2"/>
              <path id="Path_5554" data-name="Path 5554" d="M177.41,513.61l-.35,6.8a1.547,1.547,0,0,1-.949-.194l.335-6.39a1.661,1.661,0,0,1,.964-.217" fill="#f8f8f8"/>
              <path id="Path_5555" data-name="Path 5555" d="M177.493,493.567l-.048.963-2.609.712a2.477,2.477,0,0,1,.073-.677c1.058-.406,2.06-.8,2.584-1" fill="#c2c2c2"/>
              <path id="Path_5556" data-name="Path 5556" d="M177.445,494.529l-.048.927c-.514.107-1.469.3-2.478.511a3.013,3.013,0,0,1-.082-.725Z" fill="#999"/>
              <path id="Path_5557" data-name="Path 5557" d="M177.06,520.41l-.3,5.762h-.037a.943.943,0,0,1-.866-1l.253-4.951a1.556,1.556,0,0,0,.95.193" fill="#c2c2c2"/>
              <path id="Path_5558" data-name="Path 5558" d="M176.446,513.826l-.335,6.391a1,1,0,0,1-.326-.329l.319-5.808a1.42,1.42,0,0,1,.342-.254" fill="#dddfdf"/>
              <path id="Path_5559" data-name="Path 5559" d="M175.157,496.716l.016,1.728-.22,0-.025-1.728a1.7,1.7,0,0,1-.293-.682c.092-.017.183-.036.284-.063a3.924,3.924,0,0,0,.238.748" fill="#999"/>
              <path id="Path_5560" data-name="Path 5560" d="M174.958,492.81l.046,1.47a1.766,1.766,0,0,0-.1.285c-.11.047-.211.084-.322.13a6.044,6.044,0,0,1,.128-.705l-.022-1.184Z" fill="#999"/>
              <path id="Path_5561" data-name="Path 5561" d="M174.928,496.713l.024,1.727-.721-.17-.025-1.652Z" fill="#dddfdf"/>
              <path id="Path_5562" data-name="Path 5562" d="M174.928,496.713l-.722-.1a1.47,1.47,0,0,1-.333-.433c.247-.045.5-.1.752-.154h.01a1.667,1.667,0,0,0,.293.682" fill="#c2c2c2"/>
              <path id="Path_5563" data-name="Path 5563" d="M174.587,494.694c.111-.046.212-.083.322-.13a2.485,2.485,0,0,0-.073.678l-.284.072c0-.22.014-.429.035-.62" fill="#999"/>
              <path id="Path_5564" data-name="Path 5564" d="M174.919,495.968c-.1.027-.192.046-.284.063h-.009a3.533,3.533,0,0,1-.074-.707v-.009l.284-.074a3.087,3.087,0,0,0,.083.727" fill="#999"/>
              <path id="Path_5565" data-name="Path 5565" d="M174.693,492.806l.022,1.184-.614.087-.014-1.116Z" fill="#dddfdf"/>
              <path id="Path_5566" data-name="Path 5566" d="M174.1,494.077l.614-.088a6.312,6.312,0,0,0-.128.7c-.294.112-.588.222-.873.324a2.79,2.79,0,0,1,.387-.941" fill="#c2c2c2"/>
              <path id="Path_5567" data-name="Path 5567" d="M174.587,494.694c-.021.191-.032.4-.035.62v.01l-.854.227a1.487,1.487,0,0,1,.016-.533c.285-.1.579-.213.873-.324" fill="#dddfdf"/>
              <path id="Path_5568" data-name="Path 5568" d="M174.552,495.324a3.541,3.541,0,0,0,.074.707c-.257.054-.505.108-.753.153a1.644,1.644,0,0,1-.175-.632Z" fill="#dddfdf"/>
              <path id="Path_5569" data-name="Path 5569" d="M173.7,495.552a1.645,1.645,0,0,0,.175.633c-.8.161-1.541.3-1.991.385-.348.063-.639-.113-.636-.342Z" fill="#999"/>
              <path id="Path_5570" data-name="Path 5570" d="M173.714,495.018a1.493,1.493,0,0,0-.016.534l-2.452.676c0-.181.179-.389.647-.545s1.122-.406,1.821-.665" fill="#c2c2c2"/>
            </g>
          </g>
        </g>
      </g>
      <g id="Group_12988" data-name="Group 12988">
        <g id="Group_12987" data-name="Group 12987" clip-path="url(#clip-path-15)">
          <g id="Group_12986" data-name="Group 12986">
            <g id="Group_12985" data-name="Group 12985" clip-path="url(#clip-path-15)">
              <path id="Path_5571" data-name="Path 5571" d="M172.592,423.2l.125.214-.261,5.6-.156,0Z" fill="#999"/>
              <path id="Path_5572" data-name="Path 5572" d="M172.592,423.2l-.292,5.808-1.528-.019.3-5.808Z" fill="#c2c2c2"/>
              <path id="Path_5573" data-name="Path 5573" d="M171.073,423.186l-.3,5.808a2.617,2.617,0,0,1-.367.115l.323-6.158a3.213,3.213,0,0,1,.345.235" fill="#dddfdf"/>
              <path id="Path_5574" data-name="Path 5574" d="M170.1,417.566a.923.923,0,0,1,.866.972l-.237,4.413a1.872,1.872,0,0,0-.894-.288Z" fill="#999"/>
              <path id="Path_5575" data-name="Path 5575" d="M170.729,422.95l-.325,6.158a3.559,3.559,0,0,1-.925.154l.356-6.6a1.889,1.889,0,0,1,.894.288" fill="#dddfdf"/>
              <path id="Path_5576" data-name="Path 5576" d="M170.4,429.109l-2.7,50.844a1.912,1.912,0,0,0-.874-.354l2.646-50.336a3.646,3.646,0,0,0,.926-.154" fill="#999"/>
              <path id="Path_5577" data-name="Path 5577" d="M169.578,480.345l.025,1.635h-.146l-.014-1.849Z" fill="#999"/>
              <path id="Path_5578" data-name="Path 5578" d="M169.443,480.13l.014,1.849-1.537-.02.013-1.848Z" fill="#c2c2c2"/>
              <path id="Path_5579" data-name="Path 5579" d="M170.1,417.566l-.265,5.1a1.753,1.753,0,0,0-.936.237l.238-4.459a.906.906,0,0,1,.963-.875" fill="#c2c2c2"/>
              <path id="Path_5580" data-name="Path 5580" d="M169.835,422.662l-.357,6.6a1.592,1.592,0,0,1-.9-.214l.323-6.15a1.757,1.757,0,0,1,.937-.237" fill="#f8f8f8"/>
              <path id="Path_5581" data-name="Path 5581" d="M169.479,429.263,166.832,479.6a1.726,1.726,0,0,0-.944.145l2.687-50.695a1.583,1.583,0,0,0,.9.214" fill="#c2c2c2"/>
              <path id="Path_5582" data-name="Path 5582" d="M167.608,516.73l.134.223-.27,5.587-.147,0Z" fill="#999"/>
              <path id="Path_5583" data-name="Path 5583" d="M168.9,422.9l-.323,6.149a.971.971,0,0,1-.307-.308l.307-5.613a1.477,1.477,0,0,1,.323-.228" fill="#dddfdf"/>
              <path id="Path_5584" data-name="Path 5584" d="M167.608,516.73l-.283,5.808-1.528-.02.292-5.808Z" fill="#c2c2c2"/>
              <path id="Path_5585" data-name="Path 5585" d="M167.934,480.111l-.014,1.848s-.119.045-.321.107l.108-2.114a1.093,1.093,0,0,1,.227.159" fill="#dddfdf"/>
              <path id="Path_5586" data-name="Path 5586" d="M167.707,479.952l-.109,2.114a4.181,4.181,0,0,1-.908.155l.142-2.623a1.927,1.927,0,0,1,.875.354" fill="#dddfdf"/>
              <path id="Path_5587" data-name="Path 5587" d="M167.6,482.067l-1.827,34.418a1.811,1.811,0,0,0-.865-.287l.971-18.539.813-15.438a4.185,4.185,0,0,0,.908-.154" fill="#999"/>
              <path id="Path_5588" data-name="Path 5588" d="M166.833,479.6l-.143,2.622a1.66,1.66,0,0,1-.932-.168l.131-2.308a1.716,1.716,0,0,1,.944-.146" fill="#f8f8f8"/>
              <path id="Path_5589" data-name="Path 5589" d="M166.69,482.221l-.813,15.438-.22-.013a.469.469,0,0,0-.008-.111,1.824,1.824,0,0,0-.291-.881l-.377.143.777-14.744a1.667,1.667,0,0,0,.932.168" fill="#c2c2c2"/>
              <path id="Path_5590" data-name="Path 5590" d="M166.089,516.71l-.292,5.808s-.138.053-.349.116l.323-6.15a3.657,3.657,0,0,1,.318.226" fill="#dddfdf"/>
              <path id="Path_5591" data-name="Path 5591" d="M165.771,516.485l-.323,6.149a4.156,4.156,0,0,1-.889.146l.346-6.582a1.818,1.818,0,0,1,.866.287" fill="#dddfdf"/>
              <path id="Path_5592" data-name="Path 5592" d="M165.877,497.658l-.971,18.539a1.708,1.708,0,0,0-.964.209l.943-17.781c.239-.053.377-.079.377-.079a1.215,1.215,0,0,0,.4-.9Z" fill="#c2c2c2"/>
              <path id="Path_5593" data-name="Path 5593" d="M165.448,522.634l-.261,4.847a.915.915,0,0,1-.926.876l.3-5.578a4.147,4.147,0,0,0,.889-.145" fill="#999"/>
              <path id="Path_5594" data-name="Path 5594" d="M165.888,479.744l-.129,2.309a1.1,1.1,0,0,1-.344-.337l.021-1.672a1.789,1.789,0,0,1,.452-.3" fill="#dddfdf"/>
              <path id="Path_5595" data-name="Path 5595" d="M165.649,497.535a.457.457,0,0,1,.008.11,1.214,1.214,0,0,1-.4.9s-.137.026-.376.079l.048-.9Z" fill="#999"/>
              <path id="Path_5596" data-name="Path 5596" d="M165.649,497.535l-.716.194.048-.932c.239-.09.377-.144.377-.144a1.83,1.83,0,0,1,.291.882" fill="#c2c2c2"/>
              <path id="Path_5597" data-name="Path 5597" d="M164.906,516.2l-.347,6.582a1.6,1.6,0,0,1-.949-.186l.333-6.187a1.706,1.706,0,0,1,.963-.209" fill="#f8f8f8"/>
              <path id="Path_5598" data-name="Path 5598" d="M164.981,496.8l-.048.933-2.608.688a2.278,2.278,0,0,1,.072-.655c1.058-.394,2.06-.77,2.584-.966" fill="#c2c2c2"/>
              <path id="Path_5599" data-name="Path 5599" d="M164.933,497.73l-.048.895c-.513.105-1.468.3-2.477.5a2.862,2.862,0,0,1-.083-.7Z" fill="#999"/>
              <path id="Path_5600" data-name="Path 5600" d="M164.559,522.779l-.3,5.577h-.037a.923.923,0,0,1-.866-.971l.251-4.791a1.6,1.6,0,0,0,.95.186" fill="#c2c2c2"/>
              <path id="Path_5601" data-name="Path 5601" d="M163.942,516.406l-.333,6.187a.991.991,0,0,1-.326-.319l.317-5.623a1.455,1.455,0,0,1,.342-.245" fill="#dddfdf"/>
              <path id="Path_5602" data-name="Path 5602" d="M162.646,499.845l.016,1.672-.229,0-.016-1.672a1.6,1.6,0,0,1-.294-.66c.092-.018.184-.034.285-.061a3.71,3.71,0,0,0,.238.724" fill="#999"/>
              <path id="Path_5603" data-name="Path 5603" d="M162.446,496.064l.046,1.423a1.641,1.641,0,0,0-.1.276c-.11.045-.211.08-.322.125a5.664,5.664,0,0,1,.128-.681l-.023-1.147Z" fill="#999"/>
              <path id="Path_5604" data-name="Path 5604" d="M162.417,499.842l.016,1.672-.712-.166-.026-1.6Z" fill="#dddfdf"/>
              <path id="Path_5605" data-name="Path 5605" d="M162.417,499.842l-.722-.093a1.331,1.331,0,0,1-.324-.419c.248-.053.495-.1.752-.148a1.606,1.606,0,0,0,.294.66" fill="#c2c2c2"/>
              <path id="Path_5606" data-name="Path 5606" d="M162.076,497.888c.11-.045.211-.08.322-.125a2.32,2.32,0,0,0-.073.655l-.284.071c0-.213.014-.416.035-.6" fill="#999"/>
              <path id="Path_5607" data-name="Path 5607" d="M162.408,499.121c-.1.026-.193.043-.285.061a2.94,2.94,0,0,1-.082-.694l.284-.07a2.853,2.853,0,0,0,.083.7" fill="#999"/>
              <path id="Path_5608" data-name="Path 5608" d="M162.181,496.06l.022,1.146-.614.085-.014-1.081Z" fill="#dddfdf"/>
              <path id="Path_5609" data-name="Path 5609" d="M161.589,497.291l.614-.085a5.794,5.794,0,0,0-.127.682c-.294.107-.589.215-.874.312a2.675,2.675,0,0,1,.387-.909" fill="#c2c2c2"/>
              <path id="Path_5610" data-name="Path 5610" d="M162.076,497.888c-.021.185-.032.388-.035.6l-.854.229a1.426,1.426,0,0,1,.015-.518c.286-.1.58-.205.874-.312" fill="#dddfdf"/>
              <path id="Path_5611" data-name="Path 5611" d="M162.041,498.488a2.934,2.934,0,0,0,.082.694c-.256.052-.5.1-.751.149l-.01,0a1.534,1.534,0,0,1-.175-.612Z" fill="#dddfdf"/>
              <path id="Path_5612" data-name="Path 5612" d="M161.187,498.718a1.539,1.539,0,0,0,.175.611c-.8.157-1.541.3-1.99.373-.349.061-.64-.109-.637-.331Z" fill="#999"/>
              <path id="Path_5613" data-name="Path 5613" d="M161.2,498.2a1.4,1.4,0,0,0-.015.518l-2.452.653c0-.176.178-.377.647-.528s1.122-.393,1.82-.643" fill="#c2c2c2"/>
            </g>
          </g>
        </g>
      </g>
      <path id="Path_5614" data-name="Path 5614" d="M162.861,415.869l-.484-.875L319.625,327.58l.5.865L221.1,388.268l-.516-.855,76.624-46.289Z" fill="#404040"/>
      <g id="Group_12992" data-name="Group 12992">
        <g id="Group_12991" data-name="Group 12991" transform="translate(0 -48.625)" clip-path="url(#clip-path-17)">
          <g id="Group_12990" data-name="Group 12990" transform="translate(311.018 46.846)">
            <g id="Group_12989" data-name="Group 12989" clip-path="url(#clip-path-18)">
              <path id="Path_5615" data-name="Path 5615" d="M330.508,295.662a2.325,2.325,0,0,1,.01.27c0,.879-.01,1.981-.021,3.188l-7.13,14.968q-.315-.651-.6-1.371Z" transform="translate(-311.018 -4.422)" fill="#ffb450"/>
              <path id="Path_5616" data-name="Path 5616" d="M330.5,295.667a2.511,2.511,0,0,1,.011.293l-7.741,17.055a22.116,22.116,0,0,1-.9-2.813l8.021-16.269a5.91,5.91,0,0,1,.609,1.711Z" transform="translate(-311.018 -4.72)" fill="#404040"/>
              <path id="Path_5617" data-name="Path 5617" d="M323.367,313.58l7.13-14.968c-.02,1.407-.04,2.954-.08,4.431l-5.94,12.46a15.051,15.051,0,0,1-1.11-1.922" transform="translate(-311.018 -3.914)" fill="#404040"/>
              <path id="Path_5618" data-name="Path 5618" d="M324.478,314.852l5.939-12.46c-.02,1.137-.06,2.239-.1,3.212l-4.78,10.666a14.757,14.757,0,0,1-1.06-1.418" transform="translate(-311.018 -3.263)" fill="#ffb450"/>
              <path id="Path_5619" data-name="Path 5619" d="M325.537,315.8l4.78-10.666c-.03.586-.059,1.125-.09,1.606-.06.973-.179,2.309-.339,3.751l-.01.012-3.12,6.54a11.6,11.6,0,0,1-1.221-1.242" transform="translate(-311.018 -2.792)" fill="#404040"/>
              <path id="Path_5620" data-name="Path 5620" d="M318.968,288.338a21.1,21.1,0,0,0-.051,3.329c.04,1.02.1,2.192.181,3.47.069,1.16.16,2.391.25,3.657.08,1.067.17,2.145.26,3.223.08,1.055.169,2.11.259,3.13v.012c.09,1.008.181,1.969.271,2.872.13,1.418.25,2.672.35,3.657.07.6.12,1.09.16,1.465.04.352.08.68.12.973a7.382,7.382,0,0,0,.88,3.036v.012c.24.363.49.586.75.961a10.82,10.82,0,0,0,1.33,1.453c.38.352.809.727,1.25,1.1.429.352.86.715,1.28,1.031.22.164.439.328.64.469.15.105.28.2.4.27,1.05.7,1.1.516,1.1.469a8.864,8.864,0,0,1,.09,1.9c-.081.727-.36,1.242-1.15.586-1.69-1.395-4.82-4.173-4.82-4.173a8.477,8.477,0,0,1-2.811-5.72c-.159-1.278-.43-3.493-.739-6.189-.141-1.3-.3-2.708-.45-4.173-.34-3.411-.67-7.138-.851-10.514-.019-.445-.039-.879-.059-1.313-.04-1.137-.071-2.215-.071-3.212,0-.762.021-1.489.051-2.145,0,0,0-5.568,2.81-3.516,1.059.774,2.09,1.512,3.1,2.321.271.223.54.445.8.668.33.281.649.574.97.891a1.27,1.27,0,0,1,.17.164l.63.633c.339.363.679.738,1.009,1.149l.781.985.02.023c.149.211.31.422.46.645a11.758,11.758,0,0,1,2.159,5.415,5.91,5.91,0,0,0-.609-1.711,12.454,12.454,0,0,0-.971-1.559c-.38-.539-.809-1.067-1.26-1.594q-.479-.563-.99-1.09c-.329-.352-.67-.692-1-1.02-.359-.352-.71-.68-1.059-1-.491-.457-.96-.867-1.38-1.231-.521-.445-.981-.82-1.34-1.114-.221-.164-.391-.3-.53-.4-.09-.07-.161-.117-.21-.152a3.945,3.945,0,0,0-.47-.27c-.67-.3-1.33-.141-1.71,2.11Z" transform="translate(-311.018 -6.429)" fill="#ffa624"/>
              <path id="Path_5621" data-name="Path 5621" d="M329.888,294.161l-8.021,16.269c-.169-.68-.319-1.407-.439-2.18l7.489-15.648a12.454,12.454,0,0,1,.971,1.559" transform="translate(-311.018 -4.949)" fill="#ffb450"/>
              <path id="Path_5622" data-name="Path 5622" d="M326.758,316.252l3.12-6.54c-.171,1.618-.4,3.376-.66,4.97h-.011l-1.38,2.415a9.174,9.174,0,0,1-1.069-.844" transform="translate(-311.018 -2.003)" fill="#ffb450"/>
              <path id="Path_5623" data-name="Path 5623" d="M329.207,313.952h.011c-.181,1.067-.37,2.063-.58,2.9a7.575,7.575,0,0,1-.811-.481Z" transform="translate(-311.018 -1.274)" fill="#404040"/>
              <path id="Path_5624" data-name="Path 5624" d="M328.917,292.836l-7.489,15.648c-.111-.645-.191-1.336-.261-2.051-.05-.492-.09-1-.14-1.477l6.63-13.714c.45.527.88,1.055,1.26,1.594" transform="translate(-311.018 -5.183)" fill="#404040"/>
              <path id="Path_5625" data-name="Path 5625" d="M328.638,316.493c-.07.34-.16.645-.24.914v.012c0,.047-.05.234-1.1-.469l.529-.938a7.687,7.687,0,0,0,.811.481" transform="translate(-311.018 -0.919)" fill="#232323"/>
              <path id="Path_5626" data-name="Path 5626" d="M327.338,321.694c.79.656,1.069.141,1.149-.586-.16,2.614-1.92.891-1.92.891l-3.219-2.731-2.16-1.829s-.771-.352-1.451-4.22c-.07-.387-.129-.809-.2-1.266-.139-1.02-.279-2.227-.389-3.669-.071-.821-.13-1.7-.18-2.672.309,2.7.58,4.911.739,6.189a8.477,8.477,0,0,0,2.811,5.72s3.13,2.778,4.82,4.173" transform="translate(-311.018 -2.709)" fill="#d37e0d"/>
              <path id="Path_5627" data-name="Path 5627" d="M327.878,75.552V415.1l-.021-.034c-.259-.492-.509-.967-.78-1.425V75.552Z" transform="translate(-311.018 -130.232)" fill="#232323"/>
              <path id="Path_5628" data-name="Path 5628" d="M327.827,316.136l-.529.938c-.12-.07-.25-.164-.4-.27-.2-.141-.42-.3-.639-.469l.5-1.043a9.09,9.09,0,0,0,1.069.844" transform="translate(-311.018 -1.043)" fill="#e0902f"/>
              <path id="Path_5629" data-name="Path 5629" d="M327.657,291.4l-6.63,13.714c-.09-.949-.189-1.887-.28-2.813l5.92-11.991q.512.527.99,1.09" transform="translate(-311.018 -5.343)" fill="#ffb450"/>
              <path id="Path_5630" data-name="Path 5630" d="M326.758,315.474l-.5,1.043c-.42-.316-.851-.68-1.28-1.031l.559-1.254a11.605,11.605,0,0,0,1.221,1.242" transform="translate(-311.018 -1.225)" fill="#232323"/>
              <path id="Path_5631" data-name="Path 5631" d="M326.667,290.462l-5.92,11.991q-.164-1.53-.3-2.989l5.22-10.022c.33.328.671.668,1,1.02" transform="translate(-311.018 -5.493)" fill="#404040"/>
              <path id="Path_5632" data-name="Path 5632" d="M326.067,75.552V412.939q-.315-.485-.63-.919a1.589,1.589,0,0,0-.17-.238V75.552Z" transform="translate(-311.018 -130.232)" fill="#232323"/>
              <path id="Path_5633" data-name="Path 5633" d="M325.667,289.588l-5.22,10.022c-.109-1.043-.21-2.051-.309-3.024l4.469-7.994c.35.316.7.645,1.06,1" transform="translate(-311.018 -5.639)" fill="#ffb450"/>
              <path id="Path_5634" data-name="Path 5634" d="M324.478,313.022a14.754,14.754,0,0,0,1.06,1.418l-.56,1.254c-.44-.375-.871-.75-1.25-1.1Z" transform="translate(-311.018 -1.434)" fill="#e0902f"/>
              <path id="Path_5635" data-name="Path 5635" d="M325.138,328.742a4.5,4.5,0,0,1-.05,1.934,3.592,3.592,0,0,1-3.441,2.168c-2.99.07-4.62-6.154-4.62-6.154a15.3,15.3,0,0,1,.87-6.365s-.54,5.193,1.071.8,2.54-3.223,2.92-2.86-1.431,3.821-1.111,6.447a12.7,12.7,0,0,0,.83,3.434c.04.117.081.234.13.352a.04.04,0,0,0,.01.023c.091.293,1.15,3.634,2.441.539.459-1.078.8-.645.95-.316m-7.471-2.051c.78,5.075,4.971,4.817,4.971,4.817-3.521-2.051-3.1-8.2-3.1-8.2a7.494,7.494,0,0,0-1.019,1.559c-.2.586-1.531-2.637-.851,1.829" transform="translate(-311.018 -0.603)" fill="#6d6d6d"/>
              <path id="Path_5636" data-name="Path 5636" d="M324.978,326.763a4.33,4.33,0,0,1,.159.656c-.15-.328-.49-.762-.949.316-1.291,3.094-2.351-.246-2.441-.539.041.094.09.2.14.293,1.32,2.5,1.78-.07,1.461-.867s.9-2.051,1.63.141" transform="translate(-311.018 0.72)" fill="#545454"/>
              <path id="Path_5637" data-name="Path 5637" d="M321.647,331.546a3.589,3.589,0,0,0,3.44-2.168c-.75,3.247-4.72,5.286-6.83.785a13.606,13.606,0,0,1-1.23-4.771s1.63,6.224,4.62,6.154" transform="translate(-311.018 0.696)" fill="#545454"/>
              <path id="Path_5638" data-name="Path 5638" d="M324.607,288.773l-4.47,7.994c-.11-1.254-.21-2.438-.28-3.528l3.37-5.7c.42.363.89.774,1.38,1.231" transform="translate(-311.018 -5.82)" fill="#404040"/>
              <path id="Path_5639" data-name="Path 5639" d="M324.478,313.3l-.75,1.571a10.908,10.908,0,0,1-1.331-1.453l.97-2.04a14.707,14.707,0,0,0,1.111,1.922" transform="translate(-311.018 -1.716)" fill="#232323"/>
              <path id="Path_5640" data-name="Path 5640" d="M324.3,122.552v335.7c-.261-.388-.53-.776-.8-1.164V122.552Z" transform="translate(-311.018 -177.232)" fill="#232323"/>
              <path id="Path_5641" data-name="Path 5641" d="M323.367,311.583l-.97,2.04c-.259-.375-.509-.6-.75-.961v-.012l1.12-2.438q.285.721.6,1.371" transform="translate(-311.018 -1.917)" fill="#e0902f"/>
              <path id="Path_5642" data-name="Path 5642" d="M323.348,318.15s.09,3.012-.71,4.032a9.452,9.452,0,0,0-1.03,6.951,12.658,12.658,0,0,1-.83-3.434c-.321-2.626,1.5-6.072,1.11-6.447s-1.3-1.535-2.92,2.86-1.07-.8-1.07-.8a12.224,12.224,0,0,0,.29-1.465,4.505,4.505,0,0,0-.29-2.684,13.493,13.493,0,0,1-1.85-3.341h.01s.46-1.723,3.68-1.723c.679,3.868,1.45,4.22,1.45,4.22Z" transform="translate(-311.018 -1.592)" fill="#545454"/>
              <path id="Path_5643" data-name="Path 5643" d="M323.228,287.706l-3.371,5.7c-.09-1.3-.129-2.473-.11-3.47l2.14-3.341c.36.293.82.668,1.341,1.114" transform="translate(-311.018 -5.983)" fill="#ffb450"/>
              <path id="Path_5644" data-name="Path 5644" d="M322.768,310.625l-1.12,2.438a7.414,7.414,0,0,1-.88-3.036h.01l1.09-2.215a21.8,21.8,0,0,0,.9,2.813" transform="translate(-311.018 -2.331)" fill="#232323"/>
              <path id="Path_5645" data-name="Path 5645" d="M322.638,330.707s-4.19.258-4.971-4.817c-.679-4.466.651-1.242.851-1.829a7.536,7.536,0,0,1,1.019-1.559s-.42,6.154,3.1,8.2" transform="translate(-311.018 0.198)" fill="#7f7f7f"/>
              <path id="Path_5646" data-name="Path 5646" d="M321.888,286.656,319.747,290c.031-2.59.46-4.044,1.611-3.739.139.094.309.234.53.4" transform="translate(-311.018 -6.048)" fill="#404040"/>
              <path id="Path_5647" data-name="Path 5647" d="M321.867,308.132l-1.09,2.215h-.01c-.04-.293-.08-.621-.12-.973-.04-.375-.09-.867-.16-1.465l.941-1.957c.12.774.269,1.5.439,2.18" transform="translate(-311.018 -2.651)" fill="#e0902f"/>
              <path id="Path_5648" data-name="Path 5648" d="M321.428,306.47l-.94,1.957c-.1-.985-.22-2.239-.35-3.657l.89-1.829c.049.481.089.985.139,1.477.071.715.151,1.407.261,2.051" transform="translate(-311.018 -3.169)" fill="#232323"/>
              <path id="Path_5649" data-name="Path 5649" d="M321.357,286.314c-1.15-.3-1.58,1.149-1.61,3.739l-.83,1.289a21.261,21.261,0,0,1,.05-3.329V288l1.71-2.11a3.867,3.867,0,0,1,.47.27c.05.035.12.082.21.152" transform="translate(-311.018 -6.104)" fill="#232323"/>
              <path id="Path_5650" data-name="Path 5650" d="M321.027,303.355l-.89,1.829c-.089-.9-.18-1.864-.27-2.872V302.3l.88-1.758c.091.926.19,1.864.28,2.813" transform="translate(-311.018 -3.582)" fill="#e0902f"/>
              <path id="Path_5651" data-name="Path 5651" d="M320.747,300.981l-.88,1.758c-.09-1.02-.18-2.075-.26-3.13l.84-1.618q.135,1.459.3,2.989" transform="translate(-311.018 -4.021)" fill="#232323"/>
              <path id="Path_5652" data-name="Path 5652" d="M320.678,285.909l-1.71,2.11c.38-2.25,1.04-2.415,1.71-2.11" transform="translate(-311.018 -6.121)" fill="#e0902f"/>
              <path id="Path_5653" data-name="Path 5653" d="M320.447,298.436l-.84,1.618c-.09-1.078-.18-2.157-.26-3.223l.79-1.418c.1.973.2,1.981.31,3.024" transform="translate(-311.018 -4.465)" fill="#e0902f"/>
              <path id="Path_5654" data-name="Path 5654" d="M320.138,295.93l-.79,1.418c-.09-1.266-.181-2.5-.25-3.657l.76-1.289c.07,1.09.17,2.274.28,3.528" transform="translate(-311.018 -4.983)" fill="#232323"/>
              <path id="Path_5655" data-name="Path 5655" d="M319.857,292.912l-.76,1.289c-.08-1.278-.14-2.45-.18-3.47l.83-1.289c-.02,1,.02,2.168.11,3.47" transform="translate(-311.018 -5.493)" fill="#e0902f"/>
              <path id="Path_5656" data-name="Path 5656" d="M319.537,311.022c.07.457.13.879.2,1.266-3.22,0-3.679,1.723-3.679,1.723h-.01a4.078,4.078,0,0,1-.16-.856c-.04-.586-.05-1.371-.05-2.133h.05s.429.949,3.649,0" transform="translate(-311.018 -1.778)" fill="#232323"/>
              <path id="Path_5657" data-name="Path 5657" d="M319.147,307.892c.11,1.442.25,2.649.39,3.669-3.22.949-3.65,0-3.65,0h-.05c0-1.348.05-2.637.05-2.637s.07-1.031.89-1.031Z" transform="translate(-311.018 -2.317)" fill="#404040"/>
              <path id="Path_5658" data-name="Path 5658" d="M319.147,309.312h-2.37a31.2,31.2,0,0,1-3.49-9.67c.19.41,4.43,9.037,5.23,2.825.15,1.465.31,2.872.45,4.173.05.973.11,1.852.18,2.672" transform="translate(-311.018 -3.737)" fill="#dd9845"/>
              <path id="Path_5659" data-name="Path 5659" d="M313.287,300.057a31.25,31.25,0,0,0,3.49,9.67c-.82,0-.889,1.031-.889,1.031s-.05,1.289-.05,2.637c0,.762.01,1.547.05,2.133a4.078,4.078,0,0,0,.16.856,13.456,13.456,0,0,0,1.849,3.341,4.5,4.5,0,0,1,.29,2.684,13.015,13.015,0,0,1-2.759-3.891v-.012a16,16,0,0,1-1.221-4.009c-.51-2.848-1.159-11.229-1.579-17.1,0-.059-.011-.117-.011-.164l.25.094c.09.832.231,1.747.42,2.731" transform="translate(-311.018 -4.152)" fill="#b56504"/>
              <path id="Path_5660" data-name="Path 5660" d="M318.188,319.283a12.1,12.1,0,0,1-.291,1.465l-2.01-1.618a5.022,5.022,0,0,1-2.11-3.411s1.86,2.567,1.65-.328a13,13,0,0,0,2.761,3.891" transform="translate(-311.018 -1.026)" fill="#d37e0d"/>
              <path id="Path_5661" data-name="Path 5661" d="M312.867,298.062c-.14-1.207-.2-2.215-.22-2.977,0,0,1.92-2.79,5.02-1.981.18,3.376.511,7.1.85,10.514-.8,6.212-5.04-2.415-5.23-2.825-.19-.985-.33-1.9-.42-2.731" transform="translate(-311.018 -4.888)" fill="#ffb450"/>
              <path id="Path_5662" data-name="Path 5662" d="M317.537,289.491v3.235l-.8.293v-5.357c.49.328.8.563.8.563Z" transform="translate(-311.018 -5.799)" fill="#232323"/>
              <path id="Path_5663" data-name="Path 5663" d="M317.537,122.552V459.658s-.31-.407-.8-.977V122.552Z" transform="translate(-311.018 -177.232)" fill="#232323"/>
              <path id="Path_5664" data-name="Path 5664" d="M316.737,287.738v5.357l-.85.3v-6.177c.32.176.61.363.85.516" transform="translate(-311.018 -5.875)" fill="#d37e0d"/>
              <path id="Path_5665" data-name="Path 5665" d="M315.888,287.286v6.177l-.8.293v-6.9c.279.141.55.281.8.434" transform="translate(-311.018 -5.938)" fill="#232323"/>
              <path id="Path_5666" data-name="Path 5666" d="M315.888,75.552V411.579c-.25-.222-.521-.426-.8-.631V75.552Z" transform="translate(-311.018 -130.232)" fill="#232323"/>
              <path id="Path_5667" data-name="Path 5667" d="M315.428,320.236c.21,2.9-1.65.328-1.65.328-.321-1.313-.621-3.259-1.14-6.4-1.271-7.678-1.62-20.887-1.62-25.869a4.633,4.633,0,0,1,.03-.551c.17-.152,1.05-.926,1.31-.152a10.026,10.026,0,0,0-.151,1.875v3.516s.16,2.532.41,5.966c0,.047.011.106.011.164.42,5.872,1.07,14.253,1.579,17.1a16,16,0,0,0,1.221,4.009Z" transform="translate(-311.018 -5.869)" fill="#ffa624"/>
              <path id="Path_5668" data-name="Path 5668" d="M315.088,286.905v6.9l-.841.3v-7.572a8.674,8.674,0,0,1,.841.363" transform="translate(-311.018 -5.992)" fill="#d37e0d"/>
              <path id="Path_5669" data-name="Path 5669" d="M314.247,286.578v7.572l-.8.293v-8.111a5.581,5.581,0,0,1,.8.246" transform="translate(-311.018 -6.028)" fill="#232323"/>
              <path id="Path_5670" data-name="Path 5670" d="M314.247,122.2v335.23a4.092,4.092,0,0,0-.8-.428V122.2Z" transform="translate(-311.018 -176.882)" fill="#232323"/>
              <path id="Path_5671" data-name="Path 5671" d="M313.447,286.342v8.111l-.8.293s-.03.539,0,1.489c.02.762.08,1.77.22,2.977l-.25-.094c-.25-3.434-.41-5.966-.41-5.966v-3.516a10.039,10.039,0,0,1,.15-1.875c.191-1,.51-1.36.74-1.489.121.023.23.035.35.07" transform="translate(-311.018 -6.038)" fill="#d37e0d"/>
              <path id="Path_5672" data-name="Path 5672" d="M311.048,287.917a1.639,1.639,0,0,1,2.05-1.641c-.231.129-.55.492-.74,1.489-.26-.774-1.14,0-1.31.152" transform="translate(-311.018 -6.042)" fill="#ffb450"/>
            </g>
          </g>
        </g>
      </g>
      <path id="Path_5673" data-name="Path 5673" d="M445.981,439.811,323.991,329.342l39.988,93.752-1.225-.287-40.531-95.131a3,3,0,0,1-.336-.473c-.15-.27-.288-.583-.288-.583.611-.272,1.561.62,1.561.62l123.7,112.013a1.292,1.292,0,0,0-.629.113.554.554,0,0,0-.245.445" fill="#404040"/>
    </g>
  </g>
</svg>
