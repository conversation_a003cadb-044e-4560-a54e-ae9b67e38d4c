// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

window.matchMedia =
  window.matchMedia ||
  function () {
    return {
      matches: false,
      // using third party CSS for react-slick package, din't found other way to fix lint issue so currently commented lint issues
      // eslint-disable-next-line
      addListener() {},
      // eslint-disable-next-line
      removeListener() {},
    };
  };
