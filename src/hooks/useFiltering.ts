import {ChangeEvent, useState} from 'react';

const useFiltering = () => {
  const [page, setPage] = useState<number>(1);
  const [searchKey, setSearchKey] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');
  const [showEntries, setShowEntries] = useState<string>('5');
  const [sortBy, setSortBy] = useState<string>('');
  const [sortingOrder, setSortingOrder] = useState<1 | -1>(-1);

  const handlePageChange = (pageNumber: string | number) => {
    setPage(Number(pageNumber));
  };

  const handleShowEntries = (event: ChangeEvent<HTMLSelectElement>) => {
    setShowEntries(event.target.value);
    setPage(1);
  };

  const handleSearchKey = (value: string) => {
    setSearchKey(value);
    setPage(1);
  };
  const handleSearchQuery = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchValue(event.target.value);
    setPage(1);
  };

  const handleSortBy = (sortKey: string, order: 'asc' | 'desc') => {
    setSortBy(sortKey);
    setSortingOrder(order === 'asc' ? 1 : -1);
  };

  return {
    page,
    searchKey,
    searchValue,
    showEntries,
    sortBy,
    sortingOrder,
    handlePageChange,
    handleShowEntries,
    handleSearchQuery,
    handleSearchKey,
    handleSortBy,
  };
};

export default useFiltering;
