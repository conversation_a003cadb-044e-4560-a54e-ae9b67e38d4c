import mockData from '@pages/Dashboard/mock/mockEximProducts.json';
import store from '@store';
import {fireEvent, render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';
import configureStore from 'redux-mock-store';

import App from './App';

describe('App', () => {
  const mockStore = configureStore([]);
  // jest.useFakeTimers();
  const header = {headerPageName: 'DASHBOARD', businessName: ''};
  const auth = {
    isLoggedIn: true,
    userData: {
      firstName: 'user',
      lastName: '',
      email: '',
      mobile: '',
      userID: '',
      createdAt: '',
    },
  };
  const loader = {isAppLoading: false};
  const alert = {
    isOpen: true,
    message: 'Error',
    code: 400,
    alertType: 'danger',
  };
  it('Should load Application', () => {
    const mockedStore = mockStore({
      loader: {isAppLoading: true},
      alert: {
        isOpen: false,
        message: 'Error',
        code: 'ERR_NETWORK',
        alertType: 'danger',
      },
      dashboard: {
        eximProducts: mockData,
        dashboardActiveProduct: 'Duty Drawback',
      },
      header,
      auth,
    });
    render(
      <BrowserRouter>
        <Provider store={mockedStore}>
          <App />
        </Provider>
      </BrowserRouter>
    );
    const loaderWrapper = screen.getAllByTestId('loader-wrapper')[0];
    expect(loaderWrapper).toBeInTheDocument();
  });

  it('Should display the alertbox', async () => {
    const mockedStore = mockStore({
      loader,
      alert,
      header,
      auth,
      dashboard: {
        eximProducts: mockData,
        dashboardActiveProduct: 'Duty Drawback',
      },
    });

    const {getByTestId} = render(
      <BrowserRouter>
        <Provider store={mockedStore}>
          <App />
        </Provider>
      </BrowserRouter>
    );
    const closeBtn = getByTestId('close-btn');
    fireEvent.click(closeBtn);
    expect(store.getState().alert.isOpen).toBeFalsy();
  });
});
