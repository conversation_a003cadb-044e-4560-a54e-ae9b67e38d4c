@import '@utils/main.scss';

body {
  height: 100vh;
  background-color: $primary-bg;
}

.dashboard-scroll-x {
  overflow-y: scroll;
}

.app-loader {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
  background-color: rgba(213, 213, 213, 0.5);
}

// INFO: Hiding the bottom arrow
.scroll-to-top {
  visibility: hidden;
}

table.common-table {
  width: 100%;
  border-spacing: 0;
}

// INFO Below Style for input fields globally
.common-input {
  .MuiInputBase-root {
    border-radius: 7px;
  }
  .MuiInputLabel-root,
  .MuiInputBase-root {
    font-size: $font-size-sm;
    &:focus-visible {
      border: none;
    }
  }
  .MuiFormHelperText-root {
    margin-left: 0;
    color: $error-msg-color;
    // TODO: We need this code in future for fix the error message position
    // position: absolute;
    // top: 35px;
  }
  // .MuiInputBase-multiline + .MuiFormHelperText-root {
  //   top: 75px;
  // }
  .MuiInputBase-root.MuiOutlinedInput-root.MuiInputBase-formControl.MuiAutocomplete-inputRoot {
    padding-right: 0;
    gap: 5px;
  }
}
