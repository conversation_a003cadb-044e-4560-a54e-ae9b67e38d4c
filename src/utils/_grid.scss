@use "sass:math";

$grid-columns: 12;
$gap: 10px;

$grid-gap: (
  "0": 0,
  "1": $gap,
  "2": $gap * 2,
  "3": $gap * 3,
);

.row {
  display: flex;
  flex-flow: row wrap;
}

//grid gap
@each $key, $val in $grid-gap {
  .gap-#{$key} > * {
    padding: $val;
  }
  .gap-#{$key} {
    margin-left: -$val;
    margin-right: -$val;
  }
}

//col classes
@include xs {
  @for $i from 1 through $grid-columns {
    .col-#{$i}-xs {
      box-sizing: border-box;
      flex-grow: 0;
      width: math.div($i * 100%, $grid-columns);
    }
  }
}

@include sm {
  @for $i from 1 through $grid-columns {
    .col-#{$i}-sm {
      box-sizing: border-box;
      flex-grow: 0;
      width: math.div($i * 100%, $grid-columns);
    }
  }
}


@include md {
  @for $i from 1 through $grid-columns {
    .col-#{$i}-md {
      box-sizing: border-box;
      flex-grow: 0;
      width: math.div($i * 100%, $grid-columns);
    }
  }
}

@include lg {
  @for $i from 1 through $grid-columns {
    .col-#{$i}-lg {
      box-sizing: border-box;
      flex-grow: 0;
      width: math.div($i * 100%, $grid-columns);
    }
  }
}

@include xl {
  @for $i from 1 through $grid-columns {
    .col-#{$i}-xl {
      box-sizing: border-box;
      flex-grow: 0;
      width: math.div($i * 100%, $grid-columns);
    }
  }
}
