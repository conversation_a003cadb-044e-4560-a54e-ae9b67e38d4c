// media-query break points
$xs: 320px;
$sm: 576px;
$md: 768px;
$lg: 992px;
$xl: 1200px;
$xxl: 1440px;

$breakpoints: (
  "xs": $xs,
  "sm": $sm,
  "md": $md,
  "lg": $lg,
  "xl": $xl,
  "xxl": $xxl,
) !default;

@mixin xs {
  @media (min-width: map-get($breakpoints,"xs")) {
    @content;
  }
}

@mixin sm {
  @media (min-width: map-get($breakpoints,"sm")) {
    @content;
  }
}

@mixin md {
  @media (min-width: map-get($breakpoints,"md")) {
    @content;
  }
}

@mixin lg {
  @media (min-width: map-get($breakpoints,"lg")) {
    @content;
  }
}

@mixin xl {
  @media (min-width: map-get($breakpoints,"xl")) {
    @content;
  }
}

@mixin breakpoint($bp: 0) {
  @media (min-width: $bp) {
    @content;
  }
}

@mixin moreThan($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Available breakpoints are: #{map-keys($breakpoints)}.";
  }
}

@mixin lessThan($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unfortunately, no value could be retrieved from `#{$breakpoint}`. "
        + "Available breakpoints are: #{map-keys($breakpoints)}.";
  }
}
