// flex
@mixin flex-item(
  $flex-direction: row,
  $justify-content: center,
  $align-items: center,
  $flex-wrap: wrap,
  $gap: 0
) {
  display: flex;
  flex-direction: $flex-direction;
  align-items: $align-items;
  justify-content: $justify-content;
  flex-wrap: $flex-wrap;
  gap: $gap;
}

@mixin flex-grow-basis($flex-grow: 1, $flex-basis: 0) {
  flex-grow: $flex-grow;
  flex-basis: $flex-basis;
}

// font-styles
@mixin font-styles($font-size, $font-weight, $font-color) {
  @include font-size($font-size);
  font-weight: $font-weight;
  color: $font-color;
}

// Hide Scrollbar
@mixin hide-scrollbar() {
  &::-webkit-scrollbar {
    width: 0px;
  }
}

@mixin scrollbar($width: none, $height: none) {
  &::-webkit-scrollbar {
    width: $width;
    height: $height;
  }
  &::-webkit-scrollbar-track {
    background: $scrollbar-background;
  }

  &::-webkit-scrollbar-thumb {
    background: $scrollbar-color;
    border-radius: 6px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: $text-mute;
  }
}
