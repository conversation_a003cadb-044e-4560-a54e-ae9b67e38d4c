# Coding practice for the project

1. All branch names must in coordination with the feature/component that we're
   building eg: for input field - feat-input-field similarly some prefixes will
   be: chore, fix
2. For commits there are different prefix that we need to follow eg: for a chore
   prefix with be chore-"descriptive message about the chore" similarly we will
   use feat for new feature, fix for bug fix, chore for chores, test for test
   coverage or test updates etc.
3. In every PR there should be description of the PR, screenshot of the
   component and test coverage screenshot of the component
4. Test coverage should be 100% for all the components use npx majestic and
   record coverage for components. Please add screenshot of the component once
   the PR is given in the comment section
5. DO NOT disble any eslint rule, eslint rules are in place to follow best
   practices and right good code.
6. Check README.md (this space) to make sure all the coding practices given here
   are followed
