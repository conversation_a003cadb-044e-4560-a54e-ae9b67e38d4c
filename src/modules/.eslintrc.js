module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: 6,
    project: ["./tsconfig.json"],
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["react", "@typescript-eslint", "import", "prettier", "react-hooks"],
  parser: '@typescript-eslint/parser',
  extends: ["eslint:recommended", "airbnb", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"],
  rules: {
    "prettier/prettier": ["error", { "endOfLine": "auto" }, { usePrettierrc: true }],
    "react/jsx-filename-extension": [1, { "extentions": [".js", ".jsx", "tsx", "ts"] }],
    "import/extensions": 'off',
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "react/prefer-stateless-function": "off",
    "react/prop-types": "off",
    "import/no-extraneous-dependencies": ["error", { "devDependencies": true }],
    "react/jsx-filename-extension": [
      "warn",
      { "extensions": ["*.js", ".jsx", "*.ts", ".tsx"] }
    ],
    "react/react-in-jsx-scope": "off",
    "import/no-extraneous-dependencies": [
      "error", {
        "devDependencies": true
      }
    ],
    // label-has-associated-control is off  for label 
    "jsx-a11y/label-has-associated-control": ["error", {
      "required": {
        "some": ["nesting", "id"]
      }
    }],
    "jsx-a11y/label-has-for": ["error", {
      "required": {
        "some": ["nesting", "id"]
      }
    }],

  },
  "settings": {
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"]
    },
    "import/resolver": {
      "node": {
        "extensions": [".js", ".jsx", ".ts", ".tsx"]
      }
    }
  }
}
