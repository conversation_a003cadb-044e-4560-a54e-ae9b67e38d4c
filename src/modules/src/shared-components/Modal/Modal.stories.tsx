import {ComponentMeta, ComponentStory} from '@storybook/react';
import {useState} from 'react';

import Modal from './Modal';

export default {
  title: 'Modal',
  component: Modal,
} as ComponentMeta<typeof Modal>;

const Template: ComponentStory<typeof Modal> = function ModalTemplate(args) {
  const {isCloseIconVisible, content, header, footer, closeIcon} = args;
  const [isOpen, setIsOpen] = useState(false);

  const handleIsOpen = (state: boolean) => {
    setIsOpen(state);
  };

  return (
    <>
      <button type='button' onClick={() => handleIsOpen(true)}>
        Click to open modal
      </button>
      <Modal
        isOpen={isOpen}
        isCloseIconVisible={isCloseIconVisible}
        content={content}
        header={header}
        footer={footer}
        closeIcon={closeIcon}
        onClose={() => handleIsOpen(false)}
        onOutSideClickClose={() => handleIsOpen(false)}
      />
    </>
  );
};

export const ModalComponent = Template.bind({});

ModalComponent.args = {
  content: 'Content',
  isCloseIconVisible: true,
  header: 'Title',
  footer: 'footer section',
};
