@import '../../utils/main.scss';

.modal {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
  font-family: $font-family-name;

  &:before {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    background-color: $black;
  }

  &-body {
    width: 500px;
    background-color: $white;
    border: 0;
    border-radius: 2px;
    pointer-events: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    outline: 0;
    .modal-header {
      @include flex-item(_, space-between, flex-end);
      position: relative;
      padding: 16px 24px;
      border-bottom: 1px solid $gray-200;
      border-radius: 2px 2px 0 0;
      min-height: 30px;
      text-align: start;
      .modal-title {
        margin: 0;
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
      }
      .close-button {
        background: none;
        transition: color 0.3s;
        border: none;
        cursor: pointer;
      }
    }
    .modal-footer {
      padding: 24px;
      border-top: 1px solid $gray-200;
      border-radius: 0 0 2px 2px;
      text-align: start;
    }
    .modal-content {
      padding: 24px;
      min-height: 120px;
      @include flex-item();
      justify-content: flex-start;
    }
  }
}
