@import '../../utils/main.scss';

.accordion-item{
   border: none;
   border-radius: 4px;
   background-color: $gray-200;
   font-family: $font-family-name;
  .accordion-title {
    @include flex-item(row, space-between, center);
    padding: 10px;
    position: relative;
    cursor: pointer;
    &:hover {
      background-color: $gray-300;
    }

    .accordion-down-icon{
      position:absolute;    
      right: 12px;
      top: 10px;
      transition: all 0.2s linear;
    }
    .accordion-up-icon{
      top: 7px;
      transform: rotate(180deg);
    }
  }  
  .accordion-title-disabled{
    opacity: 0.5;
    cursor: default;
    &:hover {
      background-color: $gray-200;
    }
  }
 .accordion-content {
    padding: 16px 10px;
    background: $white;
  }
  .accordion-border-show {
    border: 1px solid $gray-300;
  }
}