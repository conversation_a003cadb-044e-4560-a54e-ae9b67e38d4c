import {ComponentMeta} from '@storybook/react';
import {BrowserRouter} from 'react-router-dom';

import aboutIcon from '../../assets/images/about.png';
import contactsIcon from '../../assets/images/contacts.png';
import homeIcon from '../../assets/images/home.svg';
import optionIcon from '../../assets/images/option.png';
import serviceIcon from '../../assets/images/service.png';
import MenuItem from './MenuItem';
import './MenuItem.scss';

export default {
  title: 'SideMenu',
  component: MenuItem,
} as ComponentMeta<typeof MenuItem>;

function Template() {
  const menuItems = [
    {
      text: 'Home',
      icon: homeIcon,
      href: '1',
    },
    {
      text: 'About',
      href: '2',
      icon: aboutIcon,
    },
    {
      text: 'Services',
      href: 'help',
      icon: serviceIcon,
      submenu: [
        {text: 'Option 1', href: '11', icon: optionIcon},
        {text: 'Option 2', href: '12', icon: optionIcon},
      ],
    },
    {text: 'Contact', href: '3', icon: contactsIcon},
  ];
  return (
    <div className='sidebar_items'>
      <BrowserRouter>
        {menuItems.map((menuItem) => (
          <MenuItem
            key={menuItem.text}
            href={menuItem.href}
            text={menuItem.text}
            submenu={menuItem.submenu}
            icon={menuItem.icon}
          />
        ))}
      </BrowserRouter>
    </div>
  );
}

export const SideMenu = Template.bind({});
