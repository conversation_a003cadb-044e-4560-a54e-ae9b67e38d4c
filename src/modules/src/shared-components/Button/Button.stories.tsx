import {storiesOf} from '@storybook/react';

import Button from './Button';
import DeleteIcon from './assets/icons8-delete.png';

// button colors
const colors = [
  'primary',
  'secondary',
  'info',
  'success',
  'danger',
  'dark',
  'warning',
];

storiesOf('Button', module)
  .addParameters({component: Button})
  .add('Solid Buttons', () => {
    return (
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        {colors.map((item) => (
          <Button key={item} variant='contained' color={item} size='small'>
            {item.charAt(0).toUpperCase() + item.slice(1)}
          </Button>
        ))}
      </div>
    );
  })
  .add('Text Buttons', () => {
    return (
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        {colors.map((item) => (
          <Button key={item} variant='text' color={item} size='small'>
            {item.charAt(0).toUpperCase() + item.slice(1)}
          </Button>
        ))}
      </div>
    );
  })
  .add('Outlined Buttons', () => (
    <div
      style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '5px',
      }}>
      {colors.map((item) => (
        <Button key={item} variant='outlined' color={item} size='small'>
          {item.charAt(0).toUpperCase() + item.slice(1)}
        </Button>
      ))}
    </div>
  ))
  .add('Disabled', () => (
    <div style={{textAlign: 'start'}}>
      <Button disabled variant='contained' color='info' size='small'>
        Disabled
      </Button>
    </div>
  ))
  .add('Size', () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-end',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        <Button variant='outlined' color='outline-primary' size='small'>
          Small
        </Button>
        <Button variant='outlined' color='outline-primary' size='medium'>
          Medium
        </Button>
        <Button variant='outlined' color='outline-primary' size='large'>
          Large
        </Button>
      </div>
    );
  })
  .add('Disabled', () => (
    <div style={{textAlign: 'start'}}>
      <Button disabled variant='contained' color='info' size='small'>
        Disabled
      </Button>
    </div>
  ))
  .add('Loading', () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-end',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        <Button isLoading size='small'>
          Loading...
        </Button>
      </div>
    );
  })
  .add('Rounded', () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-end',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        <Button rounded variant='outlined' color='success' size='medium'>
          Rounded
        </Button>
      </div>
    );
  })
  .add('Circular button', () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-end',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        {colors.map((item) => (
          <Button
            key={item}
            variant='outlined'
            color={item}
            iconBtn
            size='small'>
            1
          </Button>
        ))}
      </div>
    );
  })
  .add('Button with icon', () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'flex-end',
          flexWrap: 'wrap',
          gap: '5px',
        }}>
        <Button variant='contained' color='light' iconBtn size='small'>
          <img width='20px' height='20px' src={DeleteIcon} alt='delete' />
        </Button>
        <Button variant='contained' color='light' size='small'>
          <img width='20px' height='20px' src={DeleteIcon} alt='delete' />
        </Button>
      </div>
    );
  });
