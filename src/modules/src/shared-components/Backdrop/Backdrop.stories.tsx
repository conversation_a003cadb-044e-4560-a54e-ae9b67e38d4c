import {ComponentMeta, ComponentStory} from '@storybook/react';
import {useState} from 'react';

import Button from '../Button/Button';
import Backdrop from './Backdrop';

export default {
  title: 'Backdrop',
  component: Backdrop,
} as ComponentMeta<typeof Backdrop>;

const Template: ComponentStory<typeof Backdrop> = function BackdropTemplate() {
  const [isOpen, setIsOpen] = useState(false);
  const handleIsOpen = (state: boolean) => {
    setIsOpen(state);
  };

  return (
    <>
      <Button
        type='button'
        variant='contained'
        color='info'
        onClick={() => handleIsOpen(true)}>
        Show Backdrop
      </Button>
      <Backdrop isOpen={isOpen} handleClose={() => handleIsOpen(false)}>
        <div
          style={{
            width: '200px',
            height: '200px',
            background: 'white',
            padding: '2rem',
          }}>
          Hello
        </div>
      </Backdrop>
    </>
  );
};

export const BackdropComponent = Template.bind({});

BackdropComponent.args = {
  isOpen: false,
};
