@import '../../utils/main.scss';

@mixin scrollbar($color) {
  &::-webkit-scrollbar {
    width: 8px;
    @include margin(-8px);
  }
  &::-webkit-scrollbar-thumb {
    background-color: $color;
    border-radius: 5px;
  }
}

.select-field {
  width: 100%;
  font-family: inherit;
  position: relative;
  box-sizing: border-box;
  font-family: $font-family-name;
  text-transform: capitalize;
  &-container {
    width: inherit;
    @include padding(5px);
    box-sizing: border-box;
    background: $light;
    box-shadow: $base-box-shadow;
    border: 1px solid $gray-300;
    @include font-size(15px);
    min-height: 40px;
    border-radius: 5px;
    font-weight: medium;
    @include flex-item(_, space-between, center, wrap);
    text-align: left;
    .divider {
      display: inline-block;
      width: 2px;
      height: 25px;
      background-color: $gray-500;
      @include margin(0 10px 0 5px);
    }
    .end-clear-box {
      height: max-content;
      @include flex-item(row, center, center, nowrap);
      color: $dark;
      .down-icon {
        background: inherit;
        outline: none;
        border: 0;
      }
      .up-icon {
        transform: rotate(180deg);
      }
    }
    .clear-all {
      height: inherit;
      @include flex-item(row, center, center, nowrap);
      background: $light;
      @include font-size(18px);
      border: none;
      cursor: pointer;
      transition: all 0.5s;
      color: $dark;
      @include margin-left(15px);
      @include margin-right(5px);
    }
    &-btn {
      flex: 1;
      width: 90%;
      max-height: 70px;
      @include flex-item(_, flex-start, center, wrap, 5px);
      overflow-y: scroll;
      @include scrollbar($gray-500);
      .filtered-options {
        .filtered-select {
          @include flex-item();
          .filtered-img {
            width: 25px;
            height: 25px;
            @include flex-item();
            @include margin-right(10px);

            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      &-filters {
        width: max-content;
        @include flex-item(row, flex-start, center, wrap, 5px);
        @include padding(5px);
        border-radius: 3px;
        background: $gray-200;
        color: $dark;
        cursor: auto;
        font-weight: medium;
        transition: all 0.4s;
        text-transform: capitalize;
        .filter-divider {
          display: inline-block;
          width: 1px;
          height: 12px;
          background-color: $gray-500;
          @include margin(0 -2px 0 2px);
        }
        .filter-button {
          outline: none;
          border: none;
          background: inherit;
          color: inherit;
          cursor: pointer;
          transition: all 0.4s;
          @include padding(2px 5px);
          &:hover {
            background: $gray-400;
          }
        }
      }
      &-input {
        width: 100px;
        @include font-size(16px);
        padding: 0;
        margin: 0;
        background: $light;
        outline: none;
        border: none;
        &::placeholder {
          @include font-size(16px);
          font-family: inherit;
        }
      }
    }
  }

  &-content {
    width: 100%;
    max-height: 200px;
    overflow-y: scroll;
    position: absolute;
    text-align: left;
    top: 110%;
    left: 0;
    background: $light;
    box-shadow: $base-box-shadow;
    border: 1px solid $gray-300;
    border-top: none;
    font-weight: medium;
    @include flex-item(column, flex-start, center, _, 5px);
    color: $dark;
    &-item {
      width: 100%;
      @include flex-item(_, flex-start, center, _, _);
      background: $light;
      @include padding(8px);
      transition: all 0.2s;
      text-transform: capitalize;
      .checkbox {
        width: 18px;
        height: 18px;
      }
      .select-img {
        width: 25px;
        height: 25px;
        @include flex-item(_, _, normal, _, _);

        img {
          width: 100%;
          height: 100%;
        }
      }
      button {
        flex: 1;
        text-align: left;
        @include font-size(16px);
        background-color: inherit;
        @include padding-left(15px);
        cursor: pointer;
        outline: none;
        border: none;
      }
      &:hover {
        background: $gray-200;
      }
    }
    .item-bg {
      background-color: $gray-300;
    }
    @include scrollbar($gray-500);
  }
}

.bottom-top {
  top: auto;
  width: 99.8%;
  bottom: 100%;
  @include margin-top(0px);
  @include margin-bottom(-1px);
  border: 1px solid $gray-300;
  box-shadow: none;
}
