import {Meta, <PERSON>} from '@storybook/react';

import ProgressBar, {ProgressBarProps} from './ProgressBar';

const meta: Meta = {
  title: 'ProgressBar',
  component: ProgressBar,
};

export default meta;

const Template: Story<ProgressBarProps> = function progressBar(args) {
  const {progress, bgColor, showProgressPercentage, total} = args;
  return (
    <ProgressBar
      progress={progress}
      bgColor={bgColor}
      showProgressPercentage={showProgressPercentage}
      total={total}
    />
  );
};

export const Default = Template.bind({});
export const Progress = Template.bind({});

Default.args = {
  progress: 80,
  bgColor: 'primary',
  showProgressPercentage: false,
};

Progress.args = {
  progress: 80,
  bgColor: 'warning',
  showProgressPercentage: true,
};
