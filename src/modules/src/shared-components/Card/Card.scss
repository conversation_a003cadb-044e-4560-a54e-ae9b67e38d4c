@import '../../utils/colors.scss';
@import '../../utils/main.scss';

.card-wrapper {
  width: max-content;
  height: max-content;
  background-color: $white;
  box-shadow: $base-box-shadow;
}

.card-header {
  font-size: $font-size-lg;
  font-weight: 600;
  @include margin(6px 0 12px 0);
  @include padding(12px);
  font-family: $font-family-name;
  border-bottom: 1px solid $defaultColor;
}

.card-content {
  font-size: $font-size-md;
  @include margin(12px 0px);
  @include padding(12px);
  font-family: $font-family-name;
}

.card-footer {
  font-size: $font-size-sm;
  font-weight: 600;
  @include padding(12px);
  font-family: $font-family-name;
  @include flex-item(row, flex-start, center);
  border-top: 1px solid $defaultColor;
}

.rounded {
  border-radius: 4px;
}

.border {
  border: 1px solid $defaultColor;
}
