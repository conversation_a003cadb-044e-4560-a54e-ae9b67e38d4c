@import "../../utils/colors.scss";
@import "../../utils/main.scss";

.select {
  option {
    min-width: 180px;
    min-height: 32px;
    font-size: $base-font-size;
    font-family: $font-family-name;
    color: $black;
  }

  option .dropdown_item {
    font-size: $font-size-lg;
  }
  .placeholder {
    font-size: $base-font-size;
    font-family: $font-family-name;
    background-color: $gray-400;
    color: $white;
  }
  .option-placeholder {
    color: $white;
    background-color: $gray-400;
  }
  .dropdown {
    width: 100%;
    height: 32px;
    border: 1px solid $gray-300;
    border-radius: 5px;
    @include padding-left(10px);
    outline: none;
    &:hover {
      border: 1px solid $gray-600;
    }
  }
}
