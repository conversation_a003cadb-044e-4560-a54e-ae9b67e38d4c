@import '../../utils/main.scss';
@import '../../utils/flex';

.textarea-container {
  @include padding(5px);
  button {
    background: none;
    border: none;
  }
  .form-textarea {
    &:hover {
      border: 1px solid;
    }
    @include flex-item('', '', '', '', '');
    border: 1px solid $defaultColor;
    @include rfs(4px, border-radius);
    cursor: text;
    transition: all 0.2s ease;
    min-height: 40px;
    .textarea-group {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 2px;
    }
    .textarea-group label {
      color: $gray-700;
      font-weight: 400;
      line-height: 20px;
      position: absolute;
      pointer-events: none;
      left: 10px;
      top: -10px;
      background-color: $white;
      @include flex-item('', '', center, '', '');
      span {
        @include padding(0 5px 0 5px);
      }
    }
    .textarea-group textarea {
      display: block;
      border: none !important;
      outline: none;
      width: 100%;
      height: 100%;
      @include padding(6px 16px 4px);
      resize: vertical;
    }
  }
  .disabled {
    &:hover {
      cursor: not-allowed;
      border: 1px solid $gray-400;
    }
    textarea {
      pointer-events: none;
      user-select: none;
      &:disabled {
        background-color: $white;
        &::placeholder {
          color: $gray-400;
        }
      }
    }
    label {
      transition: none;
    }
  }
  .disabled {
    background-color: $white;
    label {
      color: $gray-400 !important;
    }
  }
}
