import {ComponentMeta, ComponentStory} from '@storybook/react';

import Container from './Container';

export default {
  title: 'Container',
  component: Container,
} as ComponentMeta<typeof Container>;

const Template: ComponentStory<typeof Container> = function ContainerTemplate(
  args
) {
  const {maxWidth} = args;
  return (
    <Container maxWidth={maxWidth}>
      <div style={{backgroundColor: 'skyblue', height: '100vh'}} />
    </Container>
  );
};

export const ContainerTemplate = Template.bind({});

ContainerTemplate.args = {
  maxWidth: 'sm',
};
