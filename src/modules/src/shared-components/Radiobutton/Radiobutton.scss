@import '../../utils/colors.scss';
@import '../../utils/main.scss';

.radio {
  display: flex;
  align-items: center;
}

.containers {
  height: 0;
  display: block;
  position: relative;
  @include padding-left(25px);
  @include margin-bottom(30px);
  cursor: pointer;
  font-family: sans-serif;
  font-size: $base-font-size;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default radio button */
.containers input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

/* Create a custom radio button */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: $gray-300;
  border-radius: 50%;
}

/* On mouse-over, add a grey background color */
.containers:hover input ~ .checkmark {
  background-color: $gray-500;
}

/* When the radio button is checked, add a blue background */
.containers input:checked ~ .checkmark {
  background-color: $primary;
}

/* Create the indicator (the dot/circle - hidden when not checked) */
.checkmark:after {
  content: '';
  position: absolute;
  display: none;
}

/* Show the indicator (dot/circle) when checked */
.containers input:checked ~ .checkmark:after {
  display: block;
}

/* Style the indicator (dot/circle) */
.containers .checkmark:after {
  top: 4px;
  left: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: $white;
}

.label {
  @include margin-right(12px);
}

.vertical-align {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

$radioButtonSizes: (
  'small': 16,
  'medium': 18,
  'large': 21,
);

@each $key, $val in $radioButtonSizes {
  .radiobutton_#{$key} {
    width: $val * 1px;
    height: $val * 1px;
  }
}

.containers .radiobutton_medium::after {
  top: 5px;
  left: 5px;
}

.containers .radiobutton_large::after {
  top: 7px;
  left: 7px;
}

// Style for disabled radio button
.containers.disabled {
  cursor: default;
  opacity: 0.6;
  input ~ .checkmark {
    background-color: $gray-500;
  }
  &:hover input ~ .checkmark {
    background-color: $gray-500;
  }
}
