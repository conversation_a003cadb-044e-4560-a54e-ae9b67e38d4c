@import '../../utils/flex';
@import '../../utils/main.scss';
@import '../../utils/_colors.scss';

.avatar-container {
  .avatar-image {
    border-radius: 5px;
    box-sizing: unset;
    img {
      border-radius: 4px;
      width: 100%;
      height: 100%;
    }
    .rounded {
      @include rfs(50%, border-radius);
    }
  }
  .avatar-text {
    border-radius: 4px;
    background-color: $info;
    text-transform: uppercase;
    @include flex-item(row, center, center, _, _);
    font-weight: 700;
  }

  /*** rounded border buttons */

  @mixin size($width, $height, $font-size) {
    width: $width;
    height: $height;
    font-size: $font-size;
  }

  .rounded {
    @include rfs(50%, border-radius);
  }

  .small {
    @include size(50px, 50px, 20px);
  }

  .medium {
    @include size(80px, 80px, 30px);
  }
  .large {
    @include size(120px, 120px, 45px);
  }
}
