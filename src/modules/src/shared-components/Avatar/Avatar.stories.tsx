import {ComponentMeta, ComponentStory} from '@storybook/react';

import Avatar from './Avatar';

export default {
  title: 'Avatar',
  component: Avatar,
} as ComponentMeta<typeof Avatar>;

const Template: ComponentStory<typeof Avatar> = function AvatarTemplate(args) {
  const {firstName, lastName, imageUrl, alt, rounded, size} = args;
  const AvatarStyle = {
    width: '500px',
    margin: '20px',
    display: 'flex',
    gap: '50px',
  };
  return (
    <div style={AvatarStyle}>
      <div>
        <Avatar
          firstName={firstName}
          lastName={lastName}
          rounded={rounded}
          imageUrl={imageUrl}
          alt={alt}
          size={size}
        />
      </div>
    </div>
  );
};

export const AvatarComponent = Template.bind({});

AvatarComponent.args = {
  firstName: 'John',
  lastName: 'David',
  imageUrl:
    'https://res.cloudinary.com/de9kghuz8/image/upload/v1658372152/thumbnail_Newuser_58f9959886.png',
  alt: 'avatar',
};
