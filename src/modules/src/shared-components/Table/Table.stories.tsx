import {ComponentMeta, ComponentStory} from '@storybook/react';

import Table from './Table';

export default {
  title: 'Table',
  component: Table,
} as ComponentMeta<typeof Table>;

const Template: ComponentStory<typeof Table> = function TableTemplate(args) {
  const {columns, data, sortableColumns, hasBorder, hasCheckbox, isSearch} =
    args;
  return (
    <div>
      <Table
        columns={columns}
        data={data}
        sortableColumns={sortableColumns}
        hasBorder={hasBorder}
        hasCheckbox={hasCheckbox}
        isSearch={isSearch}
      />
    </div>
  );
};

export const TableComponent = Template.bind({});

TableComponent.args = {
  columns: [
    {title: 'Serial No', dataIndex: 'id', type: 'number'},
    {title: 'User Name on Application', dataIndex: 'name', type: 'string'},
    {title: 'Email', dataIndex: 'email', type: 'string'},
    {title: 'Date(MM/DD/YYYY)', dataIndex: 'date', type: 'date'},
  ],
  data: [
    {
      id: '1',
      name: 'A',
      email: '<EMAIL>',
      date: '01/01/2021',
    },
    {
      id: '2',
      name: 'C',
      email: '<EMAIL>',
      date: '03/03/2021',
    },
    {
      id: '3',
      name: 'DB',
      email: '<EMAIL>',
      date: '06/06/2021',
    },
    {
      id: '4',
      name: 'F',
      email: '<EMAIL>',
      date: '02/02/2021',
    },
    {
      id: '5',
      name: 'D',
      email: '<EMAIL>',
      date: '08/02/2021',
    },
    {
      id: '6',
      name: 'DA',
      email: '<EMAIL>',
      date: '12/06/2021',
    },
    {
      id: '7',
      name: 'B',
      email: '<EMAIL>',
      date: '11/12/2021',
    },
  ],
  sortableColumns: ['date', 'name'],
  hasBorder: true,
  hasCheckbox: true,
  isSearch: true,
};
