import React from 'react';

interface DownIconProps {
  width?: number;
  height?: number;
  className?: string;
  color?: string;
}

function DownIcon(props: DownIconProps) {
  const {width, height, className, color} = props;
  return (
    <svg
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
      fill={color}
      className={`bi bi-chevron-down ${className}`}
      viewBox='0 0 16 16'>
      <path d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z' />
    </svg>
  );
}
DownIcon.defaultProps = {
  width: 20,
  height: 20,
  className: '',
  color: 'currentColor',
};
export default DownIcon;
