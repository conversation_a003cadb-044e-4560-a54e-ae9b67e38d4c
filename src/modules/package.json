{"name": "db_bank_fe", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react-dom": "^18.0.6", "@types/react-router-dom": "^5.3.3", "@types/react-transition-group": "^4.4.5", "babel-plugin-macros": "^3.1.0", "i18next": "^21.8.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.1", "i18next-xhr-backend": "^3.2.2", "lint-staged": "^13.2.0", "moment": "^2.29.4", "react-i18next": "^11.18.3", "react-icons": "^4.4.0", "react-router": "^6.3.0", "react-router-dom": "^6.4.3", "react-scripts": "5.0.1", "react-transition-group": "^4.4.5", "rfs": "^9.0.6", "sass": "^1.54.9", "storybook": "^6.5.16", "styled-components": "^5.3.5", "typescript": "^4.7.4", "web-vitals": "^2.1.4"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "scripts": {"start": "set PORT=3005 && react-scripts start", "lin:start": "PORT=3006 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint .", "fix": "eslint . --fix", "check-types": "tsc -p tsconfig.json --noEmit", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "format": "prettier --write \"./**/*.{ts,tsx,json}", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.stories.tsx"]}, "devDependencies": {"@babel/core": "^7.18.10", "@storybook/addon-actions": "^6.5.10", "@storybook/addon-essentials": "^6.5.10", "@storybook/addon-interactions": "^6.5.10", "@storybook/addon-links": "^6.5.10", "@storybook/builder-webpack5": "^6.5.10", "@storybook/manager-webpack5": "^6.5.10", "@storybook/node-logger": "^6.5.10", "@storybook/preset-create-react-app": "^4.1.2", "@storybook/react": "^6.5.10", "@storybook/testing-library": "^0.0.13", "@trivago/prettier-plugin-sort-imports": "^3.3.0", "@types/react": "^18.0.17", "@types/styled-components": "^5.1.25", "@typescript-eslint/eslint-plugin": "^5.32.0", "@typescript-eslint/parser": "^5.32.0", "babel-eslint": "^10.1.0", "babel-loader": "^8.2.5", "css-loader": "^6.7.1", "eslint": "^8.21.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.6.4", "husky": "^8.0.1", "prettier": "^2.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-test-renderer": "^18.2.0", "sass-loader": "^10.2.0", "style-loader": "^3.3.1", "typescript": "^4.7.4"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint"]}}