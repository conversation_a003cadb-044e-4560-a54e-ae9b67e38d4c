# Coding practice for the project

1. All branch names must in coordination with the feature/component that we're
   building eg: for input field - feat-input-field similarly some prefixes will
   be: chore, fix
2. For commits there are different prefix that we need to follow eg: for a chore
   prefix with be chore-"descriptive message about the chore" similarly we will
   use feat for new feature, fix for bug fix, chore for chores, test for test
   coverage or test updates etc.
3. In every PR there should be description of the PR, screenshot of the
   component and test coverage screenshot of the component
4. Test coverage should be 100% for all the components use npx majestic and
   record coverage for components. Please add screenshot of the component once
   the PR is given in the comment section
5. DO NOT disable any eslint rule, eslint rules are in place to follow best
   practices and right good code.
6. Check README.md (this space) to make sure all the coding practices given here
   are followed

## How to run the project on local?

1. bitbucket
2. clone eximhero-frontend
3. checkout release/develop
4. "git submodule update --init --recursive"
5. yarn install
6. yarn start

## How to raise PR against submodule and update branch?

1. Create a new branch from main (release/develop) branch of the submodule,
2. Raise the PR from that newly created branch against the submodule main
   (release/develop) branch
3. Once submodule PR is merged update the Exim Hero with the latest changes, to
   do that pull the release/develop changes in the modules of Exim Hero, and
   raised the PR with the updated commit version
